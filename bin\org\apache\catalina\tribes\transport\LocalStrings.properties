# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

PooledSender.senderDisconnectFail=Failed to disconnect sender

pooledSender.closed.queue=Queue is closed

receiverBase.bind.failed=Failed bind replication listener on address:[{0}]
receiverBase.socket.bind=Receiver Server Socket bound to:[{0}]
receiverBase.udp.bind=UDP Receiver Server Socket bound to:[{0}]
receiverBase.unable.bind=Unable to bind server socket to:[{0}] throwing error.
receiverBase.unable.bind.udp=Unable to bind UDP socket to:[{0}] throwing error.
