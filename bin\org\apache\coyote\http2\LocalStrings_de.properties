# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractStream.windowSizeInc=Verbindung [{0}], Stream [{1}], erhöhe Flow Contrrol Window um [{2}] auf [{3}]

connectionPrefaceParser.mismatch=Es wurde eine unerwartete Byte Sequenz beim Start der Client Preface Phase [{0}] empfangen

connectionSettings.debug=Verbindung [{0}], Parameter typ [{1}] gesetzt auf [{2}]
connectionSettings.headerTableSizeLimit=Verbindung [{0}], versuchte die Kopfzeilentabellengröße auf [{1}] zu setzen aber die Grenze liegt bei 16k

hpack.invalidCharacter=Das Unicode Zeichen [{0}] an Code Punkt [{1}] kann nicht kodiert werden, da es außerhalb des erlaubten Bereiches von 0 bis 255 ist.

hpackdecoder.maxMemorySizeExceeded=Die header table Größe [{0}] überschreitet die maximale Größe von [{1}]
hpackdecoder.nullHeader=Null header bei Index [{0}]

http2Parser.headerLimitSize=Verbindung [{0}], Stream [{1}], Gesamt-Header-Größe zu groß
http2Parser.processFrameData.window=Verbindung [{0}], Client hat mehr Daten gesendet als das Stream-Fenster zulässt
http2Parser.processFrameHeaders.decodingDataLeft=Nach der HPACK-Dekodierung sind noch Daten übrig - die hätten verarbeitet sein sollen

stream.header.unknownPseudoHeader=Verbindung [{0}], Stream [{1}], Unbekannten Pseudo-Header [{2}] empfangen

streamProcessor.service.error=Fehler bei der Anfrageverarbeitung

upgradeHandler.ioerror=Verbindung [{0}]
upgradeHandler.pingFailed=Verbindung [{0}] – Das Senden eines ''ping'' zum Klienten schlug fehl.
upgradeHandler.socketCloseFailed=Fehler beim Schließen des Sockets.
upgradeHandler.upgrade=Verbindung [{0}], HTTP/1.1 Upgrade auf Stream [1]
upgradeHandler.upgrade.fail=Verbindung [{0}], HTTP/1.1 upgrade fehlgeschlagen
upgradeHandler.windowSizeTooBig=Verbindung [{0}], Stream [{1}], Fenster-Größe zu groß
upgradeHandler.writeHeaders=Verbindung [{0}], Stream [{1}]
