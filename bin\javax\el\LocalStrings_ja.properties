# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

beanNameELResolver.beanReadOnly=Bean名[{0}]は読み取り専用です

elProcessor.defineFunctionInvalidClass=クラス [{0}] はpublicではありません。
elProcessor.defineFunctionInvalidMethod=クラス [{1}] のメソッド [{0}] は public static メソッドではありません。
elProcessor.defineFunctionInvalidParameterList=クラス [{2}] のメソッド [{1}] に不正なパラメーターリスト [{0}] が指定されました。
elProcessor.defineFunctionInvalidParameterTypeName=クラス[{2}]のメソッド[{1}]のパラメータタイプ[{0}]が無効です
elProcessor.defineFunctionNoMethod=クラス[{1}]のpublic staticメソッド[{0}]が見つかりませんでした。
elProcessor.defineFunctionNullParams=1つ以上の入力パラメータがnullでした。

expressionFactory.cannotCreate=型[{0}]のExpressionFactoryを作成できません。
expressionFactory.cannotFind=[{0}]型のExpressionFactoryを見つけることができません。
expressionFactory.readFailed=[{0}]の読み取りに失敗しました

importHandler.ambiguousImport=クラス [{0}] はすでにインポートした [{1}] と衝突するためインポートできません。
importHandler.ambiguousStaticImport=static import [{0}] はすでにインポートした [{1}] と衝突するため処理できません。
importHandler.classNotFound=存在しないクラス [{0}] はインポートできません。
importHandler.invalidClass=クラス[{0}]は publicで、(Java 9以降では) エクスポートされたパッケージ中にあり、非abstract で非インタフェースである必要があります
importHandler.invalidClassName=インポートするクラスの名前[{0}]にはパッケージが含まれている必要があります
importHandler.invalidClassNameForStatic=クラス [{0}] の static import [{1}] は不正です。
importHandler.invalidStaticName=インポートするstaticメソッドまたはフィールドの名前[{0}]にはクラスが含まれている必要があります。
importHandler.staticNotFound=インポート[{2}]の静的インポート[{0}]はクラス[{1}]で見つかりませんでした

lambdaExpression.tooFewArgs=少なくとも[{1}]を必要とするラムダ式に対しては、[{0}]引数のみが提供されました。

objectNotAssignable=クラス [{0}] のオブジェクトはクラス [{1}] のオブジェクト配列へ追加できません。
propertyNotFound=プロパティ[{1}]がタイプ[{0}]で見つかりません
propertyNotReadable=タイプ[{0}]でプロパティ[{1}]を読み込めません。
propertyNotWritable=プロパティ[{1}]はタイプ[{0}]に書き込み可能ではありません
propertyReadError=タイプ[{0}]の[{1}]の読み取りエラー
propertyWriteError=タイプ[{0}]の[{1}]への書き込みエラー

staticFieldELResolver.methodNotFound=クラス[{1}]に[{0}]という名前に一致するpublic staticメソッドが見つかりません。
staticFieldELResolver.notFound=(Java 9 以降ではエクスポートされた) クラス[{1}]に[{0}]という名前のpublic staticフィールドが見つかりませんでした。
staticFieldELResolver.notWriteable=静的フィールド（この場合、クラス[{1}]のフィールド[{0}）への書き込みは許可されていません。

util.method.ambiguous=曖昧さのないメソッドを見つけることができません：{0}。{1}（{2}）
util.method.notfound=メソッドが見つかりません：{0}。{1}（{2}）
