# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jsseSupport.certTranslationError=错误的转换证书[{0}]
jsseSupport.clientCertError=尝试从客户端获取证书时出错

jsseUtil.excludeProtocol=此JRE支持的SSL协议[{0}]已从Tomcat可用的协议中排除
jsseUtil.noDefaultProtocols=无法确定sslEnabledProtocols的默认值。设置显式值以确保连接器可以启动。

pemFile.noMultiPrimes=PKCS#1证书是多素数格式的，Java不提供从该格式构造RSA私钥对象的API
pemFile.notValidRFC5915=提供的key文件不符合RFC 5915
pemFile.parseError=无法从 [{0}] 解析 key
