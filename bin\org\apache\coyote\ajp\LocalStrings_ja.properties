# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

ajpMessage.invalidPos=AJP メッセージの終端より先の位置 [{0}] のバイト読み取りを要求されました。

ajpmessage.invalid=署名[{0}]で無効なメッセージが受信されました。
ajpmessage.invalidLength=長さ[{0}]の無効なメッセージが受信されました
ajpmessage.null=null 値は追加できません。
ajpmessage.overflow=バッファーの位置 [{1}] へ [{0}] バイトのデータを追加しようとして、オーバーフローエラーが発生しました。

ajpprocessor.certs.fail=証明書変換に失敗しました。
ajpprocessor.header.error=ヘッダーメッセージの解析に失敗しました
ajpprocessor.header.tooLong=受信したヘッダーに指定されたメッセージ長は [{0}] ですがpacketSize は [{1}] しかありません。
ajpprocessor.readtimeout=ソケットからのデータの読み取りをタイムアウトにします。
ajpprocessor.request.prepare=リクエスト準備中エラー
ajpprocessor.request.process=リクエスト処理エラー

ajpprotocol.noSSL=AJP は SSL に対応していません。SSL ホスト構成 [{0}] を無視します。
ajpprotocol.noSecret=AJP コネクタは secretRequired="true" として構成されていますが、secret 属性は null または空文字列が設定されています。この組み合わせは有効ではありません
ajpprotocol.noUpgrade=AJP はプロトコルアップグレードに対応していないため、[{0}] の設定を無視しました。
ajpprotocol.noUpgradeHandler=アップグレードはAJPではサポートされていません。 HttpUpgradeHandler [{0}]を処理できません。
