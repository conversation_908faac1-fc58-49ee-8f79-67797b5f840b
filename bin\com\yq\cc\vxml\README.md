# IVR 到 VXML 转换工具

## 概述

`IvrToVxmlUtil` 是一个用于将 JSON 格式的 IVR（交互式语音应答）呼叫流程配置转换为 VXML（Voice XML）格式的工具类。

## 功能特性

- 支持从 JSON 文件读取 IVR 配置
- 自动解析 IVR 流程中的节点和连线关系
- 生成符合 VXML 2.1 标准的语音应用
- 支持多种节点类型：开始节点、用户任务节点、服务任务节点、结束节点
- 支持条件分支逻辑
- 自动生成语法规则和错误处理

## 使用方法

### 1. 从文件转换

```java
// 从 JSON 文件转换为 VXML
String vxml = IvrToVxmlUtil.convertFromFile("path/to/ivr.json");
System.out.println(vxml);
```

### 2. 从 JSON 字符串转换

```java
// 从 JSON 字符串转换为 VXML
String jsonContent = "{ ... }"; // IVR JSON 配置
String vxml = IvrToVxmlUtil.convertJsonToVxml(jsonContent);
System.out.println(vxml);
```

### 3. 运行测试

```bash
# 编译
javac -encoding UTF-8 -cp "lib/json-20160810.jar;E:/work/server/mars/lib/fastjson-1.2.62.jar" -d bin src/com/yq/cc/vxml/IvrToVxmlUtil.java

# 运行
java -cp "bin;lib/json-20160810.jar;E:/work/server/mars/lib/fastjson-1.2.62.jar" com.yq.cc.vxml.IvrToVxmlUtil
```

## JSON 配置格式

IVR JSON 配置应包含以下主要结构：

```json
{
  "ivrCode": "vxml01",
  "ivrName": "vxml测试流程",
  "nodeList": [
    {
      "id": "START_xxx",
      "name": "开始节点",
      "type": "start",
      "multiInstance": {
        "voicePlayContent": "欢迎语音内容"
      }
    },
    {
      "id": "USERTASK_xxx",
      "name": "播放语音",
      "type": "userTask",
      "voicePlayContent": "语音播放内容"
    },
    {
      "id": "SERVICETASK_xxx",
      "name": "用户输入",
      "type": "serviceTask",
      "paramName": "SERVICE_TASK_RESULT",
      "paramMinSize": "1",
      "paramMaxSize": "1"
    },
    {
      "id": "END_xxx",
      "name": "结束节点",
      "type": "end"
    }
  ],
  "lineList": [
    {
      "id": "LINE_xxx",
      "from": "START_xxx",
      "to": "USERTASK_xxx",
      "label": "连线标签"
    },
    {
      "id": "LINE_xxx",
      "from": "SERVICETASK_xxx",
      "to": "END_xxx",
      "label": "用户输入1",
      "expression": {
        "param": "SERVICE_TASK_RESULT",
        "conditions": "eq",
        "val1": "1"
      }
    }
  ]
}
```

## 节点类型说明

### 1. 开始节点 (start)
- 流程的入口点
- 通常不包含语音内容
- 自动跳转到下一个节点

### 2. 用户任务节点 (userTask)
- 用于播放语音内容
- `voicePlayContent` 字段包含要播放的文本
- 播放完成后自动跳转到下一个节点

### 3. 服务任务节点 (serviceTask)
- 用于接收用户输入（按键）
- `paramName` 定义变量名
- `paramMinSize` 和 `paramMaxSize` 定义输入长度
- 支持条件分支逻辑

### 4. 结束节点 (end)
- 流程的结束点
- 可以包含结束语音
- 自动执行 `<exit/>` 退出应用

## 生成的 VXML 结构

生成的 VXML 包含以下主要部分：

1. **XML 声明和根元素**
2. **主表单 (mainForm)**
   - 语音播放块 (`<block>`)
   - 用户输入字段 (`<field>`)
   - 语法规则 (`<grammar>`)
   - 条件逻辑 (`<if>`)
   - 错误处理 (`<noinput>`, `<nomatch>`)
3. **结束块 (endBlock)**
   - 结束语音
   - 退出指令

## 依赖库

- FastJSON 1.2.62 (用于 JSON 解析)
- 或者 org.json (备选 JSON 库)

## 注意事项

1. JSON 文件应使用 UTF-8 编码
2. 生成的 VXML 使用 GBK 编码（可根据需要修改）
3. 工具会自动处理 XML 特殊字符转义
4. 支持循环引用检测，避免无限递归
5. 对于复杂的流程分支，建议仔细测试生成的 VXML

## 扩展性

该工具类设计为可扩展的，您可以：

1. 添加新的节点类型处理
2. 自定义 VXML 生成逻辑
3. 支持更多的条件表达式
4. 添加更多的错误处理机制

## 示例输出

基于提供的 `ivr.json` 文件，工具会生成包含以下功能的 VXML：

1. 播放两段语音内容
2. 等待用户按键输入
3. 根据用户输入（是否为"1"）播放不同的响应语音
4. 最终退出应用

生成的 VXML 文件可以直接用于支持 VXML 2.1 标准的语音平台。
