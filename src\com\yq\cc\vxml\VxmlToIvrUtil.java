package com.yq.cc.vxml;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.w3c.dom.*;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.*;
import java.util.*;

/**
 * VXML 转 IVR JSON 配置工具类
 *
 * 功能：解析 VXML 文件，生成对应的 IVR JSON 配置
 *
 * <AUTHOR>
 * @version 1.0
 */
public class VxmlToIvrUtil {

    /**
     * 主方法 - 用于测试
     */
    public static void main(String[] args) {
        try {
            // 读取 VXML 文件
            String vxmlFilePath = "src/com/yq/cc/vxml/ivr.vxml";
            String jsonResult = convertVxmlToIvrJson(vxmlFilePath);

            System.out.println("生成的 IVR JSON:");
            System.out.println(jsonResult);

            // 保存到文件
            String outputPath = "src/com/yq/cc/vxml/generated_ivr.json";
            saveJsonToFile(jsonResult, outputPath);
            System.out.println("\nIVR JSON 已保存到 " + outputPath);

        } catch (Exception e) {
            System.err.println("转换过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 将 VXML 文件转换为 IVR JSON 配置
     *
     * @param vxmlFilePath VXML 文件路径
     * @return IVR JSON 字符串
     * @throws Exception 转换异常
     */
    public static String convertVxmlToIvrJson(String vxmlFilePath) throws Exception {
        // 解析 VXML 文档
        Document doc = parseVxmlFile(vxmlFilePath);

        // 创建 IVR JSON 结构
        JSONObject ivrJson = new JSONObject();

        // 设置基本信息
        setupBasicInfo(ivrJson);

        // 解析 VXML 结构并生成节点和连线
        VxmlParseResult parseResult = parseVxmlStructure(doc);

        // 设置节点列表
        ivrJson.put("nodeList", parseResult.nodeList);

        // 设置连线列表
        ivrJson.put("lineList", parseResult.lineList);

        return JSON.toJSONString(ivrJson, true);
    }

    /**
     * 解析 VXML 文件
     */
    private static Document parseVxmlFile(String filePath) throws ParserConfigurationException, SAXException, IOException {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setNamespaceAware(true);
        DocumentBuilder builder = factory.newDocumentBuilder();

        // 使用UTF-8编码读取文件
        try (FileInputStream fis = new FileInputStream(filePath);
             InputStreamReader isr = new InputStreamReader(fis, "GBK")) {
            return builder.parse(new org.xml.sax.InputSource(isr));
        }
    }

    /**
     * 设置基本信息
     */
    private static void setupBasicInfo(JSONObject ivrJson) {
        ivrJson.put("ivrCode", "vxml_generated_" + System.currentTimeMillis());
        ivrJson.put("servicePhoneId", new JSONArray().fluentAdd("generated_phone_id"));
        ivrJson.put("bakup", "");
        ivrJson.put("ivrName", "VXML转换生成的IVR流程");
        ivrJson.put("ivrType", "1");
        ivrJson.put("icon", "layui-icon-app");
        ivrJson.put("id", String.valueOf(System.currentTimeMillis()));
        ivrJson.put("idxNum", "1");
        ivrJson.put("enableStatus", "4");
        ivrJson.put("ivrImg", "/ivr_img/generated.png");
    }

    /**
     * VXML 解析结果
     */
    private static class VxmlParseResult {
        JSONArray nodeList = new JSONArray();
        JSONArray lineList = new JSONArray();
    }

    /**
     * 解析 VXML 结构
     */
    private static VxmlParseResult parseVxmlStructure(Document doc) {
        VxmlParseResult result = new VxmlParseResult();

        // 节点ID计数器
        Map<String, Integer> nodeCounters = new HashMap<>();
        nodeCounters.put("START", 1);
        nodeCounters.put("USERTASK", 1);
        nodeCounters.put("SERVICETASK", 1);
        nodeCounters.put("END", 1);
        nodeCounters.put("LINE", 1);

        // 存储节点ID映射
        Map<String, String> nodeIdMap = new HashMap<>();

        // 1. 创建开始节点
        String startNodeId = createStartNode(result, nodeCounters);

        // 2. 解析 form 中的内容
        NodeList formList = doc.getElementsByTagName("form");
        if (formList.getLength() > 0) {
            Element form = (Element) formList.item(0);
            parseFormContent(form, result, nodeCounters, nodeIdMap, startNodeId);
        }

        // 3. 解析独立的 block 元素（如 endBlock）
        parseIndependentBlocks(doc, result, nodeCounters, nodeIdMap);

        return result;
    }

    /**
     * 创建开始节点
     */
    private static String createStartNode(VxmlParseResult result, Map<String, Integer> nodeCounters) {
        String nodeId = "START_" + generateRandomId();

        JSONObject startNode = new JSONObject();
        startNode.put("id", nodeId);
        startNode.put("name", "开始节点");
        startNode.put("type", "start");
        startNode.put("color", "#23D982");
        startNode.put("ico", "el-icon-video-play");
        startNode.put("top", "93px");
        startNode.put("left", "609px");
        startNode.put("targetType", "assignee");
        startNode.put("target", "");
        startNode.put("state", "");
        startNode.put("extensionElements", new JSONArray());
        startNode.put("service", new JSONObject());
        startNode.put("systemConfig", createDefaultSystemConfig());
        startNode.put("multiInstance", createDefaultMultiInstance());

        result.nodeList.add(startNode);
        return nodeId;
    }

    /**
     * 解析 form 内容
     */
    private static void parseFormContent(Element form, VxmlParseResult result,
            Map<String, Integer> nodeCounters, Map<String, String> nodeIdMap, String startNodeId) {

        String previousNodeId = startNodeId;

        // 解析 block 元素（播放语音）
        NodeList blocks = form.getElementsByTagName("block");
        for (int i = 0; i < blocks.getLength(); i++) {
            Element block = (Element) blocks.item(i);
            String blockNodeId = parseBlockElement(block, result, nodeCounters, i + 1);

            // 创建连线
            if (previousNodeId != null) {
                createConnection(result, nodeCounters, previousNodeId, blockNodeId, null);
            }
            previousNodeId = blockNodeId;
        }

        // 解析 field 元素（用户输入）
        NodeList fields = form.getElementsByTagName("field");
        for (int i = 0; i < fields.getLength(); i++) {
            Element field = (Element) fields.item(i);
            String fieldNodeId = parseFieldElement(field, result, nodeCounters, nodeIdMap);

            // 创建连线
            if (previousNodeId != null) {
                createConnection(result, nodeCounters, previousNodeId, fieldNodeId, null);
            }
            previousNodeId = fieldNodeId;
        }
    }

    /**
     * 解析独立的 block 元素
     */
    private static void parseIndependentBlocks(Document doc, VxmlParseResult result,
            Map<String, Integer> nodeCounters, Map<String, String> nodeIdMap) {

        // 查找所有独立的 block 元素（不在 form 内的）
        NodeList allBlocks = doc.getElementsByTagName("block");
        for (int i = 0; i < allBlocks.getLength(); i++) {
            Element block = (Element) allBlocks.item(i);

            // 检查是否是独立的 block（父节点不是 form）
            Node parent = block.getParentNode();
            if (parent != null && !"form".equals(parent.getNodeName())) {
                String blockId = block.getAttribute("id");
                if (blockId != null && !blockId.isEmpty()) {
                    String nodeId = parseIndependentBlock(block, result, nodeCounters);
                    nodeIdMap.put(blockId, nodeId);
                }
            }
        }
    }

    /**
     * 解析 block 元素
     */
    private static String parseBlockElement(Element block, VxmlParseResult result,
            Map<String, Integer> nodeCounters, int blockIndex) {

        String nodeId = "USERTASK_" + generateRandomId();

        // 获取 prompt 内容
        String promptContent = "";
        NodeList prompts = block.getElementsByTagName("prompt");
        if (prompts.getLength() > 0) {
            promptContent = prompts.item(0).getTextContent().trim();
        }

        JSONObject blockNode = new JSONObject();
        blockNode.put("id", nodeId);
        blockNode.put("name", "放音" + blockIndex);
        blockNode.put("type", "userTask");
        blockNode.put("color", "#76CAFB");
        blockNode.put("ico", "el-icon-headset");
        blockNode.put("top", (170 + blockIndex * 50) + "px");
        blockNode.put("left", (538 + blockIndex * 50) + "px");
        blockNode.put("targetType", "assignee");
        blockNode.put("target", "");
        blockNode.put("state", "");
        blockNode.put("voicePlayContent", promptContent);
        blockNode.put("voicePlayType", "2");
        blockNode.put("voicePlayNum", "1");
        blockNode.put("voicePlayInterruput", "N");
        blockNode.put("formkey", "");
        blockNode.put("extensionElements", new JSONArray());
        blockNode.put("service", new JSONObject());
        blockNode.put("style", new JSONObject());
        blockNode.put("systemConfig", createDefaultSystemConfig());
        blockNode.put("multiInstance", createDefaultMultiInstanceForUserTask(promptContent));

        result.nodeList.add(blockNode);
        return nodeId;
    }

    /**
     * 解析 field 元素（用户输入）
     */
    private static String parseFieldElement(Element field, VxmlParseResult result,
            Map<String, Integer> nodeCounters, Map<String, String> nodeIdMap) {

        String nodeId = "SERVICETASK_" + generateRandomId();
        String fieldName = field.getAttribute("name");

        // 获取输入参数信息
        String paramName = fieldName.toUpperCase();
        String paramMinSize = "1";
        String paramMaxSize = "1";

        // 从 type 属性中提取长度信息
        String typeAttr = field.getAttribute("type");
        if (typeAttr.contains("length=")) {
            String lengthStr = typeAttr.substring(typeAttr.indexOf("length=") + 7);
            if (lengthStr.contains(")")) {
                lengthStr = lengthStr.substring(0, lengthStr.indexOf(")"));
            }
            paramMaxSize = lengthStr;
        }

        JSONObject serviceNode = new JSONObject();
        serviceNode.put("id", nodeId);
        serviceNode.put("name", "用户按键");
        serviceNode.put("type", "serviceTask");
        serviceNode.put("color", "#93BB49");
        serviceNode.put("ico", "el-icon-circle-plus-outline");
        serviceNode.put("top", "363px");
        serviceNode.put("left", "643px");
        serviceNode.put("targetType", "assignee");
        serviceNode.put("target", "");
        serviceNode.put("state", "");
        serviceNode.put("paramName", paramName);
        serviceNode.put("paramMinSize", paramMinSize);
        serviceNode.put("paramMaxSize", paramMaxSize);
        serviceNode.put("paramFmt", "1");
        serviceNode.put("voicePlayType", "1");
        serviceNode.put("voicePlayInterruput", "N");
        serviceNode.put("extensionElements", new JSONArray());
        serviceNode.put("service", new JSONObject());
        serviceNode.put("systemConfig", createDefaultSystemConfig());
        serviceNode.put("multiInstance", createDefaultMultiInstanceForServiceTask(paramName, paramMinSize, paramMaxSize));

        result.nodeList.add(serviceNode);

        // 解析 filled 元素中的条件逻辑
        parseFilledConditions(field, result, nodeCounters, nodeIdMap, nodeId, paramName);

        // 解析 noinput 和 nomatch 处理逻辑
        parseInputHandlers(field, result, nodeCounters, nodeIdMap, nodeId, paramName);

        return nodeId;
    }

    /**
     * 解析 noinput 和 nomatch 处理逻辑
     */
    private static void parseInputHandlers(Element field, VxmlParseResult result,
            Map<String, Integer> nodeCounters, Map<String, String> nodeIdMap,
            String serviceNodeId, String paramName) {

        // 处理 noinput 元素
        NodeList noinputList = field.getElementsByTagName("noinput");
        if (noinputList.getLength() > 0) {
            Element noinput = (Element) noinputList.item(0);
            String noinputContent = "";
            NodeList prompts = noinput.getElementsByTagName("prompt");
            if (prompts.getLength() > 0) {
                noinputContent = prompts.item(0).getTextContent().trim();
            }

            // 创建noinput处理节点
            String noinputNodeId = createUserTaskNode(result, nodeCounters, noinputContent, false);

            // 创建从serviceTask到noinput节点的连线（超时条件）
            ConditionInfo timeoutCondInfo = new ConditionInfo(paramName, "timeout", "", false);
            createConditionalConnection(result, nodeCounters, serviceNodeId, noinputNodeId, timeoutCondInfo);

            // 检查是否有reprompt，如果有则回到serviceTask，否则结束
            NodeList repromptList = noinput.getElementsByTagName("reprompt");
            if (repromptList.getLength() > 0) {
                // 回到收号节点重新收号
                createConnection(result, nodeCounters, noinputNodeId, serviceNodeId, "重新收号");
            } else {
                // 结束流程
                String endNodeId = findOrCreateEndNode(result, nodeCounters, nodeIdMap);
                createConnection(result, nodeCounters, noinputNodeId, endNodeId, "结束流程");
            }
        }

        // 处理 nomatch 元素
        NodeList nomatchList = field.getElementsByTagName("nomatch");
        if (nomatchList.getLength() > 0) {
            Element nomatch = (Element) nomatchList.item(0);
            String nomatchContent = "";
            NodeList prompts = nomatch.getElementsByTagName("prompt");
            if (prompts.getLength() > 0) {
                nomatchContent = prompts.item(0).getTextContent().trim();
            }

            // 创建nomatch处理节点
            String nomatchNodeId = createUserTaskNode(result, nodeCounters, nomatchContent, false);

            // 创建从serviceTask到nomatch节点的连线（输入无效条件）
            ConditionInfo invalidCondInfo = new ConditionInfo(paramName, "invalid", "", false);
            createConditionalConnection(result, nodeCounters, serviceNodeId, nomatchNodeId, invalidCondInfo);

            // 检查是否有reprompt，如果有则回到serviceTask，否则结束
            NodeList repromptList = nomatch.getElementsByTagName("reprompt");
            if (repromptList.getLength() > 0) {
                // 回到收号节点重新收号
                createConnection(result, nodeCounters, nomatchNodeId, serviceNodeId, "重新收号");
            } else {
                // 检查是否有exit，如果有则结束流程
                NodeList exitList = nomatch.getElementsByTagName("exit");
                if (exitList.getLength() > 0) {
                    String endNodeId = findOrCreateEndNode(result, nodeCounters, nodeIdMap);
                    createConnection(result, nodeCounters, nomatchNodeId, endNodeId, "结束流程");
                }
            }
        }
    }

    /**
     * 解析 filled 中的条件逻辑
     */
    private static void parseFilledConditions(Element field, VxmlParseResult result,
            Map<String, Integer> nodeCounters, Map<String, String> nodeIdMap,
            String serviceNodeId, String paramName) {

        NodeList filledList = field.getElementsByTagName("filled");
        if (filledList.getLength() > 0) {
            Element filled = (Element) filledList.item(0);
            NodeList ifList = filled.getElementsByTagName("if");

            String correctInputNodeId = null;

            for (int i = 0; i < ifList.getLength(); i++) {
                Element ifElement = (Element) ifList.item(i);
                String condition = ifElement.getAttribute("cond");

                if (condition != null && !condition.isEmpty()) {
                    // 解析条件表达式
                    ConditionInfo condInfo = parseCondition(condition, paramName);

                    // 获取目标内容
                    String targetContent = "";
                    NodeList prompts = ifElement.getElementsByTagName("prompt");
                    if (prompts.getLength() > 0) {
                        targetContent = prompts.item(0).getTextContent().trim();
                    }

                    // 创建目标节点
                    String targetNodeId = createUserTaskNode(result, nodeCounters, targetContent, condInfo.isPositive);

                    // 如果是正确输入，记录节点ID用于后续连接到结束节点
                    if (condInfo.isPositive) {
                        correctInputNodeId = targetNodeId;
                    }

                    // 创建条件连线
                    createConditionalConnection(result, nodeCounters, serviceNodeId, targetNodeId, condInfo);
                }
            }

            // 为正确输入节点创建到结束节点的连线
            if (correctInputNodeId != null) {
                String endNodeId = findOrCreateEndNode(result, nodeCounters, nodeIdMap);
                createConnection(result, nodeCounters, correctInputNodeId, endNodeId, "结束流程");
            }

            // 为错误输入创建连线到错误处理节点（放音2）
            createErrorHandlingConnection(result, nodeCounters, serviceNodeId, paramName);
        }
    }

    /**
     * 条件信息类
     */
    private static class ConditionInfo {
        String param;
        String operator;
        String value;
        boolean isPositive;

        ConditionInfo(String param, String operator, String value, boolean isPositive) {
            this.param = param;
            this.operator = operator;
            this.value = value;
            this.isPositive = isPositive;
        }
    }

    /**
     * 解析条件表达式
     */
    private static ConditionInfo parseCondition(String condition, String paramName) {
        // 示例: service_task_result == '1' 或 service_task_result != '1'
        boolean isPositive = condition.contains("==");
        String operator = isPositive ? "eq" : "neq";

        String value = "";
        if (condition.contains("'")) {
            int start = condition.indexOf("'") + 1;
            int end = condition.lastIndexOf("'");
            if (end > start) {
                value = condition.substring(start, end);
            }
        }

        return new ConditionInfo(paramName, operator, value, isPositive);
    }

    /**
     * 创建用户任务节点
     */
    private static String createUserTaskNode(VxmlParseResult result, Map<String, Integer> nodeCounters,
            String voiceContent, boolean isPositive) {

        String nodeId = "USERTASK_" + generateRandomId();
        String nodeName = isPositive ? "正确输入" : "错误输入";

        JSONObject userNode = new JSONObject();
        userNode.put("id", nodeId);
        userNode.put("name", nodeName);
        userNode.put("type", "userTask");
        userNode.put("color", "#76CAFB");
        userNode.put("ico", "el-icon-headset");
        userNode.put("top", isPositive ? "512px" : "508px");
        userNode.put("left", isPositive ? "444px" : "822px");
        userNode.put("targetType", "assignee");
        userNode.put("target", "");
        userNode.put("state", "");
        userNode.put("voicePlayContent", voiceContent);
        userNode.put("voicePlayType", "2");
        userNode.put("voicePlayNum", "1");
        userNode.put("voicePlayInterruput", "N");
        userNode.put("formkey", "");
        userNode.put("extensionElements", new JSONArray());
        userNode.put("service", new JSONObject());
        userNode.put("style", new JSONObject());
        userNode.put("systemConfig", createDefaultSystemConfig());
        userNode.put("multiInstance", createDefaultMultiInstanceForUserTask(voiceContent));

        result.nodeList.add(userNode);
        return nodeId;
    }

    /**
     * 创建条件连线
     */
    private static void createConditionalConnection(VxmlParseResult result, Map<String, Integer> nodeCounters,
            String fromNodeId, String toNodeId, ConditionInfo condInfo) {

        String lineId = "LINE_" + generateRandomId();
        String label;

        // 根据条件类型设置标签
        if ("timeout".equals(condInfo.operator)) {
            label = "超时无输入";
        } else if ("invalid".equals(condInfo.operator)) {
            label = "输入无效";
        } else {
            label = condInfo.isPositive ? "用户输入" + condInfo.value : "用户输入其他";
        }

        JSONObject line = new JSONObject();
        line.put("id", lineId);
        line.put("from", fromNodeId);
        line.put("to", toNodeId);
        line.put("label", label);
        line.put("lx_tj", condInfo.operator);
        line.put("lx_cs", condInfo.param);
        line.put("lx_fz", condInfo.value);
        line.put("lx_ms", "");
        line.put("lx_disable", false);
        line.put("ifSelect", false);
        line.put("isqj", false);

        // 创建表达式对象
        JSONObject expression = new JSONObject();
        expression.put("param", condInfo.param);
        expression.put("conditions", condInfo.operator);
        expression.put("val1", condInfo.value);
        expression.put("bakup", "");
        line.put("expression", expression);

        // 创建线条对象
        JSONObject lineObj = new JSONObject();
        lineObj.put("ifSelect", false);
        line.put("line", lineObj);

        result.lineList.add(line);
    }

    /**
     * 创建普通连线
     */
    private static void createConnection(VxmlParseResult result, Map<String, Integer> nodeCounters,
            String fromNodeId, String toNodeId, String label) {

        String lineId = "LINE_" + generateRandomId();

        JSONObject line = new JSONObject();
        line.put("id", lineId);
        line.put("from", fromNodeId);
        line.put("to", toNodeId);
        if (label != null) {
            line.put("label", label);
        }

        // 创建线条对象
        JSONObject lineObj = new JSONObject();
        lineObj.put("ifSelect", false);
        line.put("line", lineObj);

        result.lineList.add(line);
    }

    /**
     * 解析独立的 block 元素
     */
    private static String parseIndependentBlock(Element block, VxmlParseResult result,
            Map<String, Integer> nodeCounters) {

        String blockId = block.getAttribute("id");
        String nodeId;

        if ("endBlock".equals(blockId)) {
            // 创建结束节点
            nodeId = "END_" + generateRandomId();

            JSONObject endNode = new JSONObject();
            endNode.put("id", nodeId);
            endNode.put("name", "结束节点");
            endNode.put("type", "end");
            endNode.put("color", "#FF5454");
            endNode.put("ico", "el-icon-video-pause");
            endNode.put("top", "655px");
            endNode.put("left", "594px");
            endNode.put("targetType", "assignee");
            endNode.put("target", "");
            endNode.put("state", "");
            endNode.put("extensionElements", new JSONArray());
            endNode.put("service", new JSONObject());

            result.nodeList.add(endNode);
        } else {
            // 其他类型的独立 block，作为用户任务处理
            nodeId = parseBlockElement(block, result, nodeCounters, 999);
        }

        return nodeId;
    }

    /**
     * 查找或创建结束节点（确保只有一个结束节点）
     */
    private static String findOrCreateEndNode(VxmlParseResult result, Map<String, Integer> nodeCounters,
            Map<String, String> nodeIdMap) {

        // 先查找是否已存在结束节点
        for (int i = 0; i < result.nodeList.size(); i++) {
            JSONObject node = result.nodeList.getJSONObject(i);
            if ("end".equals(node.getString("type"))) {
                return node.getString("id");
            }
        }

        // 如果不存在，创建新的结束节点
        String nodeId = "END_" + generateRandomId();

        JSONObject endNode = new JSONObject();
        endNode.put("id", nodeId);
        endNode.put("name", "结束节点");
        endNode.put("type", "end");
        endNode.put("color", "#FF5454");
        endNode.put("ico", "el-icon-video-pause");
        endNode.put("top", "655px");
        endNode.put("left", "594px");
        endNode.put("targetType", "assignee");
        endNode.put("target", "");
        endNode.put("state", "");
        endNode.put("extensionElements", new JSONArray());
        endNode.put("service", new JSONObject());

        result.nodeList.add(endNode);
        return nodeId;
    }

    /**
     * 创建错误处理连线（收号节点直接连接到结束节点）
     */
    private static void createErrorHandlingConnection(VxmlParseResult result, Map<String, Integer> nodeCounters,
            String serviceNodeId, String paramName) {

        // 根据用户要求：收号节点不能指向放音2节点
        // 收号节点要新增一条连线到结束节点，条件为用户输入的不为1

        // 直接创建从收号节点到结束节点的条件连线（用户输入非1）
        String endNodeId = findOrCreateEndNode(result, nodeCounters, null);
        ConditionInfo errorCondInfo = new ConditionInfo(paramName, "neq", "1", false);
        createConditionalConnection(result, nodeCounters, serviceNodeId, endNodeId, errorCondInfo);
    }

    /**
     * 生成随机ID
     */
    private static String generateRandomId() {
        return String.valueOf(System.currentTimeMillis() + (int)(Math.random() * 1000));
    }

    /**
     * 创建默认系统配置
     */
    private static JSONArray createDefaultSystemConfig() {
        JSONArray systemConfig = new JSONArray();

        String[] configNames = {"NOT_WORK_TIME", "NOT_BLACK_USER", "NOT_RED_USER"};
        for (String configName : configNames) {
            JSONObject config = new JSONObject();
            config.put("configName", configName);
            config.put("operaType", "");
            config.put("voicePlayType", "");
            config.put("configLevel", "");
            config.put("voicePlayContent", "");
            config.put("configValue", "");
            config.put("operaFuncName", "");
            systemConfig.add(config);
        }

        return systemConfig;
    }

    /**
     * 创建默认多实例配置
     */
    private static JSONObject createDefaultMultiInstance() {
        JSONObject multiInstance = new JSONObject();

        // 设置所有默认字段
        String[] stringFields = {
            "zrg_dx", "fy_text2", "fy_text3", "fy_text5", "fy_text6", "fy_text7", "fy_text8",
            "transPhoneType", "fy_text40", "hs_name", "radio", "voicePlayType3", "voicePlayType2",
            "radio3", "voicePlayType5", "voicePlayType4", "radio2", "fy_value50", "voicePlayType30",
            "fy_text10", "fy_text50", "voicePlayContent4", "voicePlayContent3", "voicePlayContent5",
            "fy_value40", "zrg_overtime", "zrg_ywdm", "zrg_value_select", "fy_text", "jb_value_select",
            "sh_input3", "fy_value2", "sh_input2", "fy_value3", "sh_input1", "fy_value4",
            "hs_value_select", "voicePlayContent2", "fy_text20", "voicePlayContent", "scriptMsg",
            "fy_value", "voicePlayType50", "fy_value5", "fy_value30", "voicePlayNum", "sh_value_select",
            "fy_text30", "transPhone", "zrg_dx_zuoxi", "fy_text9", "zrg_fs", "fy_value20",
            "voicePlayType40", "sh_value"
        };

        for (String field : stringFields) {
            multiInstance.put(field, "");
        }

        // 设置特殊字段
        multiInstance.put("isjiami", "1");
        multiInstance.put("voicePlayType", "1");
        multiInstance.put("voicePlayInterruput", false);
        multiInstance.put("loopCardinality", 0);

        return multiInstance;
    }

    /**
     * 创建用户任务的多实例配置
     */
    private static JSONObject createDefaultMultiInstanceForUserTask(String voiceContent) {
        JSONObject multiInstance = createDefaultMultiInstance();

        // 设置语音相关字段
        multiInstance.put("voicePlayType", "2");
        multiInstance.put("voicePlayContent", voiceContent);
        multiInstance.put("fy_text", voiceContent);
        multiInstance.put("voicePlayNum", "1");

        return multiInstance;
    }

    /**
     * 创建服务任务的多实例配置
     */
    private static JSONObject createDefaultMultiInstanceForServiceTask(String paramName, String paramMinSize, String paramMaxSize) {
        JSONObject multiInstance = createDefaultMultiInstance();

        // 设置参数相关字段
        multiInstance.put("paramName", paramName);
        multiInstance.put("paramMinSize", paramMinSize);
        multiInstance.put("paramMaxSize", paramMaxSize);
        multiInstance.put("paramFmt", "1");
        multiInstance.put("sh_value_select", paramName);
        multiInstance.put("sh_input1", "1");
        multiInstance.put("sh_value", "1");

        return multiInstance;
    }

    /**
     * 保存 JSON 到文件
     */
    private static void saveJsonToFile(String jsonContent, String filePath) throws IOException {
        try (OutputStreamWriter writer = new OutputStreamWriter(
                new FileOutputStream(filePath), "UTF-8")) {
            writer.write(jsonContent);
        }
    }
}
