# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

memoryUserDatabase.fileClose=[{0}]のクローズに失敗しました
memoryUserDatabase.fileDelete=[{0}]を削除できませんでした
memoryUserDatabase.fileNotFound=ユーザー情報データベースとして指定された [{0}] は存在しません。
memoryUserDatabase.notPersistable=ユーザーデータベースは永続的ではありません - ディレクトリに対する書き込み権限がありません。
memoryUserDatabase.nullGroup=Nullまたは長さゼロのグループ名が指定されています。 グループは無視されます。
memoryUserDatabase.nullRole=NULLまたは長さゼロのロール名が指定されています。 ロールは無視されます。
memoryUserDatabase.nullUser=Nullまたは長さゼロのユーザー名が指定されています。 ユーザーは無視されます。
memoryUserDatabase.readOnly=ユーザー情報データベースは読み取り専用になっています。変更を保存できません。
memoryUserDatabase.reload=更新されたソース[{1}]からメモリユーザーデータベース[{0}]を再ロードしています
memoryUserDatabase.reloadError=更新されたソース[{1}]からメモリユーザーデータベース[{0}]を再ロード中のエラー
memoryUserDatabase.renameNew=新しいファイル名を [{0}] に変更できません
memoryUserDatabase.renameOld=元のファイル名を [{0}] に変更できません
memoryUserDatabase.restoreOrig=[{0}]を元のファイルに復元できません
memoryUserDatabase.writeException=[{0}] に書き込み中の入出力例外です
memoryUserDatabase.xmlFeatureEncoding=XMLファイルのJavaエンコーディング名を許可するためにdigesterを設定する際の例外。 IANAのエンコーディング名のみがサポートされます。
