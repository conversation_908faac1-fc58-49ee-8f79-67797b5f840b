# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

applicationContext.addFilter.ise=Filters cannot be added to context [{0}] as the context has been initialised
applicationContext.addJspFile.iae=The JSP file [{0}] is not valid
applicationContext.addListener.iae.cnfe=Unable to create an instance of type [{0}]
applicationContext.addListener.iae.init=Unable to add an instance of type [{0}] as a listener
applicationContext.addListener.iae.sclNotAllowed=Once the first ServletContextListener has been called, no more ServletContextListeners may be added.
applicationContext.addListener.iae.wrongType=The type specified [{0}] is not one of the expected listener types
applicationContext.addListener.ise=Listeners cannot be added to context [{0}] as the context has been initialised
applicationContext.addRole.ise=Roles cannot be added to context [{0}] as the context has been initialised
applicationContext.addServlet.ise=Servlets cannot be added to context [{0}] as the context has been initialised
applicationContext.attributeEvent=Exception thrown by attributes event listener
applicationContext.illegalDispatchPath=An application attempted to obtain a request dispatcher with an illegal path [{0}] that was rejected because it contained an encoded directory traversal attempt
applicationContext.invalidFilterName=Unable to add filter definition due to invalid filter name [{0}].
applicationContext.invalidServletName=Unable to add servlet definition due to invalid servlet name [{0}].
applicationContext.lookup.error=Failed to locate resource [{0}] in context [{1}]
applicationContext.mapping.error=Error during mapping
applicationContext.requestDispatcher.iae=Path [{0}] does not start with a "/" character
applicationContext.resourcePaths.iae=Path [{0}] does not start with a "/" character
applicationContext.role.iae=An individual role to declare for context [{0}] may not be null nor the empty string
applicationContext.roles.iae=Array of roles to declare for context [{0}] cannot be null
applicationContext.setAttribute.namenull=Name cannot be null
applicationContext.setInitParam.ise=Initialization parameters cannot be set after the context has been initialized
applicationContext.setRequestEncoding.ise=The request encoding cannot be set for context [{0}] as the context has been initialised
applicationContext.setResponseEncoding.ise=The response encoding cannot be set for context [{0}] as the context has been initialised
applicationContext.setSessionTimeout.ise=The session timeout cannot be set for context [{0}] as the context has been initialised
applicationContext.setSessionTracking.iae.invalid=The session tracking mode [{0}] requested for context [{1}] is not supported by that context
applicationContext.setSessionTracking.iae.ssl=The session tracking modes requested for context [{0}] included SSL and at least one other mode. SSL may not be configured with other modes.
applicationContext.setSessionTracking.ise=The session tracking modes for context [{0}] cannot be set whilst the context is running

applicationDispatcher.allocateException=Allocate exception for servlet [{0}]
applicationDispatcher.deallocateException=Deallocate exception for servlet [{0}]
applicationDispatcher.forward.ise=Cannot forward after response has been committed
applicationDispatcher.isUnavailable=Servlet [{0}] is currently unavailable
applicationDispatcher.serviceException=Servlet.service() for servlet [{0}] threw exception
applicationDispatcher.specViolation.request=Original ServletRequest or wrapped original ServletRequest not passed to RequestDispatcher in violation of SRV.8.2 and SRV.14.2.5.1
applicationDispatcher.specViolation.response=Original ServletResponse or wrapped original ServletResponse not passed to RequestDispatcher in violation of SRV.8.2 and SRV.14.2.5.1

applicationFilterConfig.jmxRegisterFail=JMX registration failed for filter of type [{0}] and name [{1}]
applicationFilterConfig.jmxUnregister=JMX de-registration complete for filter of type [{0}] and name [{1}]
applicationFilterConfig.jmxUnregisterFail=JMX de-registration failed for filter of type [{0}] and name [{1}]
applicationFilterConfig.preDestroy=Failed the call to preDestroy for the filter named [{0}] of type [{1}]
applicationFilterConfig.release=Failed to destroy the filter named [{0}] of type [{1}]

applicationFilterRegistration.nullInitParam=Unable to set initialisation parameter for filter due to null name and/or value. Name [{0}], Value [{1}]
applicationFilterRegistration.nullInitParams=Unable to set initialisation parameters for filter due to null name and/or value. Name [{0}], Value [{1}]

applicationHttpRequest.fragmentInDispatchPath=The fragment in dispatch path [{0}] has been removed

applicationPushBuilder.methodInvalid=The HTTP method for a push request must be both cacheable and safe but [{0}] is not
applicationPushBuilder.methodNotToken=HTTP methods must be tokens but [{0}] contains a non-token character
applicationPushBuilder.noCoyoteRequest=Unable to find the underlying Coyote request object (which is required to create a push request) from the request of type [{0}]

applicationServletRegistration.setServletSecurity.iae=Null constraint specified for servlet [{0}] deployed to context with name [{1}]
applicationServletRegistration.setServletSecurity.ise=Security constraints can''t be added to servlet [{0}] deployed to context with name [{1}] as the context has already been initialised

applicationSessionCookieConfig.ise=Property [{0}] cannot be added to SessionCookieConfig for context [{1}] as the context has been initialised

aprListener.aprDestroy=Failed shutdown of the Apache Tomcat Native library
aprListener.aprInit=The Apache Tomcat Native library which allows using OpenSSL was not found on the java.library.path: [{0}]
aprListener.aprInitDebug=The Apache Tomcat Native library could not be found using names [{0}] on the java.library.path [{1}]. The errors reported were [{2}]
aprListener.aprInitError=The Apache Tomcat Native library failed to load. The error reported was [{0}]
aprListener.config=APR/OpenSSL configuration: useAprConnector [{0}], useOpenSSL [{1}]
aprListener.currentFIPSMode=Current FIPS mode: [{0}]
aprListener.enterAlreadyInFIPSMode=AprLifecycleListener is configured to force entering FIPS mode, but library is already in FIPS mode [{0}]
aprListener.flags=APR capabilities: IPv6 [{0}], sendfile [{1}], accept filters [{2}], random [{3}].
aprListener.initializeFIPSFailed=Failed to enter FIPS mode
aprListener.initializeFIPSSuccess=Successfully entered FIPS mode
aprListener.initializedOpenSSL=OpenSSL successfully initialized [{0}]
aprListener.initializingFIPS=Initializing FIPS mode...
aprListener.requireNotInFIPSMode=AprLifecycleListener is configured to require the library to already be in FIPS mode, but it was not in FIPS mode
aprListener.skipFIPSInitialization=Already in FIPS mode; skipping FIPS initialization.
aprListener.sslInit=Failed to initialize the SSLEngine.
aprListener.tcnInvalid=An incompatible version [{0}] of the Apache Tomcat Native library is installed, while Tomcat requires version [{1}]
aprListener.tcnValid=Loaded Apache Tomcat Native library [{0}] using APR version [{1}].
aprListener.tcnVersion=An older version [{0}] of the Apache Tomcat Native library is installed, while Tomcat recommends a minimum version of [{1}]
aprListener.tooLateForFIPSMode=Cannot setFIPSMode: SSL has already been initialized
aprListener.tooLateForSSLEngine=Cannot setSSLEngine: SSL has already been initialized
aprListener.tooLateForSSLRandomSeed=Cannot setSSLRandomSeed: SSL has already been initialized
aprListener.wrongFIPSMode=Unexpected value of FIPSMode option of AprLifecycleListener: [{0}]

asyncContextImpl.asyncDispatchError=Error during asynchronous dispatch
asyncContextImpl.asyncRunnableError=Error during processing of asynchronous Runnable via AsyncContext.start()
asyncContextImpl.dispatchingStarted=Asynchronous dispatch operation has already been called. Additional asynchronous dispatch operation within the same asynchronous cycle is not allowed.
asyncContextImpl.fireOnComplete=Firing onComplete() event for any AsyncListeners
asyncContextImpl.fireOnError=Firing onError() event for any AsyncListeners
asyncContextImpl.fireOnStartAsync=Firing onStartAsync() event for any AsyncListeners
asyncContextImpl.fireOnTimeout=Firing onTimeout() event for any AsyncListeners
asyncContextImpl.noAsyncDispatcher=The dispatcher returned from the ServletContext does not support asynchronous dispatching
asyncContextImpl.onCompleteError=onComplete() call failed for listener of type [{0}]
asyncContextImpl.onErrorError=onError() call failed for listener of type [{0}]
asyncContextImpl.onStartAsyncError=onStartAsync() call failed for listener of type [{0}]
asyncContextImpl.onTimeoutError=onTimeout() call failed for listener of type [{0}]
asyncContextImpl.request.ise=It is illegal to call getRequest() after complete() or any of the dispatch() methods has been called
asyncContextImpl.requestEnded=The request associated with the AsyncContext has already completed processing.
asyncContextImpl.response.ise=It is illegal to call getResponse() after complete() or any of the dispatch() methods has been called

containerBase.backgroundProcess.cluster=Exception processing cluster [{0}] background process
containerBase.backgroundProcess.realm=Exception processing realm [{0}] background process
containerBase.backgroundProcess.unexpectedThreadDeath=Unexpected death of background thread [{0}]
containerBase.backgroundProcess.valve=Exception processing valve [{0}] background process
containerBase.nullName=Container name cannot be null
containerBase.threadedStartFailed=A child container failed during start
containerBase.threadedStopFailed=A child container failed during stop

defaultInstanceManager.invalidInjection=Invalid method resource injection annotation
defaultInstanceManager.restrictedClass=Access to class [{0}] is forbidden. It is a restricted class. A web application must be configured as privileged to be able to load it
defaultInstanceManager.restrictedContainerServlet=Access to class [{0}] is forbidden. It is a restricted class (implements ContainerServlet interface). A web application must be configured as privileged to be able to load it
defaultInstanceManager.restrictedFiltersResource=Restricted filters property file not found [{0}]
defaultInstanceManager.restrictedListenersResource=Restricted listeners property file not found [{0}]
defaultInstanceManager.restrictedServletsResource=Restricted servlets property file not found [{0}]
defaultInstanceManager.restrictedWrongValue=Wrong value in restricted classes property file [{0}] for class name [{1}]. Expected value: [restricted], actual value: [{2}]

filterChain.filter=Filter execution threw an exception
filterChain.servlet=Servlet execution threw an exception

jreLeakListener.authPolicyFail=Error whilst attempting to prevent memory leak in javax.security.auth.Policy class
jreLeakListener.classToInitializeFail=Failed to load class [{0}] during Tomcat start to prevent possible memory leaks.
jreLeakListener.gcDaemonFail=Failed to trigger creation of the GC Daemon thread during Tomcat start to prevent possible memory leaks. This is expected on non-Sun JVMs.
jreLeakListener.jarUrlConnCacheFail=Failed to disable Jar URL connection caching by default
jreLeakListener.ldapPoolManagerFail=Failed to trigger creation of the com.sun.jndi.ldap.LdapPoolManager class during Tomcat start to prevent possible memory leaks. This is expected on non-Sun JVMs.
jreLeakListener.xmlParseFail=Error whilst attempting to prevent memory leaks during XML parsing

naming.addEnvEntry=Adding environment entry [{0}]
naming.addResourceEnvRef=Adding resource env ref [{0}]
naming.bindFailed=Failed to bind object: [{0}]
naming.invalidEnvEntryType=Environment entry [{0}] has an invalid type
naming.invalidEnvEntryValue=Environment entry [{0}] has an invalid value
naming.jmxRegistrationFailed=Failed to register in JMX: [{0}]
naming.namingContextCreationFailed=Creation of the naming context failed: [{0}]
naming.unbindFailed=Failed to unbind object: [{0}]
naming.wsdlFailed=Failed to find wsdl file: [{0}]

noPluggabilityServletContext.notAllowed=Section 4.4 of the Servlet 3.0 specification does not permit this method to be called from a ServletContextListener that was not defined in web.xml, a web-fragment.xml file nor annotated with @WebListener

pushBuilder.noPath=It is illegal to call push() before setting a path

standardContext.applicationListener=Error configuring application listener of class [{0}]
standardContext.applicationSkipped=Skipped installing application listeners due to previous error(s)
standardContext.backgroundProcess.instanceManager=Exception processing instance manager [{0}] background process
standardContext.backgroundProcess.loader=Exception processing loader [{0}] background process
standardContext.backgroundProcess.manager=Exception processing manager [{0}] background process
standardContext.backgroundProcess.resources=Exception processing resources [{0}] background process
standardContext.cluster.noManager=No manager found. Checking if cluster manager should be used. Cluster configured: [{0}], Application distributable: [{1}]
standardContext.configurationFail=One or more components marked the context as not correctly configured
standardContext.cookieProcessor.null=It is not permitted to set the CookieProcessor for a Context to null
standardContext.duplicateListener=The listener [{0}] is already configured for this context. The duplicate definition has been ignored.
standardContext.errorPage.error=Error page location [{0}] must start with a ''/''
standardContext.errorPage.required=ErrorPage cannot be null
standardContext.errorPage.warning=WARNING: Error page location [{0}] must start with a ''/'' in Servlet 2.4
standardContext.extensionValidationError=Error while attempting to validate required application extensions
standardContext.filterFail=One or more Filters failed to start. Full details will be found in the appropriate container log file
standardContext.filterMap.either=Filter mapping must specify either a <url-pattern> or a <servlet-name>
standardContext.filterMap.name=Filter mapping specifies an unknown filter name [{0}]
standardContext.filterMap.pattern=Invalid <url-pattern> [{0}] in filter mapping
standardContext.filterStart=Exception starting filter [{0}]
standardContext.invalidWrapperClass=[{0}] is not a subclass of StandardWrapper
standardContext.isUnavailable=This application is not currently available
standardContext.listenerFail=One or more listeners failed to start. Full details will be found in the appropriate container log file
standardContext.listenerStart=Exception sending context initialized event to listener instance of class [{0}]
standardContext.listenerStop=Exception sending context destroyed event to listener instance of class [{0}]
standardContext.loadOnStartup.loadException=Servlet [{1}] in web application [{0}] threw load() exception
standardContext.loginConfig.errorPage=Form error page [{0}] must start with a ''/''
standardContext.loginConfig.errorWarning=WARNING: Form error page [{0}] must start with a ''/'' in Servlet 2.4
standardContext.loginConfig.loginPage=Form login page [{0}] must start with a ''/''
standardContext.loginConfig.loginWarning=WARNING: Form login page [{0}] must start with a ''/'' in Servlet 2.4
standardContext.loginConfig.required=LoginConfig cannot be null
standardContext.manager=Configured a manager of class [{0}]
standardContext.managerFail=The session manager failed to start
standardContext.namingResource.destroy.fail=Failed to destroy old naming resources
standardContext.namingResource.init.fail=Failed to init new naming resources
standardContext.notStarted=Context with name [{0}] has not yet been started
standardContext.notWrapper=Child of a Context must be a Wrapper
standardContext.parameter.duplicate=Duplicate context initialization parameter [{0}]
standardContext.parameter.required=Both parameter name and parameter value are required
standardContext.pathInvalid=A context path must either be an empty string or start with a ''/'' and do not end with a ''/''. The path [{0}] does not meet these criteria and has been changed to [{1}]
standardContext.postconstruct.duplicate=Duplicate post construct method definition for class [{0}]
standardContext.postconstruct.required=Both fully qualified class name and method name are required
standardContext.predestroy.duplicate=Duplicate @PreDestroy method definition for class [{0}]
standardContext.predestroy.required=Both fully qualified class name and method name are required
standardContext.reloadingCompleted=Reloading Context with name [{0}] is completed
standardContext.reloadingStarted=Reloading Context with name [{0}] has started
standardContext.requestListener.requestInit=Exception sending request initialized lifecycle event to listener instance of class [{0}]
standardContext.resourcesInit=Error initializing static Resources
standardContext.resourcesStart=Error starting static Resources
standardContext.resourcesStop=Error stopping static Resources
standardContext.sciFail=Error during ServletContainerInitializer processing
standardContext.securityConstraint.mixHttpMethod=It is not permitted to mix <http-method> and <http-method-omission> in the same web resource collection
standardContext.securityConstraint.pattern=Invalid <url-pattern> [{0}] in security constraint
standardContext.servletFail=One or more Servlets failed to load on startup. Full details will be found in the appropriate container log file
standardContext.servletMap.name=Servlet mapping specifies an unknown servlet name [{0}]
standardContext.servletMap.pattern=Invalid <url-pattern> [{0}] in servlet mapping
standardContext.startFailed=Context [{0}] startup failed due to previous errors
standardContext.startingContext=Exception starting Context with name [{0}]
standardContext.stop.asyncWaitInterrupted=Interrupt received while waiting unloadDelay milliseconds for in-flight asynchronous requests to complete. Context stop will continue without further delay.
standardContext.stoppingContext=Exception stopping Context with name [{0}]
standardContext.threadBindingListenerError=An error occurred in the thread binding listener configured for Context [{0}]
standardContext.urlPattern.patternWarning=WARNING: URL pattern [{0}] must start with a ''/'' in Servlet 2.4
standardContext.webappClassLoader.missingProperty=Unable to set the web application class loader property [{0}] to [{1}] as the property does not exist.
standardContext.workCreateException=Failed to determine absolute work directory from directory [{0}] and CATALINA_HOME [{1}] for context [{2}]
standardContext.workCreateFail=Failed to create work directory [{0}] for context [{1}]
standardContext.workPath=Exception obtaining work path for context [{0}]

standardContextValve.acknowledgeException=Failed to acknowledge request with a 100 (Continue) response

standardEngine.jvmRouteFail=Failed to set Engine's jvmRoute attribute from system property
standardEngine.notHost=Child of an Engine must be a Host
standardEngine.notParent=Engine cannot have a parent Container

standardHost.clientAbort=Remote Client Aborted Request, IOException: [{0}]
standardHost.invalidErrorReportValveClass=Couldn''t load specified error report valve class: [{0}]
standardHost.noContext=No Context configured to process this request
standardHost.notContext=Child of a Host must be a Context
standardHost.nullName=Host name is required
standardHost.problematicAppBase=Using an empty string for appBase on host [{0}] will set it to CATALINA_BASE, which is a bad idea

standardHostValue.customStatusFailed=Custom error page [{0}] could not be dispatched correctly

standardPipeline.basic.start=Error starting new basic Valve
standardPipeline.basic.stop=Error stopping old basic Valve
standardPipeline.valve.destroy=Error destroying Valve
standardPipeline.valve.start=Error starting Valve
standardPipeline.valve.stop=Error stopping Valve

standardServer.accept.error=An IO exception occurred trying to accept on the socket listening for the shutdown command
standardServer.accept.readError=An IO exception occurred trying to read the shutdown command
standardServer.accept.security=A security error occurred trying to accept on the socket listening for the shutdown command
standardServer.accept.timeout=The socket listening for the shutdown command experienced an unexpected timeout [{0}] milliseconds after the call to accept(). Is this an instance of bug 56684?
standardServer.invalidShutdownCommand=Invalid shutdown command [{0}] received
standardServer.shutdownViaPort=A valid shutdown command was received via the shutdown port. Stopping the Server instance.
standardServer.storeConfig.contextError=Error storing context [{0}] configuration
standardServer.storeConfig.error=Error storing server configuration
standardServer.storeConfig.notAvailable=No StoreConfig implementation was registered as an MBean named [{0}] so no configuration could be saved. A suitable MBean is normally registered via the StoreConfigLifecycleListener.

standardService.connector.destroyFailed=Failed to destroy connector [{0}]
standardService.connector.initFailed=Failed to initialize connector [{0}]
standardService.connector.pauseFailed=Failed to pause connector [{0}]
standardService.connector.startFailed=Failed to start connector [{0}]
standardService.connector.stopFailed=Failed to stop connector [{0}]
standardService.engine.startFailed=Failed to start associated Engine
standardService.engine.stopFailed=Failed to stop associated Engine
standardService.mapperListener.startFailed=Failed to start associated MapperListener
standardService.mapperListener.stopFailed=Failed to stop associated MapperListener
standardService.start.name=Starting service [{0}]
standardService.stop.name=Stopping service [{0}]

standardWrapper.allocate=Error allocating a servlet instance
standardWrapper.allocateException=Allocate exception for servlet [{0}]
standardWrapper.deallocateException=Deallocate exception for servlet [{0}]
standardWrapper.destroyException=Servlet.destroy() for servlet [{0}] threw exception
standardWrapper.destroyInstance=InstanceManager.destroy() for servlet [{0}] threw exception
standardWrapper.initException=Servlet.init() for servlet [{0}] threw exception
standardWrapper.instantiate=Error instantiating servlet class [{0}]
standardWrapper.isUnavailable=Servlet [{0}] is currently unavailable
standardWrapper.notChild=Wrapper container may not have child containers
standardWrapper.notClass=No servlet class has been specified for servlet [{0}]
standardWrapper.notContext=Parent container of a Wrapper must be a Context
standardWrapper.notFound=Servlet [{0}] is not available
standardWrapper.notServlet=Class [{0}] is not a Servlet
standardWrapper.serviceException=Servlet.service() for servlet [{0}] in context with path [{1}] threw exception
standardWrapper.serviceExceptionRoot=Servlet.service() for servlet [{0}] in context with path [{1}] threw exception [{2}] with root cause
standardWrapper.unavailable=Marking servlet [{0}] as unavailable
standardWrapper.unloadException=Servlet [{0}] threw unload() exception
standardWrapper.unloading=Cannot allocate servlet [{0}] because it is being unloaded
standardWrapper.waiting=Waiting for [{0}] instance(s) to be deallocated for Servlet [{1}]

threadLocalLeakPreventionListener.containerEvent.error=Exception processing container event [{0}]
threadLocalLeakPreventionListener.lifecycleEvent.error=Exception processing lifecycle event [{0}]
