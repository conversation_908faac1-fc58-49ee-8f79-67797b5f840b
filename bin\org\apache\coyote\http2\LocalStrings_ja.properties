# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractStream.windowSizeDec=コネクション[{0}]、ストリーム[{1}]、フロー制御ウィンドウを[{2}]ずつ[{3}]に縮小
abstractStream.windowSizeInc=コネクション [{0}]、ストリーム [{1}]、フロー制御ウインドウを [{2}] から [{3}] に増加します。
abstractStream.windowSizeTooBig=コネクション[{0}]、ストリーム[{1}]、[{2}]のウィンドウサイズが許容最大値を超える[{3}]に増加しました。

connectionPrefaceParser.eos=オープニングクライアントPrefaceのバイトシーケンスを読み取っているときに予期しないストリームの終わりが発生しました。 [{0}]バイトだけが読み込まれます。
connectionPrefaceParser.ioError=オープニングクライアントPreface のバイトシーケンスの読み取りに失敗しました。
connectionPrefaceParser.mismatch=コネクションプリフェイス [{0}] の先頭に未知のバイト列を受信しました。

connectionSettings.debug=コネクション [{0}]、パラメータ [{1}] に [{2}] を設定しました。
connectionSettings.enablePushInvalid=コネクション[{0}]、有効プッシュ[{1}]にリクエストされた値が許容値（0または1）のいずれでもありません。
connectionSettings.headerTableSizeLimit=コネクション [{0}]、ヘッダーテーブルサイズに [{1}] を指定されましたが上限は 16k です。
connectionSettings.maxFrameSizeInvalid=コネクション [{0}]、要求された最大フレームサイズ [{1}] は可能な範囲の [{2}] から [{3}] を超えています。
connectionSettings.unknown=コネクション [{0}]、未知の設定名 [{1}] の値 [{2}] を無視しました。
connectionSettings.windowSizeTooBig=コネクション [{0}]、要求されたウインドウサイズ [{1}] は上限値 [{2}] を越えています。

frameType.checkPayloadSize=[{0}]のペイロードサイズがフレームタイプ[{1}]に無効です
frameType.checkStream=無効なフレームタイプ[{0}]

hpack.integerEncodedOverTooManyOctets=エンコードされたHPACK可変長整数は多くのオクテットを超過。最大値は[{0}]
hpack.invalidCharacter=コードポイント [{1}] のユニコード文字 [{0}] は有効範囲 0 から 255 の範囲外のため、エンコードできません。

hpackEncoder.encodeHeader=ヘッダー[{0}]を値[{1}]でエンコードしています

hpackdecoder.headerTableIndexInvalid=[{1}]静的エントリと[{2}]動的エントリが存在するため、ヘッダーテーブルインデックス[{0}]は無効です。
hpackdecoder.maxMemorySizeExceeded=ヘッダテーブルサイズ [{0}] は最大サイズ [{1}] を超えています
hpackdecoder.notImplemented=まだ実装されていません。
hpackdecoder.nullHeader=インデックス [{0}] のヘッダは Null です
hpackdecoder.tableSizeUpdateNotAtStart=すべてのテーブルサイズの更新はヘッダーブロックの先頭に送信する必要があります。
hpackdecoder.zeroNotValidHeaderTableIndex=ゼロは有効なヘッダーテーブルインデックスではありません。

hpackhuffman.huffmanEncodedHpackValueDidNotEndWithEOS=HPACK ヘッダーのハフマン符号化した値は EOS パディングで終了していません。
hpackhuffman.stringLiteralEOS=HPACK ヘッダ中のハフマン符号化値に EOS 記号が含まれています
hpackhuffman.stringLiteralTooMuchPadding=Huffman 符号化された文字列リテラルの終わりに、7ビット以上のEOSパディングが提供されました。

http2Parser.headerLimitCount=コネクション [{0}]、ストリーム [{1}]、ヘッダーが多すぎます。
http2Parser.headerLimitSize=コネクション [{0}]、ストリーム [{1}]、合計ヘッダーサイズが大きすぎます。
http2Parser.headers.wrongFrameType=コネクション[{0}]、ストリーム[{1}]の進行中のヘッダー、しかしタイプ[{2}]のフレームが受信されました。
http2Parser.headers.wrongStream=接続[{0}]、ストリーム[{1}]のヘッダ処理中にストリーム[{2}]のフレームが受信されました
http2Parser.nonZeroPadding=コネクション[{0}]、ストリーム[{1}]、非ゼロのパディングを受信しました。
http2Parser.payloadTooBig=ペイロードの長さは[{0}]バイトですが、最大フレームサイズは[{1}]です
http2Parser.preface.invalid=無効なコネクションpreface が提示されました
http2Parser.preface.io=コネクションprefaceを読むことができません。
http2Parser.processFrame=コネクション[{0}]、ストリーム[{1}]、フレームタイプ[{2}]、フラグ[{3}]、ペイロードサイズ[{4}]
http2Parser.processFrame.tooMuchPadding=コネクション [{0}]、ストリーム [{1}]、ペイロード [{3}] に対してパディング長 [{2}] は大きすぎます。
http2Parser.processFrame.unexpectedType=予想されるフレームタイプ[{0}]、しかし受信されたフレームタイプ[{1}]
http2Parser.processFrameContinuation.notExpected=コネクション[{0}]、進行中のヘッダーがないときにストリーム[{1}]のContinuationフレームが受信されました
http2Parser.processFrameData.lengths=コネクション[{0}]、ストリーム[{1}]、データ長、[{2}]、パディング長[{3}]
http2Parser.processFrameData.window=コネクション [{0}]、クライアントはストリームウインドウサイズより大きなデータを送信しました。
http2Parser.processFrameHeaders.decodingDataLeft=HPAC をデコードしたのにデータが残っています。すべて使用するべきです。
http2Parser.processFrameHeaders.decodingFailed=HTTP ヘッダーの HPACK 復号化中にエラーが発生しました。
http2Parser.processFrameHeaders.payload=コネクション[{0}]、ストリーム[{1}]、サイズ[{2}]のヘッダーペイロードを処理中
http2Parser.processFramePriority.invalidParent=コネクション[{0}]、ストリーム[{1}]、ストリーム自体に依存しない可能性があります。
http2Parser.processFramePushPromise=コネクション [{0}]、ストリーム [{1}]、クライアントから PUSH_PROMISE フレームを送信するべきではありません。
http2Parser.processFrameSettings.ackWithNonZeroPayload=ACKフラグがセットされ、ペイロードが存在する状態で受信されたSettingsフレーム
http2Parser.processFrameWindowUpdate.debug=コネクション [{0}]、ストリーム [{1}]、ウインドウサイズを [{2}] に拡大します。
http2Parser.processFrameWindowUpdate.invalidIncrement=無効な増分サイズ[{0}]で受信されたWindow Updateフレーム
http2Parser.swallow.debug=コネクション[{0}]、ストリーム[{1}]、飲み込まれた[{2}]バイト

pingManager.roundTripTime=コネクション [{0}] の往復時間は [{1}] ns でした。

stream.closed=コネクション [{0}]、ストリーム [{1}]、切断したストリームには書き込みできません。
stream.header.case=コネクション [{0}]、ストリーム [{1}]、HTTP ヘッダー名 [{2}] は小文字でなければなりません。
stream.header.connection=コネクション [{0}]、ストリーム [{1}]、HTTP/2 のリクエストには HTTP ヘッダー [connection] を指定することはできません。
stream.header.contentLength=コネクション [{0}]、ストリーム [{1}]、content length ヘッダーの値 [{2}] と受信したデータ長 [{3}] は一致しません。
stream.header.debug=コネクション [{0}]、ストリーム [{1}]、HTTP ヘッダー [{2}]、値は [{3}]
stream.header.duplicate=コネクション [{0}]、ストリーム [{1}]、ヘッダー [{2}] を複数受信しました。
stream.header.empty=接続 [{0}]、ストリーム [{1}]、無効な空ヘッダ名です
stream.header.invalid=コネクション [{0}]、ストリーム [{1}]、ヘッダー [{2}] に不正な値 [{3}] が含まれています。
stream.header.noPath=コネクション [{0}]、ストリーム [{1}]、疑似ヘッダー [:path] が空です。
stream.header.required=コネクション [{0}]、ストリーム [{1}]、１つ以上の必須ヘッダがありません。
stream.header.te=コネクション [{0}]、ストリーム [{1}]、HTTP/2 のリクエストでは HTTP ヘッダー [te] の値に [{2}] を指定できません。
stream.header.unexpectedPseudoHeader=コネクション [{0}]、ストリーム [{1}]、通常のヘッダーの後に疑似ヘッダー [{2}] を受信しました。
stream.header.unknownPseudoHeader=コネクション [{0}]、ストリーム [{1}]、未知の疑似ヘッダー [{2}] を受信しました。
stream.inputBuffer.copy=入力バッファーから出力バッファーへコピーしたのは  [{0}] バイトです。
stream.inputBuffer.dispatch=read interest が登録されると、inBufferにデータが追加されます。 読み取りディスパッチをトリガします。
stream.inputBuffer.empty=ストリーム入力バッファが空です。 より多くのデータを待っています。
stream.inputBuffer.readTimeout=クライアントからデータを読み取る待機中のタイムアウト
stream.inputBuffer.reset=ストリームリセット
stream.inputBuffer.signal=読み込みスレッドが待機している間に inBuffer へデータが追加されました。スレッドへ処理の再開を通知しす。
stream.notWritable=コネクション [{0}]、ストリーム [{1}]、このストリームには書き込みできません。
stream.outputBuffer.flush.debug=コネクション[{0}]、ストリーム[{1}]、バッファポジション[{2}]で出力をフラッシュ、writeInProgress [{3}]、クローズ[{4}]
stream.reprioritisation.debug=コネクション[{0}]、ストリーム[{1}]、排他[{2}]、Parent[{3}]、重み[{4}]
stream.reset.fail=コネクション [{0}]、ストリーム [{1}]、ストリームをリセットできません。
stream.reset.receive=コネクション[{0}]、ストリーム[{1}]、[{2}]のために受信されたリセット
stream.reset.send=コネクション [{0}]、ストリーム [{1}]、[{2}] が原因で RESET を送信しました。
stream.trailerHeader.noEndOfStream=コネクション[{0}]、ストリーム[{1}]、trailer ヘッダーにストリーム終了フラグが含まれていません。
stream.writeTimeout=クライアントがストリームデータの書き込みを許可するためにフロー制御ウィンドウを増やすのを待つタイムアウト

streamProcessor.cancel=コネクション [{0}]、ストリーム [{1}]、残りのリクエストボディは必要とされません
streamProcessor.error.connection=コネクション[{0}]、ストリーム[{1}]、コネクションに致命的なエラーが処理中に発生しました。
streamProcessor.error.stream=コネクション[{0}]、ストリーム[{1}]、処理中にストリームに致命的なエラーが発生しました。
streamProcessor.flushBufferedWrite.entry=コネクション [{0}]、ストリーム [{1}]、書き込み用バッファをフラッシュします。
streamProcessor.service.error=リクエスト処理中のエラー

streamStateMachine.debug.change=コネクション [{0}]、ストリーム [{1}]、状態を [{2}] から [{3}] へ変更しました。
streamStateMachine.invalidFrame=コネクション [{0}]、ストリーム [{1}]、状態 [{2}]、フレーム種類 [{3}]

upgradeHandler.allocate.debug=コネクション[{0}]、ストリーム[{1}]、割り当てられた[{2}]バイト
upgradeHandler.allocate.left=コネクション[{0}]、ストリーム[{1}]、[{2}]バイトが未割り当て - 子への割り当てを試みています。
upgradeHandler.allocate.recipient=コネクション[{0}]、ストリーム[{1}]、重み[{3}]の潜在的な受信者[{2}]
upgradeHandler.connectionError=接続エラー
upgradeHandler.dependency.invalid=コネクション [{0}]、ストリーム [{1}]、ストリームは自分自身に依存するべきではありません。
upgradeHandler.goaway.debug=コネクション[{0}]、Goaway、最終ストリーム[{1}]、エラーコード[{2}]、デバッグデータ[{3}]
upgradeHandler.init=コネクション[{0}]、状態[{1}]
upgradeHandler.initialWindowSize.invalid=コネクション[{0}]、[{1}]の無効な値は初期ウィンドウサイズで無視されました
upgradeHandler.invalidPreface=コネクション[{0}]、無効なConnection Preface
upgradeHandler.ioerror=コネクション[{0}]
upgradeHandler.noAllocation=接続 [{0}]、ストリーム [{1}]、割り当ての待機がタイムアウトしました
upgradeHandler.noNewStreams=コネクション [{0}]、ストリーム [{1}]、このコネクションには新しいストリームを作成できないためストリームを無視します。
upgradeHandler.pause.entry=コネクション[{0}] 一時停止中
upgradeHandler.pingFailed=コネクション ID [{0}] はクライアントへの ping 送信に失敗しました。
upgradeHandler.prefaceReceived=コネクション [{0}]、クライアントからコネクションプリフェイスを受信しました。
upgradeHandler.pruneIncomplete=コネクション [{0}]、ストリーム [{1}]、コネクションを削除できませんでした。アクティブなストリーム数 [{2}] は多すぎます。
upgradeHandler.pruneStart=コネクション[{0}] 古いストリームのプルーニングを開始します。 上限は[{1}] で、現在[{2}]ストリームがあります。
upgradeHandler.pruned=コネクション [{0}]、完了したストリーム [{1}] は削除します。
upgradeHandler.prunedPriority=コネクション [{0}]、優先度木に登録されていた可能性のある未使用のストリーム [{1}] を取り除きました。
upgradeHandler.releaseBacklog=コネクション[{0}]、ストリーム[{1}]はバックログから解放されました。
upgradeHandler.rst.debug=コネクション [{0}]、ストリーム [{1}]、エラー [{2}]、メッセージ [{3}]、RST (ストリームを切断します)
upgradeHandler.sendPrefaceFail=コネクション [{0}]、クライアントにプリフェイスを送信できませんでした。
upgradeHandler.socketCloseFailed=ソケットクローズ中のエラー
upgradeHandler.stream.closed=ストリーム[{0}]がしばらく閉じられていました
upgradeHandler.stream.even=新しいリモートストリーム ID [{0}] を要求されましたがリモートストリームの ID は奇数でなければなりません。
upgradeHandler.stream.notWritable=コネクション[{0}]、ストリーム[{1}]、このストリームは書き込み可能ではありません
upgradeHandler.stream.old=新しいリモートストリーム ID [{0}] を要求されましたが、最新のストリームは [{1}] です。
upgradeHandler.tooManyRemoteStreams=クライアントは[{0}]以上のアクティブなストリームを使用しようとしました
upgradeHandler.tooMuchOverhead=Connection [{0}]、オーバーヘッドが多すぎるため、接続が閉じられます。
upgradeHandler.unexpectedAck=コネクション[{0}]、ストリーム[{1}]、予期しないときにsettings ackを受信しました。
upgradeHandler.upgrade=コネクション[{0}]、ストリーム[1]へのHTTP / 1.1 upgrade
upgradeHandler.upgrade.fail=コネクション[{0}]、HTTP / 1.1のアップグレードに失敗しました
upgradeHandler.upgradeDispatch.entry=エントリ、コネクション[{0}]、ソケット状態 [{1}]
upgradeHandler.upgradeDispatch.exit=終了、コネクション[{0}]、ソケット状態[{1}]
upgradeHandler.windowSizeReservationInterrupted=コネクション[{0}]、ストリーム[{1}]、[{2}]バイトの予約
upgradeHandler.windowSizeTooBig=コネクション[{0}]、ストリーム[{1}]、ウィンドウサイズが大きすぎます
upgradeHandler.writeBody=コネクション [{0}]、ストリーム [{1}]、データ長 [{2}]
upgradeHandler.writeHeaders=コネクション [{0}], ストリーム [{1}]
upgradeHandler.writePushHeaders=コネクション[{0}]、ストリーム[{1}]、プッシュされたストリーム[{2}]、EndOfStream [{3}]

windowAllocationManager.dispatched=接続 [{0}]、ストリーム [{1}]、ディスパッチされました
windowAllocationManager.notified=接続 [{0}]、ストリーム [{1}]、通知されました
windowAllocationManager.notify=接続 [{0}]、ストリーム [{1}]、待機タイプ [{2}]、通知タイプ [{3}]
windowAllocationManager.waitFor.connection=接続 [{0}]、ストリーム [{1}]、接続フロー制御ウィンドウ (blocking) を待機中です (タイムアウト [{2}])
windowAllocationManager.waitFor.ise=接続 [{0}]、ストリーム [{1}]、すでに待機中です
windowAllocationManager.waitFor.stream=接続 [{0}]、ストリーム [{1}]、ストリームフロー制御ウィンドウ (blocking) を待機中です (タイムアウト [{2}])
windowAllocationManager.waitForNonBlocking.connection=接続 [{0}]、ストリーム [{1}]、接続フロー制御ウィンドウ (non-blocking) を待機中です
windowAllocationManager.waitForNonBlocking.stream=接続 [{0}]、ストリーム [{1}]、ストリームフロー制御ウィンドウ (non-blocking) を待機中です

writeStateMachine.endWrite.ise=書き込みが完了したら新しい状態に[{0}]を指定するのは不正です。
writeStateMachine.ise=状態[{1}]の[{0}()]を呼び出すことは不正です。
