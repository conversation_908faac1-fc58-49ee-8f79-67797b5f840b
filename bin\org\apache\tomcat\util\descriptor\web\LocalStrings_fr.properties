# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

filterDef.invalidFilterName=Valeur Invalide de <filter-name> [{0}] dans la définition du filtre.

securityConstraint.uncoveredHttpMethod=Les méthodes HTTP [{1}] des contraintes de sécurité du modèle d''URL [{0}] sont protégées, toutes les autres ne le sont pas
securityConstraint.uncoveredHttpMethodFix=Ajout de contraintes de sécurité avec le masque d''URL [{0}] pour empêcher l''accès aux méthodes HTTP non couvertes qui ne sont pas une de celles ci [{1}]
securityConstraint.uncoveredHttpOmittedMethod=Les méthodes HTTP [{1}] des contraintes de sécurité du modèle d''URL [{0}] ne sont pas protégées
securityConstraint.uncoveredHttpOmittedMethodFix=Ajout de contraintes de sécurité avec le masque d''URL [{0}] pour empêcher l''accès aux méthodes HTTP [{1}] non couvertes

servletDef.invalidServletName=<servlet-name> [{0}] invalide dans la définition du Servlet

webRuleSet.absoluteOrdering=L’élément <absolute-ordering> est invalide dans web-fragment.xml et sera ignoré
webRuleSet.absoluteOrderingCount=L'élément <absolute-ordering> est limité à 1 ocurrence
webRuleSet.nameCount=L'élément <name> est limité à 1 ocurrence
webRuleSet.postconstruct.duplicate=La méthode post construct est dupliquée dans la classe [{0}]
webRuleSet.predestroy.duplicate=Double définition de l''annotation de méthode @PreDestroy pour la classe [{0}]
webRuleSet.relativeOrdering=L’élément <ordering> est invalide dans web.xml et sera ignoré
webRuleSet.relativeOrderingCount=L'élément <ordering> est limité à 1 ocurrence

webXml.duplicateEnvEntry=Le nom de l''env-entry [{0}] a été déclaré en double
webXml.duplicateFilter=Nom du filtre dupliqué [{0}]
webXml.duplicateFragment=Il y a plus d''un fragment nommé [{0}], ce qui n''est pas légal avec l''ordre relatif ; voir la section 8.2.2 2c de la spécification Servlet, l''ordre absolu peut éventuellement être utilisé
webXml.duplicateMessageDestination=Le nom de la message-destination [{0}] a été déclaré en double
webXml.duplicateMessageDestinationRef=Le nom de la message-destination-ref [{0}] a été déclaré en double
webXml.duplicateResourceEnvRef=Le nom de la resource-env-ref [{0}] a été déclaré en double
webXml.duplicateResourceRef=Le nom de la resource-ref [{0}] a été déclaré en double
webXml.duplicateServletMapping=Les servlets nommés [{0}] et [{1}] sont tous deux associés au même modèle d''URL [{2}], ce qui n''est pas permis
webXml.duplicateTaglibUri=Librairies de tags déclarée en double avec l''URI [{0}]
webXml.mergeConflictDisplayName=Le nom d''affichage a été défini de manière inconsistante entre différents fragments dont le fragment [{0}] situé à [{1}]
webXml.mergeConflictFilter=Le Filter [{0}] a été défini de manière inconsistante entre différents fragments dont le fragment [{1}] situé à [{2}]
webXml.mergeConflictLoginConfig=Le LoginConfig a été défini de manière inconsistante entre différents fragments dont le fragment [{0}] situé à [{1}]
webXml.mergeConflictOrder=L'ordre relatif des fragments contient des références circulaires, cela peut être résolu en utilisant un ordre absolu dans web.xml
webXml.mergeConflictResource=La Resource [{0}] a été définie de manière inconsistante entre différents fragments dont le fragment [{1}] situé à [{2}]
webXml.mergeConflictServlet=Le Servlet [{0}] a été défini de manière inconsistante entre différents fragments dont le fragment [{1}] situé à [{2}]
webXml.mergeConflictSessionCookieComment=Le commentaire de cookie de session a été défini de manière inconsistante avec des valeurs différentes entre différents fragments dont le fragment [{0}] situé à [{1}]
webXml.mergeConflictSessionCookieDomain=Le domaine de cookie de session a été défini de manière inconsistante avec des valeurs différentes entre différents fragments dont le fragment [{0}] situé à [{1}]
webXml.mergeConflictSessionCookieHttpOnly=L''indicateur http-only du cookie de session a été défini de manière inconsistante avec des valeurs différentes entre différents fragments dont le fragment [{0}] situé à [{1}]
webXml.mergeConflictSessionCookieMaxAge=Le max-age du cookie de session a été défini de manière inconsistante avec des valeurs différentes entre différents fragments dont le fragment [{0}] situé à [{1}]
webXml.mergeConflictSessionCookieName=Le nom de cookie de session a été défini de manière inconsistante avec des valeurs différentes entre différents fragments dont le fragment [{0}] situé à [{1}]
webXml.mergeConflictSessionCookiePath=Le chemin du cookie de session a été défini de manière inconsistante avec des valeurs différentes entre différents fragments dont le fragment [{0}] situé à [{1}]
webXml.mergeConflictSessionCookieSecure=L''indicateur secure du cookie de session a été défini de manière inconsistante avec des valeurs différentes entre différents fragments dont le fragment [{0}] situé à [{1}]
webXml.mergeConflictSessionTimeout=Le timeout de la session a été défini de manière inconsistante avec des valeurs différentes entre différents fragments dont le fragment [{0}] situé à [{1}]
webXml.mergeConflictSessionTrackingMode=Les modes de gestion de la session ont été déclarés de manière inconsistante entre plusieurs fragments nommés [{0}] et localisés à [{1}]
webXml.mergeConflictString=Le [{0}] avec comme nom [{1}] a été défini de manière inconsistante entre différents fragments dont le fragment [{2}] situé à [{3}]
webXml.multipleOther=Plusieurs entrées <others> sont incluses dans l'élément <ordering>
webXml.reservedName=Un fichier web.xml a été détecté avec un nom réservé [{0}], l''élément name sera ignoré pour ce fragment
webXml.unrecognisedPublicId=L''identifiant public [{0}] ne correspond à aucun des identifiants connus pour les fichiers web.xml, donc la version n''a pu être indentifiée
webXml.version.unknown=Version [{0}] inconnue, utilisation de la version par défaut
webXml.wrongFragmentName=Utilisation d''un mauvais nom de fragment [{0}] dans le tag absolute-ordering de web.xml

webXmlParser.applicationParse=Erreur de traitement du web.xml de l''application à [{0}]
webXmlParser.applicationPosition=S''est produit à la ligne [{0}] colonne [{1}]
webXmlParser.applicationStart=Traitement du web.xml de l''application à [{0}]
