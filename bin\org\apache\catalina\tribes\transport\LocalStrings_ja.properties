# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

PooledSender.senderDisconnectFail=Senderの切断に失敗しました。

pooledSender.closed.queue=キューは閉じられています。

receiverBase.bind.failed=アドレス [{0}] にレプリケーションリスナーを束縛できません。
receiverBase.socket.bind=Receiver Serverソケットがバインドされました：[{0}]
receiverBase.udp.bind=UDP 受信用のサーバーソケットを [{0}] にバインドしました。
receiverBase.unable.bind=[{0}] へサーバーソケットをバインドできなかったためエラーを送出しました。
receiverBase.unable.bind.udp=UDP ソケットを [{0}] へバインドできません。エラーを送出します。
