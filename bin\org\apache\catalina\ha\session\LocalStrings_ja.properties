# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

backupManager.noCluster=コンテキスト: [{0}]にクラスターが関連付けられていません。
backupManager.startFailed=BackupManager: [{0}]の起動に失敗しました。
backupManager.startUnable=BackupManager を開始できません。: [{0}]
backupManager.stopped=Manager  [{0}] を停止します。

clusterSessionListener.noManager=Context Managerが存在しません。

deltaManager.createMessage.access=Manager [{0}]: セッション [{1}] へのアクセスメッセージを作成します。
deltaManager.createMessage.accessChangePrimary=Manager[{0}]: セッション [{1}] でプライマリノード変更メッセージを作成しました。
deltaManager.createMessage.allSessionData=Manager  [{0}] はすべてのセッションデータを送信しました。
deltaManager.createMessage.allSessionTransferred=Manager [{0}]はすべてのセッションデータ転送を送信しました。
deltaManager.createMessage.delta=Manager [{0}]：セッション[{1}]のデルタリクエストメッセージを作成します
deltaManager.createMessage.expire=Manager [{0}]：セッション[{1}]のセッションの期限切れメッセージを作成します。
deltaManager.createMessage.unableCreateDeltaRequest=セッションID [{0}]のDeltaRequestシリアライズできません。
deltaManager.createSession.newSession=DeltaSession (ID は [{0}]) を作成しました。総数は [{1}] です。
deltaManager.dropMessage=Manager [{0}]：GET_ALL_SESSIONS同期フェーズの開始日[{2}] メッセージの日付[{3}]内のメッセージ[{1}]をドロップします。
deltaManager.expireSessions=シャットダウン時にManager[{0}]はセッションを満了します。
deltaManager.foundMasterMember=コンテキスト [{0}] のレプリケーションマスターメンバー [{1}] を発見しました。
deltaManager.loading.cnfe=永続セッションのロード中にClassNotFoundExceptionが発生しました：[{0}]
deltaManager.loading.existing.session=既存セッション[{0}]のオーバーロード
deltaManager.loading.ioe=永続化セッションの読み込み中に IOException が発生しました: [{0}]
deltaManager.managerLoad=永続化ストレージからセッションの読み込み中に例外が発生しました。
deltaManager.noCluster=Starting ...このコンテキスト[{0}]に関連付けられたクラスタはありません。
deltaManager.noContextManager=マネージャ[{0}]：[{2}] msで[{1}]でNo Context Managerの送信が受信されました
deltaManager.noMasterMember=ドメイン[{1}]のコンテキスト[{0}]に他のメンバーがいない状態で開始しています。
deltaManager.noMembers=Manager [{0}]：状態転送をスキップします。 クラスタグループ内でアクティブなメンバーはいません。
deltaManager.noSessionState=Manager [{0}]：[{1}]で送信されたセッション状態はありませんでした。[{2}] ms後にタイムアウトしました。
deltaManager.receiveMessage.accessed=Manager [{0}]：セッション[{1}]のセッションアクセスメッセージを受信しました。
deltaManager.receiveMessage.allSessionDataAfter=Manager[{0}]: 全てのセッション状態をデシリアライズしました。
deltaManager.receiveMessage.allSessionDataBegin=Manager[{0}]：すべてのセッション状態データを受信しました。
deltaManager.receiveMessage.createNewSession=Manager [{0}]：セッション[{1}]のセッション作成メッセージを受信しました。
deltaManager.receiveMessage.delta=Manager [{0}]：セッション[{1}]のセッションデルタメッセージを受信しました。
deltaManager.receiveMessage.delta.unknown=マネージャ[{0}]：未知のセッション[{1}]デルタを受信しました。
deltaManager.receiveMessage.error=Manager [{0}]: TCP チャンネルからメッセージを受信できません。
deltaManager.receiveMessage.eventType=Manager[{0}]: [{2}] からセッションメッセージ [{1}] を受信しました。
deltaManager.receiveMessage.expired=Manager[{0}]：セッション[{1}]のセッションの期限切れメッセージを受信しました。
deltaManager.receiveMessage.noContextManager=Manager [{0}]はノード[{1}：{2}]からコンテキストマネージャ無しメッセージを受信しました。
deltaManager.receiveMessage.transfercomplete=Manager [{0}]はノード[{1}：{2}]からセッション状態が転送を受信しました。
deltaManager.receiveMessage.unloadingAfter=Manager[{0}]：セッションのアンロードが完了しました。
deltaManager.receiveMessage.unloadingBegin=Manager [{0}]：セッションのアンロードを開始します
deltaManager.registerCluster=マネージャー [{0}] をクラスターの構成要素 [{1}] に名前 [{2}] で登録しました。
deltaManager.sendMessage.newSession=Manager[{0}]が新しいセッションを送信します。[{1}]
deltaManager.sessionReceived=Manager [{0}]; [{2}] msで受信した[{1}]で送信されたセッション状態。
deltaManager.startClustering=[{0}]でクラスタリングマネージャを開始しています。
deltaManager.stopped=マネージャ[{0}]が停止しています
deltaManager.unableSerializeSessionID=セッション ID [{0}] をシリアライズできません。
deltaManager.unloading.ioe=永続セッションを保存中のIOException：[{0}]
deltaManager.waitForSessionState=Manager[{0}]、[{1}]からのセッション状態を要求しています。 [{2}]秒以内にセッション状態が受信されなかった場合、この操作はタイムアウトになります。

deltaRequest.invalidAttributeInfoType=無効な属性情報タイプ= [{0}]
deltaRequest.removeUnable=要素を削除できません。
deltaRequest.showPrincipal=プリンシパル [{0}]はセッション [{1}]に設定されています
deltaRequest.ssid.mismatch=セッションIDが一致しません。デルタリクエストが実行されていません。
deltaRequest.ssid.null=setSessionId に指定したセッション ID が null です。
deltaRequest.wrongPrincipalClass=ClusterManagerはGenericPrincipalのみをサポートします。 あなたのRealmはプリンシパルクラス[{0}]を使用しました。

deltaSession.notifying=クラスタにセッションの有効期限を通知する：primary = [{1}]、sessionId [{2}]
deltaSession.readSession=readObject() はセッション [{0}] を読み込みました。
deltaSession.writeSession=writeObject() によりセッション [{0}] を格納しました。

jvmRoute.cannotFindSession=セッション [{0}] がありません。
jvmRoute.changeSession=セッションを [{0}] から [{1}] へ変更しました。
jvmRoute.failover=他の jvmRoute へのフェールオーバーを検出しました。元のルートは [{0}]、新しいルートは [{1}]、セッション ID は [{2}] です。
jvmRoute.foundManager=コンテキスト [{1}] のCluster Manager  [{0}] を発見しました。
jvmRoute.missingJvmRouteAttribute=jvmRoute 属性にエンジンが指定されていません。
jvmRoute.noCluster=JvmRouterBinderValveは設定されていますが、クラスタリングは使用されていません。 PersistentManagerが使用されている場合、フェールオーバーは引き続き機能します。
jvmRoute.notFoundManager=[{0}]でCluster Managerが見つかりません。
jvmRoute.set.originalsessionid=オリジナルSession idをリクエスト属性[{0}]の値：[{1}]で設定します。
jvmRoute.turnoverInfo=折り返しの所要時間は [{0}] ミリ秒でした。
jvmRoute.valve.started=JvmRouteBinderValve が起動しました。
jvmRoute.valve.stopped=JvmRouteBinderValve が停止しました。

standardSession.notSerializable=セッション[{1}]のセッション属性[{0}]をシリアライズできません。
standardSession.removeAttribute.ise=removeAttribute: Session already invalidated
standardSession.setAttribute.namenull=setAttribute：nameパラメータをnullにすることはできません。
