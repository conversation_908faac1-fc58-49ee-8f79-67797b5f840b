package com.yq.cc.vxml;

import java.io.*;
import java.nio.charset.StandardCharsets;

/**
 * IVR 到 VXML 转换工具使用示例
 */
public class IvrToVxmlExample {
    
    public static void main(String[] args) {
        // 示例1：转换现有的 ivr.json 文件
        example1_ConvertExistingFile();
        
        // 示例2：创建自定义 IVR 配置并转换
        example2_ConvertCustomConfig();
        
        // 示例3：批量转换多个文件
        example3_BatchConvert();
    }
    
    /**
     * 示例1：转换现有的 ivr.json 文件
     */
    public static void example1_ConvertExistingFile() {
        System.out.println("=== 示例1：转换现有的 ivr.json 文件 ===");
        
        try {
            String vxml = IvrToVxmlUtil.convertFromFile("src/com/yq/cc/vxml/ivr.json");
            
            // 保存到文件
            saveToFile(vxml, "src/com/yq/cc/vxml/example1_output.vxml");
            
            System.out.println("转换成功！输出文件：example1_output.vxml");
            System.out.println("VXML 预览：");
            System.out.println(vxml.substring(0, Math.min(500, vxml.length())) + "...");
            
        } catch (Exception e) {
            System.err.println("转换失败：" + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
    
    /**
     * 示例2：创建自定义 IVR 配置并转换
     */
    public static void example2_ConvertCustomConfig() {
        System.out.println("=== 示例2：创建自定义 IVR 配置并转换 ===");
        
        // 创建一个简单的 IVR 配置
        String customIvrJson = createSimpleIvrConfig();
        
        try {
            String vxml = IvrToVxmlUtil.convertJsonToVxml(customIvrJson);
            
            // 保存到文件
            saveToFile(vxml, "src/com/yq/cc/vxml/example2_output.vxml");
            
            System.out.println("转换成功！输出文件：example2_output.vxml");
            System.out.println("VXML 预览：");
            System.out.println(vxml.substring(0, Math.min(500, vxml.length())) + "...");
            
        } catch (Exception e) {
            System.err.println("转换失败：" + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
    
    /**
     * 示例3：批量转换多个文件
     */
    public static void example3_BatchConvert() {
        System.out.println("=== 示例3：批量转换演示 ===");
        
        // 这里演示如何批量处理多个 IVR 配置
        String[] configs = {
            createSimpleIvrConfig(),
            createComplexIvrConfig()
        };
        
        for (int i = 0; i < configs.length; i++) {
            try {
                String vxml = IvrToVxmlUtil.convertJsonToVxml(configs[i]);
                String filename = "src/com/yq/cc/vxml/example3_batch_" + (i + 1) + ".vxml";
                saveToFile(vxml, filename);
                System.out.println("批量转换 " + (i + 1) + " 成功：" + filename);
                
            } catch (Exception e) {
                System.err.println("批量转换 " + (i + 1) + " 失败：" + e.getMessage());
            }
        }
        
        System.out.println();
    }
    
    /**
     * 创建简单的 IVR 配置
     */
    private static String createSimpleIvrConfig() {
        return "{\n" +
               "  \"ivrCode\": \"simple01\",\n" +
               "  \"ivrName\": \"简单IVR流程\",\n" +
               "  \"nodeList\": [\n" +
               "    {\n" +
               "      \"id\": \"START_001\",\n" +
               "      \"name\": \"开始\",\n" +
               "      \"type\": \"start\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"USERTASK_001\",\n" +
               "      \"name\": \"欢迎语\",\n" +
               "      \"type\": \"userTask\",\n" +
               "      \"voicePlayContent\": \"欢迎致电客服热线，请按1继续，按0转人工。\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"SERVICETASK_001\",\n" +
               "      \"name\": \"用户选择\",\n" +
               "      \"type\": \"serviceTask\",\n" +
               "      \"paramName\": \"USER_CHOICE\",\n" +
               "      \"paramMinSize\": \"1\",\n" +
               "      \"paramMaxSize\": \"1\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"END_001\",\n" +
               "      \"name\": \"结束\",\n" +
               "      \"type\": \"end\"\n" +
               "    }\n" +
               "  ],\n" +
               "  \"lineList\": [\n" +
               "    {\n" +
               "      \"id\": \"LINE_001\",\n" +
               "      \"from\": \"START_001\",\n" +
               "      \"to\": \"USERTASK_001\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"LINE_002\",\n" +
               "      \"from\": \"USERTASK_001\",\n" +
               "      \"to\": \"SERVICETASK_001\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"LINE_003\",\n" +
               "      \"from\": \"SERVICETASK_001\",\n" +
               "      \"to\": \"END_001\"\n" +
               "    }\n" +
               "  ]\n" +
               "}";
    }
    
    /**
     * 创建复杂的 IVR 配置（带条件分支）
     */
    private static String createComplexIvrConfig() {
        return "{\n" +
               "  \"ivrCode\": \"complex01\",\n" +
               "  \"ivrName\": \"复杂IVR流程\",\n" +
               "  \"nodeList\": [\n" +
               "    {\n" +
               "      \"id\": \"START_001\",\n" +
               "      \"name\": \"开始\",\n" +
               "      \"type\": \"start\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"USERTASK_001\",\n" +
               "      \"name\": \"主菜单\",\n" +
               "      \"type\": \"userTask\",\n" +
               "      \"voicePlayContent\": \"请选择服务：1-查询余额，2-转账服务，9-返回上级菜单，0-转人工服务。\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"SERVICETASK_001\",\n" +
               "      \"name\": \"菜单选择\",\n" +
               "      \"type\": \"serviceTask\",\n" +
               "      \"paramName\": \"MENU_CHOICE\",\n" +
               "      \"paramMinSize\": \"1\",\n" +
               "      \"paramMaxSize\": \"1\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"USERTASK_002\",\n" +
               "      \"name\": \"余额查询\",\n" +
               "      \"type\": \"userTask\",\n" +
               "      \"voicePlayContent\": \"您的账户余额为1000元。\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"USERTASK_003\",\n" +
               "      \"name\": \"转账服务\",\n" +
               "      \"type\": \"userTask\",\n" +
               "      \"voicePlayContent\": \"转账服务暂时不可用，请稍后再试。\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"END_001\",\n" +
               "      \"name\": \"结束\",\n" +
               "      \"type\": \"end\"\n" +
               "    }\n" +
               "  ],\n" +
               "  \"lineList\": [\n" +
               "    {\n" +
               "      \"id\": \"LINE_001\",\n" +
               "      \"from\": \"START_001\",\n" +
               "      \"to\": \"USERTASK_001\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"LINE_002\",\n" +
               "      \"from\": \"USERTASK_001\",\n" +
               "      \"to\": \"SERVICETASK_001\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"LINE_003\",\n" +
               "      \"from\": \"SERVICETASK_001\",\n" +
               "      \"to\": \"USERTASK_002\",\n" +
               "      \"label\": \"选择1\",\n" +
               "      \"expression\": {\n" +
               "        \"param\": \"MENU_CHOICE\",\n" +
               "        \"conditions\": \"eq\",\n" +
               "        \"val1\": \"1\"\n" +
               "      }\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"LINE_004\",\n" +
               "      \"from\": \"SERVICETASK_001\",\n" +
               "      \"to\": \"USERTASK_003\",\n" +
               "      \"label\": \"选择2\",\n" +
               "      \"expression\": {\n" +
               "        \"param\": \"MENU_CHOICE\",\n" +
               "        \"conditions\": \"eq\",\n" +
               "        \"val1\": \"2\"\n" +
               "      }\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"LINE_005\",\n" +
               "      \"from\": \"USERTASK_002\",\n" +
               "      \"to\": \"END_001\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"LINE_006\",\n" +
               "      \"from\": \"USERTASK_003\",\n" +
               "      \"to\": \"END_001\"\n" +
               "    }\n" +
               "  ]\n" +
               "}";
    }
    
    /**
     * 保存内容到文件
     */
    private static void saveToFile(String content, String filename) throws IOException {
        try (PrintWriter writer = new PrintWriter(
                new OutputStreamWriter(new FileOutputStream(filename), StandardCharsets.UTF_8))) {
            writer.print(content);
        }
    }
}
