# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

McastService.domain=Impossible d'envoyer la mise à jour du domaine
McastService.parseSoTimeout=Impossible de lire SoTimeout : [{0}]
McastService.parseTTL=Impossible d''analyser le TTL : [{0}]
McastService.payload=Impossible d'envoyer les données de mise à jour
McastService.stopFail=Impossible d''arrêter le service mcast, niveau [{0}]

mcastService.exceed.maxPacketSize=La taille du paquet [{0}] excède la taille maximale qui est de [{1}] octets
mcastService.missing.property=McastService :La propriété obligatoire [{0}] manque.
mcastService.noStart=L'envoi multicast n'est pas démarré ou activé

mcastServiceImpl.bind=Tentive d''associer le socket multicast à [{0} : {1}]
mcastServiceImpl.bind.failed=Echec de l'association à l’adresse multicast, association uniquement sur le port
mcastServiceImpl.error.receiving=Erreur en recevant un paquet multicast, attente de 500ms
mcastServiceImpl.invalid.startLevel=Niveau de départ invalide. Les seuls niveaux acceptables sont Channel.MBR_RX_SEQ et Channel.MBR_TX_SEQ
mcastServiceImpl.invalid.stopLevel=Niveau de stop invalide, les seuls niveaux valides sont Channel.MBR_RX_SEQ et Channel.MBR_TX_SEQ
mcastServiceImpl.memberDisappeared.failed=Impossible de traiter le message indiquant un membre disparu
mcastServiceImpl.packet.tooLong=Le paquet multicast reçu est trop long, il est abandonné : [{0}]
mcastServiceImpl.receive.running=McastService.receive est déjà en cours d'exécution
mcastServiceImpl.recovery=Le multicast est non fonctionnel, le registre de membres de Tribes exécute le processus de récupération
mcastServiceImpl.recovery.failed=La tentative de récupération numéro [{0}] échouée, nouvel essai dans [{1}] secondes
mcastServiceImpl.recovery.startFailed=Le thread de récupération n'a pas pu démarrer le registre de membres
mcastServiceImpl.recovery.stopFailed=Le thread de récupération n'a pu arrêter le registre de membres
mcastServiceImpl.recovery.successful=Succès de récupération du registre de membres
mcastServiceImpl.send.failed=Impossible d'envoyer le message mcast.
mcastServiceImpl.send.running=McastService.send est déjà en cours d'exécution.
mcastServiceImpl.setInterface=Définition de l''interface multicast multi réseaux comme [{0}]
mcastServiceImpl.setSoTimeout=Réglage du mcast soTimeout du cluster à [{0}]
mcastServiceImpl.setTTL=Le multicast TTL du cluster est fixé à [{0}]
mcastServiceImpl.unable.join=Incapable de rejoindre le le groupe de multidiffusion ("multicast"). Assurez-vous que la multidiffusion est activée sur votre système.
mcastServiceImpl.unableReceive.broadcastMessage=N'a pas pu recevoir de message général (broadcast)
mcastServiceImpl.waitForMembers.done=Fin de l''attente, le registre de membres est établi, démarrage de niveau : [{0}]
mcastServiceImpl.waitForMembers.start=Attente de [{0}] millisecondes pour établir le registre de membres du cluster, démarrage au niveau [{1}]

memberImpl.invalid.package.begin=Le paquet est invalide, il devrait démarrer par [{0}]
memberImpl.invalid.package.end=le paquet est invalide, il devrait se terminer par : [{0}]
memberImpl.large.payload=Le contenu est trop gros pour être géré par Tribes
memberImpl.notEnough.bytes=Pas assez d'octets dans le paquet membre
memberImpl.package.small=Le paquet du membre est trop petit pour être validé
memberImpl.unableParse.hostname=Incapable d'analyser le nom d'hôte (hostname)

staticMember.invalid.uuidLength=Un UUID doit faire exactement 16 octets et non [{0}]
