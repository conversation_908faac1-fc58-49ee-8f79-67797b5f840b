# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jarScan.classloaderFail=在多级类加载器中扫描[{0}]失败
jarScan.classloaderJarNoScan=跳过classpath路径[{0}]下的jar包扫描。
jarScan.classloaderJarScan=从classpath扫描JAR[{0}]
jarScan.classloaderStart=在类加载器层次结构中扫描JAR
jarScan.jarUrlStart=正在扫描URL [{0}] 上的JAR文件
jarScan.webinfclassesFail=无法扫描/WEB-INF/classes
jarScan.webinflibFail=无法从/WEB-INF/lib扫描JAR[{0}]。
jarScan.webinflibJarNoScan=没有扫描到/WEB-INF/lib目录下的JAR [{0}]
jarScan.webinflibJarScan=从/WEB-INF/lib扫描JAR[{0}]
jarScan.webinflibStart=扫描./WEB-INF/lib 中的JARs
