# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

PooledSender.senderDisconnectFail=No pude desconectar al remitente

receiverBase.bind.failed=Fallo al atachar el escuchador de replicación en la dirección: [{0}]\n
receiverBase.unable.bind=Imposible vincular el socket del servidor a:[{0}]  lanzando error.
receiverBase.unable.bind.udp=Imposible atar el socket UDP a:[{0}] lanzando error.\n
