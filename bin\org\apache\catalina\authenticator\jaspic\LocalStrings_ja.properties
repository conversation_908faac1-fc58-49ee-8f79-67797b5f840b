# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

authConfigFactoryImpl.load=[{0}]から永続化プロバイダの登録を読み込みます。
authConfigFactoryImpl.registerClass=アプリケーションコンテキスト [{2}] のレイヤー [{1}] にクラス [{0}] を登録します。
authConfigFactoryImpl.registerInstance=認証構成プロバイダ [{0}] のインスタンスを、レイヤ [{1}] とアプリケーションコンテキスト [{2}] に登録しています
authConfigFactoryImpl.zeroLengthAppContext=文字列長が 0 のアプリケーションコンテキスト名は不正です。
authConfigFactoryImpl.zeroLengthMessageLayer=長さゼロのメッセージレイヤ名は無効です

callbackHandlerImpl.jaspicCallbackMissing=受信したタイプ[{0}]のサポートされていないJASPICコールバックが無視されました。

jaspicAuthenticator.authenticate=JASPIC経由で[{0}]へのリクエストを認証しています

persistentProviderRegistrations.deleteFail=一時ファイル [{0}] を削除できません。
persistentProviderRegistrations.existsDeleteFail=同名の一時ファイル [{0}] が存在し、削除もできませんでした。
persistentProviderRegistrations.moveFail=[{0}]を[{1}]に移動できませんでした。

simpleServerAuthConfig.noModules="ServerAuthModulesが設定されていません"
