# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

combinedRealm.addRealm=添加[{0}]领域，共有[{1}]个领域。
combinedRealm.authFail=无法使用域[{1}]对用户[{0}]进行身份验证
combinedRealm.authStart=正在尝试使用领域[{1}]对用户[{0}]进行身份验证
combinedRealm.authSuccess=认证用户[{0}]，权限[{1}]
combinedRealm.getPassword=永远不应该调用getPassword（）方法
combinedRealm.getPrincipal=方法getPrincipal()永远不应该被调用
combinedRealm.realmStartFail=无法启动[{0}]领域
combinedRealm.unexpectedMethod=对组合域上的方法进行了意外调用

credentialHandler.invalidStoredCredential=域提供了无效的存储凭据字符串[{0}]，以便与用户提供的凭据匹配

dataSourceRealm.authenticateFailure=用户名[{0}]未成功验证
dataSourceRealm.authenticateSuccess=用户名[{0}]已成功通过身份验证。
dataSourceRealm.close=关闭数据库连接时发生异常
dataSourceRealm.exception=认证异常
dataSourceRealm.getPassword.exception=获取用户名[{0}]对应的密码失败。
dataSourceRealm.getRoles.exception=检索角色[{0}]异常

jaasCallback.username=返回用户名 [{0}]

jaasRealm.accountExpired=由于帐户过期，用户名[{0}]未通过身份验证
jaasRealm.authenticateFailure=用户 [{0}] 认证失败
jaasRealm.authenticateSuccess=用户名 [{0}] 已被成功认证为身份 [{1}] -- 主体也已创建
jaasRealm.beginLogin=使用应用程序[{1}的LoginContext为用户名[{0}]请求JAASRealm登录
jaasRealm.checkPrincipal=正在检查主体[{0}[{1}]
jaasRealm.credentialExpired=由于凭据过期，用户名[{0}]未通过身份验证
jaasRealm.failedLogin=由于登录失败，用户名 [{0}] 无法授权
jaasRealm.loginContextCreated=为用户名创建的JAAS 登陆上下文[{0}]
jaasRealm.loginException=登录异常，认证用户名  [{0}]
jaasRealm.rolePrincipalAdd=正在将角色主体[{0}]添加到此用户主体的角色
jaasRealm.rolePrincipalFailure=找不到有效的角色主体。
jaasRealm.unexpectedError=意外错误
jaasRealm.userPrincipalFailure=未发现有效的用户Principal
jaasRealm.userPrincipalSuccess=主体[{0}]是有效的用户类。我们将其用作用户主体。

jdbcRealm.authenticateFailure=用户名称[{0}]未校验成功
jdbcRealm.authenticateSuccess=用户名[{0}]已成功通过身份验证
jdbcRealm.close=关闭数据库连接异常
jdbcRealm.exception=执行身份验证时发生异常
jdbcRealm.open=打开数据库连接时发生异常
jdbcRealm.open.invalidurl=驱动程序[{0}]不支持url[{1}]。

jndiRealm.authenticateFailure=用户名[{0}]没有成功认证
jndiRealm.authenticateSuccess=用户名[{0}]成功认证
jndiRealm.cipherSuites=启用 [{0}] 作为 TLS 连接的加密套件。
jndiRealm.close=关闭目录服务器连接时发生异常
jndiRealm.emptyCipherSuites=给定密码套件的空字符串。使用默认密码套件
jndiRealm.exception=执行认证异常
jndiRealm.exception.retry=执行身份验证时发生异常。正在重试。。。
jndiRealm.invalidHostnameVerifier=[{0}]不是HostnameVerifier的有效类名
jndiRealm.invalidSslProtocol=给定的协议[{0}]无效。它必须是[{1}]之一
jndiRealm.invalidSslSocketFactory=[{0}]不是SSLSocketFactory的有效类名。
jndiRealm.multipleEntries=用户名称[{0}]拥有多个实体
jndiRealm.negotiatedTls=使用协议[{0}]协商的TLS连接
jndiRealm.open=打开目录服务器链接异常
jndiRealm.tlsClose=关闭tls响应时出现异常

lockOutRealm.authLockedUser=尝试对锁定的用户[{0}]进行身份验证
lockOutRealm.removeWarning=用户[{0}]在[{1}]秒后从失败的用户缓存中删除，以将缓存大小保持在限制集内

mdCredentialHandler.unknownEncoding=不支持编码{0}，因此仍将使用当前的设置{1}

memoryRealm.authenticateFailure=用户名[{0}]未成功通过身份验证
memoryRealm.authenticateSuccess=用户名称[{0}]认证成功
memoryRealm.loadExist=内存数据库文件[{0}]无法读取
memoryRealm.loadPath=从内存数据库文件 [{0}] 加载用户
memoryRealm.readXml=读取内存数据库文件时出现异常
memoryRealm.xmlFeatureEncoding=配置Digester以允许XML文件中的java编码名称的异常。只支持IANA编码名称。

pbeCredentialHandler.invalidKeySpec=无法生成基于密码的密钥

realmBase.algorithm=无效的消息摘要算法[{0}]
realmBase.authenticateFailure=用户名 [{0}] 认证失败
realmBase.authenticateSuccess=用户名[{0}]已成功通过身份验证
realmBase.cannotGetRoles=无法从主体[{0}]获取角色
realmBase.createUsernameRetriever.ClassCastException=类[{0}] 不是一个X509UsernameRetriever.
realmBase.createUsernameRetriever.newInstance=无法创建类型为{0}的对象。
realmBase.credentialNotDelegated=虽然已请求存储，但用户[{0}]的凭据尚未委派
realmBase.delegatedCredentialFail=无法获取用户[{0}]的委派凭据。
realmBase.digest=对用户凭证摘要发生错误
realmBase.forbidden=已拒绝访问所请求的资源
realmBase.gotX509Username=从X509证书中获取用户名：[{0}]
realmBase.gssContextNotEstablished=身份验证器实现错误：传递的安全上下文未完全建立
realmBase.gssNameFail=无法从已建立的GSSContext中提取名称
realmBase.hasRoleFailure=用户[{0}]没有角色[{1}]
realmBase.hasRoleSuccess=用户名[{0}] 有角色[{1}]

userDatabaseRealm.lookup=在键[{0}]下查找用户数据库时发生异常
userDatabaseRealm.noDatabase=未找到key[{0}]对应的UserDatabase组件。
