# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

namingResources.cleanupCloseFailed=コンテナ[{2}]内のリソース[{1}]のメソッド[{0}]の呼び出しに失敗したため、そのリソースに対してクリーンアップが実行されませんでした。
namingResources.cleanupCloseSecurity=コンテナ [{2}] のリソース [{1}] からメソッド [{0}] を取得できません。リソースを後始末しませんでした。
namingResources.cleanupNoClose=コンテナー [{1}] のリソース [{0}] にはメソッド [{2}] がないため、リソースの後始末を行いません。
namingResources.cleanupNoContext=コンテナ[{0}]のJNDIネーミングコンテキストの取得に失敗したため、そのコンテナに対してクリーンアップが実行されませんでした。
namingResources.cleanupNoResource=コンテナ[{1}]のJNDIリソース[{0}]の取得に失敗したため、そのリソースに対してクリーンアップが実行されませんでした。
namingResources.ejbLookupLink=EJB リソース [{0}] に ejb-link と lookup-name の両方が指定されています。
namingResources.envEntryLookupValue=環境エントリ[{0}]は、検索名と値の両方を指定します。
namingResources.mbeanCreateFail=ネーミングリソース[{0}]のMBeanの作成に失敗しました
namingResources.mbeanDestroyFail=ネーミングリソース[{0}]のMBeanを破棄できませんでした。
namingResources.resourceTypeFail=[{0}]という名前のJNDIリソースは[{1}]タイプですが、タイプはそのリソース用に構成された注入ターゲットのタイプと矛盾しています。
