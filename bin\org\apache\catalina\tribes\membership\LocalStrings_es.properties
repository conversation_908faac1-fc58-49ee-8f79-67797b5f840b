# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

mcastServiceImpl.bind=Tratando de atar el socket multicast a [{0}:{1}]\n
mcastServiceImpl.recovery=Membresía Tribes ejecutando hilo de recuperación, multicasting no esta operativo.
mcastServiceImpl.send.running=McastService.send aún esta corriendo.
mcastServiceImpl.setInterface=Fijando interfaz multicase multihme a:[{0}]\n
mcastServiceImpl.setSoTimeout=Fijando cluster mcast para tiempo de espera gotado en [{0}]
mcastServiceImpl.setTTL=Fijando cluster cluster mcast TTL a [{0}]\n
mcastServiceImpl.unableReceive.broadcastMessage=Incapaz de recibir el mensaje de broadcast

memberImpl.notEnough.bytes=No hay suficientes bytes en el paquete miembro
