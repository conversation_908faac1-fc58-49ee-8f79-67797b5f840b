# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

diagnostics.threadDumpTitle=풀 쓰레드 덤프
diagnostics.vmInfoClassCompilation=클래스 컴파일
diagnostics.vmInfoClassLoading=클래스 로딩
diagnostics.vmInfoGarbageCollectors=Garbage Collector [{0}]
diagnostics.vmInfoLogger=Logger 정보
diagnostics.vmInfoMemory=메모리 정보
diagnostics.vmInfoMemoryManagers=메모리 매니저 [{0}]
diagnostics.vmInfoMemoryPools=메모리 풀 [{0}]
diagnostics.vmInfoOs=운영체제 정보
diagnostics.vmInfoPath=경로 정보
diagnostics.vmInfoRuntime=런타임 정보
diagnostics.vmInfoStartup=프로그램 시작 아규먼트들
diagnostics.vmInfoSystem=시스템 프로퍼티들
diagnostics.vmInfoThreadCounts=쓰레드 개수
diagnostics.vmInfoThreadMxBean=ThreadMXBean 용량정보들
