# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

pojoEndpointBase.closeSessionFail=Echec de fermeture de la session WebSocket pendant le traitement d'erreur
pojoEndpointBase.onCloseFail=Echec lors de l''appel de la méthode onClose du point de terminaison POJO de type [{0}]
pojoEndpointBase.onError=Aucun gestionnaire d''erreur n''est configuré pour [{0}] et l''erreur suivante s''est produite
pojoEndpointBase.onErrorFail=Echec de l''appel de la méthode onError du point de terminaison POJO pour le type [{0}]
pojoEndpointBase.onOpenFail=Impossible d’appeler la méthode onOpen du point de terminaison POJO de type [{0}]

pojoEndpointServer.getPojoInstanceFail=Échec de création d''une instance de POJO de type [{0}]

pojoMessageHandlerWhole.decodeIoFail=Erreur d'IO lors du décodage du message
pojoMessageHandlerWhole.maxBufferSize=La taille maximale de message supportée par cette implémentation est Integer.MAX_VALUE

pojoMethodMapping.decodePathParamFail=Echec de décodage de la valeur de paramètre de chemin [{0}] vers le type attendu [{1}]
pojoMethodMapping.duplicateAnnotation=Annotations dupliquées [{0}] présentes pour la classe [{1}]
pojoMethodMapping.duplicateLastParam=Multiple (derniers) paramètres booléens présents sur la méthode [{0}] de classe [{1}], qui était annotée par OnMessage
pojoMethodMapping.duplicateMessageParam=De multiples paramètres de message sont présents sur la méthode [{0}] de la classe [{1}] qui a été annotée avec OnMessage
pojoMethodMapping.duplicatePongMessageParam=De multiples paramètres PongMessage sont présents sur la méthode [{0}] de la classe [{1}] qui a été annotée avec OnMessage
pojoMethodMapping.duplicateSessionParam=De multiples paramètres de session sont présents sur la méthode [{0}] de la classe [{1}] qui a été annotée avec OnMessage
pojoMethodMapping.invalidDecoder=Le décodeur de type [{0}] spécifié n''a pas pu être instantié
pojoMethodMapping.methodNotPublic=La méthode [{0}] annotée n''est pas publique
pojoMethodMapping.noDecoder=Aucun décodeur n''a été trouvé pour les paramètres de message présents sur la méthode [{0}] de la classe [{1}] qui a été annotée avec OnMessage
pojoMethodMapping.noPayload=Pas de paramètre de données présent sur la méthode [{0}] de la classe [{1}] qui a été annotée par OnMessage
pojoMethodMapping.onErrorNoThrowable=Aucun paramètre Throwable n''est présent sur la méthode [{0}] de la classe [{1}] qui est annotée par OnError
pojoMethodMapping.paramWithoutAnnotation=Un paramètre de type [{0}] a été trouvé sur la méthode [{1}] de la classe [{2}] qui n''avait pas d''annotation @PathParam
pojoMethodMapping.partialInputStream=L''InputStream et les paramètres booléens présents sur la méthode [{0}] de la classe [{1}] qui a été annotée par OnMessage sont invalides
pojoMethodMapping.partialObject=L''objet et la paramètres booléens présents sur la méthode [{0}] de la classe [{1}] qui a été annotée par OnMessage sont invalides
pojoMethodMapping.partialPong=Le PongMessage et les paramètres booléens présents sur la méthode [{0}] de la classe [{1}] qui a été annotée par OnMessage sont invalides
pojoMethodMapping.partialReader=Le Reader et les paramètres booléens présents sur la méthode [{0}] de la classe [{1}] qui a été annotée par OnMessage sont invalides
pojoMethodMapping.pongWithPayload=Le PongMessage et les paramètres de message présents sur la méthode [{0}] de la classe [{1}] qui a été annotée par OnMessage sont invalides

pojoPathParam.wrongType=Le type [{0}] n''est pas autorisé en tant que paramètre de chemin, les paramètres annotés avec @PathParm doivent être uniquement des String, des primitives Java ou des versions encapsulées de celles ci
