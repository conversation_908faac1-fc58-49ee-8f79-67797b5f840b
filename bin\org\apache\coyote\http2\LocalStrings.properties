# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractStream.windowSizeDec=Connection [{0}], Stream [{1}], reduce flow control window by [{2}] to [{3}]
abstractStream.windowSizeInc=Connection [{0}], Stream [{1}], increase flow control window by [{2}] to [{3}]
abstractStream.windowSizeTooBig=Connection [{0}], Stream [{1}], increased window size by [{2}] to [{3}] which exceeded permitted maximum

connectionPrefaceParser.eos=Unexpected end of stream while reading opening client preface byte sequence. Only [{0}] bytes read.
connectionPrefaceParser.ioError=Failed to read opening client preface byte sequence
connectionPrefaceParser.mismatch=An unexpected byte sequence was received at the start of the client preface [{0}]

connectionSettings.debug=Connection [{0}], Endpoint [{1}], Parameter type [{2}] set to [{3}]
connectionSettings.enablePushInvalid=Connection [{0}], The requested value for enable push [{1}] is not one of the permitted values (zero or one)
connectionSettings.headerTableSizeLimit=Connection [{0}], Attempted to set a header table size of [{1}] but the limit is 16k
connectionSettings.maxFrameSizeInvalid=Connection [{0}], The requested maximum frame size of [{1}] is outside the permitted range of [{2}] to [{3}]
connectionSettings.unknown=Connection [{0}], An unknown setting with identifier [{1}] and value [{2}] was ignored
connectionSettings.windowSizeTooBig=Connection [{0}], The requested window size of [{1}] is bigger than the maximum permitted value of [{2}]

frameType.checkPayloadSize=Payload size of [{0}] is not valid for frame type [{1}]
frameType.checkStream=Invalid frame type [{0}]

hpack.integerEncodedOverTooManyOctets=HPACK variable length integer encoded over too many octets, max is [{0}]
hpack.invalidCharacter=The Unicode character [{0}] at code point [{1}] cannot be encoded as it is outside the permitted range of 0 to 255.

hpackEncoder.encodeHeader=Encoding header [{0}] with value [{1}]

hpackdecoder.headerTableIndexInvalid=The header table index [{0}] is not valid as there are [{1}] static entries and [{2}] dynamic entries
hpackdecoder.maxMemorySizeExceeded=The header table size [{0}] exceeds the maximum size [{1}]
hpackdecoder.notImplemented=Not yet implemented
hpackdecoder.nullHeader=Null header at index [{0}]
hpackdecoder.tableSizeUpdateNotAtStart=Any table size update must be sent at the start of a header block
hpackdecoder.zeroNotValidHeaderTableIndex=Zero is not a valid header table index

hpackhuffman.huffmanEncodedHpackValueDidNotEndWithEOS=Huffman encoded value in HPACK headers did not end with EOS padding
hpackhuffman.stringLiteralEOS=Huffman encoded value in HPACK headers contained the EOS symbol
hpackhuffman.stringLiteralTooMuchPadding=More than 7 bits of EOS padding were provided at the end of an Huffman encoded string literal

http2Parser.headerLimitCount=Connection [{0}], Stream [{1}], Too many headers
http2Parser.headerLimitSize=Connection [{0}], Stream [{1}], Total header size too big
http2Parser.headers.wrongFrameType=Connection [{0}], headers in progress for stream [{1}] but a frame of type [{2}] was received
http2Parser.headers.wrongStream=Connection [{0}], headers in progress for stream [{1}] but a frame for stream [{2}] was received
http2Parser.nonZeroPadding=Connection [{0}], Stream [{1}], Non-zero padding received
http2Parser.payloadTooBig=The payload is [{0}] bytes long but the maximum frame size is [{1}]
http2Parser.preface.invalid=Invalid connection preface presented
http2Parser.preface.io=Unable to read connection preface
http2Parser.processFrame=Connection [{0}], Stream [{1}], Frame type [{2}], Flags [{3}], Payload size [{4}]
http2Parser.processFrame.tooMuchPadding=Connection [{0}], Stream [{1}], The padding length [{2}] was too big for the payload [{3}]
http2Parser.processFrame.unexpectedType=Expected frame type [{0}] but received frame type [{1}]
http2Parser.processFrameContinuation.notExpected=Connection [{0}], Continuation frame received for stream [{1}] when no headers were in progress
http2Parser.processFrameData.lengths=Connection [{0}], Stream [{1}], Data length [{2}], Padding length [{3}]
http2Parser.processFrameData.window=Connection [{0}], Client sent more data than stream window allowed
http2Parser.processFrameHeaders.decodingDataLeft=Data left over after HPACK decoding - it should have been consumed
http2Parser.processFrameHeaders.decodingFailed=There was an error during the HPACK decoding of HTTP headers
http2Parser.processFrameHeaders.payload=Connection [{0}], Stream [{1}], Processing headers payload of size [{2}]
http2Parser.processFramePriority.invalidParent=Connection [{0}], Stream [{1}], A stream may not depend on itself
http2Parser.processFramePushPromise=Connection [{0}], Stream [{1}], Push promise frames should not be sent by the client
http2Parser.processFrameSettings.ackWithNonZeroPayload=Settings frame received with the ACK flag set and payload present
http2Parser.processFrameWindowUpdate.debug=Connection [{0}], Stream [{1}], Window size increment [{2}]
http2Parser.processFrameWindowUpdate.invalidIncrement=Window update frame received with an invalid increment size of [{0}]
http2Parser.swallow.debug=Connection [{0}], Stream [{1}], Swallowed [{2}] bytes

http2Protocol.jmxRegistration.fail=JMX registration for the HTTP/2 protocol failed

pingManager.roundTripTime=Connection [{0}] Round trip time measured as [{1}]ns

stream.clientCancel=Client reset the stream before the response was complete
stream.closed=Connection [{0}], Stream [{1}], Unable to write to stream once it has been closed
stream.header.case=Connection [{0}], Stream [{1}], HTTP header name [{2}] must be in lower case
stream.header.connection=Connection [{0}], Stream [{1}], HTTP header [connection] is not permitted in an HTTP/2 request
stream.header.contentLength=Connection [{0}], Stream [{1}], The content length header value [{2}] does not agree with the size of the data received [{3}]
stream.header.debug=Connection [{0}], Stream [{1}], HTTP header [{2}], Value [{3}]
stream.header.duplicate=Connection [{0}], Stream [{1}], received multiple [{2}] headers
stream.header.empty=Connection [{0}], Stream [{1}], Invalid empty header name
stream.header.invalid=Connection [{0}], Stream [{1}], The header [{2}] contained invalid value [{3}]
stream.header.noPath=Connection [{0}], Stream [{1}], The [:path] pseudo header was empty
stream.header.required=Connection [{0}], Stream [{1}], One or more required headers was missing
stream.header.te=Connection [{0}], Stream [{1}], HTTP header [te] is not permitted to have the value [{2}] in an HTTP/2 request
stream.header.unexpectedPseudoHeader=Connection [{0}], Stream [{1}], Pseudo header [{2}] received after a regular header
stream.header.unknownPseudoHeader=Connection [{0}], Stream [{1}], Unknown pseudo header [{2}] received
stream.inputBuffer.copy=Copying [{0}] bytes from inBuffer to outBuffer
stream.inputBuffer.dispatch=Data added to inBuffer when read interest is registered. Triggering a read dispatch
stream.inputBuffer.empty=The Stream input buffer is empty. Waiting for more data
stream.inputBuffer.readTimeout=Timeout waiting to read data from client
stream.inputBuffer.reset=Stream reset
stream.inputBuffer.signal=Data added to inBuffer when read thread is waiting. Signalling that thread to continue
stream.notWritable=Connection [{0}], Stream [{1}], This stream is not writable
stream.outputBuffer.flush.debug=Connection [{0}], Stream [{1}], flushing output with buffer at position [{2}], writeInProgress [{3}] and closed [{4}]
stream.recycle=Connection [{0}], Stream [{1}] has been recycled
stream.reprioritisation.debug=Connection [{0}], Stream [{1}], Exclusive [{2}], Parent [{3}], Weight [{4}]
stream.reset.fail=Connection [{0}], Stream [{1}], Failed to reset stream
stream.reset.receive=Connection [{0}], Stream [{1}], Reset received due to [{2}]
stream.reset.send=Connection [{0}], Stream [{1}], Reset sent due to [{2}]
stream.trailerHeader.noEndOfStream=Connection [{0}], Stream [{1}], The trailer headers did not include the end of stream flag
stream.writeTimeout=Timeout waiting for client to increase flow control window to permit stream data to be written

streamProcessor.cancel=Connection [{0}], Stream [{1}], The remaining request body is not required.
streamProcessor.error.connection=Connection [{0}], Stream [{1}], An error occurred during processing that was fatal to the connection
streamProcessor.error.stream=Connection [{0}], Stream [{1}], An error occurred during processing that was fatal to the stream
streamProcessor.flushBufferedWrite.entry=Connection [{0}], Stream [{1}], Flushing buffered writes
streamProcessor.service.error=Error during request processing

streamStateMachine.debug.change=Connection [{0}], Stream [{1}], State changed from [{2}] to [{3}]
streamStateMachine.invalidFrame=Connection [{0}], Stream [{1}], State [{2}], Frame type [{3}]

upgradeHandler.allocate.debug=Connection [{0}], Stream [{1}], allocated [{2}] bytes
upgradeHandler.allocate.left=Connection [{0}], Stream [{1}], [{2}] bytes unallocated - trying to allocate to children
upgradeHandler.allocate.recipient=Connection [{0}], Stream [{1}], potential recipient [{2}] with weight [{3}]
upgradeHandler.connectionError=Connection error
upgradeHandler.dependency.invalid=Connection [{0}], Stream [{1}], Streams may not depend on themselves
upgradeHandler.goaway.debug=Connection [{0}], Goaway, Last stream [{1}], Error code [{2}], Debug data [{3}]
upgradeHandler.init=Connection [{0}], State [{1}]
upgradeHandler.initialWindowSize.invalid=Connection [{0}], Illegal value of [{1}] ignored for initial window size
upgradeHandler.invalidPreface=Connection [{0}], Invalid connection preface
upgradeHandler.ioerror=Connection [{0}]
upgradeHandler.noAllocation=Connection [{0}], Stream [{1}], Timeout waiting for allocation
upgradeHandler.noNewStreams=Connection [{0}], Stream [{1}], Stream ignored as no new streams are permitted on this connection
upgradeHandler.pause.entry=Connection [{0}] Pausing
upgradeHandler.pingFailed=Connection [{0}] Failed to send ping to client
upgradeHandler.prefaceReceived=Connection [{0}], Connection preface received from client
upgradeHandler.pruneIncomplete=Connection [{0}], Stream [{1}], Failed to fully prune the connection because there are [{2}] too many active streams
upgradeHandler.pruneStart=Connection [{0}] Starting pruning of old streams. Limit is [{1}] and there are currently [{2}] streams.
upgradeHandler.pruned=Connection [{0}] Pruned completed stream [{1}]
upgradeHandler.prunedPriority=Connection [{0}] Pruned unused stream [{1}] that may have been part of the priority tree
upgradeHandler.releaseBacklog=Connection [{0}], Stream [{1}] released from backlog
upgradeHandler.rst.debug=Connection [{0}], Stream [{1}], Error [{2}], Message [{3}],  RST (closing stream)
upgradeHandler.sendPrefaceFail=Connection [{0}], Failed to send preface to client
upgradeHandler.socketCloseFailed=Error closing socket
upgradeHandler.stream.closed=Stream [{0}] has been closed for some time
upgradeHandler.stream.even=A new remote stream ID of [{0}] was requested but all remote streams must use odd identifiers
upgradeHandler.stream.notWritable=Connection [{0}], Stream [{1}], This stream is not writable
upgradeHandler.stream.old=A new remote stream ID of [{0}] was requested but the most recent stream was [{1}]
upgradeHandler.tooManyRemoteStreams=The client attempted to use more than [{0}] active streams
upgradeHandler.tooMuchOverhead=Connection [{0}], Too much overhead so the connection will be closed
upgradeHandler.unexpectedAck=Connection [{0}], Stream [{1}], A settings acknowledgement was received when not expected
upgradeHandler.upgrade=Connection [{0}], HTTP/1.1 upgrade to stream [1]
upgradeHandler.upgrade.fail=Connection [{0}], HTTP/1.1 upgrade failed
upgradeHandler.upgradeDispatch.entry=Entry, Connection [{0}], SocketStatus [{1}]
upgradeHandler.upgradeDispatch.exit=Exit, Connection [{0}], SocketState [{1}]
upgradeHandler.windowSizeReservationInterrupted=Connection [{0}], Stream [{1}], reservation for [{2}] bytes
upgradeHandler.windowSizeTooBig=Connection [{0}], Stream [{1}], Window size too big
upgradeHandler.writeBody=Connection [{0}], Stream [{1}], Data length [{2}], EndOfStream [{3}]
upgradeHandler.writeHeaders=Connection [{0}], Stream [{1}], Writing the headers, EndOfStream [{2}]
upgradeHandler.writePushHeaders=Connection [{0}], Stream [{1}], Pushed stream [{2}], EndOfStream [{3}]

windowAllocationManager.dispatched=Connection [{0}], Stream [{1}], Dispatched
windowAllocationManager.notified=Connection [{0}], Stream [{1}], Notified
windowAllocationManager.notify=Connection [{0}], Stream [{1}], Waiting type [{2}], Notify type [{3}]
windowAllocationManager.waitFor.connection=Connection [{0}], Stream [{1}], Waiting for Connection flow control window (blocking) with timeout [{2}]
windowAllocationManager.waitFor.ise=Connection [{0}], Stream [{1}], Already waiting
windowAllocationManager.waitFor.stream=Connection [{0}], Stream [{1}], Waiting for Stream flow control window (blocking) with timeout [{2}]
windowAllocationManager.waitForNonBlocking.connection=Connection [{0}], Stream [{1}], Waiting for Connection flow control window (non-blocking)
windowAllocationManager.waitForNonBlocking.stream=Connection [{0}], Stream [{1}], Waiting for Stream flow control window (non-blocking)

writeStateMachine.endWrite.ise=It is illegal to specify [{0}] for the new state once a write has completed
writeStateMachine.ise=It is illegal to call [{0}()] in state [{1}]
