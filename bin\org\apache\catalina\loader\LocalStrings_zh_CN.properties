# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

webappClassLoader.addExportsRmi=在Java 9上运行时，需要在JVM命令行参数中添加“-add opens=Java.rmi/sun.rmi.transport=ALL-UNNAMED”，以启用rmi目标内存泄漏检测。或者，可以通过禁用RMI目标内存泄漏检测来抑制此警告。
webappClassLoader.addExportsThreadLocal=在Java 9上运行时，需要在JVM命令行参数中添加“-add opens=Java.base/Java.lang=ALL-UNNAMED”，以启用线程本地内存泄漏检测。或者，可以通过禁用ThreadLocal内存泄漏检测来抑制此警告。
webappClassLoader.addPermissionNoCanonicalFile=无法从URL[{0}]获取规范文件路径
webappClassLoader.addPermissionNoProtocol=不支持URL[{1}]中的协议[{0}]，因此未授予位于此URL的资源的读取权限
webappClassLoader.addTransformer=将类文件转换器[{0}]添加到Web应用程序[{1}]。
webappClassLoader.addTransformer.duplicate=将类文件转换器[{0}]添加到web应用程序[{1}]的重复调用被忽略。
webappClassLoader.addTransformer.illegalArgument=Web应用程序[{0}]试图添加空类文件转换器。
webappClassLoader.checkThreadLocalsForLeaks=web应用程序[{0}]创建了一个ThreadLocal，其键类型为[{1}]（值为[{2}]），值类型为[{3}]（值为[{4}），但在停止web应用程序时未能将其删除。线程将随着时间的推移而更新，以尝试避免可能的内存泄漏
webappClassLoader.checkThreadLocalsForLeaks.badKey=无法确定类型为[{0}]的键的字符串表示形式
webappClassLoader.checkThreadLocalsForLeaks.badValue=无法确定类型为 [{0}] 的值的字符串表示形式
webappClassLoader.checkThreadLocalsForLeaks.unknown=未知
webappClassLoader.checkThreadLocalsForLeaksFail=检查ThreadLocal引用失败，web应用程序：[{0}]
webappClassLoader.checkThreadLocalsForLeaksNone=web应用程序 [{0}] 创建了1个ThreadLocal变量(键：[{2}] (类型[{1}]) ，值：[{4}](类型[{3}]) )。键仅被ThreadLocal Map弱引用，所以不是内存泄露。
webappClassLoader.checkThreadLocalsForLeaksNull=web应用程序[{0}]创建了一个ThreadLocal，其键类型为[{1}]（值为[{2}]）。ThreadLocal已正确设置为null，GC将删除该键
webappClassLoader.checkThreadsHttpClient=找到使用web应用程序类加载器的HttpClient保持活动线程。通过将线程切换到父类加载器修复。
webappClassLoader.clearJdbc=Web应用程序 [{0}] 注册了JDBC驱动程序 [{1}]，但在Web应用程序停止时无法注销它。 为防止内存泄漏，JDBC驱动程序已被强制取消注册。
webappClassLoader.clearObjectStreamClassCachesFail=无法清除web应用程序[{0}]的ObjectStreamClass$缓存中的软引用
webappClassLoader.clearRmi=找到具有存根类类[{0}]和值[{1}]的RMI目标。已强制移除此RMI目标，以防止内存泄漏。
webappClassLoader.clearRmiFail=无法清除从web应用程序[{0}]的sun.rmi.transport.Target引用的上下文类加载器
webappClassLoader.clearRmiInfo=找不到类sun.rmi.transport.Target以清除web应用程序[{0}]的上下文类加载器。这在非Sun jvm上是预期的。
webappClassLoader.getThreadGroupError=无法获得线程组[{0}]的父级。不可能检查所有线程是否存在潜在的内存泄漏。
webappClassLoader.jarsAdded=一个或多个jar已添加到web应用程序[{0}]
webappClassLoader.jarsModified=一个或多个jar已在web应用程序中修改[{0}]
webappClassLoader.jarsRemoved=一个或多个 JAR 已被从 Web 应用程序 [{0}] 中删除
webappClassLoader.javaseClassLoaderNull=j2seClassLoader属性不能为空
webappClassLoader.jdbcRemoveFailed=Web应用程序 [{0}] 的JDBC驱动程序注销失败
webappClassLoader.loadedByThisOrChildFail=无法完全检查[{0}]实例中的条目，看上下文[{1}]中是否存在潜在的内存泄漏
webappClassLoader.readError=资源读取错误:不能加载 [{0}].
webappClassLoader.removeTransformer=已从web应用程序[{1}]中删除类文件转换器[{0}]。
webappClassLoader.resourceModified=资源[{0}]已被修改。上次修改时间是[{1}]，现在是[{2}]
webappClassLoader.restrictedPackage=安全冲突，尝试使用受限类[{0}]
webappClassLoader.securityException=尝试在findClassInternal[{1}]中查找类[{0}]时出现安全异常
webappClassLoader.stackTrace=Web应用程序[{0}]似乎启动了一个名为[{1}]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[{2}]
webappClassLoader.stackTraceRequestThread=web应用程序[{0}]仍在处理一个尚未完成的请求。这很可能会造成内存泄漏。您可以使用标准上下文实现的unloadDelay属性来控制请求完成所允许的时间。请求处理线程的堆栈跟踪：[{2}]
webappClassLoader.stopThreadFail=为web应用程序[{1}]终止线程[{0}]失败
webappClassLoader.stopTimerThreadFail=无法终止名为[{0}]的TimerThread，web应用程序：[{1}]
webappClassLoader.stopped=非法访问：此Web应用程序实例已停止。无法加载[{0}]。为了调试以及终止导致非法访问的线程，将抛出以下堆栈跟踪。
webappClassLoader.superCloseFail=调用父类的close()方法出现异常。
webappClassLoader.transformError=检测错误：无法转换类[{0}]，因为它的类文件格式是不合法的。
webappClassLoader.warnTimerThread=Web应用程序[{0}]似乎已通过java.util.Timer API启动了名为[{1}]的TimerThread，但未能将其停止。 为防止内存泄漏，计时器（以及相关联的线程）已被强制取消。
webappClassLoader.wrongVersion=(无法载入的.类 [{0}])

webappClassLoaderParallel.registrationFailed=将org.apache.catalina.loader.ParallelWebappClassLoader注册为能够并行加载类失败

webappLoader.deploy=将类存储库部署到工作目录[{0}]
webappLoader.reloadable=无法将可重载属性设置为[{0}]
webappLoader.setContext.ise=当加载器启动的时候设置上下文是不被允许的
webappLoader.starting=启动此加载程序
webappLoader.stopping=停止此加载程序
