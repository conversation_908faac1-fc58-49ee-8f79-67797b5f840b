# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jsseSupport.certTranslationError=証明書の翻訳中にエラーが発生しました[{0}]
jsseSupport.clientCertError=クライアント証明書を取得中のエラー

jsseUtil.excludeProtocol=JRE は SSL プロトコル [{0}] に対応しています。しかし Tomcat の利用可能プロトコルからは除外されています。
jsseUtil.noDefaultProtocols=sslEnableProtocols の既定値を取得できません。コネクターを開始できるよう明示的に値を設定してください。

pemFile.noMultiPrimes=PKCS#1 証明書は multi-prime RSA フォーマットですが、Java はそのようなフォーマットに対する RSA 秘密鍵を構築する API を提供していません
pemFile.notValidRFC5915=与えられたキーファイルは RFC 5915 に準拠していません
pemFile.parseError=秘密鍵ファイル [{0}] を解析できませんでした。
