# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

customObjectInputStream.logRequired=Ein gültiger Logger ist für Klassennamenfilterung mit Loggen nötig
customObjectInputStream.nomatch=Die Klasse [{0}] wird nicht mit dem regulären Ausdruck [{1}] gefunden, der erlaubte deserialisierte Klassen bestimmt.

extensionValidator.extension-not-found-error=ExtensionValidator[{0}][{1}]: Benötigte Erweiterung [{2}] nicht gefunden

introspection.classLoadFailed=Konnte Klasse [{0}] nicht laden

lifecycleBase.initFail=Konnte Komponente [{0}] nicht initialisieren
lifecycleBase.setState=Setze Status für [{0}] auf [{1}]

netmask.cidrNegative=Die CIDR [{0}] ist negativ
netmask.invalidAddress=Die Adresse [{0}] ist nicht gültig

sessionIdGeneratorBase.randomProvider=Während der Zufallsgenerator mit Hilfe des Providers [{0}] initialisiert wurde, trat eine Exception auf
