<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE mbeans-descriptors PUBLIC
   "-//Apache Software Foundation//DTD Model MBeans Configuration File"
   "http://jakarta.apache.org/commons/dtds/mbeans-descriptors.dtd">
<mbeans-descriptors>

  <mbean name="ApplicationFilterConfig"
         description="Wrapper that represents an individual servlet-filter definition"
         domain="Catalina"
         group="Filter"
         type="org.apache.catalina.core.ApplicationFilterConfig">

     <attribute name="filterName"
                description="The name used to reference the filter in web.xml"
                type="java.lang.String"
                writeable="false"/>

     <attribute name="filterClass"
                description="Fully qualified class name of the filter object"
                type="java.lang.String"
                writeable="false"/>

     <attribute name="filterInitParameterMap"
                description="Return the initialization parameters associated with this filter"
                type="java.util.Map"
                writeable="false" />

  </mbean>

  <mbean name="NamingContextListener"
         description="Helper class used to initialize and populate the JNDI context associated with each context and server"
         domain="Catalina"
         group="Listener"
         type="org.apache.catalina.core.NamingContextListener">

    <attribute name="className"
               description="Fully qualified class name of the managed object"
               type="java.lang.String"
               writeable="false"/>

    <attribute name="name"
               description="Name of the associated naming context"
               type="java.lang.String"
               writeable="false"/>

  </mbean>

  <mbean name="StandardContext"
         description="Standard Context Component"
         domain="Catalina"
         group="Context"
         type="org.apache.catalina.core.StandardContext"
         className="org.apache.catalina.mbeans.ContextMBean">

    <attribute name="altDDName"
               description="The alternate deployment descriptor name."
               type="java.lang.String" />

    <attribute name="antiResourceLocking"
               description="Take care to not lock resources"
               type="boolean" />

    <attribute name="baseName"
               description="The base name used for directories, WAR files (with .war appended) and context.xml files (with .xml appended)."
               type="java.lang.String"
               writeable="false"/>

    <attribute name="children"
               description="Object names of all children"
               type="[Ljavax.management.ObjectName;"/>

    <attribute name="clearReferencesRmiTargets"
               description="Should Tomcat look for memory leaks in RMI Targets and clear them if found as a work around for application coding errors?"
               type="boolean"/>

    <attribute name="clearReferencesStopThreads"
               description="Should Tomcat attempt to terminate threads that have been started by the web application? Advisable to be used only in a development environment."
               type="boolean"/>

    <attribute name="clearReferencesStopTimerThreads"
               description="Should Tomcat attempt to terminate TimerThreads that have been started by the web application? Advisable to be used only in a development environment."
               type="boolean"/>

    <attribute name="clearReferencesThreadLocals"
               description="Should Tomcat attempt to clear ThreadLocal variables that have been populated with classes loaded by the web application?"
               type="boolean"/>

    <attribute name="configFile"
               description="Location of the context.xml resource or file"
               type="java.net.URL"/>

    <attribute name="configured"
               description="The correctly configured flag for this Context."
               type="boolean"
               writeable="false" />

    <attribute name="cookies"
               description="Should we attempt to use cookies for session id communication?"
               type="boolean"/>

    <attribute name="crossContext"
               description="Should we allow the ServletContext.getContext() method to access the context of other web applications in this server?"
               type="boolean"/>

    <attribute name="defaultContextXml"
               description="Location of the default context.xml resource or file"
               type="java.lang.String"/>

    <attribute name="defaultWebXml"
               description="Location of the default web.xml resource or file"
               type="java.lang.String"/>

    <attribute name="delegate"
               description=""
               type="boolean"/>

    <attribute name="displayName"
               description="The display name of this web application"
               type="java.lang.String"/>

    <attribute name="distributable"
               description="The distributable flag for this web application."
               type="boolean"/>

    <attribute name="docBase"
               description="The document root for this web application"
               type="java.lang.String"/>

    <attribute name="encodedPath"
               description="The encoded path"
               type="java.lang.String"
               writeable="false" />

    <attribute name="ignoreAnnotations"
               description="Ignore annotations flag."
               type="boolean" />

    <attribute name="instanceManager"
               description="Object that creates and destroys servlets, filters, and listeners. Include dependency injection and postConstruct/preDestroy handling"
               type="org.apache.tomcat.InstanceManager" />

    <attribute name="javaVMs"
               description="The Java virtual machines on which this module is running"
               type="[Ljava.lang.String;"/>

    <attribute name="loader"
               description="Associated loader."
               type="org.apache.catalina.Loader" />

    <attribute name="logEffectiveWebXml"
               description="Should the effective web.xml be logged when the context starts?"
               type="boolean" />

    <attribute name="logger"
               description="Associated logger."
               type="org.apache.juli.logging.Log" />

    <attribute name="managedResource"
               description="The managed resource this MBean is associated with"
               type="java.lang.Object"/>

    <attribute name="manager"
               description="Associated manager."
               type="org.apache.catalina.Manager" />

    <attribute name="mapperContextRootRedirectEnabled"
               description="Should the Mapper be used for context root redirects"
               type="boolean" />

    <attribute name="mapperDirectoryRedirectEnabled"
               description="Should the Mapper be used for directory redirects"
               type="boolean" />

    <attribute name="namingContextListener"
               description="Associated naming context listener."
               type="org.apache.catalina.core.NamingContextListener" />

    <attribute name="objectName"
               description="Name of the object"
               type="java.lang.String"
               writeable="false" />

    <attribute name="originalDocBase"
               description="The original document root for this web application"
               type="java.lang.String" />

    <attribute name="override"
               description="The default context.xml override flag for this web application"
               type="boolean"/>

    <attribute name="name"
               description="The name of this Context"
               type="java.lang.String"/>

    <attribute name="parentClassLoader"
               description="Parent class loader."
               type="java.lang.ClassLoader" />

    <attribute name="path"
               description="The context path for this Context"
               type="java.lang.String"/>

    <attribute name="paused"
               description="The request processing pause flag (while reloading occurs)"
               type="boolean"
               writeable="false" />

    <attribute name="privileged"
               description="Access to tomcat internals"
               type="boolean"/>

    <attribute name="processingTime"
               description="Cumulative execution times of all servlets in this context"
               type="long"
               writeable="false" />

    <attribute name="maxTime"
               description="Maximum execution time of all servlets in this context"
               type="long"
               writeable="false" />

    <attribute name="minTime"
               description="Minimum execution time of all servlets in this context"
               type="long"
               writeable="false" />

    <attribute name="requestCount"
               description="Cumulative request count of all servlets in this context"
               type="int"
               writeable="false" />

    <attribute name="errorCount"
               description="Cumulative error count of all servlets in this context"
               type="int"
               writeable="false" />

    <attribute name="publicId"
               description="The public identifier of the DTD for the web application deployment descriptor version that is being parsed"
               type="java.lang.String"
               writeable="false" />

    <attribute name="realm"
               description="Associated realm."
               type="org.apache.catalina.Realm" />

    <attribute name="reloadable"
               description="The reloadable flag for this web application"
               type="boolean"/>

    <attribute name="renewThreadsWhenStoppingContext"
               description="Should Tomcat renew the threads of the thread pool when the application is stopped to avoid memory leaks because of uncleaned ThreadLocal variables."
               type="boolean"/>

    <attribute name="server"
               description="The J2EE Server this module is deployed on"
               type="java.lang.String"/>

    <attribute name="sessionCookieName"
               description="The name to use for session cookies.'null' indicates that the name is controlled by the application."
               type="java.lang.String"/>

    <attribute name="sessionCookieDomain"
               description="The domain to use for session cookies.'null' indicates that the domain is controlled by the application."
               type="java.lang.String"/>

    <attribute name="sessionCookiePath"
               description="The path to use for session cookies.'null' indicates that the path is controlled by the application."
               type="java.lang.String"/>

    <attribute name="sessionTimeout"
               description="The session timeout (in minutes) for this web application"
               type="int"/>

    <attribute name="startTime"
               description="Time (in milliseconds since January 1, 1970, 00:00:00) when this context was started"
               type="long"
               writeable="false" />

    <attribute name="startupTime"
               description="Time (in milliseconds) it took to start this context"
               type="long"/>

    <attribute name="stateName"
               description="The name of the LifecycleState that this component is currently in"
               type="java.lang.String"
               writeable="false"/>

    <attribute name="swallowOutput"
               description="Flag to set to cause the system.out and system.err to be redirected to the logger when executing a servlet"
               type="boolean"/>

    <attribute name="tldScanTime"
               description="Time spend scanning jars for TLDs for this context"
               type="long"/>

    <attribute name="tldValidation"
               description="Should the parsing of *.tld files be performed by a validating parser?"
               type="boolean"/>

    <attribute name="unloadDelay"
               description="Amount of ms that the container will wait for servlets to unload"
               type="long"/>

    <attribute name="unpackWAR"
               description="Unpack WAR property"
               type="boolean"/>

    <attribute name="useHttpOnly"
               description="Indicates that session cookies should use HttpOnly"
               type="boolean"/>

    <attribute name="useNaming"
               description="Create a JNDI naming context for this application?"
               is="true"
               type="boolean"/>

    <attribute name="useRelativeRedirects"
               description="When generating location headers for 302 responses, should a relative URI be used?"
               type="boolean"/>

    <attribute name="webappVersion"
               description="The version of this web application - used in parallel deployment to differentiate different versions of the same web application"
               type="java.lang.String"
               writeable="false"/>

    <attribute name="welcomeFiles"
               description="The welcome files for this context"
               type="[Ljava.lang.String;"
               writeable="false"/>

    <attribute name="workDir"
               description="The pathname to the work directory for this context"
               type="java.lang.String"/>

    <attribute name="xmlValidation"
               description="Should the parsing of web.xml and web-fragment.xml files be performed by a validating parser?"
               type="boolean"/>

    <attribute name="xmlNamespaceAware"
               description="Should the parsing of web.xml and web-fragment.xml files be performed by a namespace aware parser?"
               type="boolean"/>

    <operation name="addApplicationListener"
               description="Add a new Listener class name to the set of Listeners configured for this application."
               impact="ACTION"
               returnType="void">
      <parameter name="listener"
                 description="Java class name of a listener class"
                 type="java.lang.String"/>
    </operation>

    <operation name="addApplicationParameter"
               description="Add a new application parameter for this application."
               impact="ACTION"
               returnType="void">
      <parameter name="listener"
                 description="Java class name of a listener class"
                 type="java.lang.String"/>
    </operation>

    <operation name="addChild"
               description="Add a child to this Context"
               impact="ACTION"
               returnType="void">
      <parameter name="type"
                 description="Type(classname) of the new child to be added"
                 type="java.lang.String"/>
      <parameter name="name"
                 description="Name of the child to be added"
                 type="java.lang.String"/>
    </operation>

    <operation name="addLifecycleListener"
               description="Add a lifecycle listener to this Context"
               impact="ACTION"
               returnType="void">
      <parameter name="type"
                 description="Type(classname) of the new lifecycle listener to be added"
                 type="java.lang.String"/>
    </operation>

    <operation name="addLocaleEncodingMappingParameter"
               description="Add a Locale Encoding Mapping"
               impact="ACTION"
               returnType="void">
      <parameter name="locale"
                 description="Locale to map an encoding for"
                 type="java.lang.String"/>
      <parameter name="encoding"
                 description="Encoding to be used for a give locale"
                 type="java.lang.String"/>
    </operation>

    <operation name="addMimeMapping"
               description="Add a new MIME mapping, replacing any existing mapping for the specified extension."
               impact="ACTION"
               returnType="void">
      <parameter name="extension"
                 description="Filename extension being mapped"
                 type="java.lang.String"/>
      <parameter name="mimeType"
                 description="Corresponding MIME type"
                 type="java.lang.String"/>
    </operation>

    <operation name="addParameter"
               description="Add a new context initialization parameter, replacing any existing value for the specified name."
               impact="ACTION"
               returnType="void">
      <parameter name="name"
                 description="Name of the new parameter"
                 type="java.lang.String"/>
      <parameter name="value"
                 description="Value of the new  parameter"
                 type="java.lang.String"/>
    </operation>

    <operation name="addRoleMapping"
               description="Add a security role reference for this web application."
               impact="ACTION"
               returnType="void">
      <parameter name="role"
                 description="Security role used in the application"
                 type="java.lang.String"/>
      <parameter name="link"
                 description="Actual security role to check for"
                 type="java.lang.String"/>
    </operation>

    <operation name="addSecurityRole"
               description="Add a new security role for this web application."
               impact="ACTION"
               returnType="void">
      <parameter name="role"
                 description="New security role"
                 type="java.lang.String"/>
    </operation>

    <operation name="addServletMapping"
               description="Add a new servlet mapping, replacing any existing mapping for the specified pattern."
               impact="ACTION"
               returnType="void">
      <parameter name="pattern"
                 description="URL pattern to be mapped"
                 type="java.lang.String"/>
      <parameter name="name"
                 description="Name of the corresponding servlet to execute"
                 type="java.lang.String"/>
    </operation>

    <operation name="addServletMapping"
               description="Add a new servlet mapping, replacing any existing mapping for the specified pattern."
               impact="ACTION"
               returnType="void">
      <parameter name="pattern"
                 description="URL pattern to be mapped"
                 type="java.lang.String"/>
      <parameter name="name"
                 description="Name of the corresponding servlet to execute"
                 type="java.lang.String"/>
      <parameter name="jspWildcard"
                 description="'true' if name identifies the JspServlet and pattern contains a wildcard; 'false' otherwise"
                 type="boolean"/>
    </operation>

    <operation name="addValve"
               description="Add a valve to this Context"
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="valveType"
                 description="Type(classname) of the new valve to be added"
                 type="java.lang.String"/>
    </operation>

    <operation name="addWatchedResource"
               description=" Add a resource which will be watched for reloading by the host auto deployer."
               impact="ACTION"
               returnType="void">
      <parameter name="name"
                 description="Path to the resource, either absolute or relative to docBase"
                 type="java.lang.String"/>
    </operation>

    <operation name="addWelcomeFile"
               description="Add a new welcome file to the set recognized by this Context."
               impact="ACTION"
               returnType="void">
      <parameter name="name"
                 description="New welcome file name"
                 type="java.lang.String"/>
    </operation>

    <operation name="addWrapperLifecycle"
               description="Add the classname of a LifecycleListener to be added to each Wrapper appended to this Context."
               impact="ACTION"
               returnType="void">
      <parameter name="listener"
                 description="Java class name of a LifecycleListener class"
                 type="java.lang.String"/>
    </operation>

    <operation name="addWrapperListener"
               description="Add the classname of a ContainerListener to be added to each Wrapper appended to this Context."
               impact="ACTION"
               returnType="void">
      <parameter name="listener"
                 description="Java class name of a ContainerListener class"
                 type="java.lang.String"/>
    </operation>

    <operation name="destroy"
               description="Destroy the context"
               impact="ACTION"
               returnType="void">
    </operation>

    <operation name="findApplicationListeners"
               description="Return the set of application listener class names configured for this application."
               impact="INFO"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findApplicationParameters"
               description="Return the set of application parameters for this application."
               impact="INFO"
               returnType="java.lang.String">
    </operation>

    <operation name="findConstraints"
               description="Return the set of security constraints for this web application. If there are none, a zero-length array is returned."
               impact="INFO"
               returnType="java.lang.String">
    </operation>

    <operation name="findContainerListenerNames"
               description="Return the set of container listener class names configured for this application."
               impact="INFO"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findErrorPage"
               description="Return the error page entry for the specified HTTP error code, if any; otherwise return null"
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="errorCode"
                 description="Error code to look up"
                 type="int"/>
    </operation>

    <operation name="findErrorPage"
               description="Return the error page entry for the specified Java exception type, if any; otherwise return null."
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="exceptionType"
                 description="Exception type to look up"
                 type="java.lang.String"/>
    </operation>

    <operation name="findErrorPages"
               description="Return the set of defined error pages for all specified error codes and exception types."
               impact="INFO"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findFilterDef"
               description="Return the filter definition for the specified filter name, if any; otherwise return null."
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="exceptionType"
                 description="Exception type to look up"
                 type="java.lang.String"/>
    </operation>

    <operation name="findFilterDefs"
               description="Return the set of defined filters for this Context."
               impact="INFO"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findFilterMaps"
               description="Return the set of filter mappings for this Context."
               impact="INFO"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findLifecycleListenerNames"
               description="Return the set of lifecycle listener class names configured for this application."
               impact="INFO"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findMimeMapping"
               description="Return the MIME type to which the specified extension is mapped, if any; otherwise return null."
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="extension"
                 description="Extension to map to a MIME type"
                 type="java.lang.String"/>
    </operation>

    <operation name="findMimeMappings"
               description="Return the extensions for which MIME mappings are defined."
               impact="ACTION"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findParameter"
               description="Return the value for the specified context initialization parameter name, if any; otherwise return null."
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="name"
                 description="Name of the parameter to return"
                 type="java.lang.String"/>
    </operation>

    <operation name="findParameters"
               description="Return the names of all defined context initialization parameters for this Context."
               impact="ACTION"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findRoleMapping"
               description="For the given security role (as used by an application), return the corresponding role name (as defined by the underlying Realm) if there is one.  Otherwise, return the specified role unchanged."
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="role"
                 description="Security role to map"
                 type="java.lang.String"/>
    </operation>

    <operation name="findSecurityRole"
               description="Return 'true' if the specified security role is defined for this application; otherwise return 'false'."
               impact="ACTION"
               returnType="boolean">
      <parameter name="role"
                 description="Security role to verify"
                 type="java.lang.String"/>
    </operation>

    <operation name="findSecurityRoles"
               description="Return the security roles defined for this application."
               impact="ACTION"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findServletMapping"
               description="Return the servlet name mapped by the specified pattern.."
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="pattern"
                 description="Pattern for which a mapping is requested"
                 type="java.lang.String"/>
    </operation>

    <operation name="findServletMappings"
               description="Return the patterns of all defined servlet mappings for this Context."
               impact="ACTION"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findStatusPage"
               description="Return the context-relative URI of the error page for the specified HTTP status code."
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="status"
                 description="HTTP status code to look up"
                 type="int"/>
    </operation>

    <operation name="findStatusPages"
               description="Return the set of HTTP status codes for which error pages have been specified."
               impact="ACTION"
               returnType="[Lint">
    </operation>

    <operation name="findWatchedResources"
               description="Return the set of watched resources for this Context."
               impact="ACTION"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findWelcomeFile"
               description="Return 'true' if the specified welcome file is defined for this Context; otherwise return 'false'."
               impact="ACTION"
               returnType="boolean">
      <parameter name="name"
                 description="Welcome file to verify"
                 type="java.lang.String"/>
    </operation>

    <operation name="findWelcomeFiles"
               description="Return the set of welcome files defined for this Context."
               impact="ACTION"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findWrapperLifecycles"
               description="Return the set of LifecycleListener classes that will be added to newly created Wrappers automatically."
               impact="ACTION"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findWrapperListeners"
               description="Return the set of ContainerListener classes that will be added to newly created Wrappers automatically."
               impact="ACTION"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="init"
               description="Register the context into the running server"
               impact="ACTION"
               returnType="void">
    </operation>

    <operation name="start"
               description="Start the context"
               impact="ACTION"
               returnType="void">
    </operation>

    <operation name="stop"
               description="Stop the context"
               impact="ACTION"
               returnType="void">
    </operation>

    <operation name="reload"
               description="Reload the web application"
               impact="ACTION"
               returnType="void">
    </operation>

    <operation name="removeApplicationListener"
               description="Remove the specified application listener class from the set of listeners for this application."
               impact="ACTION"
               returnType="void">
      <parameter name="listener"
                 description="Java class name of the listener to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeApplicationParameter"
               description="Remove the application parameter with the specified name from the set for this application."
               impact="ACTION"
               returnType="void">
      <parameter name="name"
                 description="Name of the application parameter to remove"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeChild"
               description="Remove a child from this Context"
               impact="ACTION"
               returnType="void">
      <parameter name="name"
                 description="Name of the existing child Container to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeLifecycleListeners"
               description="Removes lifecycle listeners of given class type from this Context"
               impact="ACTION"
               returnType="void">
      <parameter name="listener"
                 description="Type(classname) of the lifecycle listeners to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeMimeMapping"
               description="Remove the MIME mapping for the specified extension, if it exists; otherwise, no action is taken.."
               impact="ACTION"
               returnType="void">
      <parameter name="extension"
                 description="Extension to remove the mapping for"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeParameter"
               description="Remove the context initialization parameter with the specified name, if it exists; otherwise, no action is taken."
               impact="ACTION"
               returnType="void">
      <parameter name="name"
                 description="Name of the parameter to remove"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeRoleMapping"
               description="Remove any security role reference for the specified name"
               impact="ACTION"
               returnType="void">
      <parameter name="role"
                 description="Security role (as used in the application) to remove"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeSecurityRole"
               description="Remove any security role with the specified name."
               impact="ACTION"
               returnType="void">
      <parameter name="role"
                 description="Security role to remove"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeServletMapping"
               description="Remove any servlet mapping for the specified pattern, if it exists; otherwise, no action is taken."
               impact="ACTION"
               returnType="void">
      <parameter name="pattern"
                 description="URL pattern of the mapping to remove"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeValve"
               description="Remove a valve from this Context"
               impact="ACTION"
               returnType="void">
      <parameter name="valveName"
                 description="Objectname of the valve to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeWatchedResource"
               description="Remove the specified watched resource name from the list associated with this Context."
               impact="ACTION"
               returnType="void">
      <parameter name="name"
                 description="Name of the watched resource to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeWelcomeFile"
               description="Remove the specified welcome file name from the list recognized by this Context."
               impact="ACTION"
               returnType="void">
      <parameter name="name"
                 description="Name of the welcome file to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeWrapperLifecycle"
               description="Remove a class name from the set of LifecycleListener classes that will be added to newly created Wrappers."
               impact="ACTION"
               returnType="void">
      <parameter name="listener"
                 description="Class name of a LifecycleListener class to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeWrapperListener"
               description="Remove a class name from the set of ContainerListener classes that will be added to newly created Wrappers."
               impact="ACTION"
               returnType="void">
      <parameter name="listener"
                 description="Class name of a ContainerListener class to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="start"
               description="Start"
               impact="ACTION"
               returnType="void" />

    <operation name="stop"
               description="Stop"
               impact="ACTION"
               returnType="void" />

  </mbean>

  <mbean name="StandardContextValve"
         description="Valve that implements the default basic behavior for the
         StandardContext container implementation"
         domain="Catalina"
         group="Valve"
         type="org.apache.catalina.core.StandardContextValve">

    <attribute name="asyncSupported"
               description="Does this valve support async reporting?"
               is="true"
               type="boolean"/>

    <attribute name="className"
               description="Fully qualified class name of the managed object"
               type="java.lang.String"
               writeable="false"/>

    <attribute name="stateName"
               description="The name of the LifecycleState that this component is currently in"
               type="java.lang.String"
               writeable="false"/>
  </mbean>

  <mbean name="StandardEngine"
         type="org.apache.catalina.core.StandardEngine"
         description="Standard Engine Component"
         domain="Catalina"
         group="Engine"
         className="org.apache.catalina.mbeans.ContainerMBean">

    <attribute name="backgroundProcessorDelay"
               description="The processor delay for this component."
               type="int"/>

    <attribute name="catalinaBase"
               description="Base (instance) directory for this Engine, typically same as catalina.base system property"
               type="java.lang.String"/>

    <attribute name="defaultHost"
               description="Name of the default Host for this Engine"
               type="java.lang.String"/>

    <attribute name="jvmRoute"
               description="Route used for load balancing"
               type="java.lang.String"/>

    <attribute name="managedResource"
               description="The managed resource this MBean is associated with"
               type="java.lang.Object"/>

    <attribute name="name"
               description="Unique name of this Engine"
               type="java.lang.String"/>

    <attribute name="realm"
               description="Associated realm."
               type="org.apache.catalina.Realm" />

    <attribute name="startChildren"
               description="Will children be started automatically when they are added."
               type="boolean"/>

    <attribute name="startStopThreads"
               description="The number of threads to use when starting and stopping child Hosts"
               type="int"/>

    <attribute name="stateName"
               description="The name of the LifecycleState that this component is currently in"
               type="java.lang.String"
               writeable="false"/>

    <operation name="addChild"
               description="Add a virtual host"
               impact="ACTION"
               returnType="void">
      <parameter name="type"
                 description="Type(classname) of the new child to be added"
                 type="java.lang.String"/>
      <parameter name="name"
                 description="Name of the child to be added"
                 type="java.lang.String"/>
    </operation>

    <operation name="addLifecycleListener"
               description="Add a lifecycle listener to this Engine"
               impact="ACTION"
               returnType="void">
      <parameter name="type"
                 description="Type(classname) of the new lifecycle listener to be added"
                 type="java.lang.String"/>
    </operation>

    <operation name="addValve"
               description="Add a valve to this Engine"
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="valveType"
                 description="Type(classname) of the new valve to be added"
                 type="java.lang.String"/>
    </operation>

    <operation name="destroy"
               description="Destroy"
               impact="ACTION"
               returnType="void" />

    <operation name="init"
               description="Init"
               impact="ACTION"
               returnType="void" />

    <operation name="removeChild"
               description="Remove a child(Host) from this Engine"
               impact="ACTION"
               returnType="void">
      <parameter name="name"
                 description="Name of the existing child Container to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeLifecycleListeners"
               description="Removes lifecycle listeners of given class type from this Engine"
               impact="ACTION"
               returnType="void">
      <parameter name="listener"
                 description="Type(classname) of the lifecycle listeners to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeValve"
               description="Remove a valve from this Engine"
               impact="ACTION"
               returnType="void">
      <parameter name="valveName"
                 description="Objectname of the valve to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="start"
               description="Start"
               impact="ACTION"
               returnType="void" />

    <operation name="stop"
               description="Stop"
               impact="ACTION"
               returnType="void" />

  </mbean>


  <mbean name="StandardEngineValve"
         description="Valve that implements the default basic behavior for the
         StandardEngine container implementation"
         domain="Catalina"
         group="Valve"
         type="org.apache.catalina.core.StandardEngineValve">

    <attribute name="asyncSupported"
               description="Does this valve support async reporting?"
               is="true"
               type="boolean"/>

    <attribute name="className"
               description="Fully qualified class name of the managed object"
               type="java.lang.String"
               writeable="false"/>

    <attribute name="stateName"
               description="The name of the LifecycleState that this component is currently in"
               type="java.lang.String"
               writeable="false"/>
  </mbean>

  <mbean name="StandardHost"
         description="Standard Host Component"
         domain="Catalina"
         group="Host"
         type="org.apache.catalina.core.StandardHost"
         className="org.apache.catalina.mbeans.ContainerMBean">

    <attribute name="aliases"
               description="Host aliases"
               type="[Ljava.lang.String;"/>

    <attribute name="appBase"
               description="The application root for this Host"
               type="java.lang.String"/>

    <attribute name="autoDeploy"
               description="The auto deploy flag for this Host"
               type="boolean"/>

    <attribute name="backgroundProcessorDelay"
               description="The processor delay for this component."
               type="int"/>

    <attribute name="children"
               description="Object names of all children"
               type="[Ljavax.management.ObjectName;"/>

    <attribute name="configClass"
               description="The configuration class for contexts"
               type="java.lang.String"/>

    <attribute name="contextClass"
               description="The Java class name of the default Context implementation class for deployed web applications."
               type="java.lang.String"
               writeable="false" />

    <attribute name="copyXML"
               description="Should XML files be copied to $CATALINA_BASE/conf/{engine}/{host} by default when a web application is deployed?"
               is="true"
               type="boolean"/>

    <attribute name="createDirs"
               description="Should we create directories upon startup for appBase and xmlBase? "
               type="boolean"/>

    <attribute name="deployIgnore"
               description="Paths within appBase ignored for automatic deployment"
               type="java.lang.String"/>

    <attribute name="deployOnStartup"
               description="The deploy on startup flag for this Host"
               type="boolean"/>

    <attribute name="deployXML"
               description="deploy Context XML config files property"
               is="true"
               type="boolean"/>

    <attribute name="errorReportValveClass"
               description="The Java class name of the default error reporter implementation class for deployed web applications."
               type="java.lang.String"
               writeable="false" />

    <attribute name="managedResource"
               description="The managed resource this MBean is associated with"
               type="java.lang.Object"/>

    <attribute name="name"
               description="Unique name of this Host"
               type="java.lang.String"/>

    <attribute name="realm"
               description="Associated realm."
               type="org.apache.catalina.Realm" />

    <attribute name="startChildren"
               description="Will children be started automatically when they are added?"
               type="boolean"/>

    <attribute name="startStopThreads"
               description="The number of threads to use when starting, stopping and deploying child Contexts"
               type="int"/>

    <attribute name="stateName"
               description="The name of the LifecycleState that this component is currently in"
               type="java.lang.String"
               writeable="false"/>

    <attribute name="undeployOldVersions"
               description="Determines if old versions of applications deployed using parallel deployment are automatically undeployed when no longer used. Requires autoDeploy to be enabled."
               type="boolean"/>

    <attribute name="unpackWARs"
               description="Unpack WARs property"
               is="true"
               type="boolean"/>

    <attribute name="valveNames"
               description="Return the MBean Names of the Valves associated with this Host"
               type="[Ljava.lang.String;"/>

    <attribute name="workDir"
               description="Work Directory base for applications"
               type="java.lang.String"/>

    <attribute name="xmlBase"
               description="The XML root for this Host."
               type="java.lang.String"/>

    <operation name="addAlias"
               description="Add an alias name that should be mapped to this Host"
               impact="ACTION"
               returnType="void">
      <parameter name="alias"
                 description="The alias to be added"
                 type="java.lang.String"/>
    </operation>

    <operation name="addChild"
               description="Add a child(Context) to this Host"
               impact="ACTION"
               returnType="void">
      <parameter name="type"
                 description="Type(classname) of the new child to be added"
                 type="java.lang.String"/>
      <parameter name="name"
                 description="Name of the child to be added"
                 type="java.lang.String"/>
    </operation>

    <operation name="addLifecycleListener"
               description="Add a lifecycle listener to this Host"
               impact="ACTION"
               returnType="void">
      <parameter name="type"
                 description="Type(classname) of the new lifecycle listener to be added"
                 type="java.lang.String"/>
    </operation>

    <operation name="addValve"
               description="Add a valve to this Host"
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="valveType"
                 description="Type(classname) of the new valve to be added"
                 type="java.lang.String"/>
    </operation>

    <operation name="destroy"
               description="Destroy"
               impact="ACTION"
               returnType="void" />

    <operation name="findAliases"
               description="Return the set of alias names for this Host"
               impact="INFO"
               returnType="[Ljava.lang.String;"/>

    <operation name="findReloadedContextMemoryLeaks"
               description="Provide a list of contexts that have leaked memory on reload. This will attempt to force a full garbage collection. Use with extreme caution on production systems."
               impact="ACTION"
               returnType="[Ljava.lang.String;" />

    <operation name="init"
               description="Init"
               impact="ACTION"
               returnType="void" />

    <operation name="removeAlias"
               description="Remove the specified alias name from the aliases for this  Host"
               impact="ACTION"
               returnType="void">
      <parameter name="alias"
                 description="Alias name to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeChild"
               description="Remove a child(Context) from this Host"
               impact="ACTION"
               returnType="void">
      <parameter name="name"
                 description="Name of the existing child Container to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeLifecycleListeners"
               description="Removes lifecycle listeners of given class type from this Host"
               impact="ACTION"
               returnType="void">
      <parameter name="listener"
                 description="Type(classname) of the lifecycle listeners to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeValve"
               description="Remove a valve from this Host"
               impact="ACTION"
               returnType="void">
      <parameter name="valveName"
                 description="Objectname of the valve to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="start"
               description="Start"
               impact="ACTION"
               returnType="void" />

    <operation name="stop"
               description="Stop"
               impact="ACTION"
               returnType="void" />

  </mbean>

  <mbean name="StandardHostValve"
         description="Valve that implements the default basic behavior for the
         StandardHost container implementation"
         domain="Catalina"
         group="Valve"
         type="org.apache.catalina.core.StandardHostValve">

    <attribute name="asyncSupported"
               description="Does this valve support async reporting?"
               is="true"
               type="boolean"/>

    <attribute name="className"
               description="Fully qualified class name of the managed object"
               type="java.lang.String"
               writeable="false"/>

    <attribute name="stateName"
               description="The name of the LifecycleState that this component is currently in"
               type="java.lang.String"
               writeable="false"/>
  </mbean>

  <mbean name="StandardServer"
         description="Standard Server Component"
         domain="Catalina"
         group="Server"
         type="org.apache.catalina.core.StandardServer">

    <attribute name="address"
               description="The address on which we wait for shutdown commands."
               type="java.lang.String"/>

    <attribute name="managedResource"
               description="The managed resource this MBean is associated with"
               type="java.lang.Object"/>

    <attribute name="port"
               description="TCP port for shutdown messages"
               type="int"/>

    <attribute name="serverInfo"
               description="Tomcat server release identifier"
               type="java.lang.String"
               writeable="false"/>

    <attribute name="serverBuilt"
               description="Tomcat server built timestamp"
               type="java.lang.String"
               writeable="false"/>

    <attribute name="serverNumber"
               description="Tomcat server's version number"
               type="java.lang.String"
               writeable="false"/>

    <attribute name="serviceNames"
               description="Object names of all services we know about"
               type="[Ljavax.management.ObjectName;"
               writeable="false" />

    <attribute name="shutdown"
               description="Shutdown password"
               type="java.lang.String"/>

    <attribute name="stateName"
               description="The name of the LifecycleState that this component is currently in"
               type="java.lang.String"
               writeable="false"/>

    <operation name="await"
               description="Wait for the shutdown message"
               impact="ACTION"
               returnType="void" />

    <operation name="storeConfig"
               description="Save current state to server.xml file"
               impact="ACTION"
               returnType="void">

    </operation>

  </mbean>


  <mbean name="StandardService"
         description="Standard Service Component"
         domain="Catalina"
         group="Service"
         type="org.apache.catalina.core.StandardService"
         className="org.apache.catalina.mbeans.ServiceMBean">

    <attribute name="connectorNames"
               description="ObjectNames of the connectors"
               type="[Ljavax.management.ObjectName;"
               writeable="false" />

    <attribute name="managedResource"
               description="The managed resource this MBean is associated with"
               type="java.lang.Object"/>

    <attribute name="name"
               description="Unique name of this Service"
               type="java.lang.String"/>

    <attribute name="stateName"
               description="The name of the LifecycleState that this component is currently in"
               type="java.lang.String"
               writeable="false"/>

    <operation name="addConnector"
               description="Add a new connector"
               impact="ACTION"
               returnType="void">
      <parameter name="address"
                 description="The IP address on which to bind"
                 type="java.lang.String"/>
      <parameter name="port"
                 description="TCP port number to listen on"
                 type="int"/>
      <parameter name="isAjp"
                 description="Create a AJP/1.3 Connector"
                 type="boolean"/>
      <parameter name="isSSL"
                 description="Create a secure Connector"
                 type="boolean"/>
    </operation>

    <operation name="addExecutor"
               description="Adds a named executor to the service"
               impact="ACTION"
               returnType="void">
      <parameter name="type"
                 description="Classname of the Executor to be added"
                 type="java.lang.String"/>
    </operation>

    <operation name="findConnectors"
               description="Find and return the set of Connectors associated with this Service"
               impact="ACTION"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findExecutors"
               description="Retrieves all executors"
               impact="ACTION"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="getExecutor"
               description="Retrieves executor by name"
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="name"
                 description="Name of the executor to be retrieved"
                 type="java.lang.String"/>
    </operation>

    <operation name="start"
               description="Start"
               impact="ACTION"
               returnType="void" />

    <operation name="stop"
               description="Stop"
               impact="ACTION"
               returnType="void" />
  </mbean>

  <mbean name="StandardThreadExecutor"
         description="Standard implementation of a thread pool"
         domain="Catalina"
         group="Executor"
         type="org.apache.catalina.core.StandardThreadExecutor">

    <attribute name="activeCount"
               description="Number of threads currently processing a task"
               type="int"
               writeable="false" />

    <attribute name="completedTaskCount"
               description="Number of tasks completed by the executor"
               type="int"
               writeable="false" />

    <attribute name="corePoolSize"
               description="Core size of the thread pool"
               type="int"
               writeable="false" />

    <attribute name="daemon"
               description="Run threads in daemon or non-daemon state?"
               is="true"
               type="boolean"/>

    <attribute name="largestPoolSize"
               description="Peak number of threads"
               type="int"
               writeable="false" />

    <attribute name="maxIdleTime"
               description="Max number of milliseconds a thread can be idle before it can be shutdown"
               type="int"/>

    <attribute name="maxQueueSize"
               description="Maximum number of tasks for the pending task queue"
               type="int"/>

    <attribute name="maxThreads"
               description="Maximum number of allocated threads"
               type="int"/>

    <attribute name="minSpareThreads"
               description="Minimum number of allocated threads"
               type="int"/>

    <attribute name="name"
               description="Unique name of this Executor"
               type="java.lang.String"/>

    <attribute name="namePrefix"
               description="Name prefix for thread names created by this executor"
               type="java.lang.String"/>

    <attribute name="poolSize"
               description="Number of threads in the pool"
               type="int"
               writeable="false" />

    <attribute name="prestartminSpareThreads"
               description="Prestart threads?"
               is="true"
               type="boolean"/>

    <attribute name="queueSize"
               description="Number of tasks waiting to be processed"
               type="int"
          writeable="false" />

    <attribute name="stateName"
               description="The name of the LifecycleState that this component is currently in"
               type="java.lang.String"
               writeable="false"/>

    <attribute name="threadPriority"
               description="The thread priority for threads in this thread pool"
               type="int"/>

    <attribute name="threadRenewalDelay"
               description="After a context is stopped, threads in the pool are renewed. To avoid renewing all threads at the same time, this delay is observed between 2 threads being renewed. Value is in ms, default value is 1000ms. If negative, threads are not renewed."
               type="long"/>

  </mbean>

  <mbean name="StandardWrapper"
         description="Wrapper that represents an individual servlet definition"
         domain="Catalina"
         group="Wrapper"
         type="org.apache.catalina.core.StandardWrapper"
         className="org.apache.catalina.mbeans.ContainerMBean">

    <attribute name="asyncSupported"
               description="Async support"
               is="true"
               type="boolean"/>

    <attribute name="available"
               description="The date and time at which this servlet will become available (in milliseconds since the epoch), or zero if the servlet is available. If this value equals Long.MAX_VALUE, the unavailability of this servlet is considered permanent."
               type="long"/>

    <attribute name="backgroundProcessorDelay"
               description="The processor delay for this component."
               type="int" />

    <attribute name="classLoadTime"
               description="Time taken to load the Servlet class"
               type="int"
               writeable="false" />

    <attribute name="countAllocated"
               description="The count of allocations that are currently active (even if they  are for the same instance, as will be true on a non-STM servlet)."
               type="int"
               writeable="false" />

    <attribute name="errorCount"
               description="Error count"
               type="int"
               writeable="false" />

    <attribute name="loadOnStartup"
               description="The load-on-startup order value (negative value means load on first call) for this servlet."
               type="int"/>

    <attribute name="loadTime"
               description="Time taken to load and initialise the Servlet"
               type="long"
               writeable="false" />

    <attribute name="maxTime"
               description="Maximum processing time of a request"
               type="long"
               writeable="false" />

    <attribute name="maxInstances"
               description="Maximum number of STM instances."
               type="int" />

    <attribute name="minTime"
               description="Minimum processing time of a request"
               type="long"
               writeable="false" />

    <attribute name="objectName"
               description="Name of the object"
               type="java.lang.String"/>

    <attribute name="processingTime"
               description="Total execution time of the servlet's service method"
               type="long"
               writeable="false" />

    <attribute name="requestCount"
               description="Number of requests processed by this wrapper"
               type="int"
               writeable="false" />

    <attribute name="runAs"
               description="The run-as identity for this servlet."
               type="java.lang.String"/>

    <attribute name="servletClass"
               description="The run-as identity for this servlet."
               type="java.lang.String"
               writeable="false" />

    <attribute name="singleThreadModel"
               description="Does this servlet implement the SingleThreadModel interface?"
               type="java.lang.Boolean"
               is="true"
               writeable="false" />

    <attribute name="stateName"
               description="The name of the LifecycleState that this component is currently in"
               type="java.lang.String"
               writeable="false"/>

    <operation name="addInitParameter"
               description="Add a valve to this Wrapper"
               impact="ACTION"
               returnType="void">
      <parameter name="name"
                 description="Name of this initialization parameter to add"
                 type="java.lang.String"/>
      <parameter name="value"
                 description="Value of this initialization parameter to add"
                 type="java.lang.String"/>
    </operation>

    <operation name="addLifecycleListener"
               description="Add a lifecycle listener to this Wrapper"
               impact="ACTION"
               returnType="void">
      <parameter name="type"
                 description="Type(classname) of the new lifecycle listener to be added"
                 type="java.lang.String"/>
    </operation>

    <operation name="addMapping"
               description="Add a mapping associated with the Wrapper."
               impact="ACTION"
               returnType="void">
      <parameter name="mapping"
                 description="The new wrapper mapping"
                 type="java.lang.String"/>
    </operation>

    <operation name="addSecurityReference"
               description="Add a new security role reference record to the set of records for this servlet."
               impact="ACTION"
               returnType="void">
      <parameter name="name"
                 description="Role name used within this servlet"
                 type="java.lang.String"/>
      <parameter name="link"
                 description="Role name used within the web application"
                 type="java.lang.String"/>
    </operation>

    <operation name="addValve"
               description="Add a valve to this Wrapper"
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="valveType"
                 description="Type(classname) of the new valve to be added"
                 type="java.lang.String"/>
    </operation>

    <operation name="findInitParameter"
               description="Return the value of an initialization parameter"
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="name"
                 description="The name of the initialization parameter"
                 type="java.lang.String"/>
    </operation>

    <operation name="findInitParameters"
               description="Return the names of all defined initialization parameters for this servlet."
               impact="INFO"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findMappings"
               description="Return the mappings associated with this wrapper"
               impact="INFO"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="findMappingObject"
               description="Return an object which may be utilized for mapping to this component"
               impact="INFO"
               returnType="org.apache.catalina.Wrapper">
    </operation>

    <operation name="findSecurityReference"
               description="Return the security role link for the specified security role reference name."
               impact="ACTION"
               returnType="java.lang.String">
      <parameter name="name"
                 description="Security role reference used within this servlet"
                 type="java.lang.String"/>
    </operation>

    <operation name="findSecurityReferences"
               description="Return the set of security role reference names associated with this servlet"
               impact="INFO"
               returnType="[Ljava.lang.String;">
    </operation>

    <operation name="removeInitParameter"
               description="Remove the specified initialization parameter from this servlet."
               impact="ACTION"
               returnType="void">
      <parameter name="name"
                 description="Name of the initialization parameter to remove"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeLifecycleListeners"
               description="Removes lifecycle listeners of given class type from this Wrapper"
               impact="ACTION"
               returnType="void">
      <parameter name="listener"
                 description="Type(classname) of the lifecycle listeners to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeMapping"
               description="Remove a mapping associated with the wrapper."
               impact="ACTION"
               returnType="void">
      <parameter name="mapping"
                 description="The pattern to remove"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeSecurityReference"
               description="Remove any security role reference for the specified role name."
               impact="ACTION"
               returnType="void">
      <parameter name="name"
                 description="Security role used within this servlet to be removed"
                 type="java.lang.String"/>
    </operation>

    <operation name="removeValve"
               description="Remove a valve from this Wrapper"
               impact="ACTION"
               returnType="void">
      <parameter name="valveName"
                 description="Objectname of the valve to be removed"
                 type="java.lang.String"/>
    </operation>

  </mbean>

  <mbean name="StandardWrapperValve"
         description="Valve that implements the default basic behavior for the StandardWrapper container implementation"
         domain="Catalina"
         group="Valve"
         type="org.apache.catalina.core.StandardWrapperValve">

     <attribute name="asyncSupported"
                description="Does this valve support async reporting?"
                type="boolean"/>

     <attribute name="className"
                description="Fully qualified class name of the managed object"
                type="java.lang.String"
                writeable="false"/>

    <attribute name="stateName"
               description="The name of the LifecycleState that this component is currently in"
               type="java.lang.String"
               writeable="false"/>
  </mbean>

</mbeans-descriptors>
