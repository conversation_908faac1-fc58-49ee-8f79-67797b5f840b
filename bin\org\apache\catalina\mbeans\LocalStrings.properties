# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jmxRemoteLifecycleListener.createRegistryFailed=Unable to create the RMI registry for the [{0}] server using port [{1}]
jmxRemoteLifecycleListener.createServerFailed=The JMX connector server could not be created or failed to start for the [{0}] server
jmxRemoteLifecycleListener.deprecated=The JmxRemoteLifecycleListener is deprecated as as the features it provides are now available in the remote JMX capability included with the JRE. This listener will be removed in Tomcat 10 and may be removed from Tomcat 8 some time after 2020-12-31.
jmxRemoteLifecycleListener.destroyServerFailed=The JMX connector server could not be stopped for the [{0}] server
jmxRemoteLifecycleListener.invalidRmiBindAddress=Invalid RMI bind address [{0}]
jmxRemoteLifecycleListener.invalidURL=The JMX Service URL requested for the [{0}] server, [{1}], was invalid
jmxRemoteLifecycleListener.start=The JMX Remote Listener has configured the registry on port [{0}] and the server on port [{1}] for the [{2}] server

mBeanFactory.contextCreate.addServicedFail=Unable to create context [{0}] as another component is currently servicing a context with that name
mBeanFactory.contextRemove.addServicedFail=Unable to remove context [{0}] as another component is currently servicing that context
mBeanFactory.managerContext=Manager components may only be added to Contexts.
