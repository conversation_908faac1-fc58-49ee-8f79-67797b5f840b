# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

engine.ciphersFailure=Fehler beim abfragen der Cipher Liste
engine.emptyCipherSuite=leere Cipher-Suite
engine.inboundClose=Die eingehende Verbindung wurde vor einer close_notify Nachricht der Gegenstelle geschlossen
engine.noSession=SSL Session-ID nicht vorhanden
engine.openSSLError=OpenSSL Fehler: [{0}] Nachricht: [{1}]
engine.unsupportedProtocol=Protokoll [{0}] ist nicht unterstützt

openssl.addedClientCaCert=Client CA Zertifikat hinzugefügt: [{0}]
openssl.certificateVerificationFailed=Zertifikatsprüfung fehlgeschlagen
openssl.errApplyConf=Die OpenSSLConf konnte nicht auf den SSL Context angewandt werden
openssl.errCheckConf=Fehler beim Prüfen der OpenSSLConf
openssl.errMakeConf=Der OpenSSLConf Context konnte nicht erzeugt werden
openssl.errorSSLCtxInit=Fehler beim initialisieren des SSL Contexts
openssl.keyManagerMissing=Kein Key-Manager gefunden
openssl.trustManagerMissing=Kein Trust-Manager gefunden

opensslconf.applyFailed=Fehler bei der Anwendung der OpenSSLConf auf den SSL Context
opensslconf.checkFailed=Fehler beim Prüfen der OpenSSLConf
