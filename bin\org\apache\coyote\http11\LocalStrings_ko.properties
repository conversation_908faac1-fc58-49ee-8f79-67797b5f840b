# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractHttp11Protocol.alpnConfigured=[{0}] connector는 ALPN을 통해 [{1}](으)로 negotiation을 지원하도록 설정되었습니다.
abstractHttp11Protocol.alpnWithNoAlpn=[{1}]을(를) 위한 업그레이드 핸들러 [{0}]은(는) 오직 ALPN을 통한 업그레이드만 지원합니다만, 해당 업그레이드 핸들러가 ALPN을 지원하지 않는 [{2}] Connector를 위해 설정되어 있습니다.
abstractHttp11Protocol.httpUpgradeConfigured=[{0}] connector가, [{1}](으)로 HTTP 업그레이드를 지원하도록 설정되어 있습니다.
abstractHttp11Protocol.upgradeJmxNameFail=업그레이드 프로토콜을 JMX에 등록하기 위해 사용할 ObjectName을 생성하지 못했습니다.
abstractHttp11Protocol.upgradeJmxRegistrationFail=업그레이드 프로토콜을 JMX에 등록하지 못했습니다.

http11processor.fallToDebug=\n\
\ 비고: HTTP 요청 파싱 오류들이 더 발생하는 경우 DEBUG 레벨 로그로 기록될 것입니다.
http11processor.header.parse=HTTP 요청 헤더를 파싱하는 중 오류 발생
http11processor.request.finish=요청을 완료하는 중 오류 발생
http11processor.request.inconsistentHosts=요청 행(request line)에 지정된 호스트가, 호스트 헤더와 일관되지 않습니다.
http11processor.request.invalidScheme=HTTP 요청이 유효하지 않은 스킴을 가진 절대 URI를 포함했습니다.
http11processor.request.invalidTransferEncoding=HTTP 요청이 유효하지 않은 Transfer-Encoding header를 포함합니다
http11processor.request.invalidUri==HTTP 요청이 유효하지 않은 URI를 포함했습니다.
http11processor.request.invalidUserInfo=HTTP 요청이, 유효하지 않은 userinfo를 가진 절대 URI를 포함했습니다.
http11processor.request.multipleContentLength=해당 요청이 복수 개의 Content-Length 헤더들을 포함했습니다.
http11processor.request.multipleHosts=요청이 여러 개의 호스트 헤더들을 포함했습니다.
http11processor.request.noHostHeader=HTTP/1.1 요청이 호스트 헤더를 제공하지 않았습니다.
http11processor.request.nonNumericContentLength=해당 요청이 숫자가 아닌 Content-Length 헤더 값을 포함했습니다.
http11processor.request.prepare=요청을 준비하는 중 오류 발생
http11processor.request.process=요청 처리 중 오류 발생
http11processor.response.finish=응답을 완료하는 중 오류 발생
http11processor.sendfile.error=sendfile을 사용하여 데이터를 보내는 중 오류 발생. 시작 지점과 종료 지점을 위한 요청 속성들이 유효하지 않아 발생했을 수 있습니다.
http11processor.socket.info=소켓에 대한 정보를 얻는 중 예외 발생

iib.available.readFail=데이터가 가용한지 결정하려 시도하는 동안, non-blocking 읽기가 실패했습니다.
iib.eof.error=소켓에서 예기치 않은 EOF를 읽었습니다.
iib.failedread.apr=APR/native 오류 코드 [{0}]와(과) 함께, 읽기가 실패했습니다.
iib.filter.npe=널인 필터를 추가할 수 없습니다.
iib.invalidHttpProtocol=HTTP 프로토콜에서 유효하지 않은 문자가 발견되었습니다.
iib.invalidPhase=파싱 국면 [{0}]에서, 유효하지 않은 HTTP 요청 라인 오류
iib.invalidRequestTarget=요청 타겟에서 유효하지 않은 문자가 발견되었습니다. 유효한 문자들은 RFC 7230과 RFC 3986에 정의되어 있습니다.
iib.invalidheader=HTTP 헤더 행 [{0}]이(가) RFC 7230을 준수하지 않아, 무시되었습니다.
iib.invalidmethod=메소드 이름에 유효하지 않은 문자가 발견되었습니다. HTTP 메소드 이름은 유효한 토큰이어야 합니다.
iib.parseheaders.ise.error=예기치 않은 상태: 헤더들이 이미 파싱되었습니다. 버퍼가 참조 해제되지 않았었나요?
iib.readtimeout=소켓으로부터 데이터를 읽으려 시도하는 중 제한 시간 초과
iib.requestheadertoolarge.error=요청 헤더가 너무 큽니다.

iob.failedwrite=쓰기 실패
iob.failedwrite.ack=HTTP 100 continue 응답을 보내지 못했습니다.
iob.responseheadertoolarge.error=응답 헤더들에 가용한 버퍼 공간을 초과하는 데이터를 쓰려는 시도가 발생했습니다. 해당 Connector의 maxHttpHeaderSize를 증가시키거나, 응답 헤더들에 보다 적은 데이터를 쓰도록 하십시오.
