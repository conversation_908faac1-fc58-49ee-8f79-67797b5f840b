# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

diagnostics.threadDumpTitle=打印全部线程
diagnostics.vmInfoClassCompilation=class汇编
diagnostics.vmInfoClassLoading=类加载中
diagnostics.vmInfoGarbageCollectors=垃圾收集器[{0}]
diagnostics.vmInfoLogger=日志记录器（Logger）信息
diagnostics.vmInfoMemory=内存信息
diagnostics.vmInfoMemoryManagers=内存管理器[{0}]
diagnostics.vmInfoMemoryPools=内存池[{0}]
diagnostics.vmInfoOs=操作系统信息
diagnostics.vmInfoPath=路径信息
diagnostics.vmInfoRuntime=运行时信息
diagnostics.vmInfoStartup=启动参数
diagnostics.vmInfoSystem=系统.属性
diagnostics.vmInfoThreadCounts=线程数
diagnostics.vmInfoThreadMxBean=ThreadMXBean功能
