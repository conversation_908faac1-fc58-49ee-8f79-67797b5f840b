# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

applicationContext.addFilter.ise=컨텍스트가 이미 초기화되었기 때문에, 필터들은 컨텍스트 [{0}]에 추가될 수 없습니다.
applicationContext.addJspFile.iae=JSP 파일 [{0}]은(는) 유효하지 않습니다.
applicationContext.addListener.iae.cnfe=타입 [{0}]의 인스턴스를 생성할 수 없습니다.
applicationContext.addListener.iae.init=타입 [{0}]의 인스턴스를 리스너로서 추가할 수 없습니다.
applicationContext.addListener.iae.sclNotAllowed=첫번째 ServletContextListener가 호출되고 나면, 더 이상 ServletContextListener들을 추가할 수 없습니다.
applicationContext.addListener.iae.wrongType=지정된 타입 [{0}]이(가) 요구되는 리스너 타입들 중의 하나가 아닙니다.
applicationContext.addListener.ise=컨텍스트가 이미 초기화 되었기에, 컨텍스트 [{0}]에 리스너를 추가할 수 없습니다.
applicationContext.addRole.ise=해당 컨텍스트가 이미 초기화되어 있기에, 역할들이 컨텍스트 [{0}]에 추가될 수 없습니다.
applicationContext.addServlet.ise=컨텍스트 [{0}]이(가) 이미 초기화되었기에, 서블릿들이 추가될 수 없습니다.
applicationContext.attributeEvent=속성 이벤트 리스너가 예외를 발생시켰습니다.
applicationContext.illegalDispatchPath=애플리케이션이 불허된 경로 [{0}](으)로 RequestDispatcher를 얻으려 시도했지만, 해당 경로가 인코딩된 디렉토리 경로 이동 시도를 포함하고 있기 때문에, 거부되었습니다.
applicationContext.invalidFilterName=유효하지 않은 필터 이름 [{0}]으로 인하여, 필터 정의를 추가할 수 없습니다.
applicationContext.invalidServletName=유효하지 않은 서블릿 이름 ([{0}]) 때문에, 서블릿 정의를 추가할 수 없습니다.
applicationContext.lookup.error=컨텍스트 [{1}] 내의 리소스 [{0}]의 위치를 결정하지 못했습니다.
applicationContext.mapping.error=매핑 중 오류 발생
applicationContext.requestDispatcher.iae=경로 [{0}]이(가) "/" 문자로 시작하지 않습니다.
applicationContext.resourcePaths.iae=경로 [{0}]이(가) "/" 문자로 시작하지 않습니다.
applicationContext.role.iae=컨텍스트 [{0}]을(를) 위해 선언되는 개별 역할이, 널이거나 빈 문자열이어서는 안됩니다.
applicationContext.roles.iae=컨텍스트 [{0}]을(를) 위해 선언된 역할들의 배열이 널이어서는 안됩니다.
applicationContext.setAttribute.namenull=속성 이름이 널이어서는 안됩니다.
applicationContext.setInitParam.ise=컨텍스트가 이미 초기화된 후에, 초기화 파라미터들이 설정될 수 없습니다.
applicationContext.setRequestEncoding.ise=컨텍스트가 이미 초기화되었기에, 요청된 인코딩은 컨텍스트 [{0}]을(를) 위해 설정될 수 없습니다.
applicationContext.setResponseEncoding.ise=컨텍스트가 이미 초기화되었으므로, 컨텍스트 [{0}]을(를) 위한 응답 인코딩을 설정할 수 없습니다.
applicationContext.setSessionTimeout.ise=컨텍스트 [{0}]이(가) 이미 초기화되었기에, 세션 제한시간이 설정할 수 없습니다.
applicationContext.setSessionTracking.iae.invalid=컨텍스트 [{1}]를 위해 요청된 세션 트랙킹 모드 [{0}]은(는), 이 컨텍스트에서 지원되지 않습니다.
applicationContext.setSessionTracking.iae.ssl=컨텍스트 [{0}]을(를) 위해 요청된 세션 트랙킹 모드들이, SSL과 적어도 다른 하나 이상의 모드들을 포함했습니다. SSL은 다른 모드들과 함께 설정될 수 없습니다.
applicationContext.setSessionTracking.ise=컨텍스트 [{0}]을(를) 위한 세션 트랙킹 모드들은, 컨텍스트가 실행되고 있는 중에 설정될 수 없습니다.

applicationDispatcher.allocateException=서블릿 [{0}]을(를) 위한 할당 중 예외 발생
applicationDispatcher.deallocateException=서블릿 [{0}]을(를) 위한 할당 해제 작업 중 예외 발생
applicationDispatcher.forward.ise=응답이 이미 커밋된 후에는 forward할 수 없습니다.
applicationDispatcher.isUnavailable=서블릿 [{0}]은(는) 현재 가용하지 않습니다.
applicationDispatcher.serviceException=서블릿 [{0}]을(를) 위한 Servlet.service() 호출이 예외를 발생시켰습니다.
applicationDispatcher.specViolation.request=원본 ServletRequest 또는 wrap된 원본 ServletRequest가 RequestDispatcher로 전달되지 않았으며, 이는 SRV.8.2와 SRV.14.2.5.1에 대한 위반입니다.
applicationDispatcher.specViolation.response=원래의 ServletResponse 또는 wrapping된 원래의 ServletResponse가 RequestDispatcher에 전달되지 않았으며, 이는 SRV.8.2와 SRV.14.2.5.1을 위반합니다.

applicationFilterConfig.jmxRegisterFail=타입이 [{0}](이)고 이름이 [{1}]인 필터를 위한 JMX 등록이 실패했습니다.
applicationFilterConfig.jmxUnregister=타입이 [{0}](이)고 이름이 [{1}]인 필터를 위해, JMX 등록 제거가 완료되었습니다.
applicationFilterConfig.jmxUnregisterFail=타입이 [{0}](이)고 이름이 [{1}]인 필터를 위한, JMX 등록을 제거하지 못했습니다.
applicationFilterConfig.preDestroy=타입이 [{1}]이고 이름이 [{0}]인 필터에 대해 preDestroy를 호출하지 못했습니다.
applicationFilterConfig.release=타입이 [{1}](이)고 이름이 [{0}]인 필터를 소멸시키지 못했습니다.

applicationFilterRegistration.nullInitParam=이름 또는 값 또는 둘 다 널이어서, 필터를 위한 초기화 파라미터를 설정할 수 없습니다. 이름: [{0}], 값: [{1}]
applicationFilterRegistration.nullInitParams=널인 이름 또는 값 때문에, 필터의 초기화 파라미터를 설정할 수 없습니다. 이름: [{0}], 값: [{1}]

applicationHttpRequest.fragmentInDispatchPath=디스패치 경로 [{0}](으)로부터 URI fragment를 제거했습니다.

applicationPushBuilder.methodInvalid=PUSH 요청을 위한 HTTP 메소드는 반드시 캐시 가능하고 안전해야 하는데, [{0}]은(는) 그렇지 않습니다.
applicationPushBuilder.methodNotToken=HTTP 메소드들은 토큰들이어야 하지만, [{0}]은(는) 토큰이 아닌 문자를 포함하고 있습니다.

applicationServletRegistration.setServletSecurity.iae=[{1}](이)라는 이름의 컨텍스트에 배치된 서블릿 [{0}]을(를) 위해, 널 constraint가 지정되었습니다.
applicationServletRegistration.setServletSecurity.ise=컨텍스트가 이미 초기화되었기에, [{1}](이)라는 이름의 컨텍스트에 배치된 서블릿 [{0}]에 security constraint들이 추가될 수 없습니다.

applicationSessionCookieConfig.ise=컨텍스트가 이미 초기화되었기에, 컨텍스트 [{1}]을(를) 위한 SessionCookieConfig에 프로퍼티 [{0}]이(가) 추가될 수 없습니다.

aprListener.aprDestroy=APR 기반 Apache Tomcat Native 라이브러리를 셧다운하지 못했습니다.
aprListener.aprInit=프로덕션 환경들에서 최적의 성능을 제공하는, APR 기반 Apache Tomcat Native 라이브러리가, 다음 java.library.path에서 발견되지 않습니다: [{0}]
aprListener.aprInitDebug=[{0}](이)라는 이름들을 사용하여, java.library.path [{1}]에서, APR 기반 Apache Tomcat Native 라이브러리를 찾을 수 없습니다. 보고된 오류들: [{2}]
aprListener.aprInitError=APR 기반 Apache Tomcat Native 라이브러리를 로드하지 못했습니다. 보고된 오류는 [{0}]입니다.
aprListener.config=APR/OpenSSL 설정: useAprConnector [{0}], useOpenSSL [{1}]
aprListener.currentFIPSMode=현재의 FIPS 모드: [{0}]
aprListener.enterAlreadyInFIPSMode=AprLifecycleListener가 강제로 FIPS 모드로 들어가도록 설정되었으나, 라이브러리가 이미 FIPS 모드 [{0}]에 있습니다.
aprListener.flags=APR 용량정보들: IPv6 [{0}], sendfile [{1}], accept filters [{2}], random [{3}].
aprListener.initializeFIPSFailed=FIPS 모드로 진입하지 못했습니다.
aprListener.initializeFIPSSuccess=FIPS 모드로 성공적으로 진입했습니다.
aprListener.initializedOpenSSL=OpenSSL이 성공적으로 초기화되었습니다: [{0}]
aprListener.initializingFIPS=FIPS 모드 초기화...
aprListener.requireNotInFIPSMode=AprLifecycleListener는 라이브러리가 이미 FIPS 모드에 있어야만 하도록 설정되어 있는데, FIPS 모드에 있지 않았습니다.
aprListener.skipFIPSInitialization=이미 FIPS 모드에 있습니다. FIPS 초기화는 건너뜁니다.
aprListener.sslInit=SSLEngine을 초기화하지 못했습니다.
aprListener.tcnInvalid=APR 기반의 Apache Tomcat Native 라이브러리가 호환되지 않는 버전 [{0}]이(가) 설치되어 있습니다. Tomcat은 버전 [{1}]을(를) 요구합니다.
aprListener.tcnValid=APR 버전 [{1}]을(를) 사용한, APR 기반 Apache Tomcat Native 라이브러리 [{0}]을(를) 로드했습니다.
aprListener.tcnVersion=APR 기반의 Apache Tomcat Native 라이브러리의 예전 버전 [{0}](이)가 설치되어 있습니다. Tomcat은 최소 버전으로서 [{1}]을(를) 추천합니다.
aprListener.tooLateForFIPSMode=setFIPSMode를 수행할 수 없습니다: SSL이 이미 초기화되었습니다.
aprListener.tooLateForSSLEngine=setSSLEngine을 호출할 수 없습니다: SSL이 이미 초기화 되었습니다.
aprListener.tooLateForSSLRandomSeed=setSSLRandomSeed를 호출할 수 없습니다: SSL이 이미 초기화되어 있습니다.
aprListener.wrongFIPSMode=예기치 않은 AprLifecycleListener의 FIPSMode 옵션 값: [{0}]

asyncContextImpl.asyncDispatchError=비동기 디스패치 도중 오류 발생
asyncContextImpl.asyncRunnableError=AsyncContext.start()를 통해, 비동기로 Runnable을 처리하는 도중 오류 발생
asyncContextImpl.dispatchingStarted=비동기 디스패치 오퍼레이션이 이미 호출되었습니다. 동일한 비동기 사이클 내에서, 추가적인 비동기 디스패치 오퍼레이션은 허용되지 않습니다.
asyncContextImpl.fireOnComplete=등록된 AsyncListener들에 onComplete() 이벤트를 호출합니다.
asyncContextImpl.fireOnError=등록된 AsyncListener들에 onError() 이벤트를 호출합니다.
asyncContextImpl.fireOnStartAsync=등록된 AsyncListener들에 onStartAsync() 이벤트를 호출합니다.
asyncContextImpl.fireOnTimeout=등록된 AsyncListener들에 onTimeout() 이벤트를 호출합니다.
asyncContextImpl.noAsyncDispatcher=ServletContext로부터 반환된 디스패처는 비동기 디스패치를 지원하지 않습니다.
asyncContextImpl.onCompleteError=타입 [{0}]의 리스너를 위한 onComplete() 호출이 실패했습니다.
asyncContextImpl.onErrorError=타입 [{0}]의 리스너를 위한 onError() 호출이 실패했습니다.
asyncContextImpl.onStartAsyncError=타입 [{0}]의 리스너를 위한 onStartAsync() 호출이 실패했습니다.
asyncContextImpl.onTimeoutError=타입이 [{0}]인 리스너를 위한, onTimeout() 호출이 실패했습니다.
asyncContextImpl.request.ise=complete() 또는 어떠한 종류의 dispatch() 메소드들이 호출된 후에, getRequest()를 호출하는 것은 불허됩니다.
asyncContextImpl.requestEnded=AsyncContext와 연관된 요청은 이미 처리를 완료했습니다.
asyncContextImpl.response.ise=complete() 또는 어떤 종류의 dispatch() 메소드라도 호출된 이후에는, getResponse()를 호출하는 것은 불허됩니다.

containerBase.backgroundProcess.cluster=클러스터 [{0}]을(를) 백그라운드 프로세스에서 처리하는 중 오류 발생
containerBase.backgroundProcess.realm=Realm [{0}]을(를) 백그라운드 프로세스에서 처리 중 예외 발생
containerBase.backgroundProcess.unexpectedThreadDeath=백그라운드 쓰레드 [{0}]이(가) 예기치 않게 종료되었습니다.
containerBase.backgroundProcess.valve=Valve [{0}]의 백그라운드 프로세스를 처리하는 중 예외 발생
containerBase.nullName=컨테이너 이름은 널일 수 없습니다.
containerBase.threadedStartFailed=자식 컨테이너를 시작 중 실패했습니다.
containerBase.threadedStopFailed=자식 컨테이너가 중지되는 중 실패했습니다.

defaultInstanceManager.invalidInjection=유효하지 않은 메소드 리소스 injection annotation
defaultInstanceManager.restrictedClass=클래스 [{0}]에 대한 접근이 금지되었습니다. 접근 제한된 클래스입니다. 그것을 로드할 수 있으려면, 웹 애플리케이션이 반드시 privileged 된 것으로 설정되어야 합니다.
defaultInstanceManager.restrictedContainerServlet=클래스 [{0}]을(를) 접근하는 것은 금지되어 있습니다. 해당 클래스는 (ContainerServlet 인터페이스를 구현하고 있는) 접근 제한 클래스입니다. 웹 애플리케이션이 만약 접근하고자 한다면 로드할 수 있는 권한이 설정되어야 합니다.
defaultInstanceManager.restrictedFiltersResource=제한 필터들에 대한 설정 파일을 찾을 수 없습니다: [{0}]
defaultInstanceManager.restrictedListenersResource=RestrictedListeners.properties 파일을 찾을 수 없습니다: [{0}]
defaultInstanceManager.restrictedServletsResource=제한 서블릿들에 대한 프로퍼티 파일을 찾을 수 없습니다: [{0}]
defaultInstanceManager.restrictedWrongValue=제한 클래스들 프로퍼티 파일 [{0}]에 클래스 이름 [{1}]을(를) 위한 잘못된 값이 설정되었습니다. 요구되는 값: [restricted], 실제 값: [{2}]

filterChain.filter=필터 실행에서 예외가 발생했습니다.
filterChain.servlet=서블릿 실행이 예외를 발생시켰습니다.

jreLeakListener.authPolicyFail=javax.security.auth.Policy 클래스에서 메모리 누수를 방지하려 시도하는 중 오류 발생
jreLeakListener.classToInitializeFail=Tomcat을 시작하던 중, 발생 가능성 있는 메모리 누수를 방지하기 위한 클래스 [{0}]을(를) 로드하지 못했습니다.
jreLeakListener.gcDaemonFail=Tomcat이 시작하는 동안, 가능성 있는 메모리 누수들을 방지하기 위한 GC Daemon 쓰레드 생성을 개시하지 못했습니다. 이는 Sun JVM들이 아닌 환경에서 발생할 수 있습니다.
jreLeakListener.jarUrlConnCacheFail=기본 설정으로서, Jar URL 연결 캐싱을 사용불능 상태로 설정하지 못했습니다.
jreLeakListener.ldapPoolManagerFail=가능성 있는 메모리 누수들을 방지하기 위하여, Tomcat이 시작하는 동안, com.sun.jndi.ldap.LdapPoolManager 클래스 생성을 개시하지 못했습니다. 이는 Sun JVM들이 아닌 환경에서 발생할 수 있습니다.
jreLeakListener.xmlParseFail=XML 파싱하는 과정에서, 메모리 누수들을 방지하려 시도하는 중 오류 발생

naming.addEnvEntry=Environment 엔트리 [{0}]을(를) 추가합니다.
naming.addResourceEnvRef=리소스 env ref [{0}]을(를) 추가합니다.
naming.bindFailed=객체를 바인딩하지 못했습니다: [{0}]
naming.invalidEnvEntryType=Environment 엔트리 [{0}]이(가) 유효하지 않은 타입을 가지고 있습니다.
naming.invalidEnvEntryValue=Environment 엔트리 [{0}]이(가) 유효하지 않은 값을 가지고 있습니다.
naming.jmxRegistrationFailed=JMX 내에서 등록 실패했습니다: [{0}]
naming.namingContextCreationFailed=Naming Context 생성 실패: [{0}]
naming.unbindFailed=객체 [{0}]을(를) 바인딩 해제하지 못했습니다.
naming.wsdlFailed=wsdl 파일을 찾지 못했습니다: [{0}]

noPluggabilityServletContext.notAllowed=Servlet 3.0 스펙의 4.4 장에 따르면, web.xml 또는 web-fragment.xml 파일에 정의되지 않거나 @WebListener로 annotate되지 않은, ServletContextListener에서 이 메소드를 호출하는 것은 허용되지 않습니다.

pushBuilder.noPath=경로를 설정하기 전에 push()를 호출하는 것은 불허됩니다.

standardContext.applicationListener=클래스 [{0}]의 애플리케이션 리스너를 설정하는 중 오류 발생
standardContext.applicationSkipped=이전 오류(들)로 인하여, 애플리케이션 리스너들을 설치하는 것을 건너뛰었습니다.
standardContext.backgroundProcess.instanceManager=인스턴스 매니저 [{0}]을(를) 백그라운드 프로세스에서 처리 중 예외 발생
standardContext.backgroundProcess.loader=로더 [{0}]을(를) 백그라운드 프로세스로 처리 중 예외 발생
standardContext.backgroundProcess.manager=매니저 [{0}]을(를) 백그라운드 프로세스로 처리하는 중 예외 발생
standardContext.backgroundProcess.resources=리소스 [{0}]을(를) 백그라운드 프로세스로 처리하는 중 예외 발생
standardContext.cluster.noManager=매니저가 발견되지 않습니다. 클러스터 매니저가 사용되어야 하는지 점검합니다. 설정된 클러스터: [{0}], 배포할 수 있는 애플리케이션: [{1}]
standardContext.configurationFail=하나 이상의 구성요소(들)이, 해당 컨텍스트가 올바로 설정되지 않았다고 표시했습니다.
standardContext.cookieProcessor.null=컨텍스트를 위한 CookieProcessor를 널로 설정하는 것은 허용되지 않습니다.
standardContext.duplicateListener=리스너 [{0}]이(가) 이미 이 컨텍스트를 위해 설정되어 있습니다. 중복된 정의는 무시되었습니다.
standardContext.errorPage.error=오류 페이지 위치 [{0}]은(는) 반드시 ''/''로 시작해야 합니다.
standardContext.errorPage.required=ErrorPage는 널이어서는 안됩니다.
standardContext.errorPage.warning=경고: Servlet 2.4에서 오류 페이지 위치 [{0}]은(는) 반드시 ''/''로 시작해야 합니다.
standardContext.extensionValidationError=필수적으로 요구되는 애플리케이션 extension들이 유효한지 확인하려 시도하는 중 오류 발생
standardContext.filterFail=하나 이상의 필터들이 시작하지 못했습니다. 모든 상세 사항은 적절한 컨테이너 로그 파일에서 찾을 수 있습니다.
standardContext.filterMap.either=필터 매핑은 반드시 <url-pattern> 또는 <servlet-name>, 둘 중 하나를 지정해야 합니다.
standardContext.filterMap.name=필터 매핑이 알 수 없는 필터 이름 [{0}]을(를) 지정하고 있습니다.
standardContext.filterMap.pattern=필터 매핑에서 유효하지 않은 <url-pattern>: [{0}]
standardContext.filterStart=필터 [{0}]을(를) 시작하는 중 오류 발생
standardContext.invalidWrapperClass=[{0}]은(는) StandardWrapper의 하위 클래스가 아닙니다.
standardContext.isUnavailable=이 애플리케이션은 현재 가용 상태가 아닙니다.
standardContext.listenerFail=하나 이상의 리스너들이 시작하지 못했습니다. 상세 내역은 적절한 컨테이너 로그 파일에서 찾을 수 있습니다.
standardContext.listenerStart=Context initialized 이벤트를 [{0}] 클래스의 인스턴스인 리스너에 전송하는 동안 예외 발생
standardContext.listenerStop=클래스 [{0}]의 인스턴스인 리스너에게 contextDestroyed 이벤트를 전송하는 중 예외 발생
standardContext.loadOnStartup.loadException=웹 애플리케이션 [{0}] 내의 서블릿 [{1}]이(가) load() 예외를 발생시켰습니다.
standardContext.loginConfig.errorPage=폼 오류 페이지 [{0}]은(는) 반드시 "/"로 시작해야 합니다.
standardContext.loginConfig.errorWarning=주의: Servlet 2.4에서 폼 오류 페이지 [{0}]은(는) 반드시 "/" 로 시작해야 합니다.
standardContext.loginConfig.loginPage=폼 로그인 페이지 [{0}]은(는) 반드시 ''/''로 시작해야 합니다.
standardContext.loginConfig.loginWarning=경고: Servlet 2.4에서 폼 로그인 페이지 [{0}]은(는) 반드시 ''/''로 시작해야 합니다.
standardContext.loginConfig.required=LoginConfig은(는) 널일 수 없습니다.
standardContext.manager=클래스 [{0}]의 매니저 객체를 설정했습니다.
standardContext.managerFail=세션 매니저가 시작하지 못했습니다.
standardContext.namingResource.destroy.fail=이전 Naming 리소스를 소멸시키지 못했습니다.
standardContext.namingResource.init.fail=새로운 Naming 리소스들을 초기화하지 못했습니다.
standardContext.notStarted=[{0}](이)라는 이름을 가진 컨텍스트는 아직 시작되지 않았습니다.
standardContext.notWrapper=컨텍스트의 자식은 반드시 Wrapper여야 합니다.
standardContext.parameter.duplicate=중복된 컨텍스트 초기화 파라미터: [{0}]
standardContext.parameter.required=파라미터 이름과 파라미터 값, 둘 다 필수적입니다.
standardContext.pathInvalid=컨텍스트 경로는 반드시 빈 문자열이거나, 또는 ''/''로 시작하고 ''/''로 끝나지 않는 문자열이어야 합니다. 해당 경로 [{0}]은(는) 이 조건을 충족시키지 않아 [{1}](으)로 변경되었습니다.
standardContext.postconstruct.duplicate=클래스 [{0}]에서 중복된 PostConstruct 메소드 정의가 발견됨
standardContext.postconstruct.required=Fully qualified 클래스 이름과 메소드 이름, 둘 다 필수적입니다.
standardContext.predestroy.duplicate=클래스 [{0}]을(를) 위해, 중복된 @PreDestroy 메소드 정의입니다.
standardContext.predestroy.required=Fully qualified 클래스 이름과 메소드 이름, 둘 다 필수적으로 요구됩니다.
standardContext.reloadingCompleted=이름이 [{0}]인 컨텍스트를 다시 로드하는 것을 완료했습니다.
standardContext.reloadingStarted=이름이 [{0}]인 컨텍스트를 다시 로드하는 작업이 시작되었습니다.
standardContext.requestListener.requestInit=클래스 [{0}]의 리스너 인스턴스에게, request initialized Lifecycle 이벤트를 보내는 중 예외 발생
standardContext.resourcesInit=정적 리소스들을 초기화하는 중 오류 발생
standardContext.resourcesStart=정적 리소스들을 시작하는 중 오류 발생
standardContext.resourcesStop=정적 리소스들을 중지시키는 중 오류 발생
standardContext.sciFail=ServletContainerInitializer 처리 중 오류 발생
standardContext.securityConstraint.mixHttpMethod=<http-method>와 <http-method-omission>을 동일한 web resource collection에서 섞어서 사용하는 것은 허용되지 않습니다.
standardContext.securityConstraint.pattern=Security constraint 엘리먼트에서 유효하지 않은 <url-pattern> [{0}]입니다.
standardContext.servletFail=하나 이상의 서블릿들이 시작 시에 제대로 로드되지 않았습니다. 상세 정보는 적절한 컨테이너 로그 파일에서 찾을 수 있습니다.
standardContext.servletMap.name=서블릿 매핑이 알 수 없는 서블릿 이름 [{0}]을(를) 지정하고 있습니다.
standardContext.servletMap.pattern=서블릿 매핑에서 유효하지 않은 <url-pattern> [{0}]
standardContext.startFailed=이전 오류들로 인해 컨텍스트 [{0}]의 시작이 실패했습니다.
standardContext.startingContext=이름이 [{0}]인 컨텍스트를 시작하는 중 예외 발생
standardContext.stop.asyncWaitInterrupted=처리 중인 비동기 요청이 완료되기를 기다리기 위해 unloadDelay 밀리초를 대기하는 동안 인터럽트를 받았습니다. 더 이상의 지체 없이 컨텍스트 중지 작업을 계속할 것입니다.
standardContext.stoppingContext=이름이 [{0}]인 컨텍스트를 중지시키는 중 예외 발생
standardContext.threadBindingListenerError=컨텍스트 [{0}]을(를) 위해 설정된 쓰레드 바인딩 리스너에서 오류가 발생했습니다.
standardContext.urlPattern.patternWarning=경고: Servlet 2.4에서 URL 패턴 [{0}]은(는) 반드시 ''/''로 시작해야 합니다.
standardContext.webappClassLoader.missingProperty=해당 프로퍼티가 존재하지 않기에, 웹 애플리케이션 클래스로더 프로퍼티 [{0}]을(를) [{1}](으)로 설정할 수 없습니다.
standardContext.workCreateException=디렉토리 [{0}]와(과) CATALINA_HOME [{1}](으)로부터, 컨텍스트 [{2}]을(를) 위한 작업 디렉토리의 절대 경로를 결정하지 못했습니다.
standardContext.workCreateFail=컨텍스트 [{1}]을(를) 위한 작업 디렉토리 [{0}]을(를) 생성하지 못했습니다.
standardContext.workPath=컨텍스트 [{0}]을(를) 위한 작업 경로를 구하는 중 예외 발생

standardContextValve.acknowledgeException=요청에 대해, 100 (Continue) 응답과 함께, ACK을 보내지 못했습니다.

standardEngine.jvmRouteFail=엔진의 jvmRoute 속성을 시스템 프로퍼티로부터 설정하지 못했습니다.
standardEngine.notHost=엔진의 자식은 반드시 호스트여야 합니다.
standardEngine.notParent=엔진은 부모 컨테이너를 가질 수 없습니다.

standardHost.clientAbort=원격 클라이언트가 요청을 중단시켰습니다. IOException: [{0}]
standardHost.invalidErrorReportValveClass=지정된 오류 보고 Valve 클래스 [{0}]을(를) 로드할 수 없었습니다.
standardHost.noContext=이 요청을 처리하기 위한 컨텍스트가 설정되지 않았습니다.
standardHost.notContext=호스트의 자식은 반드시 컨텍스트이어야 합니다.
standardHost.nullName=호스트 이름이 필수적입니다.
standardHost.problematicAppBase=호스트 [{0}]에서 appBase를 위해 빈 문자열을 사용하는 것은, 결국 appBase를 CATALINA_BASE로 설정하게 되는데, 이는 좋은 생각이 아닙니다.

standardHostValue.customStatusFailed=커스텀 오류 페이지 [{0}]은(는) 올바르게 디스패치될 수 없었습니다.

standardPipeline.basic.start=새로운 기본 Valve를 시작하는 중 오류 발생
standardPipeline.basic.stop=이전 기본 Valve를 중지시키는 중 오류 발생
standardPipeline.valve.destroy=Valve를 소멸시키는 중 오류 발생
standardPipeline.valve.start=Valve를 시작시키는 중 오류 발생
standardPipeline.valve.stop=Valve를 중지시키는 중 오류 발생

standardServer.accept.error=셧다운 명령을 위해 listen하고 있는 소켓에서, accept를 시도하는 중, IOException이 발생했습니다.
standardServer.accept.readError=셧다운 명령을 읽으려 시도하는 중 IOException이 발생했습니다.
standardServer.accept.security=셧다운 명령을 위해 listen하고 있는 소켓에서, accept를 시도하는 중, 보안 오류가 발생했습니다.
standardServer.accept.timeout=셧다운 명령을 위해 listen하고 있는 소켓이, accept()를 호출 한 후, 예기치 않은 제한 시간 초과([{0}] 밀리초)를 발생시켰습니다. 버그 56684가 발생한 경우일까요?
standardServer.invalidShutdownCommand=유효하지 않은 셧다운 명령 [{0}]을(를) 받았습니다.
standardServer.shutdownViaPort=셧다운 포트를 통해 유효한 셧다운 명령을 받았습니다. 서버 인스턴스를 중지시킵니다.
standardServer.storeConfig.contextError=컨텍스트 [{0}]의 설정을 저장하는 중 오류 발생
standardServer.storeConfig.error=서버 설정을 저장하는 중 오류 발생
standardServer.storeConfig.notAvailable=[{0}](이)라는 이름의 MBean으로서 StoreConfig 구현 객체가 등록되지 않았으므로, 어떤 설정도 저장될 수 없었습니다. 보통 StoreConfigLifecycleListener을 통하여 적절한 MBean이 등록됩니다.

standardService.engine.startFailed=연관된 엔진을 시작하지 못했습니다.
standardService.engine.stopFailed=연관된 엔진을 중지시키지 못했습니다.
standardService.mapperListener.startFailed=연관된 MapperListener를 시작하지 못했습니다.
standardService.mapperListener.stopFailed=연관된 MapperListener를 중지시키지 못했습니다.
standardService.start.name=서비스 [{0}]을(를) 시작합니다.
standardService.stop.name=서비스 [{0}]을(를) 중지시킵니다.

standardWrapper.allocate=서블릿 인스턴스를 할당하는 중 오류 발생
standardWrapper.allocateException=서블릿 [{0}]을(를) 위해 할당하던 중 예외 발생
standardWrapper.deallocateException=서블릿 [{0}]을(를) 위한 할당 해제 처리 중 예외 발생
standardWrapper.destroyException=서블릿 [{0}]을(를) 위한 Servlet.destroy() 호출 중 익셉션이 발생했습니다.
standardWrapper.destroyInstance=서블릿 [{0}]을(를) 위한 InstanceManager.destroy() 호출이 예외를 발생시켰습니다.
standardWrapper.initException=서블릿 [{0}]을(를) 위한 Servlet.init() 호출이 예외를 발생시켰습니다.
standardWrapper.instantiate=서블릿 클래스 [{0}](으)로부터 인스턴스 생성하는 중 오류 발생
standardWrapper.isUnavailable=서블릿 [{0}]은(는) 현재 가용하지 않습니다.
standardWrapper.notChild=Wrapper 컨테이너는 자식 컨테이너들을 가질 수 없습니다.
standardWrapper.notClass=서블릿 [{0}]을(를) 위한 서블릿 클래스가 지정되지 않았습니다.
standardWrapper.notContext=Wrapper의 부모 컨테이너는 반드시 컨텍스트여야 합니다.
standardWrapper.notFound=서블릿 [{0}]은(는) 가용하지 않습니다.
standardWrapper.notServlet=클래스 [{0}]은(는) 서블릿이 아닙니다,
standardWrapper.serviceException=경로가 [{1}]인 컨텍스트의 서블릿 [{0}]을(를) 위한 Servlet.service() 호출이 예외를 발생시켰습니다.
standardWrapper.serviceExceptionRoot=경로 [{1}]의 컨텍스트 내의 서블릿 [{0}]을(를) 위한 Servlet.service() 호출이, 근본 원인(root cause)과 함께, 예외 [{2}]을(를) 발생시켰습니다.
standardWrapper.unavailable=서블릿 [{0}]을(를) 가용하지 않은 상태로 표시합니다.
standardWrapper.unloadException=서블릿 [{0}]을(를) 위한 unload() 호출 시, 예외를 발생시켰습니다.
standardWrapper.unloading=서블릿이 언로드되었기 때문에, 서블릿 [{0}]을(를) 할당할 수 없습니다.
standardWrapper.waiting=서블릿 [{1}]을(를) 위해, [{0}]개의 인스턴스(들)이 할당 해제되기를 기다립니다.

threadLocalLeakPreventionListener.containerEvent.error=컨테이너 이벤트 [{0}]을(를) 처리하는 중 예외 발생
threadLocalLeakPreventionListener.lifecycleEvent.error=Lifecycle 이벤트 [{0}]을(를) 처리하는 중 예외 발생
