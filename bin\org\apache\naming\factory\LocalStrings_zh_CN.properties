# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

dataSourceLinkFactory.badWrapper=不是类型[{0}]的包装

factoryBase.factoryClassError=无法加载资源工厂类
factoryBase.factoryCreationError=无法创建资源工厂实例
factoryBase.instanceCreationError=无法创建资源实例

lookupFactory.circularReference=找到一个涉及[{0}]的循环引用
lookupFactory.createFailed=无法创建JNDI查找工厂类实例
lookupFactory.loadFailed=无法加载JNDI查找工厂类
lookupFactory.typeMismatch=期望JNDI引用[{0}]的类型为[{1}]，但查找[{2}]返回类型为[{3}]的对象

resourceFactory.factoryCreationError=无法创建资源工厂实例

resourceLinkFactory.invalidGlobalContext=调用方提供的全局上下文无效
resourceLinkFactory.nullType=引用全局资源 [{1}] 的本地资源链接 [{0}] 未指定所需的属性类型
resourceLinkFactory.unknownType=引用全局资源[{1}]的本地资源链接[{0}]指定了未知类型[{2}]
resourceLinkFactory.wrongType=引用全局资源[{1}]的本地资源链接[{0}]应返回[{2}]的实例，但返回了[{3}]的实例
