# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

elProcessor.defineFunctionInvalidClass=Die Klasse [{0}] ist nicht öffentlich
elProcessor.defineFunctionInvalidMethod=Die Methode [{0}] der Klasse [{1}] ist nicht public static

expressionFactory.cannotFind=Kann die ExpressionFactory mit dem Typ [{0}] nicht finden

importHandler.classNotFound=Die Klasse [{0}] konnte nicht importiert werden, da sie nicht gefunden werden konnte
importHandler.invalidClassNameForStatic=Ungültige Klasse [{0}] für angegebenen static import [{1}]
