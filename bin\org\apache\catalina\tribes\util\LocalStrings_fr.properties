# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

arrays.keyoffset.outOfBounds=Le décalage (offset) de la clé est en-dehors des limites.
arrays.length.outOfBounds=Pas assez de données dans la clé, la longueur dépasse les limites
arrays.malformed.arrays=les tableaux d'octets doivent être représentés tels que {1,3,4,5,6}
arrays.srcoffset.outOfBounds=srcoffset est hors limites

executorFactory.not.running=L'Executor ne tourne pas, impossible de forcer une commande dans les files d'attente
executorFactory.queue.full=La file d'attente est complète

uuidGenerator.createRandom=La création d''une instance de SecureRandom pour le génération des UUID en utilisant [{0}] a pris [{1}] millisecondes
uuidGenerator.unable.fit=Impossible de faire rentrer [{0}] octets dans le tableau, longueur : [{1}] longueur requise : [{2}]
