# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

channel.nio.interrupted=현재 쓰레드가 중단되었습니다.
channel.nio.ssl.appInputNotEmpty=애플리케이션 입력 버퍼가 여전히 데이터를 포함하고 있습니다. 데이터를 잃을 뻔했습니다.
channel.nio.ssl.appOutputNotEmpty=애플리케이션 출력 버퍼가 여전히 데이터를 포함하고 있습니다. 데이터를 잃을 뻔했습니다.
channel.nio.ssl.closeSilentError=이미 예상했던대로, 연결을 깨끗하게 닫으려 시도하던 중 예외 발생이 있었습니다.
channel.nio.ssl.closing=채널이 닫는 중 상태에 있습니다.
channel.nio.ssl.eofDuringHandshake=Handshake 하는 동안 EOF 발생
channel.nio.ssl.expandNetInBuffer=네트워크 입력 버퍼를 [{0}] 바이트 크기로 확장합니다.
channel.nio.ssl.expandNetOutBuffer=네트워크 출력 버퍼를 [{0}] 바이트 크기로 확장합니다.
channel.nio.ssl.foundHttp=암호화된 TLS 연결이어야 하는 곳에서, plain 텍스트 HTTP 요청이 발견되었습니다.
channel.nio.ssl.handshakeError=Handshake 오류
channel.nio.ssl.incompleteHandshake=Handshake가 완료되지 않았습니다. 데이터를 읽기 전 반드시 handshake를 완료해야 합니다.
channel.nio.ssl.invalidCloseState=유효하지 않은, 닫힘 상태입니다. 네트워크 데이터를 보내지 않을 것입니다.
channel.nio.ssl.invalidStatus=예기치 않은 상태 [{0}].
channel.nio.ssl.netInputNotEmpty=네트워크 입력 버퍼가 여전히 데이터를 포함하고 있습니다. Handshake가 실패할 것입니다.
channel.nio.ssl.netOutputNotEmpty=네트워크 출력 버퍼가 여전히 데이터를 포함하고 있습니다. Handshake는 실패할 것입니다.
channel.nio.ssl.notHandshaking=Handshake 중 NOT_HANDSHAKING 발생
channel.nio.ssl.pendingWriteDuringClose=쓰기가 진행 중이어서, 네트워크 버퍼에 데이터가 남아 있습니다. SSL 닫기 메시지를 보낼 수 없습니다. close(true)를 대신 사용하여 강제로 닫을 것입니다.
channel.nio.ssl.remainingDataDuringClose=네트워크 버퍼에 데이터가 남아 있어, SSL 닫기 메시지를 보낼 수 없습니다. close(true)를 대신 사용하여 강제로 닫으려 합니다.
channel.nio.ssl.sniDefault=요청된 SNI 호스트 이름을 결정하기 위한 버퍼를 충분히 채울 수 없습니다. 기본 값을 사용합니다.
channel.nio.ssl.sniHostName=연결 [{0}]을(를) 위해 추출된 SNI 호스트 이름은 [{1}]입니다.
channel.nio.ssl.timeoutDuringHandshake=Handshake 도중 제한 시간 초과
channel.nio.ssl.unexpectedStatusDuringUnwrap=Handshake UNWRAP 처리 중 예기치 않은 상태: [{0}]
channel.nio.ssl.unexpectedStatusDuringWrap=WRAP을 위해 handshake 수행 중 예기치 않은 상태 [{0}]입니다.
channel.nio.ssl.unwrapFail=데이터를 unwrap할 수 없습니다. 유효하지 상태: [{0}]
channel.nio.ssl.unwrapFailResize=버퍼가 너무 작아서 데이터를 unwrap할 수 없습니다. 유효하지 않은 상태 [{0}]
channel.nio.ssl.wrapException=Wrap하는 중 handshake가 실패했습니다.
channel.nio.ssl.wrapFail=데이터를 wrap할 수 없습니다. 유효하지 않은 상태 [{0}]

endpoint.accept.fail=소켓 accept 실패
endpoint.alpn.fail=[{0}]을(를) 사용하여 ALPN을 위한 엔드포인트를 설정하지 못했습니다.
endpoint.alpn.negotiated=ALPN을 사용하여 [{0}] 프로토콜로 negotiate 했습니다.
endpoint.apr.applyConf=OpenSSLConfCmd를 SSL 컨텍스트에 적용합니다.
endpoint.apr.checkConf=OpenSSLConf를 점검합니다.
endpoint.apr.errApplyConf=OpenSSLConf를 SSL 컨텍스트에 적용할 수 없었습니다.
endpoint.apr.errCheckConf=OpenSSLConf 점검 중 오류 발생
endpoint.apr.errMakeConf=OpenSSLConf 컨텍스트를 생성할 수 없었습니다.
endpoint.apr.failSslContextMake=SSLContext를 생성할 수 없습니다. AprLifecycleListener에서 SSLEngine이 사용가능 상태로 설정되었는지, AprLifecycleListener가 올바로 초기화되었는지, 그리고 유효한 SSLProtocol이 지정되었는지 점검하십시오.
endpoint.apr.invalidSslProtocol=SSLProtocol 속성을 위해 유효하지 않은 값 [{0}]이(가) 제공되었습니다.
endpoint.apr.maxConnections.running=APR 엔드포인트는 실행 중 maxConnections 설정을 지원하지 않습니다. 기존 값인 [{0}]이(가) 계속해서 사용될 것입니다.
endpoint.apr.maxConnections.unlimited=APR 엔드포인트는 무제한 연결들을 지원하지 않습니다. 기존 값 [{0}]이(가) 계속해서 사용될 것입니다.
endpoint.apr.noSendfileWithSSL=SSL이 사용가능 상태로 설정되어 있을 때에는, APR/native connector를 위한 sendfile이 지원되지 않습니다.
endpoint.apr.pollAddInvalid=소켓 [{0}]을(를) poller에 추가하려는, 유효하지 않은 시도입니다.
endpoint.apr.pollError=Poller가 다음 오류와 함께 실패했습니다. [{0}] : [{1}]
endpoint.apr.pollMergeEvents=병합 이벤트 [{2}]을(를) 생성하기 위해, 소켓 [{0}]을(를) 위한 poller 이벤트 [{1}]을(를) 병합합니다.
endpoint.apr.pollUnknownEvent=인식되지 않는 이벤트 [{0}]와(과) 함께, 소켓이 poller로 부터 반환되었습니다.
endpoint.apr.remoteport=APR 소켓 [{0}]이(가) 원격 포트 [{1}](으)로 열렸습니다.
endpoint.apr.tooManyCertFiles=AprEndpoint가 처리할 수 있는 것 보다 더 많은 인증서 파일들이 설정되었습니다.
endpoint.debug.channelCloseFail=채널을 닫지 못했습니다.
endpoint.debug.destroySocket=소켓 [{0}]을(를) 소멸시킵니다.
endpoint.debug.pollerAdd=addList에 추가합니다: 소켓 [{0}], 제한시간 [{1}], 플래그들 [{2}]
endpoint.debug.pollerAddDo=Poller에 소켓 [{0}]을(를) 추가합니다.
endpoint.debug.pollerProcess=다음 이벤트(들)을 위해 소켓 [{0}]을(를) 처리합니다: [{1}]
endpoint.debug.pollerRemove=Poller로부터 [{0}]을(를) 제거하려 시도 중
endpoint.debug.pollerRemoved=Poller로부터 [{0}]을(를) 제거했습니다.
endpoint.debug.registerRead=[{0}]을(를) 위한 readInterest를 등록했습니다.
endpoint.debug.registerWrite=[{0}]을(를) 위한 writeInterest를 등록했습니다.
endpoint.debug.socket=소켓 [{0}]
endpoint.debug.socketCloseFail=소켓을 닫지 못했습니다.
endpoint.debug.socketTimeout=제한 시간 초과로 처리합니다: [{0}]
endpoint.debug.unlock.fail=포트 [{0}]에 대한 accept의 잠금을 풀고자 시도하는 중 예외 발생
endpoint.debug.unlock.localFail=[{0}]을(를) 위한 로컬 주소를 결정할 수 없습니다.
endpoint.debug.unlock.localNone=로컬 주소가 가용하지 않기 때문에, [{0}]을(를) 위한 acceptor의 잠금 상태를 풀지 못했습니다.
endpoint.duplicateSslHostName=호스트 이름 [{0}]을(를) 위해 여러 개의 SSLHostConfig 엘리먼트들이 제공되었습니다. 호스트 이름들은 반드시 유일해야 합니다.
endpoint.err.close=소켓을 닫으려 시도하는 중 예외 발생
endpoint.err.handshake=Handshake가 실패했습니다.
endpoint.err.unexpected=소켓 처리 중 예기치 않은 오류 발생
endpoint.executor.fail=Executor가 소켓 [{0}]을(를) 처리하기를 거부했습니다.
endpoint.getAttribute=[{0}]의 값은 [{1}]입니다.
endpoint.init.bind=소켓 바인딩 실패: [{0}] [{1}]
endpoint.init.bind.inherited=Connector가 상속된 채널을 사용하도록 설정되었는데, 상속된 채널이 없습니다.
endpoint.init.listen=소켓 listen 실패: [{0}] [{1}]
endpoint.init.notavail=APR이 가용하지 않음
endpoint.invalidJmxNameSslHost=호스트 [{0}]와(과) 연관된 SSLHostConfig를 위한, 유효한 JMX 객체 이름을 생성할 수 없습니다.
endpoint.invalidJmxNameSslHostCert=호스트가 [{0}]이고 인증서 타입이 [{1}]인 SSLHostConfigCertificate을 위한, 유효한 JMX 객체 이름을 생성할 수 없습니다.
endpoint.jmxRegistrationFailed=JMX 객체를 [{0}](이)라는 이름으로 등록시키지 못했습니다.
endpoint.jsse.noSslContext=호스트 이름 [{0}]을(를) 위한 SSLContext를 찾을 수 없습니다.
endpoint.launch.fail=새로운 Runnable을 시작하지 못했습니다.
endpoint.nio.registerFail=Poller로부터의 selector와 함께, 소켓을 등록하지 못했습니다.
endpoint.nio.selectorCloseFail=Poller를 닫을 때, selector를 닫지 못했습니다.
endpoint.nio.stopLatchAwaitFail=Poller들이 요구되는 시간 내에 중지되지 않았습니다.
endpoint.nio.stopLatchAwaitInterrupted=이 쓰레드는 poller들이 중지되기를 기다리는 동안 중단되었습니다.
endpoint.nio.timeoutCme=제한 시간 초과들을 처리하는 동안 예외 발생. 해당 코드는 반복해서 점검되어 왔고 ConcurrentModificationException이 발생하지 않아 왔습니다. 이 오류를 재현할 수 있다면, Tomcat 버그 티켓을 여시고 오류 재현 과정들을 제공해 주십시오.
endpoint.nio2.exclusiveExecutor=NIO2 Connector가 셧다운 시에 정상적으로 동작하기 위해서는, 배타적인 Executor가 필수적으로 요구됩니다.
endpoint.noSslHostConfig=호스트 이름 [{0}]을(를) 사용하여, Connector [{1}]을(를) 위한 defaultSSLHostConfigName과 부합하는 SSLHostConfig 엘리먼트를 찾지 못했습니다.
endpoint.noSslHostName=SSL 호스트 설정을 위한 호스트 이름이 제공되지 않았습니다.
endpoint.poll.error=예기치 않은 poller 오류 발생
endpoint.poll.fail=심각한 poller 실패 (poller를 재시작합니다): [{0}] [{1}]
endpoint.poll.initfail=Poller 생성이 실패했습니다.
endpoint.poll.limitedpollsize=지정된 크기 [{0}]로 poller를 생성하지 못했습니다.
endpoint.process.fail=소켓 프로세서를 할당하는 중 오류 발생
endpoint.processing.fail=소켓 프로세서 실행 중 오류 발생
endpoint.removeDefaultSslHostConfig=기본 SSLHostConfig(이름: [{0}])는 제거될 수 없습니다.
endpoint.sendfile.addfail=Sendfile 실패: [{0}] [{1}]
endpoint.sendfile.error=예기치 않은 sendfile 오류
endpoint.serverSocket.closeFailed=엔드포인트 [{0}]을(를) 위한 서버 소켓을 닫지 못했습니다.
endpoint.setAttribute=[{1}]에 [{0}]을(를) 설정합니다.
endpoint.timeout.err=소켓 제한 시간 초과 처리 중 오류 발생
endpoint.unknownSslHostName=SSL 호스트 이름 [{0}]은(는), 이 엔드포인트를 위해 인식되지 않는 이름입니다.
endpoint.warn.executorShutdown=쓰레드 풀 [{0}]와(과) 연관된 해당 Executor는 완전히 종료되지 않았습니다. 일부 애플리케이션 쓰레드들이 여전히 실행 중일 수 있습니다.
endpoint.warn.incorrectConnectionCount=잘못된 연결 개수. 동일한 소켓에 여러 번의 socket.close가 호출되었음.
endpoint.warn.noLocalAddr=소켓 [{0}]을(를) 위한 로컬 주소를 결정할 수 없습니다.
endpoint.warn.noLocalName=소켓 [{0}]을(를) 위한 로컬 호스트 이름을 결정할 수 없습니다.
endpoint.warn.noLocalPort=소켓 [{0}]을(를) 위한 로컬 포트를 결정할 수 없습니다.
endpoint.warn.noRemoteAddr=소켓 [{0}]을(를) 위한 원격 주소를 결정할 수 없습니다.
endpoint.warn.noRemoteHost=소켓 [{0}]을(를) 위한 원격 호스트 이름을 결정할 수 없습니다.
endpoint.warn.noRemotePort=소켓 [{0}]을(를) 위한 원격 포트를 결정할 수 없습니다.
endpoint.warn.unlockAcceptorFailed=Acceptor 쓰레드 [{0}]이(가) 잠금을 풀지 못했습니다. 강제로 소켓을 셧다운합니다.

sniExtractor.clientHelloInvalid=ClientHello 메시지가 정확히 포맷되지 않았습니다.
sniExtractor.clientHelloTooBig=ClientHello가 단일 TLS 레코드에 존재하지 않았기에, SNI 정보를 추출할 수 없었습니다.
sniExtractor.tooEarly=클라이언트 헬로 메시지가 파싱되기 전에 이 메소드를 호출하는 것은 허용되지 않습니다.

socket.apr.clientAbort=클라이언트가 연결을 중단했습니다.
socket.apr.closed=이 연결과 연관된 소켓 [{0}]이(가) 이미 닫혀 있습니다.
socket.apr.read.error=Wrapper [{2}]을(를) 사용하여, APR/native 소켓 [{1}]으로부터 데이터를 읽는 중, 예기치 않은 오류 발생: [{0}]
socket.apr.write.error=Wrapper [{2}]을(를) 가지고 APR/native 소켓 [{1}]에 데이터를 쓰는 중, 예기치 않은 오류 발생: [{0}]
socket.closed=이 연결과 연관된 해당 소켓은 이미 닫혔습니다.
socket.sslreneg=SSL 연결을 re-negotiate하는 동안 예외 발생

socketWrapper.readTimeout=읽기 타임아웃
socketWrapper.writeTimeout=쓰기 타임아웃

sslHostConfig.certificate.notype=여러 개의 인증서들이 지정되었는데, 적어도 하나의 인증서에 필수 속성 타입이 없습니다.
sslHostConfig.certificateVerificationInvalid=인증서 검증 값 [{0}]은(는) 인식되지 않는 값입니다.
sslHostConfig.fileNotFound=설정된 파일 [{0}]이(가) 존재하지 않습니다.
sslHostConfig.invalid_truststore_password=Trust 저장소를 잠금을 풀거나 유효한지 확인하는 용도로, 제공된 Trust 저장소 비밀번호를 사용할 수 없었습니다. 널 비밀번호를 사용하여, 해당 Trust 저장소에 대한 접근을 다시 시도합니다. 이는 유효한지 확인하는 작업을 건너뛸 것입니다.
sslHostConfig.mismatch=[{1}](이)라는 이름의 SSLHostConfig에 프로퍼티 [{0}]이(가) 설정되었는데, 이 프로퍼티는 [{2}] 설정 문법을 위한 것이나, 해당 SSLHostConfig은 [{3}] 설정 문법으로 사용되고 있습니다.
sslHostConfig.opensslconf.alreadyset=또 다른 OpenSSLConf을 설정하려는 시도는 무시되었습니다.
sslHostConfig.opensslconf.null=널인 OpenSSLConf를 설정하려는 시도가 무시되었습니다.
sslHostConfig.prefix_missing=프로토콜 [{0}]이(가) [{1}](이)라는 이름을 가진 SSLHostConfig의 프로토콜 목록에 추가되어 있습니다. +/- prefix가 누락되었는지 점검하십시오.

sslHostConfigCertificate.mismatch=프로퍼티 [{0}]이(가) [{1}](이)라는 이름의 SSLHostConfigCertificate에 설정되었고, 이는 인증서 저장소 타입 [{2}]을(를) 위한 것이지만, 인증서가 타입 [{3}]의 인증서 저장소와 함께 사용되고 있습니다.

sslImplementation.cnfe=클래스 [{0}]의 SSLImplementation 객체를 생성할 수 없습니다.

sslUtilBase.active=활성화 된 [{0}]은(는) 다음과 같습니다: [{1}]
sslUtilBase.aliasIgnored=FIPS이 활성화되어 있어, 별칭 [{0}]은(는) 무시됩니다. 키 저장소에 둘 이상의 키가 존재하는 경우, 어떤 키를 사용할지는 키 스토어 구현에 의존하게 됩니다.
sslUtilBase.alias_no_key_entry=별칭 이름 [{0}]을(를) 사용하여 키 엔트리를 식별해낼 수 없습니다.
sslUtilBase.invalidTrustManagerClassName=trustManagerClassName에 의해 제공된 클래스 [{0}]은(는) javax.net.ssl.TrustManager를 구현하지 않았습니다.
sslUtilBase.keystore_load_failed=[{2}](으)로 인하여, 경로 [{1}]에 있고 타입이 [{0}]인 키 저장소를 로드하지 못했습니다.
sslUtilBase.noCertFile=SSLHostConfig의 속성인 certificateFile은, 반드시 SSL connector를 사용할 때에만 정의되어야 합니다.
sslUtilBase.noCrlSupport=truststoreProvider [{0}]은(는) certificateRevocationFile 설정 옵션을 지원하지 않습니다.
sslUtilBase.noKeys=개인 키들에 대한 별칭들이 키 저장소에 없습니다.
sslUtilBase.noVerificationDepth=truststoreProvider [{0}]은(는) certificateVerificationDepth 설정 옵션을 지원하지 않습니다.
sslUtilBase.noneSupported=지정된 [{0}]의 어느 것도 SSL 엔진에 의해 지원되지 않습니다: [{1}]
sslUtilBase.skipped=지정된 [{0}]의 일부가 SSL 엔진에 의해 지원되지 않아 건너뜁니다: [{1}]
sslUtilBase.ssl3=SSLv3이 명시적으로 사용 가능 상태로 설정되었습니다. 이 프로토콜은 안전하지 않은 것으로 알려져 있습니다.
sslUtilBase.tls13.auth=JSSE TLS 1.3 구현이 초기 handshake 이후의 인증을 지원하지 않음에 따라, 선택사항인 클라이언트 인증과 호환되지 않습니다.
sslUtilBase.trustedCertNotChecked=인증서가 알 수 없는 타입이라서, 별칭이 [{0}]인 신뢰되는 인증서의 유효일자들이 점검되지 않았습니다.
sslUtilBase.trustedCertNotValid=별칭이 [{0}](이)고 DN이 [{1}]인 해당 신뢰받는 인증서는 [{2}](으)로 인하여 유효하지 않습니다. 이 신뢰되는 인증서에 의해 서명된 인증서들은 받아들여질 것입니다.
