# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

farmWarDeployer.hostOnly=FarmWarDeployer sólo puede operar como un subelemento de una máquina del cluster!
farmWarDeployer.modInstall=Installando webapp [{0}] desde [{1}]\n
farmWarDeployer.modInstallFail=Incapaz de instalar WAR file
farmWarDeployer.msgIoe=Incapáz de leer el archivo de despliegue de la granja
farmWarDeployer.msgRxDeploy=Recibe el camino de despliegue del cluster [{0}], war [{1}]
farmWarDeployer.removeFailLocal=Borrado local de [{0}] fallido
farmWarDeployer.removeFailRemote=El borrado local de [{0}] falló, otro manager tiene la aplicación en servicio!
farmWarDeployer.sendFragment=Fragmento war enviado al cluster con camino [{0}], war [{1}] a [{2}]\n
farmWarDeployer.servicingUndeploy=La applicación [{0}] esta en servicion y no pude ser removida del nodo de respaldo del cluster
farmWarDeployer.undeployEnd=El revertimiendo del despliegue de [{0}] ha terminado.\n

fileMessageFactory.deleteFail=Fallo al borrar [{0}]\n

warWatcher.cantListWatchDir=No se pueden listar archivos en WatchDir [{0}]: verifique si es un directorio y tiene permisos de lectura.\n
warWatcher.checkWarResult=WarInfo.check() devolvió [{0}] para [{1}]
warWatcher.checkingWar=Verificando archivo WAR [{0}]
