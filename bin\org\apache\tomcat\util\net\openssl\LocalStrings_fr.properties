# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

engine.ciphersFailure=Echec en essayant d'obtenir la liste des chiffres
engine.emptyCipherSuite=La suite de chiffrement (cipher suite) est vide
engine.engineClosed=Le moteur a déjà été fermé
engine.failedCipherSuite=Impossible d''activer la suite de chiffres [{0}]
engine.inboundClose=L'entrée a été fermée avant d'avoir reçu le close_notify du pair
engine.invalidBufferArray=offset : [{0}], length : [{1}] (attendu : offset <= offset + length <= srcs.length [{2}])
engine.invalidDestinationBuffersState=L'état des buffers de destination a changé de manière concurrente lors de l'unwrap des octets
engine.noRestrictSessionCreation=OpenSslEngine ne permet pas de restreindre le moteur à la récupération des sessions existantes
engine.noSSLContext=Pas de contexte SSL
engine.noSession=ID de session SSL non disponible
engine.nullBuffer=Tampon null
engine.nullBufferInArray=Tampon null dans le tableau
engine.nullCipherSuite=Suite de chiffres nulle
engine.nullName=La valeur du nom est null
engine.nullValue=La valeur est null
engine.openSSLError=Erreur OpenSSL : [{0}] message : [{1}]
engine.oversizedPacket=Le paquet crypté est trop gros
engine.unsupportedCipher=Suite de chiffres non supportée : [{0}] [{1}]
engine.unsupportedProtocol=Le protocole [{0}] n''est pas supporté
engine.unverifiedPeer=Le pair n'est pas vérifié
engine.writeToSSLFailed=Echec d''écriture vers SSL, code de retour : [{0}]

openssl.X509FactoryError=Impossible d'obtenir l'instance de la fabrique X509
openssl.addedClientCaCert=Ajout du certificat CA du client : [{0}]
openssl.applyConf=Application de OpenSSLConfCmd au contexte SSL
openssl.certificateVerificationFailed=La vérification du certificat a échoué
openssl.checkConf=Vérification de OpenSSLConf en cours
openssl.doubleInit=Le contexte SSL a déjà été initialisé, ignoré
openssl.errApplyConf=Impossible d'appliquer la OpenSSLConf au contexte SSL
openssl.errCheckConf=Erreur pendant la vérification de OpenSSLConf
openssl.errMakeConf=Impossible de créer le contexte de OpenSSLConf
openssl.errorSSLCtxInit=Erreur d'initialisation du contexte SSL
openssl.keyManagerMissing=Aucun gestionnaire de clés trouvé
openssl.makeConf=Création du contexte de OpenSSLConf
openssl.nonJsseCertificate=Le certificat [{0}] ou sa clé privée [{1}] n''a pas pu être traité en utilisant un gestionnaire de clé de JSSE, et sera directement passée à OpenSSL
openssl.nonJsseChain=La chaîne de certificat [{0}] n''a pas été spécifiée ou est invalide et JSSE requiert une chaîne de certificats valide, donc OpenSSL sera utilisé directement
openssl.trustManagerMissing=Gestionnaire de confiance non trouvé

opensslconf.applyCommand=Application de la commande OpenSSLConf (nom [{0}] valeur [{1}])
opensslconf.applyFailed=Erreur en appliquant OpenSSLConf au contexte SSL
opensslconf.checkCommand=Vérification de la commande OpenSSLConf (nom [{0}] valeur [{1}])
opensslconf.checkFailed=Echec de la vérification de OpenSSLConf
opensslconf.failedCommand=La commande OpenSSLConf (nom [{0}] valeur [{1}]) a échoué avec le résultat [{2}] qui sera ignoré
opensslconf.finishFailed=OpenSSLConf s''est terminé en échec avec le résultat [{0}]
opensslconf.noCommandName=Pas de nom de commande OpenSSLConf (valeur [{0}]), cela sera ignoré
opensslconf.resultCommand=La commande OpenSSLConf (nom [{0}] valeur [{1}]) a retourné [{2}]

sessionContext.nullTicketKeys=Clés nulles
