# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

serverContainer.addNotAllowed=이전에 등록된 엔드포인트들 중 하나라도 사용하려고 시도하게 되면, 엔드포인트들을 더 이상 등록할 수 없습니다.
serverContainer.configuratorFail=타입이 [{1}]인 POJO를 위한 타입 [{0}]의 Configurator를 생성하지 못했습니다.
serverContainer.duplicatePaths=여러 개의 엔드포인트들이 동일한 경로 [{0}]에 배치될 수 없습니다: 기존 엔드포인트는 [{1}]였으며 신규 엔드포인트는 [{2}]입니다.
serverContainer.encoderFail=타입이 [{0}]인 인코더를 생성할 수 없습니다.
serverContainer.failedDeployment=이전 배치의 실패로 인하여, 호스트 [{1}] 내에 경로 [{0}]의 웹 애플리케이션에 대한 웹소켓 엔드포인트들의 배치가 허용되지 않습니다.
serverContainer.missingAnnotation=클래스가 @ServerEndpoint로 annotate되어 있지 않기에, POJO 클래스 [{0}]을(를) 배치할 수 없습니다.
serverContainer.servletContextMissing=지정된 ServletContext가 없습니다.

upgradeUtil.incompatibleRsv=호환되지 않는 RSV 비트를 사용하여, Extension들이 지정되었습니다.

uriTemplate.duplicateParameter=허용되지 않는 경로에서, 파라미터 [{0}]이(가) 두번 이상 나타나고 있습니다.
uriTemplate.emptySegment=경로 [{0}]이(가), 하나 이상의 허용되지 않는 empty segment들을 포함하고 있습니다.
uriTemplate.invalidPath=경로 [{0}](은)는 유효하지 않습니다.
uriTemplate.invalidSegment=세그먼트 [{0}]은(는) 제공된 경로 [{1}] 내에 유효하지 않습니다.

wsFrameServer.bytesRead=[{0}] 바이트를 입력 버퍼에 읽어 처리를 준비합니다.
wsFrameServer.illegalReadState=예기치 않은 읽기 상태 [{0}]
wsFrameServer.onDataAvailable=메소드 엔트리

wsHttpUpgradeHandler.closeOnError=오류 발생으로 인하여, 웹소켓 연결을 닫습니다.
wsHttpUpgradeHandler.destroyFailed=웹소켓 HttpUpgradeHandler를 소멸시키는 중, WebConnection을 닫지 못했습니다.
wsHttpUpgradeHandler.noPreInit=컨테이너가 init()을 호출하기 전에 웹소켓 HttpUpgradeHandler를 설정하기 위하여, preInit() 메소드가 반드시 호출되어야만 합니다. 통상 이는 WsHttpUpgradeHandler 인스턴스를 생성한 서블릿도 preInit()을 호출해야 함을 의미합니다.
wsHttpUpgradeHandler.serverStop=서버가 중지되고 있는 중입니다.

wsRemoteEndpointServer.closeFailed=해당 ServletOutputStream의 연결을 깨끗하게 닫지 못했습니다.
