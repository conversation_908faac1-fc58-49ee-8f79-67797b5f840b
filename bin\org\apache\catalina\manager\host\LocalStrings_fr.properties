# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

hostManagerServlet.add=add : Ajouter l''hôte [{0}]
hostManagerServlet.addFailed=ECHEC - L''hôte [{0}] n''a pas pu être ajouté
hostManagerServlet.addSuccess=OK - L''hôte [{0}] a été ajouté
hostManagerServlet.alreadyHost=ECHEC - Un hôte existe déjà pour le nom [{0}]
hostManagerServlet.alreadyStarted=ECHEC - L''hôte [{0}] est déjà démarré
hostManagerServlet.alreadyStopped=ECHEC - L''hôte [{0}] est déjà arrêté
hostManagerServlet.appBaseCreateFail=ECHEC - Echec de création du répertoire de base (appBase) [{0}] de l''hôte [{1}]
hostManagerServlet.cannotRemoveOwnHost=ECHEC - Impossible de retirer son propre hôte
hostManagerServlet.cannotStartOwnHost=ECHEC - Ne peut pas démarrer son propre hôte
hostManagerServlet.cannotStopOwnHost=ECHEC - Impossible d'arrêter son propre hôte
hostManagerServlet.configBaseCreateFail=ECHEC - N''a pas pu trouver le configBase de l''hôte [{0}]
hostManagerServlet.exception=ECHEC - Exception [{0}]
hostManagerServlet.invalidHostName=ECHEC - Un nom d''hôte [{0}] invalide a été spécifié
hostManagerServlet.list=list : Liste des hôte pour le moteur [{0}]
hostManagerServlet.listed=OK - Liste des hôtes
hostManagerServlet.managerXml=ECHEC - "manager.xml" n'a pas pu être installé
hostManagerServlet.noCommand=ECHEC - Aucune commande n'a été spécifiée
hostManagerServlet.noHost=ECHEC - Le nom d''hôte [{0}] n''existe pas
hostManagerServlet.noWrapper=Le conteneur n'a pas appelé setWrapper() pour ce Servlet
hostManagerServlet.persist=persist : Persister la configuration actuelle
hostManagerServlet.persistFailed=ECHEC - N'a pas pu persister la configuration
hostManagerServlet.persisted=OK - Configuration persistée
hostManagerServlet.postCommand=ECHEC - Essai d''utilisation de GET pour la commande [{0}] mais POST est nécessaire
hostManagerServlet.remove=supprimer : suppression de l''hôte [{0}]
hostManagerServlet.removeFailed=ECHEC - N''a pas pu retirer l''hôte [{0}]
hostManagerServlet.removeSuccess=OK - L''hôte [{0}] a été enlevé
hostManagerServlet.start=start : Démarrage de l''hôte [{0}]
hostManagerServlet.startFailed=ECHEC - Impossible de démarrer l''hôte [{0}]
hostManagerServlet.started=OK - L''hôte [{0}] est démarré
hostManagerServlet.stop=stop : Arrêt de l''hôte [{0}]
hostManagerServlet.stopFailed=ECHEC - L''arrêt de l''hôte [{0}] a échoué
hostManagerServlet.stopped=OK - L''hôte [{0}] est arrêté
hostManagerServlet.unknownCommand=ECHEC - Commande inconnue [{0}]

htmlHostManagerServlet.addAliases=Alias :
htmlHostManagerServlet.addAppBase=Répertoire de base :
htmlHostManagerServlet.addAutoDeploy=Déploiement automatique
htmlHostManagerServlet.addButton=Ajouter
htmlHostManagerServlet.addCopyXML=Copier le XML
htmlHostManagerServlet.addDeployOnStartup=Déploiement au démarrage
htmlHostManagerServlet.addDeployXML=Déployer le XML
htmlHostManagerServlet.addHost=Hôte
htmlHostManagerServlet.addManager=App gestionnaire
htmlHostManagerServlet.addName=Nom :
htmlHostManagerServlet.addTitle=Ajouter un hôte virtuel (Virtual Host)
htmlHostManagerServlet.addUnpackWARs=Décompresser les WARs
htmlHostManagerServlet.helpHtmlManager=Aide HTML du manager d'hôte
htmlHostManagerServlet.helpHtmlManagerFile=../docs/html-host-manager-howto.html
htmlHostManagerServlet.helpManager=Aide du Gestionnaire d'Hôtes
htmlHostManagerServlet.helpManagerFile=../docs/host-manager-howto.html
htmlHostManagerServlet.hostAliases=Alias de l'hôte
htmlHostManagerServlet.hostName=Nom d'hôte
htmlHostManagerServlet.hostTasks=Commandes
htmlHostManagerServlet.hostThis=Gestionnaire d'Hôtes installé, commandes désactivées
htmlHostManagerServlet.hostsRemove=Retirer
htmlHostManagerServlet.hostsStart=Démarrer
htmlHostManagerServlet.hostsStop=Arrêt
htmlHostManagerServlet.list=Liste des Hôtes Virtuels
htmlHostManagerServlet.manager=Gestionaire d'hôte ("Host Manager")
htmlHostManagerServlet.messageLabel=Message :
htmlHostManagerServlet.persistAll=Enregistrer la configuration, y compris les hôtes virtuels, dans server.xml et les fichiers context.xml pour chaque application
htmlHostManagerServlet.persistAllButton=Tout
htmlHostManagerServlet.persistTitle=Persister la configuration
htmlHostManagerServlet.serverJVMVendor=Fournisseur de la JVM
htmlHostManagerServlet.serverJVMVersion=Version de la JVM
htmlHostManagerServlet.serverOSArch=Architecture du système
htmlHostManagerServlet.serverOSName=Nom de l''OS
htmlHostManagerServlet.serverOSVersion=Version de l'OS
htmlHostManagerServlet.serverTitle=Information sur le serveur
htmlHostManagerServlet.serverVersion=Version de Tomcat
htmlHostManagerServlet.title=Gestionnaire d'Hôtes Virtuels de Tomcat

statusServlet.complete=Etat complet du serveur
statusServlet.title=Etat du serveur
