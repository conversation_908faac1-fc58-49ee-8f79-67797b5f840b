# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

addDefaultCharset.unsupportedCharset=不支持指定的字符集[{0}]

corsFilter.invalidPreflightMaxAge=无法解析 preflightMaxAge
corsFilter.invalidSupportsCredentials=当allowedOrigins=[*]时，不允许配置supportsCredentials=[true]。
corsFilter.nullRequest=HttpServletRequest 对象为空
corsFilter.nullRequestType=CORSRequestType对象为空(null)
corsFilter.onlyHttp=CORS不支持非HTTP请求或响应
corsFilter.wrongType1=期望类型为[{0}]的HttpServletRequest对象
corsFilter.wrongType2=期望类型为[{0}]或[{1}]的HttpServletRequest对象

csrfPrevention.invalidRandomClass=不能使用class [{0}]创建随机源。

expiresFilter.exceptionProcessingParameter=异常处理配置参数[{0}]：[{1}]
expiresFilter.expirationHeaderAlreadyDefined=请求[{0}]的响应状态为[{1}]内容类型[{2}]，已经定义了到期标头
expiresFilter.filterInitialized=使用配置[{0}]初始化的筛选器
expiresFilter.invalidDurationNumber=指令[{1}]中的持续时间（数字）[{0}]无效
expiresFilter.invalidDurationUnit=指令[{1}]中的持续时间单位无效（年|月|周|天|小时|分钟|秒）[{0}]
expiresFilter.noDurationFound=在指令[{0}]中找不到持续时间
expiresFilter.noDurationUnitAfterAmount=在指令[{1}]中的amount[{0}]之后找不到持续时间单位
expiresFilter.noExpirationConfigured=请求[{0}]，其响应状态为[{1}]内容类型[{2}]，未配置到期日期
expiresFilter.noExpirationConfiguredForContentType=没有为 content-type [{0}] 找到过期配置
expiresFilter.numberError=分析逗号分隔列表[{1}中位置[{0}（基于零）处的数字时发生异常
expiresFilter.responseAlreadyCommitted=请求[{0}]，无法对已提交的响应应用ExpiresFilter。
expiresFilter.setExpirationDate=请求{0}，响应状态为{1}，内容类型为{2}，设置过期日期{3}
expiresFilter.skippedStatusCode=请求{0}，响应状态为{1}，内容类型为{1}，跳过给定状态的过期头生成。
expiresFilter.startingPointInvalid=在指令[{1}]中无效的起点(访问|现在|修改|a<秒>|m<秒>)[{0}]
expiresFilter.startingPointNotFound=起始点（access|now|modification|a<seconds>|m<seconds>）未在指令[{0}]中找到
expiresFilter.unknownParameterIgnored=忽略值为[{1}]的未知参数[{0}]！
expiresFilter.unsupportedStartingPoint=不支持的起始点 [{0}]
expiresFilter.useDefaultConfiguration=对内容类型[{1}]使用默认的[{0}]返回[{2}]
expiresFilter.useMatchingConfiguration=对内容类型[{2}]返回[{3}]使用[{0}]匹配[{1}]

filterbase.noSuchProperty=未为[{1}]类型的筛选器定义属性[{0}]

http.403=禁止访问指定资源 [{0}] 。

httpHeaderSecurityFilter.clickjack.invalid=为防点击挟持的响应消息头指定了非法值 [{0}]
httpHeaderSecurityFilter.committed=在进入HttpHeaderSecurityFilter的时候响应消息已经提交导致不能添加响应消息头

remoteCidrFilter.invalid=为{0}提供的配置无效。有关详细信息，请参阅以前的消息。
remoteCidrFilter.noRemoteIp=客户端没有 IP 地址，请求被拒绝。

remoteIpFilter.invalidHostHeader=HTTP头 [{1}]中，为Host找到一个无效值 [{0}]
remoteIpFilter.invalidHostWithPort=HTTP头 [{1}]中的Host值 [{0}]包含一个被忽略的端口号
remoteIpFilter.invalidNumber=参数[{0}]的数字非法：[{1}]

requestFilter.deny=基于属性:[{1}]，[{0}]的请求被拒绝。

restCsrfPreventionFilter.invalidNonce=CSRF nonce验证失败

webDavFilter.xpProblem=WebdavFixFilter:已知XP-x64-SP2客户端不使用WebDAV Servlet
webDavFilter.xpRootContext=WebdavFixFilter:XP-x64-SP2客户端将仅与根上下文一起工作
