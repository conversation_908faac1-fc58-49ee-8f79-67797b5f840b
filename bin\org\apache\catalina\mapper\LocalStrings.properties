# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

mapper.addHost.sameHost=Duplicate registration of the same host [{0}]. Ignored.
mapper.addHost.success=Registered host [{0}]
mapper.addHostAlias.sameHost=Duplicate registration of alias [{0}] for the same host [{1}]. Ignored.
mapper.addHostAlias.success=Registered alias [{0}] for host [{1}]
mapper.duplicateHost=Duplicate Host [{0}]. The name is already used by Host [{1}]. This Host will be ignored.
mapper.duplicateHostAlias=Duplicate host <PERSON><PERSON> [{0}] in Host [{1}]. The name is already used by Host [{2}]. This Alias will be ignored.
mapper.removeWrapper=Removing wrapper from Context [{0}] with path [{1}]

mapperListener.pauseContext=Register Context [{0}] as being reloaded for service [{1}]
mapperListener.registerContext=Register Context [{0}] for service [{1}]
mapperListener.registerHost=Register host [{0}] at domain [{1}] for service [{2}]
mapperListener.registerWrapper=Register Wrapper [{0}] in Context [{1}] for service [{2}]
mapperListener.unknownDefaultHost=Unknown default host [{0}] for service [{1}]. Tomcat will not be able process HTTP/1.0 requests that do not specify a host name.
mapperListener.unregisterContext=Unregister Context [{0}] for service [{1}]
mapperListener.unregisterHost=Unregister host [{0}] at domain [{1}] for service [{2}]
mapperListener.unregisterWrapper=Unregister Wrapper [{0}] in Context [{1}] for service [{2}]
