<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<body>

<p>This package contains <code>Realm</code> implementations for the
various supported realm technologies for authenticating users and
identifying their associated roles.  The <code>Realm</code> that is
associated with a web application's <code>Context</code> (or a hierarchically
superior Container) is used to resolve authentication and role presence
questions when a web application uses container managed security as described
in the Servlet API Specification.</p>

<p>The implementations share a common base class that supports basic
functionality for all of the standard <code>Realm</code> implementations.</p>

</body>
