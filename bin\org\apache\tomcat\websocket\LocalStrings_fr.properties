# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

asyncChannelGroup.createFail=Impossible de créer un AsynchronousChannelGroup dédié poour les clients Websockets ce qui est nécessaire pour éviter des fuites de mémoire dans un conteneur EE

asyncChannelWrapperSecure.check.notOk=La négociation TLS a renvoyé un état inattendu [{0}]
asyncChannelWrapperSecure.check.unwrap=Des octets ont été écrits sur la sortie pendant la lecture
asyncChannelWrapperSecure.check.wrap=Des octets ont été consommés depuis l'entrée lors d'une écriture
asyncChannelWrapperSecure.closeFail=Impossible de fermer proprement le canal
asyncChannelWrapperSecure.concurrentRead=Les opérations de lecture concurrentes ne sont pas permises
asyncChannelWrapperSecure.concurrentWrite=Les opérations d'écriture concurrentes ne sont pas permises
asyncChannelWrapperSecure.eof=Fin de flux inattendue
asyncChannelWrapperSecure.notHandshaking=Etat NOT_HANDSHAKING inattendu pendant la négociation TLS
asyncChannelWrapperSecure.statusUnwrap=Etat inattendu de SSLEngineResult après une opération unwrap()
asyncChannelWrapperSecure.statusWrap=Etat inattendu de SSLEngineResult après une opération wrap()
asyncChannelWrapperSecure.tooBig=Le résultat [{0}] est trop grand pour pouvoir être converti en Integer
asyncChannelWrapperSecure.wrongStateRead=L'indicateur de lecture en cours était faux alors qu'il aurait dû vrai lors d'une tentative pour terminer une opération de lecture
asyncChannelWrapperSecure.wrongStateWrite=L'indicateur d'écriture en cours était faux alors qu'il aurait dû vrai lors d'une tentative pour terminer une opération d'écriture

backgroundProcessManager.processFailed=Un processus d'arrière-plan a échoué

caseInsensitiveKeyMap.nullKey=Les clés nulles ne sont pas admises

futureToSendHandler.timeout=Le délai d''attente de l''opération est dépassé après avoir attendu [{0}] [{1}] pour qu''elle se termine

perMessageDeflate.alreadyClosed=Le transformateur a été fermé et ne peut plus être utilisé
perMessageDeflate.deflateFailed=Impossible de décompresser une trame WebSocket compressée
perMessageDeflate.duplicateParameter=Double définition pour le paramètre d''extension [{0}]
perMessageDeflate.invalidState=Etat invalide
perMessageDeflate.invalidWindowSize=Une taille [{1}] de fenêtre invalide a été spécifiée pour [{0}], les valeurs valides sont les entiers de 8 à 15 inclus
perMessageDeflate.unknownParameter=Un paramètre d''extension inconnu [{0}] a été défini

transformerFactory.unsupportedExtension=L''extension [{0}] n''est pas supportée

util.invalidMessageHandler=Le gestionnaire de messages fourni n'a pas de méthode onMessage(Object)
util.invalidType=Incapable de convertir la valeur [{0}] en le type [{1}]. Ce type n''est pas supporté.
util.notToken=Un paramètre d''extension illégal a été spécifié avec le nom [{0}] et la valeur [{1}]
util.unknownDecoderType=Le Decoder de type [{0}] n''est pas reconnu

wsFrame.alreadyResumed=Le message reçu a déjà été suspendu puis recommencé
wsFrame.alreadySuspended=La réception des messages a déjà été suspendue
wsFrame.bufferTooSmall=Le tampon de taille [{0}] est trop petit pour le message de taille [{1}] et les messages asynchrones ne sont pas supportés
wsFrame.byteToLongFail=trop d''octets fournis ([{0}]) pour une conversion vers "long"
wsFrame.closed=Nouvelle trame (frame) reçue après une trame de contrôle de fermeture
wsFrame.controlFragmented=Une trame de contrôle fragmentée a été reçue mais les trames de contrôle ne peuvent pas être fragmentées
wsFrame.controlNoFin=Une trame de contrôle qui a été envoyée n'avait pas le bit fin mis, alors qu'elles ne peuvent pas utiliser de trame de continuation
wsFrame.controlPayloadTooBig=Une trame de contrôle a été envoyée avec des données de taille [{0}] ce qui est supérieur aux 125 octets autorisés au maximum
wsFrame.illegalReadState=Etat en lecture inattendu [{0}]
wsFrame.invalidOpCode=Une trame a été envoyée avec un opCode non reconnu [{0}]
wsFrame.invalidUtf8=Une trame texte Websocket a été reçue et n'a pu 6etre traitée car elle contenait des séquence d'octets UTF-8 invalides
wsFrame.invalidUtf8Close=Une trame de fermeture Websocket a été reçue avec une cause qui contenait des séquences UTF-8 invalides
wsFrame.ioeTriggeredClose=Une IOException non récupérable est survenue donc la connection a été fermée
wsFrame.messageTooBig=Le message fait [{0}] octets mais le MessageHandler a une limite de [{1}] octets
wsFrame.noContinuation=Un nouveau message a été démarré quand une trame de continuation était attendue
wsFrame.notMasked=La trame du client n'a pas de masque alors que toutes les trames des clients doivent en avoir un
wsFrame.oneByteCloseCode=Le client a envoyé une trame de fermeture avec un octet de données ce qui est invalide
wsFrame.partialHeaderComplete=Une trame Websocket a été recue, fin [{0}], rsv [{1}], opCode [{2}], taille de données [{3}]
wsFrame.payloadMsbInvalid=Une trame WebSocket invalide a été reçue, le bit le plus significatif d'un bloc de 64 bits ne peut être mis
wsFrame.readFailed=La lecture asynchrone du client a échoué
wsFrame.sessionClosed=Les données du client ne peuvent pas être traitées car la session a déjà été fermée
wsFrame.suspendRequested=La suspension de la réception des messages a déjà été demandée
wsFrame.textMessageTooBig=Le message texte décodé était trop grand pour le tampon de sortie et la terminaison ne supporte pas les messages partiels
wsFrame.wrongRsv=La trame cliente (client frame) a les bits réservés d''un message dont l''opCode est [{1}] définis à [{0}], et ce n''est pas supporté par cette terminaison

wsFrameClient.ioe=Echec lors de la lecture des données envoyées par le serveur

wsHandshakeRequest.invalidUri=La chaîne de caractères [{0}] ne peut être utilisée pour construire un URL valide
wsHandshakeRequest.unknownScheme=Le schéma [{0}] de la requête n''est pas reconnu

wsRemoteEndpoint.acquireTimeout=Le message en cours n'a pas été complètement envoyé dans le délai imparti
wsRemoteEndpoint.changeType=Quand un message fragmenté est envoyé, tous les fragments doivent être de même type
wsRemoteEndpoint.closed=Le message ne sera pas envoyé parce que la session WebSocket a été fermée
wsRemoteEndpoint.closedDuringMessage=Le reste du message ne sera pas envoyé parce que la session WebSocket est déjà fermée.
wsRemoteEndpoint.closedOutputStream=La méthode ne peut pas être appelée alors que l'OutputStream a été fermée
wsRemoteEndpoint.closedWriter=Cette méthode ne doit pas être appelée car le Writer a été fermé
wsRemoteEndpoint.flushOnCloseFailed=Le groupement de messages est toujours actif après fermeture de la session, impossible d'envoyer les messages restants
wsRemoteEndpoint.invalidEncoder=L''encodeur spécifié de type [{0}] n''a pu être instancié
wsRemoteEndpoint.noEncoder=Pas d''encodeur spécifié pour un objet de classe [{0}]
wsRemoteEndpoint.nullData=Argument nul invalide.
wsRemoteEndpoint.nullHandler=Argument null invalide pour le gestionnaire
wsRemoteEndpoint.sendInterrupt=Le thread actuel a été interrompu alors qu'il attendait qu'un envoi bloquant ne se termine
wsRemoteEndpoint.tooMuchData=Un ping ou pong ne peut pas envoyer plus de 125 octets
wsRemoteEndpoint.writeTimeout=Délai d'attente dépassé pour l'écriture bloquante
wsRemoteEndpoint.wrongState=La terminaison distante est dans l''état [{0}] ce qui est invalide pour la méthode appelée

wsSession.closed=La session WebSocket [{0}] a été fermée et aucune méthode (à part close()) ne peut être appelée sur une session fermée
wsSession.created=Création de la session WebSocket [{0}]
wsSession.doClose=Fermeture de la session WebSocket [{1}]
wsSession.duplicateHandlerBinary=Un gestionnaire de message binaire a déjà été configuré
wsSession.duplicateHandlerPong=Un gestionnaire de messages pong a déjà été configuré
wsSession.duplicateHandlerText=Un gestionnaire de message texte a déjà été configuré
wsSession.flushFailOnClose=Impossible d'envoyer la file de messages lors de la fermeture de la session
wsSession.instanceNew=L'enregistrement de l'instance de la terminaison a échoué
wsSession.invalidHandlerTypePong=Un gestionnaire de message pong doit implémenter MessageHandler.Whole
wsSession.messageFailed=Impossible d'écrire le message WebSocket complet car la connection a été fermée
wsSession.removeHandlerFailed=Impossible d''enlever le gestionnaire [{0}] car il n''était pas enregistré dans la session
wsSession.sendCloseFail=Impossible d''envoyer le message de fermeture pour la session [{0}] à la terminaison distante
wsSession.timeout=Le délai d''attente maximum de la session WebSocket [{0}] a été dépassé
wsSession.timeoutRead=Le délai d''inactivité en lecture de la session WebSocket [{0}] a expiré
wsSession.timeoutWrite=Le délai d''inactivité en écriture de la session WebSocket [{0}] a expiré
wsSession.unknownHandler=Impossible d''ajouter le gestionnaire de messages [{0}] pour le type non reconnu [{1}]
wsSession.unknownHandlerType=Incapable d''ajouter le gestionnaire de messages [{0}] puisqu''il est enveloppé (wrapped) comme le type non reconnu [{1}]

wsWebSocketContainer.asynchronousSocketChannelFail=Impossible d'ouvrir une connection vers le serveur
wsWebSocketContainer.connect.entry=Connection à l''instance d''endpoint de type [{0}] à [{1}]
wsWebSocketContainer.connect.write=Ecriture de la requête d''upgrade HTTP depuis le tampon à partir de [{0}] avec une limite de [{1}] depuis l''adresse locale [{2}]
wsWebSocketContainer.defaultConfiguratorFail=Impossible de créer le configurateur par défaut
wsWebSocketContainer.endpointCreateFail=Echec de création d''un point d''entrée local de type [{0}]
wsWebSocketContainer.failedAuthentication=Echec du traitement du code de réponse HTTP [{0}], l''en-tête d''authentification n''a pas été accepté par le serveur
wsWebSocketContainer.httpRequestFailed=La requête HTTP pour initier la connection WebSocket a échoué
wsWebSocketContainer.invalidExtensionParameters=Le serveur a répondu avec des paramètres d'extension que le client n'est pas capable de traiter
wsWebSocketContainer.invalidHeader=Impossible de traiter l''en-tête HTTP car deux-points n''est pas présents pour délimiter le nom et la valeur dans [{0}], l''en-tête a été sauté
wsWebSocketContainer.invalidStatus=La réponse HTTP du serveur [{0}] n''a pas permis une mise à niveau de HTTP vers WebSocket
wsWebSocketContainer.invalidSubProtocol=Le serveur WebSocket a renvoyé plusieurs valeurs pour l'en-tête Sec-WebSocket-Protocol
wsWebSocketContainer.maxBuffer=L'implémentation limites la valeur maximale d'un tampon à Integer.MAX_VALUE
wsWebSocketContainer.missingAnnotation=Impossible d''utiliser la classe POJO [{0}] car elle n''est pas annotée avec @ClientEndpoint
wsWebSocketContainer.missingLocationHeader=Echec du traitement du code de réponse HTTP [{0}], l''en-tête location n''est pas présent dans la réponse
wsWebSocketContainer.missingWWWAuthenticateHeader=Echec de traitement du code HTTP de réponse [{0}] : la réponse ne contient pas de header WWW-Authenticate.
wsWebSocketContainer.pathNoHost=Aucun hôte n'est spécifié dans l'URI
wsWebSocketContainer.pathWrongScheme=Le schéma [{0}] n''est pas supporté, seuls sont supportés ws et wss
wsWebSocketContainer.proxyConnectFail=Impossible de se connecter au Proxy [{0}] configuré, le code HTTP de la réponse est [{0}]
wsWebSocketContainer.redirectThreshold=L''en-tête Location [{0}] est cyclique, le nombre de redirections [{1}] a été atteint sur le maximum [{2}]
wsWebSocketContainer.responseFail=L''upgrade de HTTP vers WebSocket a échouée mais des données partielles peuvent avoir été reçues: Status Code [{0}], En têtes HTTP [{1}]
wsWebSocketContainer.sessionCloseFail=La session avec ID  [{0}] n''a pas été fermée proprement.
wsWebSocketContainer.shutdown=L'application web s'arrête
wsWebSocketContainer.sslEngineFail=Impossible de créer un SSLEngine pour supporter les connections TLS
wsWebSocketContainer.unsupportedAuthScheme=Impossible de gérer le code de réponse HTTP [{0}], un schéma authentification [{1}] non supporté a été retourné
