# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jasper.error.emptybodycontent.nonempty=Nach der TLD muss [{0}] leer sein, ist es aber nicht

jsp.error.action.isnottagfile=[{0}] Action kann nur in Tag-Dateien benutzt werden
jsp.error.attribute.deferredmix=Kann nicht ${} und #{} gleichzeitig als EL Ausdrücke in demselben Attribut Wert verwenden
jsp.error.attribute.duplicate=Qualifizierte Attributnamen müssen innerhalb eines Elements eindeutig sein
jsp.error.attribute.noequal=Gleichheitszeichen erwartet
jsp.error.attribute.nowhitespace=Die JSP Spezifikation verlangt, dass einem Attribut Namen ein Leerzeichen vorangeht.
jsp.error.compiler=Keine Java-Compiler verfügbar
jsp.error.data.file.processing=Fehler beim Verarbeiten der Datei [{0}]
jsp.error.el.template.deferred=#{...} is im Template Text nicht erlaubt
jsp.error.fallback.invalidUse=jsp:fallback muss ein direktes Kind von jsp:plugin sein
jsp.error.file.not.found=Datei [{0}] nicht gefunden
jsp.error.internal.filenotfound=Interner Fehler: Datei [{0}] nicht gefunden
jsp.error.invalid.attribute=[{0}] hat ein ungültiges Attribut: [{1}]
jsp.error.invalid.tagdir=Tag Verzeichnis [{0}] beginnt nicht mit "/WEB-INF/tags"
jsp.error.invalid.version=Ungültige JSP Version für tag-Datei in [{0}] definiert
jsp.error.ise_on_clear=Nicht erlaubter Aufruf von clear() wenn Puffer-Größe == 0
jsp.error.jspbody.emptybody.only=Das [{0}] Tag kann nur jsp:attribute im Body haben.
jsp.error.jspbody.invalidUse=jsp:body muss ein Unterelement einer Standard- oder Custom-Action sein
jsp.error.jspbody.required=jsp:body muss für den Tag Inhalt für [{0}] genutzt werden, wenn jsp:attribute benutzt wird
jsp.error.jspelement.missing.name=Das erforderliche XML Attribut 'name' fehlt
jsp.error.location=Zeile: [{0}], Spalte: [{1}]
jsp.error.mandatory.attribute=[{0}]: Zwingend anzugebendes Attribut [{1}] fehlt
jsp.error.no.scratch.dir=Die JSP-Engine ist nicht mir einem leeren Verzeichnis konfiguriert. Bitte fügen Sie "jsp.initparams=scratchdir=<verzeichnisname>" in die servlets.properties Datei für diesen Kontext.
jsp.error.no.scriptlets=Die Skript-Elemente ( &lt;%!, &lt;jsp:declaration, &lt;%=, &lt;jsp:expression, &lt;%, &lt;jsp:scriptlet ) sind hier nicht erlaubt.
jsp.error.noFunction=Die Funktion [{0}] kann mit dem angegebenen Prefix nicht gefunden werden
jsp.error.not.in.template=[{0}] ist nicht im Body des Template Textes erlaubt
jsp.error.outputfolder=kein Ausgabeordner
jsp.error.parse.xml=Fehler bei der Verarbeitung der XML Datei [{0}]
jsp.error.scripting.variable.missing_name=Kann den Namen der Skript Variable vom Attribut [{0}] ableiten
jsp.error.simpletag.badbodycontent=Die TLD für Klasse [{0}] spezifiziert einen ungültigen Body-Content (JSP) für ein SimpleTag.
jsp.error.taglibDirective.absUriCannotBeResolved=Die absolute URI: [{0}] kann weder durch web.xml noch durch die JAR-Files dieser Anwendung aufgelöst werden
jsp.error.taglibDirective.missing.location=Weder 'uri' noch 'tagdir' Attribute sind angegeben
jsp.error.taglibDirective.uriInvalid=Die URI, die für die Tag Bibliothek [{0}] zur Verfügung gestellt wurde, ist keine gültige URI
jsp.error.tld.mandatory.element.missing=Notwendiges TLD Element  [{0}] fehlt oder ist leer in TLD  [{1}]
jsp.error.unable.deleteClassFile=Klassendatei konnte nicht gelöscht werden
jsp.error.unable.renameClassFile=Fehler beim Umbenennen der Klassendatei von [{0}] in [{1}]
jsp.error.unable.to_find_method=Keine Setter Methode für das Attribut [{0}] gefunden.
jsp.error.unavailable=JSP wurde als nicht verfügbar markiert
jsp.error.unknown_attribute_type=Unbekannter Attributstyp [{1}] für Attribut [{0}].
jsp.error.xml.badStandardAction=Ungültige Standard Aktion: [{0}]
jsp.exception=Beim Verarbeiten von [{0}] ist in Zeile [{1}] eine Ausnahme erzeugt worden
jsp.info.ignoreSetting=Ignoriere Einstellung für [{0}] von [{1}], da ein SecurityManager eingeschaltet war
jsp.message.jsp_queue_update=Passe Queue im Kontext [{1}] für JSP mit Pfad [{0}] an
jsp.message.jsp_removed_excess=Lösche überschüssige JSP für Pfad [{0}] aus der Warteschlange für Context [{1}]
jsp.warning.enablePooling=Warnung: Ungültiger Wert für den initParam enablePooling. Benutze den Standard-Wert "true"
jsp.warning.unknown.targetVM=Unbekannte Ziel-VM [{0}] ignoriert

jspc.webfrg.footer=\n\
</web-fragment>\n\
\n
jspc.webfrg.header=<?xml version="1.0" encoding="{0}"?>\n\
<web-fragment xmlns="http://xmlns.jcp.org/xml/ns/javaee"\n\
\              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"\n\
\              xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee\n\
\                      http://xmlns.jcp.org/xml/ns/javaee/web-fragment_3_1.xsd"\n\
\              version="3.1"\n\
\              metadata-complete="true">\n\
\  <name>org_apache_jasper.jspc</name>\n\
\  <distributable/>\n\
<!--\n\
Automatisch erstellt von Apache Tomcat JspC.\n\
-->\n\
\n
jspc.webinc.header=\n\
<!--\n\
Automatisch erstellt von Apache Tomcat JspC.\n\
-->\n\
\n
jspc.webxml.footer=\n\
</web-app>\n\
\n
jspc.webxml.header=<?xml version="1.0" encoding="{0}"?>\n\
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"\n\
\         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"\n\
\         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee\n\
\                 http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd"\n\
\         version="3.1"\n\
\         metadata-complete="false">\n\
<!--\n\
Automatisch erstellt durch Apache Tomcat JspC.\n\
-->\n\
\n

org.apache.jasper.compiler.TldCache.servletContextNull=Der angegebene ServletContext war null
