# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

coyoteAdapter.accesslogFail=Exception while attempting to add an entry to the access log
coyoteAdapter.asyncDispatch=Exception while processing an asynchronous request
coyoteAdapter.authenticate=Authenticated user [{0}] provided by connector
coyoteAdapter.authorize=Authorizing user [{0}] using Tomcat''s Realm
coyoteAdapter.checkRecycled.request=Encountered a non-recycled request and recycled it forcedly.
coyoteAdapter.checkRecycled.response=Encountered a non-recycled response and recycled it forcedly.
coyoteAdapter.debug=The variable [{0}] has value [{1}]
coyoteAdapter.nullRequest=An asynchronous dispatch may only happen on an existing request

coyoteConnector.invalidEncoding=The encoding [{0}] is not recognised by the JRE. The Connector will continue to use [{1}]
coyoteConnector.invalidPort=The connector cannot start since the specified port value of [{0}] is invalid
coyoteConnector.notAsciiSuperset=The encoding [{0}] is not a superset of ASCII as required by RFC 7230. This may have unexpected side effects
coyoteConnector.parseBodyMethodNoTrace=TRACE method MUST NOT include an entity (see RFC 2616 Section 9.6)
coyoteConnector.protocolHandlerDestroyFailed=Protocol handler destroy failed
coyoteConnector.protocolHandlerInitializationFailed=Protocol handler initialization failed
coyoteConnector.protocolHandlerInstantiationFailed=Protocol handler instantiation failed
coyoteConnector.protocolHandlerNoAprLibrary=The configured protocol [{0}] requires the APR/native library which is not available
coyoteConnector.protocolHandlerNoAprListener=The configured protocol [{0}] requires the AprLifecycleListener which is not available
coyoteConnector.protocolHandlerPauseFailed=Protocol handler pause failed
coyoteConnector.protocolHandlerResumeFailed=Protocol handler resume failed
coyoteConnector.protocolHandlerStartFailed=Protocol handler start failed
coyoteConnector.protocolHandlerStopFailed=Protocol handler stop failed

coyoteInputStream.nbNotready=In non-blocking mode you may not read from the ServletInputStream until the previous read has completed and isReady() returns true

coyoteOutputStream.nbNotready=In non-blocking mode you may not write to the ServletOutputStream until the previous write has completed and isReady() returns true

coyoteRequest.alreadyAuthenticated=This request has already been authenticated
coyoteRequest.attributeEvent=Exception thrown by attributes event listener
coyoteRequest.authenticate.ise=Cannot call authenticate() after the response has been committed
coyoteRequest.changeSessionId=Cannot change session ID. There is no session associated with this request.
coyoteRequest.chunkedPostTooLarge=Parameters were not parsed because the size of the posted data was too big. Because this request was a chunked request, it could not be processed further. Use the maxPostSize attribute of the connector to resolve this if the application should accept large POSTs.
coyoteRequest.filterAsyncSupportUnknown=Unable to determine if any filters do not support async processing
coyoteRequest.getContextPath.ise=Unable to find match between the canonical context path [{0}] and the URI presented by the user agent [{1}]
coyoteRequest.getInputStream.ise=getReader() has already been called for this request
coyoteRequest.getReader.ise=getInputStream() has already been called for this request
coyoteRequest.gssLifetimeFail=Failed to obtain remaining lifetime for user principal [{0}]
coyoteRequest.maxPostSizeExceeded=The multi-part request contained parameter data (excluding uploaded files) that exceeded the limit for maxPostSize set on the associated connector
coyoteRequest.noAsync=Unable to start async because the following classes in the processing chain do not support async [{0}]
coyoteRequest.noMultipartConfig=Unable to process parts as no multi-part configuration has been provided
coyoteRequest.parseParameters=Exception thrown whilst processing POSTed parameters
coyoteRequest.postTooLarge=Parameters were not parsed because the size of the posted data was too big. Use the maxPostSize attribute of the connector to resolve this if the application should accept large POSTs.
coyoteRequest.sendfileNotCanonical=Unable to determine canonical name of file [{0}] specified for use with sendfile
coyoteRequest.sessionCreateCommitted=Cannot create a session after the response has been committed
coyoteRequest.sessionEndAccessFail=Exception triggered ending access to session while recycling request
coyoteRequest.setAttribute.namenull=Cannot call setAttribute with a null name
coyoteRequest.uploadCreate=Creating the temporary upload location [{0}] as it is required by the servlet [{1}]
coyoteRequest.uploadCreateFail=Failed to create the upload location [{0}]
coyoteRequest.uploadLocationInvalid=The temporary upload location [{0}] is not valid

coyoteResponse.encoding.invalid=The encoding [{0}] is not recognised by the JRE
coyoteResponse.getOutputStream.ise=getWriter() has already been called for this response
coyoteResponse.getWriter.ise=getOutputStream() has already been called for this response
coyoteResponse.reset.ise=Cannot call reset() after response has been committed
coyoteResponse.resetBuffer.ise=Cannot reset buffer after response has been committed
coyoteResponse.sendError.ise=Cannot call sendError() after the response has been committed
coyoteResponse.sendRedirect.ise=Cannot call sendRedirect() after the response has been committed
coyoteResponse.sendRedirect.note=<html><body><p>Redirecting to <a href="{0}">{0}</a></p></body></html>
coyoteResponse.setBufferSize.ise=Cannot change buffer size after data has been written

inputBuffer.requiresNonBlocking=Not available in non blocking mode
inputBuffer.streamClosed=Stream closed

outputBuffer.writeNull=The String argument to write(String,int,int) may not be null

request.asyncNotSupported=A filter or servlet of the current chain does not support asynchronous operations.
request.fragmentInDispatchPath=The fragment in dispatch path [{0}] has been removed
request.illegalWrap=The request wrapper must wrap the request obtained from getRequest()
request.notAsync=It is illegal to call this method if the current request is not in asynchronous mode (i.e. isAsyncStarted() returns false)
request.session.failed=Failed to load session [{0}] due to [{1}]

requestFacade.nullRequest=The request object has been recycled and is no longer associated with this facade

response.illegalWrap=The response wrapper must wrap the response obtained from getResponse()
response.sendRedirectFail=Failed to redirect to [{0}]

responseFacade.nullResponse=The response object has been recycled and is no longer associated with this facade
