# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jmxRemoteLifecycleListener.createRegistryFailed=无法使用端口[{1}]为[{0}]服务器创建RMI注册表
jmxRemoteLifecycleListener.createServerFailed=无法为[{0}]服务器创建JMX连接器服务器或启动失败
jmxRemoteLifecycleListener.invalidURL=为[{0}]服务器[{1}]请求的JMX服务URL无效

mBeanFactory.managerContext=管理器组件只能添加到上下文中。
