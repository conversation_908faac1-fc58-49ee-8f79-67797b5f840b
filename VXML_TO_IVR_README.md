# VXML 转 IVR JSON 工具

## 概述

`VxmlToIvrUtil` 是一个用于将 VXML (Voice XML) 文件转换为 IVR JSON 配置的 Java 工具类。该工具可以解析标准的 VXML 文件，并生成与现有 IVR 系统兼容的 JSON 配置文件。

## 功能特性

- **完整的 VXML 解析**: 支持解析 VXML 中的 form、block、field、prompt、grammar、filled 等元素
- **智能节点生成**: 自动生成开始节点、结束节点、用户任务节点和服务任务节点
- **条件逻辑转换**: 将 VXML 中的条件判断转换为 IVR 的连线表达式
- **语音内容提取**: 自动提取 prompt 中的语音播放内容
- **参数映射**: 将 VXML 的 field 参数映射为 IVR 的服务任务参数
- **完整的 JSON 结构**: 生成包含所有必需字段的完整 IVR JSON 配置

## 使用方法

### 基本用法

```java
// 转换 VXML 文件为 IVR JSON
String jsonResult = VxmlToIvrUtil.convertVxmlToIvrJson("path/to/input.vxml");

// 保存到文件
VxmlToIvrUtil.convertVxmlToIvrJsonFile("path/to/input.vxml", "path/to/output.json");
```

### 命令行使用

```bash
# 编译
javac -encoding UTF-8 -cp "lib/json-20160810.jar;E:/work/server/mars/lib/fastjson-1.2.62.jar" -d bin src/com/yq/cc/vxml/VxmlToIvrUtil.java

# 运行
java -cp "bin;lib/json-20160810.jar;E:/work/server/mars/lib/fastjson-1.2.62.jar" com.yq.cc.vxml.VxmlToIvrUtil
```

## 转换映射规则

### VXML 元素到 IVR 节点的映射

| VXML 元素 | IVR 节点类型 | 说明 |
|-----------|-------------|------|
| `<form>` | 流程容器 | 包含整个 IVR 流程 |
| `<block>` | userTask | 语音播放节点 |
| `<field>` | serviceTask | 用户输入收集节点 |
| `<prompt>` | 语音内容 | 提取为节点的语音播放内容 |
| 独立 `<block id="endBlock">` | end | 结束节点 |

### 条件逻辑转换

VXML 中的条件判断：
```xml
<filled>
    <if cond="service_task_result == '1'">
        <prompt>感谢您对我们的评价，再见！</prompt>
        <goto next="#endBlock"/>
    </if>
</filled>
```

转换为 IVR 连线表达式：
```json
{
    "lx_tj": "eq",
    "lx_cs": "SERVICE_TASK_RESULT",
    "lx_fz": "1",
    "expression": {
        "param": "SERVICE_TASK_RESULT",
        "conditions": "eq",
        "val1": "1"
    }
}
```

## 生成的 JSON 结构

### 基本信息
- `ivrCode`: 自动生成的 IVR 代码
- `ivrName`: IVR 流程名称
- `ivrType`: IVR 类型（默认为 "1"）
- `servicePhoneId`: 服务电话 ID 数组

### 节点列表 (nodeList)
每个节点包含：
- `id`: 唯一标识符
- `name`: 节点名称
- `type`: 节点类型 (start/end/userTask/serviceTask)
- `color`: 节点颜色
- `ico`: 节点图标
- `top/left`: 节点位置
- `multiInstance`: 多实例配置
- `systemConfig`: 系统配置

### 连线列表 (lineList)
每条连线包含：
- `id`: 唯一标识符
- `from/to`: 起始和目标节点 ID
- `label`: 连线标签
- `expression`: 条件表达式（条件连线）
- `line`: 连线对象配置

## 测试验证

运行测试类验证转换结果：

```bash
java -cp "bin;lib/json-20160810.jar;E:/work/server/mars/lib/fastjson-1.2.62.jar" com.yq.cc.vxml.VxmlToIvrUtilTest
```

测试内容包括：
- 转换功能测试
- JSON 结构验证
- 节点类型统计
- 连线类型验证

## 示例

### 输入 VXML
```xml
<?xml version="1.0" encoding="UTF-8"?>
<vxml version="2.1">
    <form id="satisfactionSurvey">
        <block>
            <prompt>客户您好，这里是10086热线，请问您对我们的服务满意吗？</prompt>
        </block>
        
        <block>
            <prompt>满意请按1，否则请挂机。</prompt>
        </block>
        
        <field name="service_task_result" type="digits?length=1">
            <filled>
                <if cond="service_task_result == '1'">
                    <prompt>感谢您对我们的评价，再见！</prompt>
                    <goto next="#endBlock"/>
                </if>
            </filled>
        </field>
    </form>
    
    <block id="endBlock">
        <prompt>通话结束</prompt>
    </block>
</vxml>
```

### 输出 IVR JSON
生成包含 6 个节点（1个开始，1个结束，3个用户任务，1个服务任务）和 4 条连线（3条普通连线，1条条件连线）的完整 IVR JSON 配置。

## 依赖项

- Java 8+
- FastJSON 1.2.62+
- JSON 库 (json-20160810.jar)

## 注意事项

1. 确保 VXML 文件格式正确且符合 W3C 标准
2. 生成的 JSON 使用 UTF-8 编码
3. 节点 ID 和连线 ID 使用时间戳生成，确保唯一性
4. 支持的 VXML 条件操作符：`==` (转换为 eq) 和 `!=` (转换为 neq)
