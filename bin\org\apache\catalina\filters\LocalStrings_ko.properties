# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

addDefaultCharset.unsupportedCharset=지정된 문자셋 [{0}]은(는) 지원되지 않습니다.

corsFilter.invalidPreflightMaxAge=preflightMaxAge를 파싱할 수 없습니다.
corsFilter.invalidSupportsCredentials=allowedOrigins=[*]인 상태일 때, supportsCredentials=[true]로 설정하는 것은 허용되지 않습니다.
corsFilter.nullRequest=HttpServletRequest 객체가 널입니다.
corsFilter.nullRequestType=CORSRequestType 객체가 널입니다.
corsFilter.onlyHttp=CORS는 HttpServletRequest나 HttpServletResponse가 아닌 요청 또는 응답을 지원하지 않습니다.
corsFilter.wrongType1=타입이 [{0}]인 HttpServletRequest 객체가 요구됩니다.
corsFilter.wrongType2=타입이 [{0}]이거나 [{1}]인 HttpServletRequest 객체가 요구됩니다.

csrfPrevention.invalidRandomClass=randomClass로 설정된 클래스 [{0}]을(를) 사용하여, java.util.Random 객체를 생성할 수 없습니다.

expiresFilter.exceptionProcessingParameter=설정 파라미터를 처리하는 중 예외 발생 (이름:[{0}], 값:[{1}])
expiresFilter.expirationHeaderAlreadyDefined=요청 [{0}]에 대한 응답 상태 [{1}], content-type [{2}], expiration 헤더는 이미 정의되었음.
expiresFilter.filterInitialized=설정 [{0}]와(과) 함께 필터가 초기화 되었습니다.
expiresFilter.invalidDurationNumber=지시어 [{1}] 내에서 유효하지 않은 duration (숫자) 값: [{0}]
expiresFilter.invalidDurationUnit=지시어 [{1}] 내에서 유효하지 않은 지속 시간 단위 (years|months|weeks|days|hours|minutes|seconds) [{0}]
expiresFilter.noDurationFound=지시어 [{0}] 내에서 duration을 찾을 수 없습니다.
expiresFilter.noDurationUnitAfterAmount=지시어 [{1}] 내에서, 수량 값 [{0}] 이후에, 지속시간 단위를 찾을 수 없습니다.
expiresFilter.noExpirationConfigured=요청 [{0}]에 대하여, 응답 상태: [{1}], content-type: [{2}], expiration헤더는 설정 안됨.
expiresFilter.noExpirationConfiguredForContentType=Content-type [{0}]을(를) 위한 Expires 설정이 존재하지 않습니다.
expiresFilter.numberError=쉼표로 구분된 목록 [{1}] 내의, [{0}]번째 (첫번째 인덱스는 0) 숫자를 파싱하는 중 예외 발생
expiresFilter.responseAlreadyCommitted=요청 [{0}]: 이미 커밋된 응답에 ExpiresFilter를 적용할 수 없습니다.
expiresFilter.setExpirationDate=요청: [{0}], 응답 상태: [{1}], Content-Type: [{2}]. 만료 시간 설정: [{3}]
expiresFilter.skippedStatusCode=응답 상태가 [{1}](이)고 Content-Type이 [{1}]인 요청 [{0}]에 대하여, 주어진 상태를 고려하여 Expiration 헤더 생성을 건너뜁니다.
expiresFilter.startingPointInvalid=지시어 [{1}]에 있는 [{0}]은(는) Expiration 설정의 시작 점 (access|now|modification|a<seconds>|m<seconds>)로서 유효하지 않습니다.
expiresFilter.startingPointNotFound=지시어 [{0}] 내에서 시작 점(access|now|modification|a<seconds>|m<seconds>)을 찾을 수 없습니다.
expiresFilter.unknownParameterIgnored=값이 [{1}]인 알 수 없는 파라미터 [{0}]은(는) 무시됩니다!
expiresFilter.unsupportedStartingPoint=지원되지 않는 시작 점 [{0}]
expiresFilter.useDefaultConfiguration=Content-Type [{1}]을(를) 위해, 기본값 [{0}]을(를) 사용하여 [{2}]을(를) 반환합니다.
expiresFilter.useMatchingConfiguration=[{0}]을(를) 사용하여 Content-type [{2}]이(가) [{1}]와(과) 부합되는지 점검하여 [{3}]을(를) 반환합니다.

filterbase.noSuchProperty=타입이 [{1}]인 필터들에 프로퍼티 [{0}]이(가) 정의되지 않았습니다.

http.403=지정된 리소스 [{0}]에 접근하는 것이 금지되어 있습니다.

httpHeaderSecurityFilter.clickjack.invalid=Anti-clickjacking 헤더로 유효하지 않은 값 [{0}]이(가) 지정되었습니다.
httpHeaderSecurityFilter.committed=HttpHeaderSecurityFilter에 진입할 때에, 응답이 이미 커밋되었기 때문에, HTTP 헤더들을 추가할 수 없습니다.

remoteCidrFilter.invalid=[{0}]을(를) 위해, 유효하지 않은 설정이 제공되었습니다. 상세 정보는 이전 메시지들을 확인하십시오.
remoteCidrFilter.noRemoteIp=클라이언트가 IP 주소를 가지고 있지 않습니다. 요청이 거절되었습니다.

remoteIpFilter.invalidHostHeader=HTTP 헤더 [{1}] 내에 유효하지 않은 값이 발견되었습니다: [{0}]
remoteIpFilter.invalidHostWithPort=HTTP 헤더 [{1}] 내의 호스트 값 [{0}]이(가) 포트 번호를 포함하고 있는데, 이는 무시될 것입니다.
remoteIpFilter.invalidNumber=파라미터 [{0}]을(를) 위해 불허되는 숫자입니다: [{1}]
remoteIpFilter.invalidRemoteAddress=보고된 원격 주소 [{0}](이)가 유효하지 않아서 원격 호스트를 식별할 수 없습니다.

requestFilter.deny=프로퍼티 [{1}]에 기반하여, [{0}]을(를) 위한 요청이 거부되었습니다.

restCsrfPreventionFilter.invalidNonce=CSRF nonce validation 실패

webDavFilter.xpProblem=WebdavFixFilter: XP-x64-SP2 클라이언트는, WebDAV 서블릿과 정상 동작하지 않는 것으로 알려져 있습니다.
webDavFilter.xpRootContext=WebdavFixFilter: XP-x64-SP2 클라이언트는, 오직 루트 컨텍스트와 정상 동작할 것입니다.
