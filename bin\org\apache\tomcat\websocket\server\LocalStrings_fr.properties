# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

serverContainer.addNotAllowed=Aucune terminaison ne peut être enregistré une fois qu'une tentative d'utilisation d'une des terminaisons précédemment enregistrée a été faite
serverContainer.configuratorFail=Echec de création du configurateur de type [{0}] pour le POJO de type [{1}]
serverContainer.duplicatePaths=Plusieurs terminaisons ne peuvent pas être déployés vers le même chemin [{0}] : la terminaison existante était [{1}] et la nouvelle est [{2}]
serverContainer.encoderFail=Impossible de créer un encodeur de type [{0}]
serverContainer.failedDeployment=Le déploiement de terminaisons WebSocket dans l''application web au chemin [{0}] dans l''hôte [{1}] n''est pas autorisé à cause de l''échec lors d''un précédent déploiement
serverContainer.missingAnnotation=Impossible de déployer la classe POJO [{0}] car elle n''a pas été annotée avec @ServerEndpoint
serverContainer.servletContextMissing=Aucun ServletContext n'a été spécifié

upgradeUtil.incompatibleRsv=Des extensions qui ont été spécifiées ont une utilisation incompatible du bit RSV

uriTemplate.duplicateParameter=Le paramètre [{0}] apparaît plus d''une fois dans le chemin ce qui n''est pas permis
uriTemplate.emptySegment=Le chemin [{0}] contient un ou plusieurs segments vide ce qui n''est pas autorisé
uriTemplate.invalidPath=Le chemin [{0}] est invalide
uriTemplate.invalidSegment=Le segment [{0}] est invalide pour le chemin fourni [{1}]

wsFrameServer.bytesRead=Lu [{0}] octets dans le buffer de réception prêts à être traités
wsFrameServer.illegalReadState=Etat de lecture inattendu [{0}]
wsFrameServer.onDataAvailable=Entrée de méthode

wsHttpUpgradeHandler.closeOnError=Fermeture de la connection WebSocket à cause d'une erreur
wsHttpUpgradeHandler.destroyFailed=Echec de la fermeture de la WebConnection lors de la destruction du HttpUpgradeHandler de WebSocket
wsHttpUpgradeHandler.noPreInit=La méthode preInit() doit être appelée pour configurer le HttpUpgradeHandler de Websockets avant que le container n'appelle init(), cela veut habituellement dire que le Servlet qui a crée l'instance du WsHttpUpgradeHandler doit aussi appeler preInit()
wsHttpUpgradeHandler.serverStop=Le serveur est en train de s'arrêter

wsRemoteEndpointServer.closeFailed=Impossible de fermer le ServletOutputStream proprement
