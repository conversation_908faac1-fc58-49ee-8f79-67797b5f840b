# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

McastService.domain=Unable to send domain update
McastService.parseSoTimeout=Unable to parse SoTimeout: [{0}]
McastService.parseTTL=Unable to parse TTL: [{0}]
McastService.payload=Unable to send payload update
McastService.stopFail=Unable to stop the mcast service, level: [{0}]

mcastService.exceed.maxPacketSize=Packet length[{0}] exceeds max packet size of [{1}] bytes.
mcastService.missing.property=McastService:Required property [{0}] is missing.
mcastService.noStart=Multicast send is not started or enabled.

mcastServiceImpl.bind=Attempting to bind the multicast socket to [{0}:{1}]
mcastServiceImpl.bind.failed=Binding to multicast address, failed. Binding to port only.
mcastServiceImpl.error.receiving=Error receiving mcast package. Sleeping 500ms
mcastServiceImpl.invalid.startLevel=Invalid start level. Only acceptable levels are Channel.MBR_RX_SEQ and Channel.MBR_TX_SEQ
mcastServiceImpl.invalid.stopLevel=Invalid stop level. Only acceptable levels are Channel.MBR_RX_SEQ and Channel.MBR_TX_SEQ
mcastServiceImpl.memberDisappeared.failed=Unable to process member disappeared message.
mcastServiceImpl.packet.tooLong=Multicast packet received was too long, dropping package:[{0}]
mcastServiceImpl.receive.running=McastService.receive already running.
mcastServiceImpl.recovery=Tribes membership, running recovery thread, multicasting is not functional.
mcastServiceImpl.recovery.failed=Recovery attempt number [{0}] failed, trying again in [{1}] seconds
mcastServiceImpl.recovery.startFailed=Recovery thread failed to start membership service.
mcastServiceImpl.recovery.stopFailed=Recovery thread failed to stop membership service.
mcastServiceImpl.recovery.successful=Membership recovery was successful.
mcastServiceImpl.send.failed=Unable to send mcast message.
mcastServiceImpl.send.running=McastService.send already running.
mcastServiceImpl.setInterface=Setting multihome multicast interface to:[{0}]
mcastServiceImpl.setSoTimeout=Setting cluster mcast soTimeout to [{0}]
mcastServiceImpl.setTTL=Setting cluster mcast TTL to [{0}]
mcastServiceImpl.unable.join=Unable to join multicast group, make sure your system has multicasting enabled.
mcastServiceImpl.unableReceive.broadcastMessage=Unable to receive broadcast message.
mcastServiceImpl.waitForMembers.done=Done sleeping, membership established, start level:[{0}]
mcastServiceImpl.waitForMembers.start=Sleeping for [{0}] milliseconds to establish cluster membership, start level:[{1}]

memberImpl.invalid.package.begin=Invalid package, should start with:[{0}]
memberImpl.invalid.package.end=Invalid package, should end with:[{0}]
memberImpl.large.payload=Payload is too large for tribes to handle.
memberImpl.notEnough.bytes=Not enough bytes in member package.
memberImpl.package.small=Member package too small to validate.
memberImpl.unableParse.hostname=Unable to parse hostname.

staticMember.invalid.uuidLength=UUID must be exactly 16 bytes, not:[{0}]
