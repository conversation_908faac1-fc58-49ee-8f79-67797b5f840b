# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

asyncChannelGroup.createFail=无法为WebSocket客户端创建专用的异步通道组，这是防止复杂类加载程序环境（如JavaEE容器）中内存泄漏所必需的

asyncChannelWrapperSecure.check.notOk=TLS握手返回意外状态[{0}]
asyncChannelWrapperSecure.check.unwrap=在读取期间将字节写入输出
asyncChannelWrapperSecure.check.wrap=在写入过程中从输入消耗了字节
asyncChannelWrapperSecure.closeFail=干净的关闭通道失败
asyncChannelWrapperSecure.concurrentRead=不允许并发读取操作
asyncChannelWrapperSecure.concurrentWrite=不允许并发写操作
asyncChannelWrapperSecure.eof=意外的流结尾
asyncChannelWrapperSecure.notHandshaking=TLS握手过程中出现意外状态[非握手]
asyncChannelWrapperSecure.statusUnwrap=unwrap()操作后SSLEngineResult 的意外状态
asyncChannelWrapperSecure.statusWrap=wrap（）操作后SSLEngineResult的意外状态
asyncChannelWrapperSecure.tooBig=结果[{0}]太大，无法表示为整数
asyncChannelWrapperSecure.wrongStateRead=尝试完成读取操作时，发现指示正在进行读取的标志为false（应该为true）
asyncChannelWrapperSecure.wrongStateWrite=尝试完成写操作时，发现指示正在进行写操作的标志为false（应该为true）

backgroundProcessManager.processFailed=后台进程失败

caseInsensitiveKeyMap.nullKey=不允许 Key 是 Null

futureToSendHandler.timeout=等待[{0}[{1}]完成后操作超时。

perMessageDeflate.alreadyClosed=转换器已经关闭且可能永远不会使用
perMessageDeflate.deflateFailed=无法压缩这个WebSocket压缩结构
perMessageDeflate.duplicateParameter=重复定义的扩展参数[{0}]
perMessageDeflate.invalidState=无效状态
perMessageDeflate.invalidWindowSize=为[{0}]指定了[{1}]大小的无效窗口。 有效值是从8到15（包括8和15）的整数。
perMessageDeflate.unknownParameter=定义了未知的扩展参数[{0}]

transformerFactory.unsupportedExtension=不支持扩展名[{0}]。

util.invalidMessageHandler=提供的消息处理程序没有onMessage（对象）方法。
util.invalidType=无法强制值[{0}]转为[{1}]类型（不支持次类型）。
util.notToken=一个非法的扩展参数被指定为名称[{0}]和值[{0}]
util.unknownDecoderType=无法识别该解码器类型[{0}]

wsFrame.alreadyResumed=已恢复邮件接收
wsFrame.alreadySuspended=消息接收已挂起。
wsFrame.bufferTooSmall=没有异步消息支持，并且缓冲区太小。缓冲区大小：[{0}]，消息大小：[{1}]
wsFrame.byteToLongFail=提供了太多字节([{0}])，转换成一个长的字节。
wsFrame.closed=在一个关闭的控制帧后受到了一个新的帧.
wsFrame.controlFragmented=接收到分段的控制帧，但控制帧可能不被分割。
wsFrame.controlNoFin=发送一个没有设置的控制帧。控制帧不允许使用连续帧。
wsFrame.controlPayloadTooBig=以大于125字节的最大允许值的大小[{0}]的有效载荷发送控制帧。
wsFrame.illegalReadState=意外的读状态[{0}]
wsFrame.invalidOpCode=用无法识别的操作码[{0}]发送了WebSocket帧
wsFrame.invalidUtf8=接收到无法解码为UTF-8的WebSocket文本帧，因为它包含无效的字节序列
wsFrame.invalidUtf8Close=接收到一个WebSocket关闭帧，其关闭原因包含无效的UTF-8字节序列
wsFrame.ioeTriggeredClose=发生不可恢复的IOException，因此连接已关闭。
wsFrame.messageTooBig=消息的长度为[{0}]个字节，但消息处理程序的限制为[{1}]个字节。
wsFrame.noContinuation=当需要延续帧时启动了新消息。
wsFrame.notMasked=客户端帧未被屏蔽，但必须屏蔽所有客户端帧
wsFrame.oneByteCloseCode=客户端发送了一个包含无效单字节有效负载的关闭帧。
wsFrame.partialHeaderComplete=接收到WebSocket帧. fin [{0}], rsv [{1}], OpCode [{2}], payload 长度 [{3}]
wsFrame.readFailed=异步客户端读取失败
wsFrame.sessionClosed=无法处理客户端数据，因为会话已被关闭
wsFrame.suspendRequested=已请求暂停接收邮件。
wsFrame.textMessageTooBig=解码的文本消息对于输出缓冲区太大，终结点不支持部分消息
wsFrame.wrongRsv=对于具有opCode [{1}]的消息，客户端帧将保留位设置为[{0}]，此端点不支持

wsFrameClient.ioe=从发送的服务器上读取数据失败

wsHandshakeRequest.invalidUri=字符串  [{0}] 不能用来组成一个有效的URI
wsHandshakeRequest.unknownScheme=请求中的计划[{0}]未识别

wsRemoteEndpoint.acquireTimeout=当前消息没有在指定的超时内完全发送
wsRemoteEndpoint.changeType=发送分段消息时，所有片段必须是相同类型的。
wsRemoteEndpoint.closed=由于 WebSocket session 已关闭，消息将不会被发送
wsRemoteEndpoint.closedDuringMessage=因为 WebSocket session 被关闭，消息的剩余部分将不会被送达
wsRemoteEndpoint.closedOutputStream=由于OutputStream已关闭，不应该调用此方法。
wsRemoteEndpoint.closedWriter=此方法不能调用，因为编写器已关闭。
wsRemoteEndpoint.flushOnCloseFailed=会话关闭后仍然启用批处理消息。无法刷新剩余的批量消息
wsRemoteEndpoint.invalidEncoder=无法实例化类型为[{0}]的指定编码器
wsRemoteEndpoint.noEncoder=没有为类 [{0}] 的对象指定编码器
wsRemoteEndpoint.nullData=无效空的data 参数
wsRemoteEndpoint.nullHandler=无效的空处理程序参数
wsRemoteEndpoint.sendInterrupt=当前线程在等待阻塞发送完成时被中断
wsRemoteEndpoint.tooMuchData=ping或pong不应该发送超过125字节
wsRemoteEndpoint.writeTimeout=阻塞写入超时
wsRemoteEndpoint.wrongState=远程 endpoint 处于 [{0}] 状态，是被调用方法的无效状态

wsSession.closed=WebSocket会话[{0}]已关闭，并且在关闭的会话上不能调用任何方法（除了close（））
wsSession.created=创建WebSocket session [{0}]。
wsSession.doClose=关闭 WebSocket session [{1}]
wsSession.duplicateHandlerBinary=已配置二进制消息处理程序
wsSession.duplicateHandlerPong=已经配置了pong消息处理器
wsSession.duplicateHandlerText=已配置文本消息处理器
wsSession.flushFailOnClose=会话关闭时刷新批处理邮件失败
wsSession.instanceNew=endpoint 实例注册失败
wsSession.invalidHandlerTypePong=一个pong消息处理程序必须实现MessageHandler.Whole
wsSession.messageFailed=无法写入完整消息，因为WebSocket连接已关闭
wsSession.removeHandlerFailed=无法删除处理程序[{0}]，因为它未在此会话中注册
wsSession.sendCloseFail=给远程端点发送关闭消息失败，session：[{0}]
wsSession.timeout=WebSocket会话[{0}]超时已过期
wsSession.timeoutRead=WebSocket会话[{0}]读取空闲超时过期
wsSession.timeoutWrite=WebSocket会话[{0}]写入空闲超时过期
wsSession.unknownHandler=无法添加消息处理程序[{0}]，因为它是针对无法识别的类型[{1}]
wsSession.unknownHandlerType=无法添加消息处理程序[{0}]，因为它被包装为无法识别的类型[{1}]。

wsWebSocketContainer.asynchronousSocketChannelFail=无法打开与服务器的连接
wsWebSocketContainer.connect.entry=连接[{0}]类型的终端实例至[{1}]
wsWebSocketContainer.defaultConfiguratorFail=无法创建默认配置程序。
wsWebSocketContainer.endpointCreateFail=未能创建类型为[{0}]的本地终结点
wsWebSocketContainer.failedAuthentication=无法处理http响应代码[{0}]。服务器不接受身份验证头。
wsWebSocketContainer.httpRequestFailed=启动WebSocket连接的HTTP请求失败
wsWebSocketContainer.invalidExtensionParameters=服务器用客户端无法支持的扩展参数响应
wsWebSocketContainer.invalidHeader=无法分析HTTP头，因为在[{0}]中没有冒号来分隔头名称和头值。已跳过标题。
wsWebSocketContainer.invalidStatus=来自服务器[{0}]的HTTP响应不允许HTTP升级到WebSocket
wsWebSocketContainer.invalidSubProtocol=WebSocket服务器为Sec-WebSocket-Protocol标头返回了多个值
wsWebSocketContainer.maxBuffer=此实现将缓冲区的最大大小限制为Integer.MAX_VALUE
wsWebSocketContainer.missingAnnotation=无法使用POJO类[{0}]，因为它未添加注解@ClientEndpoint
wsWebSocketContainer.missingLocationHeader=处理HTTP响应码 [{0}] 失败。响应头缺少Location
wsWebSocketContainer.missingWWWAuthenticateHeader=无法处理HTTP响应代码[{0}]。 缺少WWW-Authenticate标头作为响应
wsWebSocketContainer.pathNoHost=URI中未指定主机
wsWebSocketContainer.pathWrongScheme=不支持方案[{0}]。支持的方案是ws和wss
wsWebSocketContainer.proxyConnectFail=失败连接到已配置的代理 [{0}]。HTTP 响应码是 [{1}]
wsWebSocketContainer.redirectThreshold=循环位置头[{0}]检测到/达到最大重定向数[{1}]的最大值[{2}]
wsWebSocketContainer.responseFail=HTTP升级WebSocket失败，但是部分数据已被接收：状态码：[{0}],HTTP请求头[{1}]
wsWebSocketContainer.sessionCloseFail=ID 为 [{0}] 的session 没有彻底关闭
wsWebSocketContainer.shutdown=web应用程序正在停止
wsWebSocketContainer.sslEngineFail=无法创建SSLEngine以支持SSL/TLS连接
wsWebSocketContainer.unsupportedAuthScheme=HTTP响应码处理失败[{0}]. Unsupported Authentication 方案 [{1}] 返回到响应
