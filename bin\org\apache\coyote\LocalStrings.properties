# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractConnectionHandler.connectionsGet=Found processor [{0}] for socket [{1}]
abstractConnectionHandler.error=Error reading request, ignored
abstractConnectionHandler.ioexception.debug=IOExceptions are normal, ignored
abstractConnectionHandler.negotiatedProcessor.fail=Failed to create Processor for negotiated protocol [{0}]
abstractConnectionHandler.oome=Failed to complete processing of a request
abstractConnectionHandler.process=Processing socket [{0}] with status [{1}]
abstractConnectionHandler.processorCreate=Created new processor [{0}]
abstractConnectionHandler.processorPop=Popped processor [{0}] from cache
abstractConnectionHandler.protocolexception.debug=ProtocolExceptions are normal, ignored
abstractConnectionHandler.socketexception.debug=SocketExceptions are normal, ignored
abstractConnectionHandler.upgradeCreate=Created upgrade processor [{0}] for socket wrapper [{1}]

abstractProcessor.fallToDebug=\n\
\ Note: further occurrences of request parsing errors will be logged at DEBUG level.
abstractProcessor.hostInvalid=The host [{0}] is not valid
abstractProcessor.httpupgrade.notsupported=HTTP upgrade is not supported by this protocol
abstractProcessor.pushrequest.notsupported=Server push requests are not supported by this protocol
abstractProcessor.setErrorState=Error state [{0}] reported while processing request
abstractProcessor.socket.ssl=Exception getting SSL attributes

abstractProtocol.mbeanDeregistrationFailed=Failed to deregister MBean named [{0}] from MBean server [{1}]
abstractProtocol.processorRegisterError=Error registering request processor
abstractProtocol.processorUnregisterError=Error unregistering request processor
abstractProtocol.waitingProcessor.add=Added processor [{0}] to waiting processors
abstractProtocol.waitingProcessor.remove=Removed processor [{0}] from waiting processors

abstractProtocolHandler.destroy=Destroying ProtocolHandler [{0}]
abstractProtocolHandler.destroyError=Failed to destroy end point associated with ProtocolHandler [{0}]
abstractProtocolHandler.getAttribute=Get attribute [{0}] with value [{1}]
abstractProtocolHandler.init=Initializing ProtocolHandler [{0}]
abstractProtocolHandler.pause=Pausing ProtocolHandler [{0}]
abstractProtocolHandler.pauseError=Failed to pause end point associated with ProtocolHandler [{0}]
abstractProtocolHandler.resume=Resuming ProtocolHandler [{0}]
abstractProtocolHandler.resumeError=Failed to resume end point associated with ProtocolHandler [{0}]
abstractProtocolHandler.setAttribute=Set attribute [{0}] with value [{1}]
abstractProtocolHandler.start=Starting ProtocolHandler [{0}]
abstractProtocolHandler.startError=Failed to start end point associated with ProtocolHandler [{0}]
abstractProtocolHandler.stop=Stopping ProtocolHandler [{0}]
abstractProtocolHandler.stopError=Failed to stop end point associated with ProtocolHandler [{0}]

asyncStateMachine.invalidAsyncState=Calling [{0}] is not valid for a request with Async state [{1}]

compressionConfig.ContentEncodingParseFail=Failed to parse Content-Encoding header when checking to see if compression was already in use

continueResponseTiming.invalid=The value [{0}] is not a valid configuration option for continueResponseTiming

request.notAsync=It is only valid to switch to non-blocking IO within async processing or HTTP upgrade processing
request.nullReadListener=The listener passed to setReadListener() may not be null
request.readListenerSet=The non-blocking read listener has already been set

response.encoding.invalid=The encoding [{0}] is not recognised by the JRE
response.notAsync=It is only valid to switch to non-blocking IO within async processing or HTTP upgrade processing
response.notNonBlocking=It is invalid to call isReady() when the response has not been put into non-blocking mode
response.nullWriteListener=The listener passed to setWriteListener() may not be null
response.writeListenerSet=The non-blocking write listener has already been set
