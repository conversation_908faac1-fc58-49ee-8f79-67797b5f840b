# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

backupManager.noCluster=이 컨텍스트 [{0}]와(과) 연관된 클러스터가 없습니다.
backupManager.startFailed=백업매니저 [{0}]을(를) 시작하지 못했습니다.
backupManager.startUnable=BackupManager를 시작할 수 없습니다: [{0}]
backupManager.stopped=매니저 [{0}]이(가) 중지되고 있습니다.

clusterSessionListener.noManager=컨텍스트 매니저가 존재하지 않습니다: [{0}]

deltaManager.createMessage.access=매니저 [{0}]: 세션(ID: [{1}])을 위한 세션 접근 메시지를 생성합니다.
deltaManager.createMessage.accessChangePrimary=매니저 [{0}]: 세션(ID: [{1}])을 위해 Primary 노드 변경 메시지를 생성합니다.
deltaManager.createMessage.allSessionData=매니저 [{0}]이(가) 모든 세션 데이터를 전송했습니다.
deltaManager.createMessage.allSessionTransferred=매니저 [{0}]이(가), 모든 세션 데이터 전송 완료 메시지를 보냈습니다.
deltaManager.createMessage.delta=매니저 [{0}]: 세션(ID: [{1}])을 위한 델타 요청 메시지를 생성합니다.
deltaManager.createMessage.expire=매니저 [{0}]: 세션(ID: [{1}])을 위한 세션 만료 메시지를 생성합니다.
deltaManager.createMessage.unableCreateDeltaRequest=세션 ID [{0}]을(를) 위한 델타 요청을 직렬화할 수 없습니다.
deltaManager.createSession.newSession=ID가 [{0}]인 DeltaSession을 생성했습니다. 총 개수=[{1}]
deltaManager.dropMessage=매니저 [{0}]: GET_ALL_SESSIONS 동기화 국면 내에서, 메시지 [{1}]을(를) 무시합니다. 시작 시간: [{2}], 메시지의 타임스탬프: [{3}]
deltaManager.expireSessions=매니저 [{0}]이(가) 셧다운 시에 세션들을 만료시킵니다.
deltaManager.foundMasterMember=컨텍스트 [{0}]을(를) 위한 복제 마스터 멤버 [{1}]을(를) 찾았습니다.
deltaManager.loading.cnfe=저장된 세션들을 로드하는 중 ClassNotFoundException 발생: [{0}]
deltaManager.loading.existing.session=기존 세션 [{0}]을(를) 오버로드합니다.
deltaManager.loading.ioe=저장된 세션들을 로드하는 중 IOException 발생: [{0}]
deltaManager.managerLoad=저장소로부터 세션들을 로드하는 중 예외 발생
deltaManager.noCluster=시작 중... 이 컨텍스트와 연관된 클러스터가 없습니다: [{0}]
deltaManager.noContextManager=매니저 [{0}]: [{1}]에서 전송되었던 ''Get all session data'' 메시지에 응답하여, ''No matching context manager'' 메시지를 [{2}] 밀리초 후에 받았습니다.
deltaManager.noMasterMember=시작 중... 도메인 [{1}]에 컨텍스트 [{0}]을(를) 위한 다른 멤버들은 없는 상태입니다.
deltaManager.noMembers=매니저 [{0}]: 상태 이전 작업을 건너뜁니다. 클러스터 그룹 내에 활성화된 멤버가 없습니다.
deltaManager.noSessionState=매니저 [{0}]: [{1}]에서 보낸 세션 상태 메시지를 받지 못했습니다. [{2}] 밀리초 이후 제한 시간 초과되었습니다.
deltaManager.receiveMessage.accessed=매니저 [{0}]: 세션(ID: [{1}])을 위한 세션 접근 메시지를 받았습니다.
deltaManager.receiveMessage.allSessionDataAfter=매니저 [{0}]: 모든 세션 상태가 역직렬화되었습니다.
deltaManager.receiveMessage.allSessionDataBegin=매니저 [{0}]: 모든 세션 상태 데이터를 받았습니다.
deltaManager.receiveMessage.createNewSession=매니저 [{0}]: 세션 [{1}]을(를) 위한 세션 생성됨 메시지를 수신했습니다.
deltaManager.receiveMessage.delta=매니저 [{0}]: 세션(ID: [{1}])을 위한 세션 델타 메시지를 받았습니다.
deltaManager.receiveMessage.delta.unknown=매니저 [{0}]: 알 수 없는 세션 [{1}]을(를) 위한 세션 델타를 받았습니다.
deltaManager.receiveMessage.error=매니저 [{0}]: TCP 채널을 통해 메시지를 받을 수 없습니다.
deltaManager.receiveMessage.eventType=매니저 [{0}]: 타입이 [{1}]인 SessionMessage를 [{2}](으)로부터 받았습니다.
deltaManager.receiveMessage.expired=매니저 [{0}]: 세션 [{1}]을(를) 위한 세션 만료 메시지를 받았습니다.
deltaManager.receiveMessage.noContextManager=매니저 [{0}]이(가) 노드 [{1}:{2}](으)로부터 no context manager 메시지를 받았습니다.
deltaManager.receiveMessage.transfercomplete=매니저 [{0}]이(가) 노드 [{1}:{2}](으)로부터, 세션 상태 이전 완료 메시지를 받았습니다.
deltaManager.receiveMessage.unloadingAfter=매니저 [{0}]: 세션들을 언로드하는 작업이 완료되었습니다.
deltaManager.receiveMessage.unloadingBegin=매니저 [{0}]: 세션들에 대해 언로드를 시작합니다.
deltaManager.registerCluster=매니저 [{0}]을(를), [{2}](이)라는 이름의 클러스터 엘리먼트 [{1}](으)로 등록합니다.
deltaManager.sendMessage.newSession=매니저 [{0}]이(가) 새로운 세션 [{1}]을(를) 전송합니다.
deltaManager.sessionReceived=매니저 [{0}]; [{1}]에서 전송된 세션 상태를 [{2}] 밀리초 이내에 받음
deltaManager.startClustering=[{0}]에서 클러스터 매니저를 시작합니다.
deltaManager.stopped=매니저 [{0}]이(가) 중지됩니다.
deltaManager.unableSerializeSessionID=세션 ID [{0}]을(를) 직렬화할 수 없습니다.
deltaManager.unloading.ioe=세션들을 저장하는 중 IOException 발생: [{0}]
deltaManager.waitForSessionState=매니저 [{0}]: [{1}](으)로부터 세션 상태를 요청합니다. 만일 [{2}]초 이내에 세션 상태를 받지 못하면, 이 오퍼레이션은 제한 시간 초과 처리될 것입니다.

deltaRequest.invalidAttributeInfoType=유효하지 않은 AttributeInfo 타입=[{0}]
deltaRequest.removeUnable=클러스터 엘리먼트를 제거할 수 없습니다:
deltaRequest.showPrincipal=Principal [{0}]이(가) 세션 [{1}]에 설정되었습니다.
deltaRequest.ssid.mismatch=세션 ID가 일치하지 않아, 델타 요청을 실행하지 않습니다.
deltaRequest.ssid.null=setSessionId를 위한 세션 ID가 널입니다.
deltaRequest.wrongPrincipalClass=ClusterManager는 오직 GenericPrincipal만을 지원합니다. 사용된 realm은 principal 클래스 [{0}]을(를) 사용했습니다.

deltaSession.notifying=클러스터에 세션 만료를 통지합니다: primary여부: [{1}], 세션ID: [{2}]
deltaSession.readSession=readObject()가 세션 [{0}]을(를) 로드합니다.
deltaSession.writeSession=writeObject()가 세션 [{0}]을(를) 저장합니다.

jvmRoute.cannotFindSession=세션 [{0}]을(를) 찾을 수 없습니다.
jvmRoute.changeSession=세션을 [{0}]에서 [{1}](으)로 변경했습니다.
jvmRoute.failover=다른 jvmRoute로 Failover를 탐지했습니다. 원래의 라우트: [{0}], 새로운 라우트: [{1}]. 세션 ID: [{2}]
jvmRoute.foundManager=[{1}]에서 클러스터 매니저 [{0}]을(를) 찾았습니다.
jvmRoute.missingJvmRouteAttribute=엔진의 jvmRoute 속성이 설정되지 않았습니다!
jvmRoute.noCluster=JvmRouterBinderValve가 설정되었지만, 클러스터링이 사용되고 있지 않습니다. PersistentManager가 사용되는 경우, Fail over는 여전히 정상 동작할 것입니다.
jvmRoute.notFoundManager=[{0}]에서 클러스터 매니저를 찾을 수 없습니다.
jvmRoute.set.originalsessionid=요청의 속성 [{0}]에 원래의 세션 ID를 설정합니다: [{1}]
jvmRoute.turnoverInfo=Failover를 위한 jvmRoute 교체 수행 시간: [{0}] 밀리초
jvmRoute.valve.started=JvmRouteBinderValve가 시작됐습니다.
jvmRoute.valve.stopped=JvmRouteBinderValve가 중지되었습니다.

standardSession.notSerializable=세션 [{1}]을 위한 세션 속성 [{0}]을(를) 직렬화할 수 없습니다.
standardSession.removeAttribute.ise=removeAttribute: 세션이 이미 무효화되었습니다.
standardSession.setAttribute.namenull=setAttribute: name 파라미터는 널일 수 없습니다.
