# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

bufferPool.created=타입이 [{1}](이)고 최대 크기가 [{0}] 바이트를 사용하여 버퍼 풀을 생성했습니다.

objectReader.retrieveFailed.socketReceiverBufferSize=소켓 receiver의 버퍼 크기를 알 수 없습니다. 기본 값인 43800 바이트로 설정합니다.

replicationStream.conflict=해당 클래스의 클래스로더와, 그 클래스의 특정 non-public 인터페이스의 클래스로더가 일치하지 않아 충돌합니다.

xByteBuffer.discarded.invalidHeader=유효하지 않은 헤더로 인해 패키지를 폐기했습니다.
xByteBuffer.no.package=XByteBuffer에 어떤 패키지도 존재하지 않습니다.
xByteBuffer.size.larger.buffer=크기가 기존 버퍼의 길이보다 큽니다.
xByteBuffer.unableCreate=데이터 패키지를 생성할 수 없습니다. 버퍼가 너무 작습니다.
xByteBuffer.unableTrim=가용한 버퍼 크기보다 더 많은 바이트들을 잘라낼 수는 없습니다. 버퍼 길이:[{0}], 잘라낼 길이:[{1}]
xByteBuffer.wrong.class=메시지가 Serializable 인터페이스를 구현하지 않은 클래스입니다. 클래스는 [{0}]입니다.
