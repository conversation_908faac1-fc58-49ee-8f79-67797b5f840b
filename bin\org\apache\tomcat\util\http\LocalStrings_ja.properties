# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cookies.fallToDebug=\n\
\ 注: 以降のCookieエラーの発生はDEBUGレベルでログに出力されます。
cookies.invalidCookieToken=Cookie: 不正な cookie です。値がトークンではないか、クォートされていません。
cookies.invalidSameSiteCookies=不明な設定 [{0}] は、次のうちのいずれか1つである必要があります: unset, none, lax, strict. 既定値は unset です。
cookies.invalidSpecial=Cookies: 不明な特別な Cookie
cookies.maxCountFail=最大数[{0}]以上のクッキーが検出されました。

headers.maxCountFail=検出したヘッダー数 [{0}] は上限値を越えています。

parameters.bytes=入力[{0}]で処理を開始します。
parameters.copyFail=デバッグログの目的で元のパラメータ値のコピーを作成できませんでした
parameters.decodeFail.debug=文字列のデコードに失敗しました。パラメーター [{0}] (値 [{1}]) は無視しました。
parameters.decodeFail.info=文字のデコードに失敗しました。 値[{1}]のパラメータ[{0}]は無視されました。 ここで引用された名前と値は、デコードに失敗したために破損している可能性があることに注意してください。 デバッグレベルのログを使用して、破損していない元の値を確認してください。
parameters.emptyChunk=空のパラメータチャンクが無視されます。
parameters.fallToDebug=\n\
\ 注: 以降のパラメータエラーの発生はDEBUGレベルでログに出力されます。
parameters.invalidChunk=バイト値 [{0}] で始まりバイト値 [{1}] で終了する不正なチャンクです。値 [{2}] を無視します。
parameters.maxCountFail=単独のリクエスト ([{0}]) のリクエストパラメーター (GET および POST) の数が上限値を超えています。上限値を超えるすべてのパラメーターは無視します。上限値を変更するには Connector 要素の maxParameterCount 属性を設定してください。
parameters.maxCountFail.fallToDebug=\n\
\ 注: 以降のこのエラーの発生はDEBUGレベルでログに出力されます。
parameters.multipleDecodingFail=文字のデコードに失敗しました。 合計[{0}]個の障害が検出されましたが、最初のものだけが記録されました。 このロガーがすべての失敗を記録するためにはデバッグレベルのロギングを有効にします。
parameters.noequal=[{2}]の値で[{0}]の位置から[{1}]の位置で終了するパラメータには、 ''=''文字が続いていませんでした。

rfc6265CookieProcessor.invalidCharInValue=無効な文字[{0}]がCookie値に存在します。
rfc6265CookieProcessor.invalidDomain=cookie に不正なドメイン [{0}] が指定されました。
rfc6265CookieProcessor.invalidPath=Cookie の path 属性に不正な値 [{0}] が指定されました。
