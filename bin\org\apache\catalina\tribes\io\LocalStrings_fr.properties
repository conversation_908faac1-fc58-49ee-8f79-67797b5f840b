# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

bufferPool.created=Création d''un pool de tampons de taille maximale : [{0}] octets de type : [{1}]

objectReader.retrieveFailed.socketReceiverBufferSize=Incapacité de récupérer la taille du tampon du socket de réception, forcé à 43800 octets

replicationStream.conflict=Conflit entre des interfaces non-publics ayant des chargeurs de classe différents

xByteBuffer.discarded.invalidHeader=L'en-tête est invalide donc le paquet a été abandonné
xByteBuffer.no.package=Il n'y a aucun package dans XByteBuffer
xByteBuffer.size.larger.buffer=La taille est plus grande que celle du buffer existant
xByteBuffer.unableCreate=Impossible de créer le package data, le tampon de mémoire est trop petit
xByteBuffer.unableTrim=Impossible d''élaguer plus d''octets que ce qui est disponible, longueur : [{0}] élagage : [{1}]
xByteBuffer.wrong.class=Message n''a pas la bonne classe. Cela doit implémenter Serializable au lieu de [{0}]
