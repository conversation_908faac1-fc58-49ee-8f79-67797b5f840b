# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

SecurityListener.checkUmaskFail=[{0}]のumask設定で開始しようとしました。 少なくとも[{1}]と同じようにumaskを指定しないでTomcatを実行すると、ライフサイクルリスナーのorg.apache.catalina.security.SecurityListener（通常はCATALINA_BASE/conf/server.xmlで構成されています）によってブロックされます。
SecurityListener.checkUmaskNone=システムプロパティ[{0}]にumask設定が見つかりませんでした。 しかし、Tomcatはumaskをサポートするプラットフォームで動作しているようです。 システムプロパティは通常、CATALINA_HOME/bin/catalina.shに設定されます。 ライフサイクルリスナーのorg.apache.catalina.security.SecurityListener（通常はCATALINA_BASE/conf/server.xmlに設定されています）では、少なくとも[{1}]と同じくらい拘束されたumaskが必要です。
SecurityListener.checkUmaskParseFail=値[{0}]を有効なumaskとして解析できませんでした。
SecurityListener.checkUmaskSkip=umask を取得できません。Tomcat を Windows で実行するときは umask をチェックしません。
SecurityListener.checkUserWarning=ユーザー[{0}]として実行中に開始しようとしました。 このユーザーでのTomcatの実行はライフサイクルリスナーorg.apache.catalina.security.SecurityListener（通常はCATALINA_BASE/conf /server.xmlで構成されている）によってブロックされています。

SecurityUtil.doAsPrivilege=PrivilegedExceptionActionブロックを実行中に例外が発生しました。
