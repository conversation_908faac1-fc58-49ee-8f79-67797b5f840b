# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

namingResources.cleanupCloseFailed=无法为容器[{2}]中的资源[{1}]调用方法[{0}]，因此未对该资源执行清理
namingResources.cleanupCloseSecurity=无法检索容器[{2}]中的资源[{1}]的方法[{0}]，因此没有对该资源进行清理
namingResources.cleanupNoClose=容器[{1}]中的资源[{0}]没有[{2}]方法，因此没有对该资源执行清理
namingResources.cleanupNoContext=无法检索容器[{0}]的JNDI命名上下文，因此未对该容器执行清理
namingResources.cleanupNoResource=无法检索容器[{1}]的JNDI资源[{0}]，因此未对该资源执行清理。
namingResources.ejbLookupLink=EJB的引用[{0}]同时指定了ejb-link和lookup-name
namingResources.envEntryLookupValue=环境参数 [{0}] 指定查询名称和值
namingResources.mbeanCreateFail=为命名资源[{0}]创建MBean失败
namingResources.mbeanDestroyFail=失败的销毁命名资源[{0}]为MBean
namingResources.resourceTypeFail=名为[{0}]的JNDI资源是类型[{1}]，但是该类型与为该资源配置的注入目标的类型不一致
