# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

engine.ciphersFailure=获取密码列表失败
engine.emptyCipherSuite=空密码套件
engine.engineClosed=引擎已经关闭
engine.failedCipherSuite=无法启用密码套件[{0}]
engine.inboundClose=入站在收到对等方的关闭通知之前关闭
engine.invalidBufferArray=偏移量：[{0}]，长度：[{1}]（应为：偏移量<=偏移量+长度<=srcs.length[{2}]）
engine.noRestrictSessionCreation=OpenSslEngine不允许限制引擎仅仅去唤醒已经存在的会话
engine.noSSLContext=没有SSL上下文
engine.noSession=SSL会话ID不可用
engine.nullBuffer=空缓冲区
engine.nullBufferInArray=数组中的空缓冲区
engine.nullCipherSuite=无加密套件
engine.nullName=空值名称
engine.nullValue=空值
engine.openSSLError=OpenSSL 错误：[{0}] 信息: [{1}]
engine.oversizedPacket=加密包过大
engine.unsupportedCipher=不支持的密码套件：[{0}][{1}]。
engine.unsupportedProtocol=不支持协议 [{0}]
engine.unverifiedPeer=未经核实的同行
engine.writeToSSLFailed=写入SSL失败，返回：[{0}]

openssl.X509FactoryError=获取X509工厂实例时出错
openssl.addedClientCaCert=添加了客户端 CA 证书：[{0}]
openssl.applyConf=将OpenSSLConfCmd应用于SSL上下文
openssl.certificateVerificationFailed=证书验证失败
openssl.checkConf=检查OpenSSLConf
openssl.doubleInit=SSL环境已经初始化，忽略
openssl.errApplyConf=无法将OpenSSLConf 应用于SSL 上下文
openssl.errCheckConf=OpenSSLConf检查期间出错。
openssl.errMakeConf=无法创建OpenSSLConf上下文
openssl.errorSSLCtxInit=初始化SSL上下文时出错
openssl.keyManagerMissing=key管理器未找到
openssl.makeConf=创建OpenSSLConf上下文
openssl.nonJsseCertificate=无法使用JSSE密钥管理器处理证书{0}或其私钥{1}，该证书将直接提供给OpenSSL
openssl.nonJsseChain=证书链[{0}]未指定或无效，JSSE需要有效的证书链，因此尝试直接使用OpenSSL
openssl.trustManagerMissing=没有找到.信任管理者

opensslconf.applyCommand=OpenSSLConf正在应用命令（name[{0}]，value[{1}]）
opensslconf.applyFailed=将OpenSSLConf应用于SSL上下文时失败
opensslconf.checkCommand=OpenSSLConf检查命令（name[{0}]，value[{1}]）
opensslconf.checkFailed=检查OpenSSLConf时失败。
opensslconf.failedCommand=OpenSSLConf失败的命令（名称为{0}，值为{1}]），结果为{2}-将被忽略
opensslconf.finishFailed=OpenSSLConf 配置失败结果为 [{0}]
opensslconf.noCommandName=OpenSSLConf没有命令名-将被忽略（命令值[{0}]）
opensslconf.resultCommand=OpenSSLConf命令（name[{0}]，value[{1}]）返回了[{2}]

sessionContext.nullTicketKeys=Null keys
