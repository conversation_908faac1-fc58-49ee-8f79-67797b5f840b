# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

asyncChannelGroup.createFail=JavaEEコンテナなどの複雑なクラスローダ環境でメモリリークを防ぐために必要なWebSocketクライアント専用のAsynchronousChannelGroupを作成できません

asyncChannelWrapperSecure.check.notOk=TLSハンドシェイクが予期しないステータスを返しました[{0}]
asyncChannelWrapperSecure.check.unwrap=読み込み中にバイトが出力に書き込まれました。
asyncChannelWrapperSecure.check.wrap=書き込み中に入力からバイトが消費されました。
asyncChannelWrapperSecure.closeFail=チャンネルをきれいに閉じることができませんでした
asyncChannelWrapperSecure.concurrentRead=コンカレントな読み取り操作を行うことはできません。
asyncChannelWrapperSecure.concurrentWrite=コンカレントな書き込み操作を行うことはできません。
asyncChannelWrapperSecure.eof=予期せぬ位置にストリームの終端を検出しました。
asyncChannelWrapperSecure.notHandshaking=TLSハンドシェイク中に予期しない状態[NOT_HANDSHAKING]
asyncChannelWrapperSecure.statusUnwrap=unwrap()操作後のSSLEngineResultの予期しないステータス
asyncChannelWrapperSecure.statusWrap=wrap()操作後のSSLEngineResultの予期しないステータス。
asyncChannelWrapperSecure.tooBig=Integer として解釈するには大きすぎる結果 [{0}] です。
asyncChannelWrapperSecure.wrongStateRead=読み取り操作の完了中に読み取り中を意味するフラグが false になっていることを検出しました (true になっているべきです)。
asyncChannelWrapperSecure.wrongStateWrite=書き込み操作を完了しようとすると、書き込みが進行中であることを示すフラグがfalse（trueであったはずです）であることが判明しました。

backgroundProcessManager.processFailed=バックグラウンド処理が失敗しました。

caseInsensitiveKeyMap.nullKey=null 値はキーに使用できません。

futureToSendHandler.timeout=完了を [{0}] [{1}] 待機後に、操作がタイムアウトしました

perMessageDeflate.alreadyClosed=transformer はクローズされました。これ以上使用されません
perMessageDeflate.deflateFailed=圧縮された WebSocket フレームを展開できません。
perMessageDeflate.duplicateParameter=[{0}]拡張パラメータの重複した定義
perMessageDeflate.invalidState=不正な状態です。
perMessageDeflate.invalidWindowSize=[{0}] のウインドウサイズに不正な値 [{1}] が指定されました。ウインドウサイズは 8 以上 15 以下でなければなりません。
perMessageDeflate.unknownParameter=未知の拡張パラメーター [{0}] が指定されました。

transformerFactory.unsupportedExtension=未対応の拡張 [{0}] です。

util.invalidMessageHandler=提供されたメッセージハンドラにonMessage(Object)メソッドがありません
util.invalidType=値[{0}]をタイプ[{1}]に強制できません。 このタイプはサポートされていません。
util.notToken=パラメーター名 [{0}]、値 [{1}] の不正な拡張パラメーターが指定されました。
util.unknownDecoderType=デコーダタイプ[{0}]は認識されません

wsFrame.alreadyResumed=メッセージの受信は既に再開されています。
wsFrame.alreadySuspended=メッセージの受信は既に中断されています。
wsFrame.bufferTooSmall=非同期メッセージの非サポートとバッファーが小さすぎます。 バッファサイズ：[{0}]、メッセージサイズ：[{1}]
wsFrame.byteToLongFail=long へ変換するために巨大なバイト列 ([{0}]) が指定されました。
wsFrame.closed=閉じたコントローフレームで新しいデータフレームを受信しました。
wsFrame.controlFragmented=断片化されたコントロールフレームが受信されましたが、コントロールフレームは断片化されません。
wsFrame.controlNoFin=fin ビットがオフになっているコントロールフレームを受信しました。コントロールフレームを継続フレームとして使用することはできません。
wsFrame.controlPayloadTooBig=コントロールフレームは [{0}] バイトのペイロードで送信されましたが、送信可能な最大値の 125 バイトを越えています。
wsFrame.illegalReadState=予期しない読み取り状態[{0}]
wsFrame.invalidOpCode=未知の opCode [{0}] の WebSocket フレームを受信しました。
wsFrame.invalidUtf8=無効なバイトシーケンスが含まれていたため、UTF-8にデコードできなかったWebSocketテキストフレームが受信されました。
wsFrame.invalidUtf8Close=WebSocket は不正な UTF-8 バイト列を含むことを原因とするクローズフレームを受信しました。
wsFrame.ioeTriggeredClose=回復不能なIOException が発生したためコネクションを切断します。
wsFrame.messageTooBig=メッセージは[{0}]バイトの長さでしたが、MessageHandlerには[{1}]バイトの制限があります。
wsFrame.noContinuation=continuation フレームが予想されたときに新しいメッセージが開始されました。
wsFrame.notMasked=クライアントのフレームはマスクされていませんが、全てのクライアントのフレームはマスクしなければなりません。
wsFrame.oneByteCloseCode=クライアントは有効ではない単一バイトのペイロードを含むクローズフレームを送信しました。
wsFrame.partialHeaderComplete=WebSocket フレームを受信しました。fin [{0}]、rsv [{1}]、OpCode [{2}]、ペイロード長 [{3}]
wsFrame.sessionClosed=セッションが既に閉じられているため、クライアントデータを処理できません。
wsFrame.suspendRequested=すでにメッセージの受信中断を要求しています。
wsFrame.textMessageTooBig=デコードされたテキストメッセージが出力バッファにとって大きすぎます。さらにエンドポイントが部分メッセージをサポートしていません。
wsFrame.wrongRsv=クライアントフレームは opCode [{1}] でメッセージを送信するため [{0}] の予約ビットを設定しましたが、エンドポイントは対応していません。

wsFrameClient.ioe=サーバーから送信されたデータを読み取る際にエラーが発生しました。

wsHandshakeRequest.invalidUri=文字列 [{0}] は正常な URI に含めることができません。
wsHandshakeRequest.unknownScheme=リクエストのスキーム[{0}]が認識されません

wsRemoteEndpoint.acquireTimeout=指定した時間内にメッセージを送信できませんでした。
wsRemoteEndpoint.changeType=フラグメント化されたメッセージを送信する場合、すべてのフラグメントは同じタイプでなければなりません。
wsRemoteEndpoint.closed=WebSocket セッションは切断済みのためメッセージを送信しません。
wsRemoteEndpoint.closedDuringMessage=WebSocket セッションが切断されているため残りのメッセージは送信できません。
wsRemoteEndpoint.closedOutputStream=このメソッドは、OutputStreamが閉じられたときに呼び出されない場合があります。
wsRemoteEndpoint.closedWriter=Writerがクローズされているため、このメソッドを呼び出すことはできません。
wsRemoteEndpoint.flushOnCloseFailed=セッションが閉じられた後にまだ可能であったバッチメッセージ。 残りのバッチメッセージをフラッシュできません。
wsRemoteEndpoint.invalidEncoder=指定されたタイプ[{0}]のエンコーダをインスタンス化できませんでした。
wsRemoteEndpoint.noEncoder=クラス [{0}] のオブジェクトのエンコーダーが未指定です。
wsRemoteEndpoint.nullData=無効なNullデータ引数
wsRemoteEndpoint.nullHandler=無効なnullハンドラ引数
wsRemoteEndpoint.sendInterrupt=同期送信の完了待ちスレッドに割り込みが発生しました。
wsRemoteEndpoint.tooMuchData=Ping および Pong は 125 バイト以上送信できません。
wsRemoteEndpoint.writeTimeout=ブロッキング書き込みのタイムアウト
wsRemoteEndpoint.wrongState=リモートエンドポイントの状態 [{0}] は呼び出したメソッドに対して不正な状態です。

wsSession.closed=WebSocket セッション [{0}] を切断しました。切断済みのセッションに close() 以外のメソッド呼び出しをすることはありません。
wsSession.created=WebSocket セッション [{0}] を作成しました。
wsSession.doClose=WebSocket セッション [{1}] を切断します。
wsSession.duplicateHandlerBinary=バイナリメッセージハンドラは既に設定されています。
wsSession.duplicateHandlerPong=pongメッセージハンドラは既に設定されています。
wsSession.duplicateHandlerText=テキストメッセージハンドラはすでに構成されています。
wsSession.flushFailOnClose=セッション切断時にバッチメッセージをフラッシュできませんでした。
wsSession.instanceNew=エンドポイントインスタンスの登録に失敗しました。
wsSession.invalidHandlerTypePong=pongメッセージハンドラはMessageHandler.Wholeを実装する必要があります。
wsSession.messageFailed=WebSocket コネクションが切断されているため、完了メッセージを送信できません。
wsSession.removeHandlerFailed=セッションに登録されていないためハンドラー [{0}] を解除できません。
wsSession.sendCloseFail=セッション[{0}]のクローズメッセージをリモートエンドポイントに送信できませんでした。
wsSession.timeout=WebSocketセッション[{0}]タイムアウトが切れました。
wsSession.unknownHandler=認識できないタイプ[{1}]のメッセージハンドラ[{0}]を追加できません。
wsSession.unknownHandlerType=認識できない型[{1}]としてラップされたメッセージハンドラ[{0}]を追加できません。

wsWebSocketContainer.asynchronousSocketChannelFail=サーバーへの接続を開始できません。
wsWebSocketContainer.defaultConfiguratorFail=既定のコンフィグレータ生成に失敗しました。
wsWebSocketContainer.endpointCreateFail=クラス [{0}] のローカルエンドポイントを作成できません。
wsWebSocketContainer.failedAuthentication=HTTP応答コード[{0}]を処理できませんでした。 認証ヘッダーがサーバーによって受け入れられませんでした。
wsWebSocketContainer.httpRequestFailed=WebSocket接続を開始するHTTPリクエストが失敗しました。
wsWebSocketContainer.invalidExtensionParameters=サーバーはクライアントの解釈できない拡張パラメーターで応答しました。
wsWebSocketContainer.invalidHeader=ヘッダー名と値の区切り文字(コロン)がない HTTP ヘッダー [{0}] は解釈できないため無視します。
wsWebSocketContainer.invalidStatus=サーバー[{0}]からのHTTPレスポンスがWebSocketへのHTTPアップグレードを許可しませんでした。
wsWebSocketContainer.invalidSubProtocol=WebSocketサーバーは、Sec-WebSocket-Protocolヘッダーに複数の値を返しました。
wsWebSocketContainer.maxBuffer=この実装はバッファの最大サイズをInteger.MAX_VALUEに制限します。
wsWebSocketContainer.missingAnnotation=POJOクラス[{0}]は@ClientEndpointでアノテーションされていないため使用できません。
wsWebSocketContainer.missingLocationHeader=HTTP レスポンスコード [{0}] を処理できません。レスポンスに Location ヘッダーがありませんでした。
wsWebSocketContainer.missingWWWAuthenticateHeader=HTTP 応答コード [{0}] を処理できません。応答ヘッダーに WWW-Authenticate ヘッダーがありません。
wsWebSocketContainer.pathNoHost=URIにホストが指定されていません
wsWebSocketContainer.pathWrongScheme=スキーマ[{0}]はサポートされていません。 サポートされているスキーマはwsとwssです
wsWebSocketContainer.proxyConnectFail=設定されたプロキシ[{0}]に接続できませんでした。 HTTPレスポンスコードは[{1}]でした。
wsWebSocketContainer.redirectThreshold=Locationヘッダー [{0}] の循環を検出、リダイレクト回数 [{1}] が上限値 [{2}] を超過しました。
wsWebSocketContainer.sessionCloseFail=ID [{0}] のセッションは正常に切断しませんでした。
wsWebSocketContainer.shutdown=Webアプリケーションは停止中です
wsWebSocketContainer.sslEngineFail=SSL/TLS 接続のための SSL エンジンを作成できません。
wsWebSocketContainer.unsupportedAuthScheme=HTTPレスポンスコード[{0}]を処理できませんでした。 サポートされていない認証スキーム[{1}]がレスポンスで返されました。
