# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractArchiveResourceSet.setReadOnlyFalse=Les archives basées sur WebResourceSets telles que celles des JARs sont fixées comme étant en lecture seule et ne peuvent être configurées en lecture écriture

abstractFileResourceSet.canonicalfileCheckFailed=La ressource de l''application web [{0}] du chemin [{1}] n''a pas été chargée car le chemin canonique [{2}] ne correspond pas; l''utilisation de liens symboliques peut être une cause possible

abstractResource.getContentFail=Impossible de retourner [{0}] en tant que tableau d''octets
abstractResource.getContentTooLarge=Impossible de retourner [{0}] comme tableau d''octets car la ressource a une taille de [{1}] octets qui est supérieure à la taille maximale d''un tableau d''octets

abstractResourceSet.checkPath=Le chemin demandé [{0}] n''est pas valide, il doit commencer par ''/''

cache.addFail=Incapable d''ajouter la ressource située [{0}] au cache de l''application web [{1}] parce qu''il n''y avait pas assez d''espace libre disponible après l''éviction des entrées de cache expirées - envisagez d''augmenter la taille maximale du cache
cache.backgroundEvictFail=Le processus d''arrière plan d''éviction du cache n''a pas pu nettoyer [{0}] pourcents du cache pour le contexte [{1}], il faudrait augmenter la taille maximale du cache ; après l''éviction, approximativement [{2}] KO de données restaient dans le cache
cache.objectMaxSizeTooBig=La valeur [{0}]kB pour l''objectMaxSize est plus grade que la limite de maxSize/20 son elle a été réduite à [{1}]kB\n
cache.objectMaxSizeTooBigBytes=La valeur de taille d''objet maximale pouvant être mis en cache de [{0}]kB est supérieure à Integer.MAX_VALUE qui est le maximum, la limite a donc été fixée à Integer.MAX_VALUE octets

cachedResource.invalidURL=La création d''une instance de CachedResourceURLStreamHandler a échouée car l''URL [{0}] est malformée

classpathUrlStreamHandler.notFound=Impossible de charger la ressource [{0}] en utilisant le chargeur de classe de contexte du thread ou celui de la classe actuelle

dirResourceSet.manifestFail=Impossible de lire le manifeste depuis [{0}]
dirResourceSet.notDirectory=Le répertoire qui a été spécifié pour la base et le chemin interne [{0}]{1}[{2}] n''existe pas
dirResourceSet.writeNpe=Le flux d'entrée ne peut pas être null

extractingRoot.jarFailed=Echec de l’extraction du fichier JAR [{0}]
extractingRoot.targetFailed=Echec de la création du répertoire [{0}] pour l''extraction des fichiers contenus dans le JAR

fileResource.getCanonicalPathFail=Impossible de déterminer le chemin canonique pour la ressource [{0}]
fileResource.getCreationFail=Impossible de déterminer la date de création de la ressource [{0}]
fileResource.getUrlFail=Impossible de déterminer l''URL pour la ressource [{0}]

fileResourceSet.notFile=Le fichier spécifié par ses chemins de base et internes [{0}]{1}[{2}] n''existe pas

jarResource.getInputStreamFail=Impossible d''obtenir une InputStream pour la ressource [{0}] située dans le JAR [{1}]

jarResourceRoot.invalidWebAppPath=Cette ressource se réfère toujours à un répertoire donc le webAppPath fourni doit se terminer avec ''/'' mais il était [{0}]

jarWarResourceSet.codingError=Erreur de programmation

standardRoot.checkStateNotStarted=Les resources ne peuvent pas être accédées tant qu'elles ne sont pas démarrées
standardRoot.createInvalidFile=Impossible de créer WebResourceSet à partir de [{0}]
standardRoot.createUnknownType=Impossible de créer un WebResourceSet pour le type inconnu [{0}]
standardRoot.invalidPath=Le chemin de ressources [{0}] est invalide
standardRoot.invalidPathNormal=Le chemin de ressource [{0}] a été normalisé en [{1}] ce qui est invalide
standardRoot.lockedFile=L''application web [{0}] n''a pas fermé le fichier [{1}] ouvert à partir de la trace
standardRoot.noContext=Un contexte n'a pas été configuré pour ce WebResourceRoot
standardRoot.startInvalidMain=L''ensemble de ressources principal [{0}] est invalide
standardRoot.unsupportedProtocol=Le protocole [{0}] de l''URL n''est pas supporté par cette implémentation des ressources web
