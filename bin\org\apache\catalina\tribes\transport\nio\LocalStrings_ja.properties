# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

nioReceiver.alreadyStarted=ServerSocketChannel は既に開始しています。
nioReceiver.cleanup.fail=セレクターの切断と後始末が失敗しました。
nioReceiver.clientDisconnect=レプリケーションクライアントが切断されました。ポーリングキーでエラーが発生しました。 クライアントを無視します。
nioReceiver.requestError=NioReceiverでリクエストを処理できません
nioReceiver.run.fail=レプリケーションリスナーを実行できません。
nioReceiver.start.fail=クラスタレシーバを起動出来ません
nioReceiver.stop.fail=クラスターレシーバーのセレクタを切断できませんでした。
nioReceiver.stop.threadRunning=NioReceiver スレッドは時間内に停止できませんでした。Selectorを切断するときに異常を検出した可能性があります。
nioReceiver.threadpool.fail=ThreadPool を初期化できません。リスナーを開始しませんでした。
nioReceiver.threadsExhausted=チャネルキーは登録されていますが、最後の[{0}]ミリ秒間はinterest ops がありませんでした。 （キャンセル {1}]）：[{2}]最終アクセス：[{3}]考えられる原因：すべてのスレッドが使用され、スレッドダンプを実行します。

nioReplicationTask.error.register.key=読み取り用のキーを登録中のエラー: [{0}]
nioReplicationTask.exception.drainChannel=TcpReplicationThread.drainChannelで例外をキャッチしました。
nioReplicationTask.process.clusterMsg.failed=クラスターメッセージを処理できませんでした。
nioReplicationTask.unable.ack=チャンネルから ACK を送信できません。切断されている可能性があります。: [{0}]
nioReplicationTask.unable.drainChannel.ioe=レプリケーションワーカーのIOException、チャネルを排除できません。 考えられる原因：キープアライブソケットが切断[{0}]。

nioSender.already.connected=NioSenderはすでに接続された状態です。
nioSender.datagram.already.established=データグラムチャネルはすでに確立されています。 コネクションが進行中である可能性があります。
nioSender.key.inValid=キーは無効です。キャンセルされている必要があります。
nioSender.not.connected=NioSender は接続していません。これは発生するべきではない事象です。
nioSender.receive.failedAck=失敗したackを受け取りました：org.apache.catalina.tribes.transport.Constants.FAIL_ACK_DATA
nioSender.sender.disconnected=Sender が切断されました。Selectキーを処理できません。
nioSender.socketChannel.already.established=ソケットチャネルはすでに確立されています。 接続が進行中である可能性があります。
nioSender.unable.disconnect=NioSenderを切断できません。 msg = [{0}]
nioSender.unable.receive.ack=レスポンスメッセージを受信できません。ソケットチャンネルは終端に達しています。
nioSender.unknown.state=不明な状態です。実行可能操作セット=[{0}]

parallelNioSender.error.keepalive=Sender：[{0}]の keepalive テスト中にエラーが発生しました。
parallelNioSender.operation.timedout=操作がタイムアウトしました（[{0}]ミリ秒）。
parallelNioSender.send.fail=メンバーの送信に失敗しました：[{0}]; Settingに設定します。
parallelNioSender.send.fail.retrying=[{0}] へのメッセージ送信が失敗しました。状態を SUSPECT へ変更して再送します。
parallelNioSender.send.failed=パラレル NIO 送信の失敗
parallelNioSender.sendFailed.attempt=送信に失敗しました：試行[{0}] 最大：[{1}]
parallelNioSender.sender.disconnected.notRetry=再試行していません：[{0}]; Sender が切断されました。
parallelNioSender.sender.disconnected.sendFailed=送信が失敗しました。送信先との接続も失われています。再送信を行いません。
parallelNioSender.unable.setup.NioSender=NioSenderのセットアップが出来ません。

pooledParallelSender.sender.disconnected=Sender 接続されていません。
pooledParallelSender.unable.open=NIO selectorをオープン出来ません。
pooledParallelSender.unable.retrieveSender=SenderプールからSenderを取得できません
pooledParallelSender.unable.retrieveSender.timeout=データSenderを取得できません。タイムアウト（[{0}] ms）エラー。
