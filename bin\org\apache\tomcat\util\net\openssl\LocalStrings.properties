# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

engine.ciphersFailure=Failed getting cipher list
engine.emptyCipherSuite=Empty cipher suite
engine.engineClosed=Engine is closed
engine.failedCipherSuite=Failed to enable cipher suite [{0}]
engine.failedToReadAvailableBytes=There are plain text bytes available to read but no bytes were read
engine.inboundClose=Inbound closed before receiving peer's close_notify
engine.invalidBufferArray=offset: [{0}], length: [{1}] (expected: offset <= offset + length <= srcs.length [{2}])
engine.invalidDestinationBuffersState=The state of the destination buffers changed concurrently while unwrapping bytes
engine.noRestrictSessionCreation=OpenSslEngine does not permit restricting the engine to only resuming existing sessions
engine.noSSLContext=No SSL context
engine.noSession=SSL session ID not available
engine.nullBuffer=Null buffer
engine.nullBufferInArray=Null buffer in array
engine.nullCipherSuite=Null cipher suite
engine.nullName=Null value name
engine.nullValue=Null value
engine.openSSLError=OpenSSL error: [{0}] message: [{1}]
engine.oversizedPacket=Encrypted packet is oversized
engine.unsupportedCipher=Unsupported cipher suite: [{0}] [{1}]
engine.unsupportedProtocol=Protocol [{0}] is not supported
engine.unverifiedPeer=Peer unverified
engine.writeToSSLFailed=Failed writing to SSL, returned: [{0}]

openssl.X509FactoryError=Error getting X509 factory instance
openssl.addedClientCaCert=Added client CA cert: [{0}]
openssl.applyConf=Applying OpenSSLConfCmd to SSL context
openssl.certificateVerificationFailed=Certificate verification failed
openssl.checkConf=Checking OpenSSLConf
openssl.doubleInit=SSL context already initialized, ignoring
openssl.errApplyConf=Could not apply OpenSSLConf to SSL context
openssl.errCheckConf=Error during OpenSSLConf check
openssl.errMakeConf=Could not create OpenSSLConf context
openssl.errorSSLCtxInit=Error initializing SSL context
openssl.keyManagerMissing=No key manager found
openssl.makeConf=Creating OpenSSLConf context
openssl.nonJsseCertificate=The certificate [{0}] or its private key [{1}] could not be processed using a JSSE key manager and will be given directly to OpenSSL
openssl.nonJsseChain=The certificate chain [{0}] was not specified or was not valid and JSSE requires a valid certificate chain so attempting to use OpenSSL directly
openssl.trustManagerMissing=No trust manager found

opensslconf.applyCommand=OpenSSLConf applying command (name [{0}], value [{1}])
opensslconf.applyFailed=Failure while applying OpenSSLConf to SSL context
opensslconf.checkCommand=OpenSSLConf checking command (name [{0}], value [{1}])
opensslconf.checkFailed=Failure while checking OpenSSLConf
opensslconf.failedCommand=OpenSSLConf failed command (name [{0}], value [{1}]) with result [{2}] - will be ignored
opensslconf.finishFailed=OpenSSLConf finish failed with result [{0}]
opensslconf.noCommandName=OpenSSLConf no command name - will be ignored (command value [{0}])
opensslconf.resultCommand=OpenSSLConf command (name [{0}], value [{1}]) returned [{2}]

sessionContext.nullTicketKeys=Null keys
