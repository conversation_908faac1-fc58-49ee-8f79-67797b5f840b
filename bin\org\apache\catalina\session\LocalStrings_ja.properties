# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

JDBCStore.SQLException=SQLエラー [{0}]
JDBCStore.checkConnectionClassNotFoundException=JDBCドライバクラスが見つかりません [{0}]
JDBCStore.checkConnectionDBClosed=データベース接続がnullであるか、クローズされているのが見つかりました。再オープンしてください。
JDBCStore.checkConnectionDBReOpenFail=データベースの再オープンが失敗しました。データベースがダウンしているかもしれません。
JDBCStore.checkConnectionSQLException=SQL例外が発生しました [{0}]
JDBCStore.close=データベース接続 [{0}] をクローズ中の例外です
JDBCStore.commitSQLException=クローズ前のデータベース接続のコミット中にSQL例外が発生しました
JDBCStore.loading=セッション [{0}] をデータベース [{1}] からロードします
JDBCStore.missingDataSourceName=指定された JNDI 名は正常ではありません
JDBCStore.removing=セッション [{0}] をデータベース [{1}] から削除します
JDBCStore.saving=セッション [{0}] をデータベース [{1}] に保存します
JDBCStore.wrongDataSource=JNDIデータソース[{0}]を開くことができません。

fileStore.createFailed=セッションデータを格納するディレクトリ[{0}]を作成できません
fileStore.deleteFailed=ファイル [{0}] を削除できなかったため、セッションストレージを作成できません。
fileStore.deleteSessionFailed=不要になったファイル[{0}]を削除できません
fileStore.loading=セッション [{0}] をファイル [{1}] からロードします
fileStore.removing=セッション [{0}] をファイル [{1}] から削除します
fileStore.saving=セッション [{0}] をファイル [{1}] に保存します

managerBase.container.noop=Contexts 以外のコンテナに追加されたManagerは決して使用されません。
managerBase.contextNull=マネージャーが使う前のコンテキストは null でなければなりません。
managerBase.createSession.ise=createSession: アクティブセッションが多すぎます
managerBase.sessionAttributeNameFilter=名前フィルタ[{1}]と一致しなかったため、[{0}]というセッション属性をスキップしました。
managerBase.sessionAttributeValueClassNameFilter=値タイプ[{1}]がフィルタ[{2}]と一致しなかったため、[{0}]という名前のセッション属性をスキップしました。
managerBase.sessionNotFound=セッション [{0}] が見つかりません。
managerBase.sessionTimeout=無効なセッションタイムアウト設定です [{0}]
managerBase.setContextNotNew=NEW 状態ではないマネージャーに関連付けられた Context を変更するために setContext() を呼び出すことは禁止されています。

persistentManager.backupMaxIdle=[{1}]秒間アイドルしているセッション [{0}] を保存するためにバックアップしています
persistentManager.deserializeError=セッション [{0}] をデシリアライズ中のエラー
persistentManager.isLoadedError=セッション [{0}] がメモリーに読み込み済みかチェック中のエラー
persistentManager.loading=[{0}] の永続化セッションをロードします
persistentManager.removeError=ストアからセッション[{0}]削除中のエラー
persistentManager.serializeError=セッション [{0}] をシリアライズ中のエラーです: [{1}]
persistentManager.storeClearError=ストア上の全セッション消去中のエラー
persistentManager.storeKeysException=セッションストアからセッションIDのリストを取得できませんでした。セッションストアが空の可能性があります。
persistentManager.storeLoadError=ストアからのセッションスワップイン中のエラー
persistentManager.storeLoadKeysError=データストアからセッションキーを読み込み中のエラー
persistentManager.storeSizeException=セッションストアに格納しているセッションの総数を確認できなかったため、セッションストアは空になっているものとして扱います。
persistentManager.swapIn=セッション [{0}] をスワップインしています
persistentManager.swapInException=スワップイン時のストア内の例外：[{0}]
persistentManager.swapInInvalid=スワップしたセッション [{0}] は不正です。
persistentManager.swapMaxIdle=[{1}]秒間アイドルしているセッション [{0}] を保存するためにスワップしています
persistentManager.swapTooManyActive=セッションが多すぎるので、[{1}]秒アイドルしているセッション [{0}] をスワップアウトします
persistentManager.tooManyActive=アクティブセッションが多すぎます、[{0}]、スワップアウトするためにアイドルセッションを探しています
persistentManager.unloading=[{0}] の永続化セッションを保存します

standardManager.deletePersistedFileFail=永続化セッションを読み込んだ [{0}] を削減できません。ファイルが残っていると将来セッションの永続化に失敗する可能性があります。
standardManager.loading=[{0}] から永続化セッションをロードしています
standardManager.loading.exception=持続されたセッションをロード中にExceptionが発生しました
standardManager.managerLoad=永続記憶装置からセッションをロード中の例外です
standardManager.managerUnload=永続記憶装置にセッションをアンロード中の例外です
standardManager.unloading=持続されたセッションを [{0}] に保存します
standardManager.unloading.debug=永続セッションのアンロード
standardManager.unloading.nosessions=アンロードする永続セッションがありません

standardSession.attributeEvent=セッション属性イベントリスナが例外を投げました
standardSession.bindingEvent=セッションバインディングイベントリスナが例外を投げました
standardSession.getAttribute.ise=getAttribute: セッションは既に無効化されています
standardSession.getAttributeNames.ise=getAttributeNames: セッションは既に無効化されています
standardSession.getCreationTime.ise=getCreationTime: セッションは既に無効化されています
standardSession.getIdleTime.ise=getIdleTime: 無効なセッションです。
standardSession.getLastAccessedTime.ise=getLastAccessedTime: セッションは既に無効化されています
standardSession.getThisAccessedTime.ise=getThisAccessedTime: セッションは既に無効化されています
standardSession.getValueNames.ise=getValueNames: セッションは既に無効化されています
standardSession.invalidate.ise=invalidate: セッションは既に無効化されています
standardSession.isNew.ise=isNew: セッションは既に無効化されています
standardSession.logoutfail=有効期限を超過したセッションからユーザーがログアウトする時、例外が発生しました。
standardSession.notDeserializable=セッション[{1}]のセッション属性[{0}]をデシリアライズできません。
standardSession.notSerializable=セッション [{1}] のためにセッション属性 [{0}] をシリアライズできません
standardSession.principalNotDeserializable=セッション [{0}] のプリンシパルオブジェクトをデシリアライズできません
standardSession.principalNotSerializable=セッション [{0}] のプリンシパルオブジェクトをシリアライズできません
standardSession.removeAttribute.ise=removeAttribute: セッションは既に無効化されています
standardSession.sessionEvent=セッションイベントリスナが例外を投げました
standardSession.setAttribute.iae=setAttribute: シリアライズできない属性です
standardSession.setAttribute.ise=setAttribute: セッションは既に無効化されています
standardSession.setAttribute.namenull=setAttribute: nameパラメタはnullであってはいけません
