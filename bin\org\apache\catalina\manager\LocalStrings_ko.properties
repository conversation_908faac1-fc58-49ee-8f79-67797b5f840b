# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

htmlManagerServlet.appsAvailable=실행 중
htmlManagerServlet.appsExpire=세션들을 만료시키기
htmlManagerServlet.appsName=표시 이름
htmlManagerServlet.appsPath=경로
htmlManagerServlet.appsReload=다시 로드
htmlManagerServlet.appsSessions=세션들
htmlManagerServlet.appsStart=시작
htmlManagerServlet.appsStop=중지
htmlManagerServlet.appsTasks=명령들
htmlManagerServlet.appsTitle=애플리케이션들
htmlManagerServlet.appsUndeploy=배치된 것을 제거
htmlManagerServlet.appsVersion=버전
htmlManagerServlet.configReloadButton=다시 읽기
htmlManagerServlet.configSslHostName=TLS 호스트 이름 (선택 사항)
htmlManagerServlet.configSslReloadTitle=TLS 설정 파일들을 다시 읽습니다.
htmlManagerServlet.configTitle=설정
htmlManagerServlet.connectorStateAliveSocketCount=Keep alive 소켓 개수:
htmlManagerServlet.connectorStateBytesReceived=수신된 바이트 크기:
htmlManagerServlet.connectorStateBytesSent=전송된 바이트 크기:
htmlManagerServlet.connectorStateErrorCount=오류 개수:
htmlManagerServlet.connectorStateHint=P: 요청을 파싱 또는 준비, S: 서비스, F: 완료 R: 준비 K: Keepalive
htmlManagerServlet.connectorStateMaxProcessingTime=최대 처리 시간:
htmlManagerServlet.connectorStateMaxThreads=최대 쓰레드 개수:
htmlManagerServlet.connectorStateProcessingTime=처리 시간:
htmlManagerServlet.connectorStateRequestCount=요청 회수:
htmlManagerServlet.connectorStateTableTitleBRecv=받은 바이트 크기
htmlManagerServlet.connectorStateTableTitleBSent=전송된 바이트 크기
htmlManagerServlet.connectorStateTableTitleClientAct=클라이언트 (실제)
htmlManagerServlet.connectorStateTableTitleClientForw=클라이언트 (Forwarded)
htmlManagerServlet.connectorStateTableTitleRequest=요청
htmlManagerServlet.connectorStateTableTitleStage=단계
htmlManagerServlet.connectorStateTableTitleTime=시간
htmlManagerServlet.connectorStateTableTitleVHost=가상호스트
htmlManagerServlet.connectorStateThreadBusy=현재 사용중인 쓰레드들:
htmlManagerServlet.connectorStateThreadCount=현재 쓰레드 개수:
htmlManagerServlet.deployButton=배치
htmlManagerServlet.deployConfig=XML 설정 파일 경로:
htmlManagerServlet.deployServer=서버에 있는 디렉토리 또는 WAR 파일을 배치합니다.
htmlManagerServlet.deployTitle=배치
htmlManagerServlet.deployUpload=배치할 WAR 파일
htmlManagerServlet.deployUploadFail=실패 - 배치관리자에서 업로드 실패, 예외: [{0}]
htmlManagerServlet.deployUploadFile=업로드할 WAR 파일을 선택하십시오.
htmlManagerServlet.deployUploadInServerXml=실패 - 컨텍스트가 server.xml에 정의되어 있다면, War 파일 [{0}]은(는) 업로드될 수 없습니다.
htmlManagerServlet.deployUploadNoFile=실패 - 파일 업로드 실패. 파일이 없습니다.
htmlManagerServlet.deployUploadNotWar=실패 - 업로드된 파일 [{0}]은(는) 반드시 .war이어야 합니다.
htmlManagerServlet.deployUploadWarExists=실패 - War 파일 [{0}]이(가) 이미 서버에 존재합니다.
htmlManagerServlet.deployWar=WAR 또는 디렉토리 경로:
htmlManagerServlet.diagnosticsLeak=웹 애플리케이션이 중지되거나, 다시 로드되거나, 또는 배치 제거될 때, 메모리 누수를 유발하는지 여부를 살펴보려 점검합니다.
htmlManagerServlet.diagnosticsLeakButton=메모리 누수 찾기
htmlManagerServlet.diagnosticsLeakWarning=이 진단 점검은 Full Garbage Collection을 개시할 것입니다. 프로덕션 시스템들에서는 극도의 주의를 기울여 사용하십시오.
htmlManagerServlet.diagnosticsSsl=TLS Connector 설정 진단
htmlManagerServlet.diagnosticsSslConnectorCertsButton=인증서들
htmlManagerServlet.diagnosticsSslConnectorCertsText=설정된 TLS 가상 호스트들과 그들 각각의 인증서 체인의 목록
htmlManagerServlet.diagnosticsSslConnectorCipherButton=Cipher들
htmlManagerServlet.diagnosticsSslConnectorCipherText=설정된 TLS 가상 호스트들과 각각을 위한 cipher들의 목록을 표시합니다.
htmlManagerServlet.diagnosticsSslConnectorTrustedCertsButton=신뢰되는 인증서들
htmlManagerServlet.diagnosticsSslConnectorTrustedCertsText=설정된 TLS 가상 호스트들과 각각을 위한 신뢰되는 인증서들의 목록을 표시합니다.
htmlManagerServlet.diagnosticsTitle=시스템 진단
htmlManagerServlet.expire.explain=idle 값 &ge;
htmlManagerServlet.expire.unit=분
htmlManagerServlet.findleaksList=다음 웹 애플리케이션들이 중지되었지만 (다시 로드되거나, 배치가 제거되어), 이전 실행 시에 로드되었던 클래스들이 여전히 메모리에 남아 있어서, 메모리 누수를 유발할 수 있습니다. (확인하려면 프로파일러를 사용하십시오):\n\
\n
htmlManagerServlet.findleaksNone=어떤 웹 애플리케이션도, 중지 시 또는 다시 로드될 때 또는 배치로부터 제거 될 때, 메모리 누수를 유발하지 않은 것 같습니다.
htmlManagerServlet.helpHtmlManager=HTML 매니저 도움말
htmlManagerServlet.helpHtmlManagerFile=../docs/html-manager-howto.html
htmlManagerServlet.helpManager=매니저 도움말
htmlManagerServlet.helpManagerFile=../docs/manager-howto.html
htmlManagerServlet.jvmFreeMemory=유휴 메모리:
htmlManagerServlet.jvmMaxMemory=최대 메모리:
htmlManagerServlet.jvmTableTitleInitial=초기
htmlManagerServlet.jvmTableTitleMaximum=최대값
htmlManagerServlet.jvmTableTitleMemoryPool=메모리 풀
htmlManagerServlet.jvmTableTitleTotal=전체
htmlManagerServlet.jvmTableTitleType=타입
htmlManagerServlet.jvmTableTitleUsed=사용된 메모리
htmlManagerServlet.jvmTotalMemory=전체 메모리:
htmlManagerServlet.list=애플리케이션들의 목록을 표시
htmlManagerServlet.manager=매니저
htmlManagerServlet.messageLabel=메시지:
htmlManagerServlet.noManager=-
htmlManagerServlet.noVersion=지정 안됨
htmlManagerServlet.osAvailableMemory=가용 메모리:
htmlManagerServlet.osFreePageFile=유휴 페이지 파일:
htmlManagerServlet.osKernelTime=프로세스 커널 타임:
htmlManagerServlet.osMemoryLoad=메모리 로드:
htmlManagerServlet.osPhysicalMemory=물리적 메모리:
htmlManagerServlet.osTotalPageFile=전체 페이지 파일:
htmlManagerServlet.osUserTime=프로세스 User Time:
htmlManagerServlet.serverHostname=호스트명
htmlManagerServlet.serverIPAddress=IP 주소
htmlManagerServlet.serverJVMVendor=JVM 벤더
htmlManagerServlet.serverJVMVersion=JVM 버전
htmlManagerServlet.serverOSArch=운영체제 아키텍처
htmlManagerServlet.serverOSName=운영체제 이름
htmlManagerServlet.serverOSVersion=운영체제 버전
htmlManagerServlet.serverTitle=서버 정보
htmlManagerServlet.serverVersion=Tomcat 버전
htmlManagerServlet.title=Tomcat 웹 애플리케이션 매니저

jmxProxyServlet.noBeanFound=객체 이름 [{0}]인 MBean을 찾을 수 없습니다
jmxProxyServlet.noOperationOnBean=[{3}] 인 객체 이름 [{2}] 에서 [{1}] 아규먼트가 있는 [{0}] 오퍼레이션을 찾을 수가 없습니다

managerServlet.alreadyContext=실패 - 애플리케이션이 이미 경로 [{0}]에 존재합니다.
managerServlet.certsNotAvailable=이 Connector로부터, 인증서 정보를 런타임에 구할 수 없습니다.
managerServlet.deleteFail=실패 - [{0}]을(를) 삭제할 수 없습니다. 이 파일이 계속해서 존재하면 문제들을 일으킬 수 있습니다.
managerServlet.deployFailed=실패 - 컨텍스트 경로 [{0}]에, 애플리케이션을 배치하지 못했습니다.
managerServlet.deployed=OK - 컨텍스트 경로 [{0}]에 애플리케이션을 배치했습니다.
managerServlet.deployedButNotStarted=실패 - 컨텍스트 경로 [{0}]에 있는 애플리케이션을 배치했으나, 컨텍스트가 시작되지 못했습니다.
managerServlet.exception=실패 - 예외 발생 [{0}]
managerServlet.findleaksFail=실패 - 잠재 메모리 누수 찾기 실패: 호스트가 StandardHost의 인스턴스가 아닙니다.
managerServlet.findleaksList=OK - 다음 애플리케이션들에서 잠재적인 메모리 누수들이 발견되었습니다:
managerServlet.findleaksNone=OK - 메모리 누수가 발견 안됨
managerServlet.inService=실패 - 애플리케이션 [{0}]이(가) 이미 서비스되고 있습니다.
managerServlet.invalidCommand=실패 - 명령 [{0}]을(를) 위해 유효하지 않은 파라미터들이 제공되었습니다.
managerServlet.invalidPath=실패 - 유효하지 않은 컨텍스트 경로 [{0}]이(가) 지정되었습니다.
managerServlet.listed=OK - 가상 호스트 [{0}]을(를) 위한 애플리케이션들의 목록이 표시되었습니다.
managerServlet.mkdirFail=실패 - 디렉토리 [{0}]을(를) 생성할 수 없습니다.
managerServlet.noCommand=실패 - 명령이 지정되지 않았습니다.
managerServlet.noContext=실패 - [{0}](이)라는 이름을 가진 컨텍스트가 없습니다.
managerServlet.noGlobal=실패 - 가용한 글로벌 JNDI 리소스들이 없습니다.
managerServlet.noManager=실패 - 경로 [{0}]을(를) 위한 매니저가 없습니다.
managerServlet.noSelf=실패 - 매니저는 자기 자신을 다시 로드하거나, 중지시키거나, 자신의 배치를 제거할 수 없습니다.
managerServlet.noWrapper=컨테이너가 이 서블릿을 위해 setWrapper()를 호출하지 않았습니다.
managerServlet.notDeployed=실패 - 컨텍스트 [{0}]이(가) server.xml에 정의되어 있어, 배치를 제거할 수 없습니다.
managerServlet.notSslConnector=SSL이 이 connector를 위해 사용 가능 상태가 아닙니다.
managerServlet.objectNameFail=실패 - 매니저 서블릿을 위한 객체 이름 [{0}]을(를) 등록할 수 없습니다.
managerServlet.postCommand=실패 - GET 요청을 통해 명령 [{0}]을(를) 사용하려 시도했으나, POST 요청이 요구됩니다.
managerServlet.reloaded=OK - 컨텍스트 경로 [{0}]의 애플리케이션을 다시 로드했습니다.
managerServlet.renameFail=실패 - [{0}]을(를) [{1}](으)로 이름을 변경할 수 없습니다. 이는 이후의 배치 작업들에서 문제들을 일으킬 수 있습니다.
managerServlet.resourcesAll=OK - 모든 타입들의 글로벌 리소스들이 목록으로 표시되었습니다.
managerServlet.resourcesType=OK - 타입이 [{0}]인 글로벌 리소스들의 목록을 표시했습니다.
managerServlet.saveFail=실패 - 설정을 저장하지 못했습니다: [{0}]
managerServlet.saved=OK - 서버 설정이 저장되었습니다.
managerServlet.savedContext=OK - 컨텍스트 [{0}]의 설정이 저장되었습니다.
managerServlet.savedContextFail=실패 - Context [{0}] 설정을 저장하지 못했습니다.
managerServlet.serverInfo=OK - 서버 정보\n\
Tomcat 버전: [{0}]\n\
운영체제 이름: [{1}]\n\
운영체제 버전: [{2}]\n\
운영체제 아키텍처: [{3}]\n\
JVM 버전: [{4}]\n\
JVM 벤더: [{5}]
managerServlet.sessiondefaultmax=세션 비활성화 최대 시간의 기본 값은 [{0}]분입니다.
managerServlet.sessions=OK - 컨텍스트 경로 [{0}]의 애플리케이션을 위한 세션 정보
managerServlet.sessiontimeout=[{0}]분: [{1}]개의 세션들
managerServlet.sessiontimeout.expired=[{0}]분: [{1}]개의 세션들이 만료되었습니다.
managerServlet.sessiontimeout.unlimited=무제한 시간: [{0}] 세션들
managerServlet.sslConnectorCerts=OK - Connector / 인증서 체인 정보
managerServlet.sslConnectorCiphers=OK - Connector / SSL Cipher 정보
managerServlet.sslConnectorTrustedCerts=OK - Connector / 신뢰되는 인증서 정보
managerServlet.sslReload=OK - [{0}]을(를) 위해 TLS 설정을 다시 로드했습니다.
managerServlet.sslReloadAll=OK - 모든 TLS 가상 호스트들을 위한 TLS 설정을 다시 로드했습니다.
managerServlet.sslReloadFail=실패 - TLS 설정을 다시 로드하지 못했습니다.
managerServlet.startFailed=실패 - 컨텍스트 경로 [{0}]의 애플리케이션이 시작될 수 없었습니다.
managerServlet.started=OK - 컨텍스트 경로 [{0}]의 애플리케이션이 시작되었습니다.
managerServlet.stopped=OK - 컨텍스트 경로 [{0}]의 애플리케이션을 중지시켰습니다.
managerServlet.storeConfig.noMBean=실패 - [{0}]에서 등록된 StoreConfig MBean이 없습니다. 보통 StoreConfigLifecycleListener에 의해 등록이 수행됩니다.
managerServlet.threaddump=OK - JVM 쓰레드 덤프
managerServlet.trustedCertsNotConfigured=이 가상 호스트를 위한 신뢰되는 인증서들이 설정되어 있지 않습니다.
managerServlet.undeployed=OK - 컨텍스트 경로 [{0}]에 배치된 애플리케이션을 제거했습니다.
managerServlet.unknownCommand=실패 - 알 수 없는 명령: [{0}]
managerServlet.vminfo=OK - VM 정보

statusServlet.complete=서버의 전체 상태
statusServlet.title=서버 상태
