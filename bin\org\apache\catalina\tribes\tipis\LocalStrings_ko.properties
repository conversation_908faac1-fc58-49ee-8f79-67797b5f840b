# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractReplicatedMap.broadcast.noReplies=broadcast 메소드가 0개의 응답을 받았습니다. 아마도 제한 시간 초과인 듯 합니다.
abstractReplicatedMap.heartbeat.failed=AbstractReplicatedMap.ping 메시지를 보낼 수 없습니다.
abstractReplicatedMap.init.completed=AbstractReplicatedMap[{0}] 초기화가 [{1}] 밀리초 내에 완료되었습니다.
abstractReplicatedMap.init.start=컨텍스트 이름 [{0}]을(를) 사용하여 AbstractReplicatedMap을 초기화합니다.
abstractReplicatedMap.leftOver.ignored=메시지 [{0}]은(는) 무시됩니다.
abstractReplicatedMap.leftOver.pingMsg=PING 메시지가 제한 시간을 초과하여 수신되었습니다. 해당 map 멤버 [{0}]은(는), map 멤버십으로부터 이미 제거되었을 수 있습니다.
abstractReplicatedMap.mapMember.unavailable=멤버 [{0}]은(는) 아직 가용하지 않습니다.
abstractReplicatedMap.mapMemberAdded.added=Map 멤버가 추가되었습니다:[{0}]
abstractReplicatedMap.mapMemberAdded.nullMember=통지된 멤버는 멤버십에 등록되어 있지 않습니다: [{0}]
abstractReplicatedMap.member.disappeared=멤버 [{0}]이(가) 사라졌습니다. 관련 Map 엔트리들은 새 노드로 다시 위치시킬 것입니다.
abstractReplicatedMap.ping.stateTransferredMember=멤버 [{0}]이(가), 상태가 이전된 상태이지만, 아직 가용하지 않습니다.
abstractReplicatedMap.ping.timeout=Map [{1}] 내의 멤버 [{0}]은(는) ping 처리 중에 제한 시간 초과되었습니다.
abstractReplicatedMap.relocate.complete=Map 엔트리들을 재위치시키는 작업이 [{0}] 밀리초에 완료되었습니다.
abstractReplicatedMap.transferState.noReplies=transferState에서 응답이 하나도 없었습니다. 아마도 제한 시간 초과된 듯합니다.
abstractReplicatedMap.unable.deserialize.MapMessage=MapMessage를 역직렬화할 수 없습니다.
abstractReplicatedMap.unable.diffObject=객체에 대한 diff를 생성할 수 없습니다. 대신 전체 객체를 복제하겠습니다.
abstractReplicatedMap.unable.get=AbstractReplicatedMap.get 오퍼레이션에서, 데이터를 복제하여 반환할 수 없습니다.
abstractReplicatedMap.unable.put=AbstractReplicatedMap.put 오퍼레이션을 위한 데이터를 외부로 복제할 수 없습니다.
abstractReplicatedMap.unable.relocate=[{0}]을(를) 새로운 백업 노드로 재위치시킬 수 없습니다.
abstractReplicatedMap.unable.remove=AbstractReplicatedMap.remove 오퍼레이션에서, 복제를 위한 데이터를 전송할 수 없습니다.
abstractReplicatedMap.unable.replicate=데이터를 복제할 수 없습니다.
abstractReplicatedMap.unable.retrieve=키 [{0}]을(를) 위한 원격 객체를 검색할 수 없습니다.
abstractReplicatedMap.unable.transferState=AbstractReplicatedMap 상태를 전이시킬 수 없습니다.
abstractReplicatedMap.unableApply.diff=키 [{0}]에 diff를 적용할 수 없습니다.
abstractReplicatedMap.unableSelect.backup=백업 노드를 선택할 수 없습니다.
abstractReplicatedMap.unableSend.startMessage=Map 시작 메시지를 전송할 수 없습니다.
abstractReplicatedMap.unableStart=ReplicatedMap을 시작할 수 없습니다.

lazyReplicatedMap.unableReplicate.backup=백업 키 [{0}]을(를) 백업 [{1}]에 복제할 수 없습니다. 사유:[{2}]
lazyReplicatedMap.unableReplicate.proxy=프록시 키 [{0}]을(를) 백업인 [{1}](으)로 복제할 수 없습니다. 사유: [{2}]

mapMessage.deserialize.error.key=MapMessage.key()에서 역직렬화에 실패했습니다.
mapMessage.deserialize.error.value=MapMessage 값을 역직렬화하지 못했습니다.

replicatedMap.member.disappeared=멤버 [{0}]이(가) 사라졌습니다. 관련 Map의 엔트리들은 새 노드로 다시 위치시킬 것입니다.
replicatedMap.relocate.complete=Map의 엔트리들 모두 다시 위치시켰습니다. 소요시간: [{0}] 밀리초.
replicatedMap.unable.relocate=멤버 [{0}]에 새로운 백업 노드를 다시 지정할 수 없습니다.
replicatedMap.unableReplicate.completely=백업 키 [{0}]을(를) 복제할 수 없습니다. 성공 노드들: [{1}]. 실패 노드들: [{2}]
