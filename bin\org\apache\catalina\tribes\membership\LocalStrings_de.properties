# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

mcastServiceImpl.bind=Versuche Multicast Socket an [{0}:{1}] zu binden
mcastServiceImpl.recovery.failed=Wiederherstellungsversuch Nummer [{0}] ist fehlgeschlagen, versuche es in [{1}] Sekunden erneut
mcastServiceImpl.send.failed=Eine mcast-Nachricht kann nicht gesendet werden.
mcastServiceImpl.send.running=McastService.send läuft bereits
mcastServiceImpl.setSoTimeout=Setze Cluster mcast soTimeout auf [{0}]
mcastServiceImpl.setTTL=Setze Cluster mcast TTL auf [{0}]
mcastServiceImpl.unableReceive.broadcastMessage=Konnte Broadcast-Nachricht nicht empfangen.

memberImpl.notEnough.bytes=Nicht genug bytes im Mitgliederpaket
