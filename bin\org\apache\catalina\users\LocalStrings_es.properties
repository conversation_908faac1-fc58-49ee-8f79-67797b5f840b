# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

memoryUserDatabase.fileNotFound=El usuario de base de datos especificado [{0}] no pudo ser encontrado
memoryUserDatabase.notPersistable=La base de datos de usuario no es persistible - no hay permisos de grabación sobre el directorio
memoryUserDatabase.nullGroup=Se ha especificado un nombre de grupo nulo o de tamaño cero. Se ignora el grupo.
memoryUserDatabase.nullRole=Se ha especificado un nombre rol nulo o de tamaño cero. Se ignora el rol.
memoryUserDatabase.nullUser=Se ha especificado un nombre de usuario nulo o de tamaño cero. Se ignora el usuario.
memoryUserDatabase.readOnly=User database has been configured to be read only. Changes cannot be saved
memoryUserDatabase.renameNew=Imposible de renombrar el archivo nuevo a [{0}]
memoryUserDatabase.renameOld=Imposible de renombrar el archivo original a [{0}]
memoryUserDatabase.restoreOrig=No se puede restablecer [{0}] al archivo original
memoryUserDatabase.writeException=IOException durante la escritura hacia [{0}]
memoryUserDatabase.xmlFeatureEncoding=Excepción al configurar el resumidor para permitir nombres codificados en java en los ficheros XML. Sólo se soportarán los nombres con codificación IANA.
