# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

beanNameELResolver.beanReadOnly=Bean 이름 [{0}]은(는) 읽기 전용입니다.

elProcessor.defineFunctionInvalidClass=클래스 [{0}]은(는) public 클래스가 아닙니다.
elProcessor.defineFunctionInvalidMethod=클래스 [{1}]의 메소드 [{0}]은(는) public static 메소드가 아닙니다.
elProcessor.defineFunctionInvalidParameterList=클래스 [{2}]의 메소드 [{1}]을(를) 위한 파라미터 목록 [{0}]이(가) 유효하지 않습니다.
elProcessor.defineFunctionInvalidParameterTypeName=클래스 [{2}]의 메소드 [{1}]을(를) 위한 파라미터 타입 [{0}]은(는) 유효하지 않습니다.
elProcessor.defineFunctionNoMethod=클래스 [{1}]에서 public static 메소드 [{0}]을(를) 찾을 수 없었습니다.
elProcessor.defineFunctionNullParams=하나 이상의 입력 파라미터들이 널이었습니다.

expressionFactory.cannotCreate=타입 [{0}]의 ExpressionFactory를 생성할 수 없습니다.
expressionFactory.cannotFind=타입 [{0}]의 ExpressionFactory를 찾을 수 없습니다.
expressionFactory.readFailed=[{0}]을(를) 읽지 못했습니다.

importHandler.ambiguousImport=이미 임포트된 [{1}]와(과) 충돌하기에, 클래스 [{0}]은(는) 임포트될 수 없었습니다.
importHandler.ambiguousStaticImport=정적 임포트 [{0}]은(는), 이미 임포트된 [{1}]와(과) 충돌하기 때문에 처리될 수 없었습니다.
importHandler.classNotFound=클래스 [{0}]을(를) 찾을 수 없어서 임포트될 수 없습니다.
importHandler.invalidClass=클래스 [{0}]은(는), 반드시 public이어야 하고 abstract가 아니어야 하며 인터페이스가 아니어야 합니다.
importHandler.invalidClassName=임포트할 클래스 이름 [{0}]은(는) 반드시 패키지를 포함해야 합니다.
importHandler.invalidClassNameForStatic=정적 임포트 [{1}]을(를) 위해 지정된 클래스 [{0}]은(는) 유효하지 않습니다.
importHandler.invalidStaticName=임포트할 정적 메소드나 필드의 이름은, 반드시 클래스를 포함해야 합니다: [{0}]
importHandler.staticNotFound=임포트 [{2}]을(를) 위한 클래스 [{1}] 내에서, 정적 임포트 [{0}]을(를) 찾을 수 없습니다.

lambdaExpression.tooFewArgs=적어도 [{1}]개의 아규먼트들을 요구하는 람다 표현식에, 단지 [{0}]개의 아규먼트들만이 제공되었습니다.

objectNotAssignable=타입 [{0}]의 객체를, 타입 [{1}]의 객체 배열에 추가할 수 없습니다.
propertyNotFound=타입 [{0}]에서 프로퍼티 [{1}]을(를) 찾을 수 없습니다.
propertyNotReadable=타입 [{0}]에서 프로퍼티 [{1}]을(를) 읽을 수 없습니다.
propertyNotWritable=타입 [{0}]에서 쓰기 가능하지 않은 프로퍼티 [{1}]
propertyReadError=타입 [{0}]에서 [{1}]을(를) 읽는 중 오류 발생
propertyWriteError=타입 [{0}]에 [{1}]을(를) 쓰는 중 오류 발생

staticFieldELResolver.methodNotFound=클래스 [{1}]에 [{0}](이)라는 이름의 public static 메소드가 발견되지 않습니다.
staticFieldELResolver.notFound=클래스 [{1}]에, [{0}](이)라는 이름의 public static 필드가 없습니다.
staticFieldELResolver.notWriteable=정적 필드들에 대해 쓰기는 허용되지 않습니다 (이번 경우는 클래스 [{1}]의 필드 [{0}]).

util.method.ambiguous=애매하지 않고 명백하게 메소드를 찾을 수 없습니다: {0}.{1}({2})
util.method.notfound=메소드를 찾을 수 없습니다: {0}.{1}({2})
