# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractReplicatedMap.broadcast.noReplies=broadcast received 0 replies, probably a timeout.
abstractReplicatedMap.heartbeat.failed=Unable to send AbstractReplicatedMap.ping message
abstractReplicatedMap.init.completed=AbstractReplicatedMap[{0}] initialization was completed in [{1}] ms.
abstractReplicatedMap.init.start=Initializing AbstractReplicatedMap with context name:[{0}]
abstractReplicatedMap.leftOver.ignored=Message[{0}] is ignored.
abstractReplicatedMap.leftOver.pingMsg=PING message has been received beyond the timeout period. The map member[{0}] might have been removed from the map membership.
abstractReplicatedMap.mapMember.unavailable=Member[{0}] is not available yet.
abstractReplicatedMap.mapMemberAdded.added=Map member added:[{0}]
abstractReplicatedMap.mapMemberAdded.nullMember=Notified member is not registered in the membership:[{0}].
abstractReplicatedMap.member.disappeared=Member[{0}] disappeared. Related map entries will be relocated to the new node.
abstractReplicatedMap.ping.stateTransferredMember=Member[{0}] is state transferred but not available yet.
abstractReplicatedMap.ping.timeout=Member[{0}] in the Map[{1}] has timed-out in the ping processing.
abstractReplicatedMap.relocate.complete=Relocation of map entries was complete in [{0}] ms.
abstractReplicatedMap.transferState.noReplies=Transfer state, 0 replies, probably a timeout.
abstractReplicatedMap.unable.deserialize.MapMessage=Unable to deserialize MapMessage.
abstractReplicatedMap.unable.diffObject=Unable to diff object. Will replicate the entire object instead.
abstractReplicatedMap.unable.get=Unable to replicate out data for an AbstractReplicatedMap.get operation
abstractReplicatedMap.unable.put=Unable to replicate out data for an AbstractReplicatedMap.put operation
abstractReplicatedMap.unable.relocate=Unable to relocate[{0}] to a new backup node
abstractReplicatedMap.unable.remove=Unable to replicate out data for an AbstractReplicatedMap.remove operation
abstractReplicatedMap.unable.replicate=Unable to replicate data.
abstractReplicatedMap.unable.retrieve=Unable to retrieve remote object for key:[{0}]
abstractReplicatedMap.unable.transferState=Unable to transfer AbstractReplicatedMap state.
abstractReplicatedMap.unableApply.diff=Unable to apply diff to key:[{0}]
abstractReplicatedMap.unableSelect.backup=Unable to select backup node.
abstractReplicatedMap.unableSend.startMessage=Unable to send map start message.
abstractReplicatedMap.unableStart=Unable to start replicated map.

lazyReplicatedMap.unableReplicate.backup=Unable to replicate backup key:[{0}] to backup:[{1}]. Reason:[{2}]
lazyReplicatedMap.unableReplicate.proxy=Unable to replicate proxy key:[{0}] to backup:[{1}]. Reason:[{2}]

mapMessage.deserialize.error.key=Failed to deserialize MapMessage key
mapMessage.deserialize.error.value=Failed to deserialize MapMessage value

replicatedMap.member.disappeared=Member[{0}] disappeared. Related map entries will be relocated to the new node.
replicatedMap.relocate.complete=Relocation of map entries was complete in [{0}] ms.
replicatedMap.unable.relocate=Unable to relocate[{0}] to a new backup node
replicatedMap.unableReplicate.completely=Unable to replicate backup key:[{0}]. Success nodes:[{1}]. Failed nodes:[{2}].
