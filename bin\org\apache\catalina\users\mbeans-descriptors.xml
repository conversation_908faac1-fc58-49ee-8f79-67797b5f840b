<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE mbeans-descriptors PUBLIC
   "-//Apache Software Foundation//DTD Model MBeans Configuration File"
   "http://jakarta.apache.org/commons/dtds/mbeans-descriptors.dtd">
<mbeans-descriptors>
  <mbean         name="MemoryUserDatabase"
            className="org.apache.catalina.mbeans.MemoryUserDatabaseMBean"
          description="In-memory user and group database"
               domain="Users"
                group="UserDatabase"
                 type="org.apache.catalina.users.MemoryUserDatabase">

    <attribute   name="groups"
          description="MBean Names of all defined groups"
                 type="[Ljava.lang.String;"
            writeable="false"/>

    <attribute   name="pathname"
          description="Relative or absolute pathname to database file"
                 type="java.lang.String"/>

    <attribute   name="roles"
          description="MBean Names of all defined roles"
                 type="[Ljava.lang.String;"
            writeable="false"/>

    <attribute   name="users"
          description="MBean Names of all defined users"
                 type="[Ljava.lang.String;"
            writeable="false"/>

    <attribute   name="readonly"
          description="No persistent save of the user database"
                 type="boolean"
            writeable="false"/>

    <attribute   name="writeable"
          description="Check if user database is writeable"
                   is="true"
           writeable="false"/>

    <operation   name="createGroup"
          description="Create new group and return MBean name"
               impact="ACTION"
           returnType="java.lang.String">
      <parameter name="groupname"
          description="Group name of the new group"
                 type="java.lang.String"/>
      <parameter name="description"
          description="Description of the new group"
                 type="java.lang.String"/>
    </operation>

    <operation   name="createRole"
          description="Create new role and return MBean name"
               impact="ACTION"
           returnType="java.lang.String">
      <parameter name="rolename"
          description="Role name of the new role"
                 type="java.lang.String"/>
      <parameter name="description"
          description="Description of the new role"
                 type="java.lang.String"/>
    </operation>

    <operation   name="createUser"
          description="Create new user and return MBean name"
               impact="ACTION"
           returnType="java.lang.String">
      <parameter name="username"
          description="User name of the new user"
                 type="java.lang.String"/>
      <parameter name="password"
          description="Password of the new user"
                 type="java.lang.String"/>
      <parameter name="fullName"
          description="Full name of the new user"
                 type="java.lang.String"/>
    </operation>

    <operation   name="findGroup"
          description="Return MBean Name of the specified group (if any)"
               impact="INFO"
           returnType="java.lang.String">
      <parameter name="groupname"
          description="Group name of the requested group"
                 type="java.lang.String"/>
    </operation>

    <operation   name="findRole"
          description="Return MBean Name of the specified role (if any)"
               impact="INFO"
           returnType="java.lang.String">
      <parameter name="rolename"
          description="Role name of the requested role"
                 type="java.lang.String"/>
    </operation>

    <operation   name="findUser"
          description="Return MBean Name of the specified user (if any)"
               impact="INFO"
           returnType="java.lang.String">
      <parameter name="username"
          description="User name of the requested user"
                 type="java.lang.String"/>
    </operation>

    <operation   name="removeGroup"
          description="Remove existing group (and all user memberships)"
               impact="ACTION"
           returnType="void">
      <parameter name="groupname"
          description="Group name of the group to remove"
                 type="java.lang.String"/>
    </operation>

    <operation   name="removeRole"
          description="Remove existing role"
               impact="ACTION"
           returnType="void">
      <parameter name="rolename"
          description="Role name of the role to remove"
                 type="java.lang.String"/>
    </operation>

    <operation   name="removeUser"
          description="Remove existing user (and all group memberships)"
               impact="ACTION"
           returnType="void">
      <parameter name="username"
          description="User name of the user to remove"
                 type="java.lang.String"/>
    </operation>

    <operation   name="save"
          description="Save current users and groups to persistent storage"
               impact="ACTION"
           returnType="void">
    </operation>
  </mbean>
</mbeans-descriptors>
