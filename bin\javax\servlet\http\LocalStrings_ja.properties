# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

err.cookie_name_blank=Cookie名はnullまたは長さゼロであってはなりません.\n
err.cookie_name_is_token=クッキー名 [{0}] は予約済のトークンです。
err.io.indexOutOfBounds=サイズ[{2}]の配列に指定されたオフセット[{0}]または長さ[{1}]が無効です。
err.io.nullArray=write メソッドに渡されたバイト配列は null です。
err.io.short_read=読み込みがすぐに終わりました。

http.method_delete_not_supported=HTTPのDELETEメソッドは、このURLではサポートされていません。
http.method_get_not_supported=HTTPのGETメソッドは、このURLではサポートされていません。
http.method_not_implemented=メソッド [{0}] はRFC 2068には定義されておらず、サーブレットAPIではサポートされません。
http.method_post_not_supported=HTTPのPOSTメソッドは、このURLではサポートされていません。
http.method_put_not_supported=HTTPのPUTメソッドは、このURLではサポートされていません。
http.non_http=リクエストが HTTP リクエストではない、あるいはレスポンスが HTTP レスポンスではありません。
