<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN">
<html>
<head>
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
</head>
<body bgcolor="white">

Classes and interfaces for the JSP 2.0 Expression Language API.

<p>
The JavaServer Pages(tm) (JSP) 2.0 specification provides a portable
API for evaluating "EL Expressions".  As of JSP 2.0, EL expressions can
be placed directly in the template text of JSP pages and tag files.
<p>
This package contains a number of classes and interfaces that describe
and define programmatic access to the Expression Language evaluator.
This API can also be used by an implementation of JSP to evaluate the
expressions, but other implementations, like open-coding into Java
bytecodes, are allowed.  This package is intended to have no dependencies
on other portions of the JSP 2.0 specification.
</body>
</html>
