# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

coyoteAdapter.accesslogFail=Exception lors d'une tentative d'ajout d'une entrée au journal d'accès (access log)
coyoteAdapter.asyncDispatch=Exception lors du traitement d'une requête asynchrone
coyoteAdapter.authenticate=L''utilisateur authentifié [{0}] a été fourni par le connecteur
coyoteAdapter.authorize=Autorisation de l''utilisateur [{0}] en utilisant le Realm de Tomcat
coyoteAdapter.checkRecycled.request=Trouvé une requête non recyclée dont le recyclage a été forcé
coyoteAdapter.checkRecycled.response=Trouvé une réponse non recyclée, et forcé son recyclage
coyoteAdapter.debug=La variable [{0}] a la valeur [{1}]
coyoteAdapter.nullRequest=Un dispatch asynchrone peut seulement se produire sur une requête existante

coyoteConnector.invalidEncoding=L''encodage [{0}] n''est pas reconnu par la JRE. Le connecteur (Connector) continuera à utiliser [{1}]
coyoteConnector.invalidPort=Le connecteur ne peut pas démarrer, parce que la valeur spécifiée du port [{0}] n''est pas valide
coyoteConnector.notAsciiSuperset=L''encodage [{0}] n''inclut pas l''ASCII comme requis par la RFC 7230, le connecteur va continuer à utiliser [{1}]
coyoteConnector.parseBodyMethodNoTrace=La méthode "TRACE" NE PEUT PAS contenir une entité (voir RFC 2616 Section 9.6)
coyoteConnector.protocolHandlerDestroyFailed=La destruction du gestionnaire de protocole a échoué
coyoteConnector.protocolHandlerInitializationFailed=L'initialisation du gestionnaire de protocole a échoué
coyoteConnector.protocolHandlerInstantiationFailed=L'instantiation du gestionnaire de protocole a échoué
coyoteConnector.protocolHandlerNoAprLibrary=Le protocole configuré [{0}] requiert la librairie APR/native qui n''est pas disponible
coyoteConnector.protocolHandlerNoAprListener=Le protocole configuré [{0}] requiert AprLifecycleListener qui n''est pas disponible
coyoteConnector.protocolHandlerPauseFailed=La suspension du gestionnaire de protocole a échouée
coyoteConnector.protocolHandlerResumeFailed=Le redémarrage du gestionnaire de protocole a échoué
coyoteConnector.protocolHandlerStartFailed=Le démarrage du gestionnaire de protocole a échoué
coyoteConnector.protocolHandlerStopFailed=L'arrêt du gestionnaire de protocole a échoué

coyoteInputStream.nbNotready=En mode non-bloquant, vous ne pouvez pas lire du ServletInputStream tant que la lecture précédente n'est pas terminée et isReady() renvoie "true"

coyoteOutputStream.nbNotready=En mode non bloquant, vous ne devez pas écrire sur la ServletOutputStream avant que l'écriture précédente ne soit terminée et que isReady() ne renvoie true

coyoteRequest.alreadyAuthenticated=Cette requête a déjà été authentifiée
coyoteRequest.attributeEvent=Une exception a été lancée par l'instance d'écoute pour l'évènement attributs (attributes)
coyoteRequest.authenticate.ise=Impossible d'appeler authenticate() après le début de l'envoi de la réponse
coyoteRequest.changeSessionId=Impossible de changer l'id de la session, il n'y a pas de session associée à cette requête
coyoteRequest.chunkedPostTooLarge=Les paramètres n'ont pas été traités parce que la taille des données du POST étaient trop grandes ; comme cette requête utilisait le découpage par morceaux (chunking), le traitement est arrêté ; utiliser l'attribut maxPostSize du connecteur pour résoudre ce problème si l'application devrait accepter des tailles de POST plus importantes
coyoteRequest.filterAsyncSupportUnknown=Incapacité de déterminer si un des filtres ne supporte pas le mode asynchrone
coyoteRequest.getContextPath.ise=Impossible de trouver une correspondance entre le chemin canonique du contexte [{0}] et l''URI envoyée par l''agent de l''utilisateur [{1}]
coyoteRequest.getInputStream.ise="getReader()" a déjà été appelé pour cette requête
coyoteRequest.getReader.ise="getInputStream()" a déjà été appelé pour cette requête
coyoteRequest.gssLifetimeFail=Echec d''obtention de la durée de vie restante pour le "user principal" [{0}]
coyoteRequest.maxPostSizeExceeded=La requête multi part contenait des données de paramètres (en excluant les fichiers envoyés) dont la taille a excédé la limite maxPostSize fixée sur le connecteur associé
coyoteRequest.noAsync=Impossible de démarrer le mode asynchrone car les classes [{0}] de la chaîne de traitement ne le supportent pas
coyoteRequest.noMultipartConfig=Impossible de traiter des parties, parce qu'aucune configuration multi-parties n'a été fournie
coyoteRequest.parseParameters=Exception lors du traitement des paramètres envoyés par POST
coyoteRequest.postTooLarge=Les paramètres n'ont pas été évalués car la taille des données postées est trop important. Utilisez l'attribut maxPostSize du connecteur pour corriger ce problème si votre application doit accepter des POSTs importants.
coyoteRequest.sendfileNotCanonical=Impossible d''obtenir le nom canonique du fichier [{0}] qui a été donné pour le sendfile
coyoteRequest.sessionCreateCommitted=Impossible de créer une session après que la réponse ait été envoyée
coyoteRequest.sessionEndAccessFail=Exception lancée durant l'arrêt de l'accès à la session durant le recyclage de la requête
coyoteRequest.setAttribute.namenull=Impossible d'appeler "setAttribute" avec un nom nul
coyoteRequest.uploadCreate=Un répertoire temporaire [{0}] pour les fichiers envoyés sera crée car il est requis par le Servlet [{1}]
coyoteRequest.uploadCreateFail=Echec de création du répertoire [{0}] pour les fichiers envoyés
coyoteRequest.uploadLocationInvalid=Le répertoire temporaire [{0}] pour les envois de fichier est invalide

coyoteResponse.encoding.invalid=L''encodage [{0}] n''est pas reconnu par le JRE
coyoteResponse.getOutputStream.ise="getWriter()" a déjà été appelé pour cette réponse
coyoteResponse.getWriter.ise="getOutputStream()" a déjà été appelé pour cette réponse
coyoteResponse.reset.ise=Impossible d'appeler reset() après le début de l'envoi de la réponse
coyoteResponse.resetBuffer.ise=Impossible de remettre à zéro le tampon après que la réponse ait été envoyée
coyoteResponse.sendError.ise=Impossible d'appeler "sendError()" après que la réponse ait été envoyée
coyoteResponse.sendRedirect.ise=Impossible d'appeler "sendRedirect()" après que la réponse ait été envoyée
coyoteResponse.sendRedirect.note=<html><body><p>Redirection vers <a href="{0}">{0}</a></p></body></html>
coyoteResponse.setBufferSize.ise=Impossible de changer la taille du tampon après que les données aient été écrites

inputBuffer.requiresNonBlocking=Pas disponible en mode non bloquant
inputBuffer.streamClosed=Le flux a été fermé

outputBuffer.writeNull=L'argument String dans write(String, int, int) ne doit pas être null

request.asyncNotSupported=Un filtre ou un Servlet de la chaîne actuelle ne supporte pas le mode asynchrone
request.fragmentInDispatchPath=Le fragment dans le chemin de dispatch [{0}] a été enlevé
request.illegalWrap=L'enrobeur de la réponse doit enrober la requête obtenue à partir de getRequest()
request.notAsync=Il est interdit d'appeler cette méthode si la requête actuelle n'est pas en mode asynchrone (isAsyncStarted() a renvoyé false)
request.session.failed=Erreur de chargement de la session [{0}] à cause de [{1}]

requestFacade.nullRequest=L'objet requête a été recyclé et n'est plus associé à cette façade

response.illegalWrap=L'enrobeur de la réponse doit enrober la réponse obtenue à partir de getResponse()
response.sendRedirectFail=Impossible d''envoyer une redirection vers [{0}]

responseFacade.nullResponse=L'objet réponse a été recyclé et n'est plus associé à cette façade
