# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

asn1Parser.lengthInvalid=无效长度 [{0}]字节报告，但是输入数据的长度是 [{1}]字节
asn1Parser.tagMismatch=期望找到值 [{0}]但是却找到值 [{1}]

b2cConverter.unknownEncoding=不支持字符编码[{0}]

byteBufferUtils.cleaner=无法使用直接ByteBuffer清洁剂，可能会发生内存泄漏

encodedSolidusHandling.invalid=值[{0}]未识别

hexUtils.fromHex.nonHex=输入只能由十六进制数字组成
hexUtils.fromHex.oddDigits=输入必须由偶数个十六进制数字组成

uDecoder.eof=文件结尾（EOF）
uDecoder.noSlash=不允许使用编码的斜杠字符
uDecoder.urlDecode.conversionError=使用编码[{1}]解码[{0}]失败
uDecoder.urlDecode.missingDigit=无法解码[{0}]，因为%字符必须后跟两个十六进制数字。
