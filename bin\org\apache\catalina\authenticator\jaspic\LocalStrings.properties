# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

authConfigFactoryImpl.load=Loading persistent provider registrations from [{0}]
authConfigFactoryImpl.registerClass=Registering class [{0}] for layer [{1}] and application context [{2}]
authConfigFactoryImpl.registerInstance=Registering instance of type[{0}] for layer [{1}] and application context [{2}]
authConfigFactoryImpl.zeroLengthAppContext=A zero length application context name is not valid
authConfigFactoryImpl.zeroLengthMessageLayer=A zero length message layer name is not valid

callbackHandlerImpl.containerMissing=Missing container for JASPIC callback of type [{0}] which was ignored
callbackHandlerImpl.jaspicCallbackMissing=Unsupported JASPIC callback of type [{0}] received which was ignored
callbackHandlerImpl.realmMissing=Missing realm for JASPIC callback of type [{0}] in container [{1}] which was ignored

jaspicAuthenticator.authenticate=Authenticating request for [{0}] via JASPIC

persistentProviderRegistrations.deleteFail=The temporary file [{0}] cannot be deleted
persistentProviderRegistrations.existsDeleteFail=The temporary file [{0}] already exists and cannot be deleted
persistentProviderRegistrations.moveFail=Failed to move [{0}] to [{1}]

simpleServerAuthConfig.noModules="No ServerAuthModules configured"
