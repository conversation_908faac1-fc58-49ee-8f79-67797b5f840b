# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

config.storeContextError=컨텍스트 [{0}]을(를) 저장하는 중 오류 발생
config.storeServerError=서버를 중지시키는 중 오류 발생

factory.storeNoDescriptor=엘리먼트 클래스 [{0}]을(를) 위한 descriptor가 설정되지 않았습니다!
factory.storeTag=태그 [{0}]을(를) 저장합니다. ( 객체: [{1}] )

storeConfigListener.notServer=서버가 아닌 다른 구성요소에 리스너가 추가되었으므로, 이 리스너는 무시될 것입니다.

storeFileMover.directoryCreationError=디렉토리 [{0}]을(를) 생성할 수 없습니다.
storeFileMover.renameError=[{0}]을(를) [{1}](으)로 이름을 변경할 수 없습니다.
