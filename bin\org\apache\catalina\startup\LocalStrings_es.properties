# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

catalina.configFail=No pude cargar la configuración del servidor desde [{0}]
catalina.serverStartFail=Tomcat no puede iniciar porque el componente Server requerido fallo al iniciar.
catalina.shutdownHookFail=El gancho de apagado ha experimentado un error al intentar para el servidor
catalina.stopServer=No se ha configurado puerto de apagado. Apagando el servidor a través de señal de SO. Servidor no apagado.

contextConfig.altDDNotFound=fichero alt-dd [{0}] no hallado
contextConfig.applicationMissing=Falta el archivo web.xml de la aplicación. Utilizando los parámetros por defecto
contextConfig.applicationParse=Error de evaluación (parse) en el archivo web.xml de la aplicación a [{0}]
contextConfig.applicationPosition=Se ha producido en la línea [{0}] columna [{1}]
contextConfig.applicationStart=Analizando fichero de aplicación web.xml en [{0}]
contextConfig.applicationUrl=No pude determinar la URL para la aplicación web.xml
contextConfig.authenticatorConfigured=Configuración de un autentificador (authenticator) para el método [{0}]
contextConfig.authenticatorInstantiate=Imposible de instanciar un autenticador (authenticator) para la clase [{0}]
contextConfig.authenticatorMissing=Imposible de configurar un autentificador (authenticator) para el método [{0}]
contextConfig.authenticatorResources=Imposible de cargar la lista de correspondencia de autenticadores (authenticators)
contextConfig.badUrl=No pude procesar descriptor de contextor [{0}]
contextConfig.cce=El objeto de los datos de evento de ciclo de vida (Lifecycle event data object) [{0}] no es un Contexto
contextConfig.contextClose=Error cerrando context.xml: [{0}]
contextConfig.contextMissing=Falta context.xml: [{0}]
contextConfig.contextParse=Error de análisis en context.xml: [{0}]
contextConfig.defaultError=Error al procesar web.xml por defecto con nombre [{0}] en [{1}]
contextConfig.defaultMissing=No se ha hallado web.xml global
contextConfig.defaultPosition=Se ha producido en la línea [{0}] columna [{1}]
contextConfig.destroy=ContextConfig: Destruyendo
contextConfig.fileUrl=No puedo crear un objeto Fichero desde la URL [{0}]
contextConfig.fixDocBase=Excepción arreglando docBase: [{0}]
contextConfig.init=ContextConfig: Inicializando
contextConfig.inputStreamFile=No puedo procesar el fichero [{0}] para las anotaciones
contextConfig.inputStreamJar=No puedo procesar la entrada de Jar [{0}] desde el Jar [{1}] para las anotaciones
contextConfig.inputStreamJndi=No puedo procesar el elemento de recurso [{0}] para las anotaciones
contextConfig.inputStreamWebResource=Imposible procesar el recurso web [{0}] para anotaciones
contextConfig.invalidSciHandlesTypes=No puedo cargar la clase [{0}] para revisar contra la anotación  @HandlesTypes de uno o más ServletContentInitializers.
contextConfig.jndiUrl=No puedo procesar la URL JNDI [{0}] para las anotaciones
contextConfig.jndiUrlNotDirContextConn=La conexión creada para la URL [{0}] no era una DirContextURLConnection
contextConfig.jspFile.error=El archivo JSP [{0}] debe de comenzar con ''/''
contextConfig.jspFile.warning=AVISO: El archivo JSP [{0}] debe de comenzar con ''/'' en Servlet 2.4
contextConfig.missingRealm=Algún reino (realm) no ha sido configurado para realizar la autenticación
contextConfig.processAnnotationsDir.debug=Escaneando el directorio por archivos de clase con anotaciones [{0}]\n
contextConfig.resourceJarFail=Hallado JAR fallido a los procesos en URL [{0}] para recursos estáticos a ser incluidos en contexto con nombre [{0}]
contextConfig.role.auth=El nombre de papel de seguridad [{0}] es usado en un <auth-constraint> sin haber sido definido en <security-role>
contextConfig.role.link=El nombre de papel de seguridad [{0}] es usado en un <role-link> sin haber sido definido en <security-role>
contextConfig.role.runas=El nombre de papel de seguridad [{0}] es usado en un <run-as> sin haber sido definido en <security-role>
contextConfig.servletContainerInitializerFail=Hallado JAR fallido a proceso en URL [{0}] para ServletContainerInitializers para el contexto con nombre [{1}]
contextConfig.start="ContextConfig": Procesando "START"
contextConfig.stop="ContextConfig": Procesando "STOP"
contextConfig.unavailable=Esta aplicación está marcada como no disponible debido a los errores precedentes
contextConfig.unknownUrlProtocol=El protocolo de URL [{0}] no fue reconocido durante el proceso de anotaciones. Se ignoró la URL [{1}].
contextConfig.urlPatternValue=Ambis valores de urlPatterns y atributo fuerno puestos para anotación de [{0}] de la clase [{1}]
contextConfig.xmlSettings=El contexto [{0}] analizará los ficheros web.xml y web-fragment.xml con validación:[{1}] y namespaceAware:[{2}]

engineConfig.cce=El objeto de los datos de evento de ciclo de vida (Lifecycle event data object) [{0}] no es un motor (engine)
engineConfig.start="EngineConfig": Procesando "START"
engineConfig.stop="EngineConfig": Procesando "STOP"

expandWar.copy=Error copiando [{0}] a [{1}]
expandWar.createFailed=No se pudo crear el directorio [{0}]
expandWar.deleteFailed=[{0}] no pudo ser completamente borrado. La presencia de los ficheros restantes puede causar problemas
expandWar.illegalPath=The archive [{0}] is malformed and will be ignored: an entry contains an illegal path [{1}] which was not expanded to [{2}] since that is outside of the defined docBase [{3}]

hostConfig.canonicalizing=Error al borrar redespliegue de recursos desde contexto [{0}]
hostConfig.cce=El objeto de los datos de evento de ciclo de vida (Lifecycle event data object) [{0}] no es una máquina (host)
hostConfig.context.remove=Error al quitar contexto [{0}]
hostConfig.context.restart=Error durante el arranque del contexto [{0}]
hostConfig.createDirs=No puedo crear directorio para despliegue: [{0}]
hostConfig.deployDescriptor=Desplieque del descriptor de configuración [{0}]
hostConfig.deployDescriptor.error=Error durante el despliegue del descriptor de configuración [{0}]
hostConfig.deployDescriptor.localDocBaseSpecified=Se ha especificado un docBase [{0}] dentro del appBase de la máquina y será ignorado
hostConfig.deployDir=Desplegando el directorio [{0}] de la aplicación web
hostConfig.deployDir.error=Error durante el despliegue del directorio [{0}] de la aplicación web
hostConfig.deployWar=Despliegue del archivo [{0}] de la aplicación web
hostConfig.deployWar.error=Error durante el despliegue del archivo [{0}] de la aplicación web
hostConfig.deployWar.hiddenDir=El directorio [{0}] será ignorado debido a que el WAR [{1}] tiene prioridad y  unpackWARs es falso
hostConfig.docBaseUrlInvalid=El docBase proporcionado no puede ser expresado como una URL
hostConfig.ignorePath=Ignorando ruta [{0}] en appBase para despliegue automático
hostConfig.illegalWarName=El nombre de war [{0}] es inválido. El archivo será ignorado.
hostConfig.jmx.register=Falló el registro del contexto [{0}]
hostConfig.jmx.unregister=Falló el desregistro del contexto [{0}]
hostConfig.reload=Falló la recarga del contexto [{0}]
hostConfig.start="HostConfig": Procesando "START"
hostConfig.stop="HostConfig": Procesando "STOP"
hostConfig.undeploy=Repliegue (undeploy) de la aplicación web que tiene como trayectoria de contexto [{0}]

userConfig.database=Excepción durante la carga de base de datos de usuario
userConfig.deploy=Despliegue de la aplicación web para el usuario [{0}]
userConfig.deploying=Desplegando aplicaciones web para el usuario
userConfig.error=Error durante el despliegue de la aplicación web para el usario [{0}]
userConfig.start="UserConfig": Procesando "START"
userConfig.stop="UserConfig": Tratamiento del "STOP"

versionLoggerListener.arg=Command line argument:             {0}
versionLoggerListener.catalina.base=CATALINA_BASE:                     {0}
versionLoggerListener.catalina.home=CATALINA_HOME:                     {0}
versionLoggerListener.env=Environment variable:              {0} = {1}
versionLoggerListener.java.home=Java Home:                         {0}
versionLoggerListener.os.arch=Arquitectura:                      {0}
versionLoggerListener.os.name=OS Name:                           {0}
versionLoggerListener.os.version=Versión de Systema Operativo:      {0}
versionLoggerListener.prop=System property:                   {0} = {1}
versionLoggerListener.serverInfo.server.built=Server built:                      {0}
versionLoggerListener.serverInfo.server.number=Número de versión de servidor:     {0}
versionLoggerListener.serverInfo.server.version=Nombre de la versión del servidor: {0}
versionLoggerListener.vm.vendor=Vededor JVM:                       {0}
versionLoggerListener.vm.version=JVM Version:                       {0}

webAnnotationSet.invalidInjection=El método de inyección de anotación no es un recurso válido
