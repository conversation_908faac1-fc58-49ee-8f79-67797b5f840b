# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

accessLogValve.alreadyExists=Failed to rename access log from [{0}] to [{1}], file already exists.
accessLogValve.closeFail=Failed to close access log file
accessLogValve.deleteFail=Failed to delete old access log [{0}]
accessLogValve.invalidLocale=Failed to set locale to [{0}]
accessLogValve.invalidPortType=Invalid port type [{0}], using server (local) port
accessLogValve.invalidRemoteAddressType=Invalid remote address type [{0}], using remote (non-peer) address
accessLogValve.openDirFail=Failed to create directory [{0}] for access logs
accessLogValve.openFail=Failed to open access log file [{0}] Note: running as user [{1}]
accessLogValve.renameFail=Failed to rename access log from [{0}] to [{1}]
accessLogValve.rotateFail=Failed to rotate access log
accessLogValve.unsupportedEncoding=Failed to set encoding to [{0}], will use the system default character set.
accessLogValve.writeFail=Failed to write log message [{0}]

# Default error page should not have '[' ']' symbols around substituted text fragments.
# https://bz.apache.org/bugzilla/show_bug.cgi?id=61134
errorReportValve.description=Description
errorReportValve.exception=Exception
errorReportValve.exceptionReport=Exception Report
errorReportValve.message=Message
errorReportValve.noDescription=No description available
errorReportValve.note=Note
errorReportValve.rootCause=Root Cause
errorReportValve.rootCauseInLogs=The full stack trace of the root cause is available in the server logs.
errorReportValve.statusHeader=HTTP Status {0} – {1}
errorReportValve.statusReport=Status Report
errorReportValve.type=Type
errorReportValve.unknownReason=Unknown Reason

extendedAccessLogValve.badXParam=Invalid x parameter format, needs to be 'x-#(...)
extendedAccessLogValve.badXParamValue=Invalid x parameter value for Servlet request [{0}]
extendedAccessLogValve.decodeError=Unable to decode the rest of chars starting with [{0}]
extendedAccessLogValve.emptyPattern=Pattern was just empty or whitespace
extendedAccessLogValve.noClosing=No closing ) found for in decode
extendedAccessLogValve.patternParseError=Error parsing pattern [{0}]

http.400.desc=The server cannot or will not process the request due to something that is perceived to be a client error (e.g., malformed request syntax, invalid request message framing, or deceptive request routing).
http.400.reason=Bad Request
http.401.desc=The request has not been applied because it lacks valid authentication credentials for the target resource.
http.401.reason=Unauthorized
http.402.desc=This status code is reserved for future use.
http.402.reason=Payment Required
http.403.desc=The server understood the request but refuses to authorize it.
http.403.reason=Forbidden
http.404.desc=The origin server did not find a current representation for the target resource or is not willing to disclose that one exists.
http.404.reason=Not Found
http.405.desc=The method received in the request-line is known by the origin server but not supported by the target resource.
http.405.reason=Method Not Allowed
http.406.desc=The target resource does not have a current representation that would be acceptable to the user agent, according to the proactive negotiation header fields received in the request, and the server is unwilling to supply a default representation.
http.406.reason=Not Acceptable
http.407.desc=This status code is similar to 401 (Unauthorized), but it indicates that the client needs to authenticate itself in order to use a proxy.
http.407.reason=Proxy Authentication Required
http.408.desc=The server did not receive a complete request message within the time that it was prepared to wait.
http.408.reason=Request Timeout
http.409.desc=The request could not be completed due to a conflict with the current state of the target resource.
http.409.reason=Conflict
http.410.desc=Access to the target resource is no longer available at the origin server and that this condition is likely to be permanent.
http.410.reason=Gone
http.411.desc=The server refuses to accept the request without a defined Content-Length.
http.411.reason=Length Required
http.412.desc=One or more conditions given in the request header fields evaluated to false when tested on the server.
http.412.reason=Precondition Failed
http.413.desc=The server is refusing to process a request because the request payload is larger than the server is willing or able to process.
http.413.reason=Payload Too Large
http.414.desc=The server is refusing to service the request because the request-target is longer than the server is willing to interpret.
http.414.reason=URI Too Long
http.415.desc=The origin server is refusing to service the request because the payload is in a format not supported by this method on the target resource.
http.415.reason=Unsupported Media Type
http.416.desc=None of the ranges in the request's Range header field overlap the current extent of the selected resource or that the set of ranges requested has been rejected due to invalid ranges or an excessive request of small or overlapping ranges.
http.416.reason=Range Not Satisfiable
http.417.desc=The expectation given in the request's Expect header field could not be met by at least one of the inbound servers.
http.417.reason=Expectation Failed
http.421.desc=The request was directed at a server that is not able to produce a response.
http.421.reason=Misdirected Request
http.422.desc=The server understands the content type of the request entity, and the syntax of the request entity is correct but was unable to process the contained instructions.
http.422.reason=Unprocessable Entity
http.423.desc=The source or destination resource of a method is locked.
http.423.reason=Locked
http.424.desc=The method could not be performed on the resource because the requested action depended on another action and that action failed.
http.424.reason=Failed Dependency
http.426.desc=the server refuses to perform the request using the current protocol but might be willing to do so after the client upgrades to a different protocol.
http.426.reason=Upgrade Required
http.428.desc=The origin server requires the request to be conditional.
http.428.reason=Precondition Required
http.429.desc=The user has sent too many requests in a given amount of time ("rate limiting").
http.429.reason=Too Many Requests
http.431.desc=The server is unwilling to process the request because its header fields are too large.
http.431.reason=Request Header Fields Too Large
http.451.desc=The server refused this request for legal reasons.
http.451.reason=Unavailable For Legal Reasons
http.500.desc=The server encountered an unexpected condition that prevented it from fulfilling the request.
http.500.reason=Internal Server Error
http.501.desc=The server does not support the functionality required to fulfill the request.
http.501.reason=Not Implemented
http.502.desc=The server, while acting as a gateway or proxy, received an invalid response from an inbound server it accessed while attempting to fulfill the request.
http.502.reason=Bad Gateway
http.503.desc=The server is currently unable to handle the request due to a temporary overload or scheduled maintenance, which will likely be alleviated after some delay.
http.503.reason=Service Unavailable
http.504.desc=The server, while acting as a gateway or proxy, did not receive a timely response from an upstream server it needed to access in order to complete the request.
http.504.reason=Gateway Timeout
http.505.desc=The server does not support, or refuses to support, the major version of HTTP that was used in the request message.
http.505.reason=HTTP Version Not Supported
http.506.desc=The server has an internal configuration error: the chosen variant resource is configured to engage in transparent content negotiation itself, and is therefore not a proper end point in the negotiation process.
http.506.reason=Variant Also Negotiates
http.507.desc=The method could not be performed on the resource because the server is unable to store the representation needed to successfully complete the request.
http.507.reason=Insufficient Storage
http.508.desc=The server terminated an operation because it encountered an infinite loop while processing a request with "Depth: infinity".
http.508.reason=Loop Detected
http.510.desc=The policy for accessing the resource has not been met in the request
http.510.reason=Not Extended
http.511.desc=The client needs to authenticate to gain network access.
http.511.reason=Network Authentication Required

jdbcAccessLogValve.close=Failed to close database
jdbcAccessLogValve.exception=Exception performing insert access entry

persistentValve.filter.failure=Unable to compile filter=[{0}]

remoteCidrValve.invalid=Invalid configuration provided for [{0}]. See previous messages for details.
remoteCidrValve.noPort=Request does not contain a valid server port. Request denied.
remoteCidrValve.noRemoteIp=Client does not have an IP address. Request denied.
remoteCidrValve.unexpectedPort=Request contains server port, although connector configuration attribute addConnectorPort is false. Request denied.

remoteIpValve.invalidHostHeader=Invalid value [{0}] found for Host in HTTP header [{1}]
remoteIpValve.invalidHostWithPort=Host value [{0}] in HTTP header [{1}] included a port number which will be ignored
remoteIpValve.invalidPortHeader=Invalid value [{0}] found for port in HTTP header [{1}]
remoteIpValve.invalidRemoteAddress=Unable to determine the remote host because the reported remote address [{0}] is not valid

requestFilterValve.configInvalid=One or more invalid configuration settings were provided for the Remote[Addr|Host]Valve which prevented the Valve and its parent containers from starting
requestFilterValve.deny=Denied request for [{0}] based on property [{1}]

sslValve.certError=Failed to process certificate string [{0}] to create a java.security.cert.X509Certificate object
sslValve.invalidProvider=The SSL provider specified on the connector associated with this request of [{0}] is invalid. The certificate data could not be processed.

stuckThreadDetectionValve.notifyStuckThreadCompleted=Thread [{0}] (id=[{3}]) was previously reported to be stuck but has completed. It was active for approximately [{1}] milliseconds.{2,choice,0#|0< There is/are still [{2}] thread(s) that are monitored by this Valve and may be stuck.}
stuckThreadDetectionValve.notifyStuckThreadDetected=Thread [{0}] (id=[{6}]) has been active for [{1}] milliseconds (since [{2}]) to serve the same request for [{4}] and may be stuck (configured threshold for this StuckThreadDetectionValve is [{5}] seconds). There is/are [{3}] thread(s) in total that are monitored by this Valve and may be stuck.
stuckThreadDetectionValve.notifyStuckThreadInterrupted=Thread [{0}] (id=[{5}]) has been interrupted because it was active for [{1}] milliseconds (since [{2}]) to serve the same request for [{3}] and was probably stuck (configured interruption threshold for this StuckThreadDetectionValve is [{4}] seconds).
