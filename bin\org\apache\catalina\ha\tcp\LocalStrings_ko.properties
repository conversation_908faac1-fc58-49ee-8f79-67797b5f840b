# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

ReplicationValve.crossContext.add=교차 컨텍스트 세션 복제 컨테이너를 replicationValve의 threadlocal에 추가합니다.
ReplicationValve.crossContext.registerSession=컨텍스트 [{1}](으)로부터 세션 ID가 [{0}]인 교차 컨텍스트 세션을 등록합니다.
ReplicationValve.crossContext.remove=replicationValve의 threadlocal로부터, 교차 컨텍스트 세션 복제 컨테이너를 제거합니다.
ReplicationValve.crossContext.sendDelta=컨텍스트 [{0}](으)로부터 교차 컨텍스트 세션 델타를 보냅니다.
ReplicationValve.filter.failure=필터 컴파일을 할 수 없습니다. filter=[{0}]
ReplicationValve.filter.loading=요청 필터를 로드합니다: [{0}]
ReplicationValve.invoke.uri=[{0}]에 복제 요청을 호출합니다.
ReplicationValve.nocluster=이 요청을 위해 설정된 클러스터가 없습니다.
ReplicationValve.resetDeltaRequest=클러스터가 독립형(standalone)입니다: 컨텍스트 [{0}]에서 세션 요청 델타를 재설정(reset)합니다.
ReplicationValve.send.failure=복제 요청을 수행 할 수 없습니다.
ReplicationValve.send.invalid.failure=세션 [id={0}] 유효하지 않음 메시지를 클러스터에 전송할 수 없습니다.
ReplicationValve.session.found=컨텍스트 [{0}]에서 세션 [{1}]을(를) 발견했으나, 이는 ClusterSession이 아닙니다.
ReplicationValve.session.indicator=컨텍스트 [{0}]: 요청 속성 [{2}]에 있는 세션 [{1}]의 Primary 여부: [{3}]
ReplicationValve.session.invalid=컨텍스트 [{0}]: 요청된 세션 [{1}]이(가), 유효하지 않거나, 제거되었거나, 또는 이 클러스터 노드로 복제되지 않았습니다.
ReplicationValve.stats=[{2}]개의 요청들, [{3}]개의 전송 요청들, [{4}]개의 교차 컨텍스트 요청들, 그리고 [{5}]개의 필터 요청들을 처리하는 동안, 평균 요청 시간=[{0}] 밀리초, 클러스터 오버헤드 시간=[{1}] 밀리초가 소요되었습니다. (총 요청 처리 시간=[{6}] 밀리초, 총 클러스터 요청 처리 시간=[{7}] 밀리초)

simpleTcpCluster.clustermanager.cloneFailed=클러스터 매니저를 복제할 수 없습니다. 기본 값인 org.apache.catalina.ha.session.DeltaManager를 사용합니다.
simpleTcpCluster.clustermanager.notImplement=매니저 [{0}]이(가) ClusterManager 인터페이스를 구현하지 않습니다. 클러스터에 추가하려는 시도는 중단됩니다.
simpleTcpCluster.member.addFailed=복제 시스템에 연결할 수 없습니다.
simpleTcpCluster.member.added=복제 멤버가 추가됨: [{0}]
simpleTcpCluster.member.disappeared=멤버 사라짐 메시지를 수신했습니다: [{0}]
simpleTcpCluster.member.removeFailed=복제 시스템으로부터 클러스터 노드를 제거할 수 없습니다.
simpleTcpCluster.sendFailed=클러스터 sender를 통해 메시지를 보낼 수 없습니다.
simpleTcpCluster.start=클러스터가 막 시작하려 합니다.
simpleTcpCluster.startUnable=클러스터를 시작할 수 없습니다.
simpleTcpCluster.stopUnable=클러스터를 중지시킬 수 없습니다.
simpleTcpCluster.unableSend.localMember=로컬 멤버 [{0}]에게 메시지를 보낼 수 없습니다.
