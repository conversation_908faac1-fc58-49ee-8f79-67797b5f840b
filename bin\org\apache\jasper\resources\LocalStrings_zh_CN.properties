# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jasper.error.emptybodycontent.nonempty=根据 TLD，[{0}] 标签必须为空，但不是

jsp.engine.info=Jasper JSP{0}引擎
jsp.error.action.isnottagfile=[{0}]行为只能用于标签文件
jsp.error.action.istagfile=标签文件中不能使用[{0}]功能
jsp.error.attempt_to_clear_flushed_buffer=错误：尝试清空已刷新的缓冲区
jsp.error.attr.quoted=应引用属性值
jsp.error.attribute.custom.non_rt_with_expr=根据标记文件中的TLD或attribute指令，attribute[{0}]不接受任何表达式
jsp.error.attribute.deferredmix=不能在同一属性值中同时使用 ${} 和 #{} EL 表达式
jsp.error.attribute.duplicate=属性限定名在元素中必须是唯一的
jsp.error.attribute.invalidPrefix=属性前缀[{0}]与任何导入的标记库都不对应
jsp.error.attribute.noequal=期望的符号是等号
jsp.error.attribute.noescape=属性值[{0}]引用[{1}]，在值内使用时必须被转义。
jsp.error.attribute.noquote=需要引号。
jsp.error.attribute.nowhitespace=JSP 规范要求一个属性名字前有空格
jsp.error.attribute.null_name=空属性名
jsp.error.attribute.standard.non_rt_with_expr=[{1}]标准操作的[{0}]属性不接受任何表达式
jsp.error.attribute.unterminated=[{0}]的属性值未正确终止
jsp.error.bad.scratch.dir=你指定的 scratchDir：[{0}] 不可用。
jsp.error.badStandardAction=无效的标准操作。
jsp.error.bad_attribute=属性[{0}]无效为tag[{1}] 通过TLD
jsp.error.bad_tag=在用前缀[{1}]导入的标记库中未定义标记[{0}]
jsp.error.beans.nomethod=在类型为[{1}]的bean中找不到读取属性[{0}]的方法
jsp.error.beans.nomethod.setproperty=在类型为[{2}]的bean中找不到用于写入类型为[{1}]的属性[{0}]的方法
jsp.error.beans.noproperty=在[{1}]类型bean中找不到任何有关属性[{0}]的信息
jsp.error.beans.nullbean=尝试获取一个bean 操作在一个空对象上.
jsp.error.beans.property.conversion=无法将字符串[{0}]转换为属性[{2}]的类[{1}]：[{3}]
jsp.error.beans.propertyeditor.notregistered=属性编辑器未注册到属性编辑管理器
jsp.error.beans.setproperty.noindexset=无法设置索引属性。
jsp.error.bug48498=无法显示JSP提取。可能是由于XML解析器错误（有关详细信息，请参阅TomcatBug48498）。
jsp.error.classname=无法从.class文件确定类名
jsp.error.coerce_to_type=无法将值[{2}]强制为属性[{0}]的类型[{1}]。
jsp.error.compilation=编译文件时出错：[{0}[{1}]
jsp.error.compiler=没有可用的Java编译器
jsp.error.compiler.config=没有可用于配置选项的Java编译器compilerClassName:[{0}]和compiler:[{1}]
jsp.error.config_pagedir_encoding_mismatch=jsp属性组[{0}]中指定的页编码与page指令[{1}]中指定的页编码不同
jsp.error.corresponding.servlet=生成的servlet错误:\n
jsp.error.could.not.add.taglibraries=不能增加一个或者多个tag 库.
jsp.error.data.file.processing=处理文件 [{0}] 错误
jsp.error.data.file.read=读取文件[{0}]时出错
jsp.error.data.file.write=写入数据文件时出错
jsp.error.deferredmethodandvalue=“deferredValue”和“deferredMethod”不能同时为“true”
jsp.error.deferredmethodsignaturewithoutdeferredmethod=如果“deferredMethod”不是“true”，则无法指定方法签名
jsp.error.deferredvaluetypewithoutdeferredvalue=如果“deferredValue”的值不是“true”的话，不能指定一个值类型
jsp.error.directive.isnottagfile=[{0}]指令只能在标记文件中使用
jsp.error.directive.istagfile=[{0}]指令不能在标记文件中使用
jsp.error.duplicate.name.jspattribute=标准或自定义操作中指定的属性[{0}]也显示为随附的jsp：属性中name属性的值
jsp.error.duplicateqname=找到具有重复限定名[{0}]的属性。属性限定名在元素中必须是唯一的。
jsp.error.dynamic.attributes.not.implemented=[{0}]标记声明它接受动态属性，但未实现所需的接口
jsp.error.el.parse=[{0}]：[{1}]
jsp.error.el.template.deferred=#{...} 不允许出现在模板文本中
jsp.error.el_interpreter_class.instantiation=加载或实例化ELInterpreter类[{0}]失败
jsp.error.fallback.invalidUse=jsp:fallback必须是jsp:plugin的直接子代
jsp.error.file.already.registered=文件[{0}]的递归包含
jsp.error.file.cannot.read=无法读取文件 [{0}]
jsp.error.file.not.found=文.件[{0}] 未找到
jsp.error.flush=刷新数据时发生异常
jsp.error.fragmentwithtype=无法同时指定“片段”和“类型”属性。如果存在“片段”，则“类型”固定为“ {0}”
jsp.error.function.classnotfound=找不到在TLD中为函数[{1}]指定的类[{0}]：[{2}]
jsp.error.include.exception=无法包含[{0}]
jsp.error.include.tag=无效的jsp:include标签
jsp.error.internal.filenotfound=内部错误：找不到文件 [{0}]
jsp.error.invalid.attribute=[{0}]有一个无效属性：[{1}]
jsp.error.invalid.bean=useBean类属性[{0}]的值无效。
jsp.error.invalid.directive=无效指令
jsp.error.invalid.expression=[{0}]包含无效表达式：[{1}]
jsp.error.invalid.implicit=[{0}]处标记文件的隐式TLD无效
jsp.error.invalid.implicit.version=[{0}]处标记文件的隐式TLD中定义的JSP版本无效。
jsp.error.invalid.scope=非法的scope属性值：[{0}]（必须是page、request、session或application中的一个）
jsp.error.invalid.tagdir=标签文件目录 [{0}] 不以"/WEB-INF/tags"开头
jsp.error.invalid.version=为标签 [{0}] 定义了无效的 JSP 版本号
jsp.error.ise_on_clear=当缓存大小等于0时调用clear()函数是非法的
jsp.error.java.line.number=在生成的java文件中的第：[{0}]行发生错误：[{1}]
jsp.error.javac=Javac异常
jsp.error.javac.env=环境：
jsp.error.jspbody.emptybody.only=标签[{}]的标签体内智能包含jsp:attribute
jsp.error.jspbody.invalidUse=JSP：主体必须是标准或自定义操作的子元素
jsp.error.jspbody.required=如果使用 jsp:attribute，则必须使用 jsp:body 为 [{0}] 指定标记正文。
jsp.error.jspc.missingTarget=缺少目标：必须指定-webapp或-uriroot或一个或多个jsp页
jsp.error.jspc.no_uriroot=未指定uriroot，无法用指定的JSP文件定位
jsp.error.jspc.uriroot_not_dir=-uriroot选项必须指定一个预先存在的目录
jsp.error.jspelement.missing.name=XML强制性约束：属性name缺失。
jsp.error.jspoutput.conflict=＆lt; jsp：output＆gt;：非法使多个[{0}]出现不同的值（旧：[{1}]，新：[{2}]）
jsp.error.jspoutput.doctypenamesystem=&lt;jsp:output&gt;: 'doctype-root-element' 和 'doctype-system' 必须一起出现
jsp.error.jspoutput.doctypepublicsystem=&amp;lt；jsp:output&amp;gt；：如果显示“doctype public”属性，则必须显示“doctype system”属性
jsp.error.jspoutput.invalidUse=&amp;lt；jsp:output&amp;gt；不能在标准语法中使用
jsp.error.jspoutput.nonemptybody=&lt;jsp:output&gt;不能有正文
jsp.error.jsproot.version.invalid=版本号 [{0}] 无效，版本号必须是"1.2"、"2.0"、"2.1"、"2.2"、"2.3"中的一个
jsp.error.jsptext.badcontent='＆lt;'，当出现在＆lt; jsp：text＆gt;的主体中时，必须封装在CDATA中
jsp.error.lastModified=无法确定文件 [{0}] 的最后修改日期
jsp.error.library.invalid=根据库[{0}](：[{1}]，jsp页无效
jsp.error.literal_with_void=为属性[{0}]指定了一个文本值，该属性定义为返回类型为void的延迟方法。在这种情况下，JSP.2.3.4不允许使用文本值。
jsp.error.loadclass.taghandler=无法为TAG [{1}]加载标记处理程序类[{0}]
jsp.error.location=行.: [{0}], 列: [{1}]
jsp.error.mandatory.attribute=[{0}]: 强制性属性 [{1}] 缺失。
jsp.error.missing.tagInfo=TLD中缺少[{0}]的TagInfo对象
jsp.error.missing_attribute=根据TLD或标记文件，标记[{1}]必须使用属性[{0}]
jsp.error.missing_var_or_varReader=缺少“var”或“varReader”属性
jsp.error.namedAttribute.invalidUse=jsp:属性必须是标准或自定义操作的子元素
jsp.error.needAlternateJavaEncoding=默认java编码[{0}]在java平台上无效。可以通过JspServlet的“javaEncoding”参数指定备用项。
jsp.error.nested.jspattribute=jsp:attribute标准操作不能嵌套在另一个jsp:attribute标准操作中
jsp.error.nested.jspbody=JSP：体标准动作不能嵌套在另一个jsp:body 或者 jsp:属性标准动作中
jsp.error.nested_jsproot=嵌套的&lt;jsp:root&gt;
jsp.error.no.more.content=在需要更多分析时到达内容结尾：标记嵌套错误？
jsp.error.no.scratch.dir=JSP引擎未配置scratch文件夹。\n\
请在对应上下文Context的servlets.properties文件中添加"jsp.initparams=scratchdir=<dir-name>"。
jsp.error.no.scriptlets=脚本( &lt;%!, &lt;jsp声明, &lt;%=, &lt;jsp表达式, &lt;%, &lt;jsp脚本变量 )不允许出现在这里
jsp.error.noFunction=无法使用指定的前缀找到函数[{0}]
jsp.error.noFunctionMethod=在类{2}中找不到函数{1}的方法{0}]
jsp.error.non_null_tei_and_var_subelems=标记{0}有一个或多个变量子元素和返回一个或多个变量信息的TagExtraInfo类
jsp.error.not.in.template=在模板文本体中, [{0}] 是不允许的.
jsp.error.outputfolder=无输出目录
jsp.error.overflow=错误：JSP缓冲区溢出
jsp.error.page.conflict.autoflush=页面指令：“ autoFlush”非法多次出现不同值（旧值: [{0}], 新值: [{1}]）
jsp.error.page.conflict.buffer=页面指令：“ buffer”非法多次出现不同值（旧值: [{0}], 新值: [{1}]）
jsp.error.page.conflict.contenttype=Page指令：非法出现多次出现的''contentType''具有不同的值（old：[{0}]，new：[{1}]）
jsp.error.page.conflict.deferredsyntaxallowedasliteral=页面指令：“ deferredSyntaxAllowedAsLiteral”非法多次出现不同值（旧值: [{0}], 新值: [{1}]）
jsp.error.page.conflict.errorpage=页指令：不同值的多次出现“errorPage”的非法值（旧:[{0}],新:[{1}]）
jsp.error.page.conflict.extends=页面指令：“ extends”非法多次出现不同值（旧值: [{0}], 新值: [{1}]）
jsp.error.page.conflict.info=页面指令：“ info”非法多次出现不同值（旧值: [{0}], 新值: [{1}]）
jsp.error.page.conflict.iselignored=页面指令：“ isELIgnored”非法多次出现不同值（旧值: [{0}], 新值: [{1}]）
jsp.error.page.conflict.iserrorpage=页面指令：“ isErrorPage”非法多次出现不同值（旧值: [{0}], 新值: [{1}]）
jsp.error.page.conflict.isthreadsafe=页面指令：“ isThreadSafe”非法多次出现不同值（旧值: [{0}], 新值: [{1}]）
jsp.error.page.conflict.language=页面指令：“ language” 非法多次出现不同值（ 旧值：[{0}]，新值：[{1}] ）
jsp.error.page.conflict.session=页面指令：“session” 非法多次出现不同值（ 旧值：[{0}]，新值：[{1}] ）
jsp.error.page.conflict.trimdirectivewhitespaces=页面指令：违法出现多个有不同的值（旧值：[{0}]，新值：[{1}]）的''trimDirectiveWhitespaces''
jsp.error.page.invalid.buffer=页面指令：buffer 值无效
jsp.error.page.invalid.deferredsyntaxallowedasliteral=页面指令：deferredSyntaxAllowedAsLiteral的 值无效
jsp.error.page.invalid.import=页面指令：import 值无效
jsp.error.page.invalid.iselignored=页面指令：isELIgnored 值无效
jsp.error.page.invalid.iserrorpage=页面指令：isErrorPage 值无效
jsp.error.page.invalid.isthreadsafe=页面指令：isThreadSafe 值无效
jsp.error.page.invalid.session=页面指令：session 值无效
jsp.error.page.invalid.trimdirectivewhitespaces=页面指令：trimDirectiveWhitespaces 值无效
jsp.error.page.language.nonjava=页面指令：language 属性无效
jsp.error.page.multi.pageencoding=页指令不能有多次出现的页编码
jsp.error.page.noSession=无法访问不参与任何会话的页中的会话作用域
jsp.error.param.invalidUse=jsp:param 不能在jsp:include、jsp:forward或jsp:params等元素外使用
jsp.error.paramexpected=使用“name”和“value”属性期望“jsp:param”标准操作
jsp.error.params.emptyBody=jsp:params必须至少包含一个嵌套的jsp:param
jsp.error.params.invalidUse=参数jsp:params必须是jsp:plugin的直接孩子参数
jsp.error.parse.error.in.TLD=标记库描述符中的分析错误：[{0}]
jsp.error.parse.xml=无法解析 XML 文件 [{0}]
jsp.error.parse.xml.line=文件[{0}]上的XML解析错误：（第[{1}]行，第[{2}]行）
jsp.error.parse.xml.scripting.invalid.body=[{0}]元素的主体不能包含任何XML元素
jsp.error.plugin.badtype=jsp:plugin中“type”属性的值非法：必须是“bean”或“applet”
jsp.error.plugin.nocode=代码未定义在jsp:plugin中
jsp.error.plugin.notype=jsp:plugin中未声明type
jsp.error.prefix.refined=尝试将前缀[{0}]重新定义为[{1}]，但当前作用域中已将其定义为[{2}]。
jsp.error.prefix.use_before_dcl=tag指令中设置的前缀[{0}]之前已被[{1}]文件[{2}]行的一个action使用
jsp.error.prolog_config_encoding_mismatch=XML prolog [{0}]中指定的页面编码与jsp-property-group [{1}]中指定的页面编码不同
jsp.error.prolog_pagedir_encoding_mismatch=指定在XML语言[{0}]的网页编码是不同于指定在网页的指令[{1}]。
jsp.error.quotes.unterminated=未终结的引号
jsp.error.scripting.variable.missing_name=无法从属性[{0}]确定脚本变量名称
jsp.error.security=上下文的安全初始化失败
jsp.error.servlet.destroy.failed=JSP页面的Servlet.destroy（）期间出现异常
jsp.error.servlet.invalid.method=JSP 只允许 GET、POST 或 HEAD。Jasper 还允许 OPTIONS
jsp.error.setLastModified=无法设置文件[{0}]的上次修改日期
jsp.error.signature.classnotfound=无法找到TLD中方法签名中为函数[{1}]指定的类[{0}]。[{2}]
jsp.error.simpletag.badbodycontent=对于一下简单的标记符，用于类[{0}]的标记描述符指定了一个无效的body内容（JSP）中
jsp.error.single.line.number=JSP文件：[{1}] 的第 [{0}] 行发生了一个错误
jsp.error.stream.close.failed=流.关闭失败
jsp.error.stream.closed=流.关闭
jsp.error.tag.conflict.attr=Tag指令：具有多个具有不同值的属性{0}的重复出现是非法的（旧的：[{1}，新的：[{2}]）
jsp.error.tag.conflict.deferredsyntaxallowedasliteral=Tag指令：具有多个具有不同值的“deferredSyntaxAllowedAsLiteral”（旧值：[{0}），新值：[{1}）的重复出现是非法的。
jsp.error.tag.conflict.iselignored=TAG指令：多次出现不同值的“isELIgnored”(旧：[{0}]，New：[{1}])
jsp.error.tag.conflict.language=标签指令：非法出现多次出现的具有不同值的“语言”（旧：[{0}]，新：[{1}]）
jsp.error.tag.conflict.trimdirectivewhitespaces=标签指令：非法地多次出现具有不同值的“trimDirectiveWhitespaces”（旧值：[{0}），新值：[{1}]）
jsp.error.tag.invalid.deferredsyntaxallowedasliteral=标签指令)：deferredSyntaxAllowedAsLiteral的值无效
jsp.error.tag.invalid.iselignored=Tag指令:对isELIgnored来说是无效值
jsp.error.tag.invalid.trimdirectivewhitespaces=Tag指令：trimDirectiveWhitespaces的值无效
jsp.error.tag.language.nonjava=标记指令：无效的语言属性。
jsp.error.tag.multi.pageencoding=Tag指令不能多次出现pageencoding
jsp.error.tagdirective.badbodycontent=标签指令中的无效的内容体[{0}]
jsp.error.tagfile.badSuffix=在文件路径[{0}]下找不到".tag"的后缀
jsp.error.tagfile.illegalPath=非法的标记文件路径：[{0}]，必须以“/WEB-INF/tags”或“/META-INF/tags”开头。
jsp.error.tagfile.missingPath=未指定标记文件的路径
jsp.error.tagfile.nameFrom.badAttribute=属性指令（在行[{1}]中声明并且其name属性为[{0}]，此name-from-attribute属性的值）必须是java.lang.String类型，是“required”而不是一个“rtexprvalue”。
jsp.error.tagfile.nameFrom.noAttribute=找不到具有值[{0}]的name属性的属性指令，该属性是name-from-attribute属性的值。
jsp.error.tagfile.nameNotUnique=[{0}]的值与第[{2}]行中的[{1}]的值相同。
jsp.error.taglibDirective.absUriCannotBeResolved=无法在web.xml或使用此应用程序部署的jar文件中解析绝对uri：[{0}]
jsp.error.taglibDirective.both_uri_and_tagdir=同时指定了“uri”和“tagdir”属性
jsp.error.taglibDirective.missing.location='uri'和'tagdir' 属性均未指定。
jsp.error.taglibDirective.uriInvalid=为标签库[{0}]提供的URI不是有效的URI
jsp.error.tei.invalid.attributes=来自TagExtraInfo的[{0}]的验证错误消息
jsp.error.teiclass.instantiation=无法加载或实例化TagExtraInfo类：[{0}]。
jsp.error.text.has_subelement=＆LT; JSP：文本＆GT; 不得有任何子元素
jsp.error.tld.fn.duplicate.name=标记库[{1}]中的函数名[{0}]重复
jsp.error.tld.fn.invalid.signature=TLD中函数签名的语法无效。 标签库：[{0}]，功能：[{1}]
jsp.error.tld.invalid_tld_file=无效的tld文件：[{0}]，有关详细信息，请参阅JSP规范第7.3.1节。
jsp.error.tld.mandatory.element.missing=TLD [{1}] 中强制 TLD 元素 [{0}] 不存在或为空
jsp.error.tld.missing=找不到URI:[{1}]的taglib[{0}]
jsp.error.tld.missing_jar=丢失了包含TLD的JAR资源[{0}]
jsp.error.tld.unable_to_get_jar=无法获取包含TLD:[{1}]的JAR资源[{0}]
jsp.error.tlv.invalid.page=):[{0}]和[{1}]的TagLibraryValidator的验证错误消息
jsp.error.tlvclass.instantiation=未能加载或实例化TagLibraryValidator类：[{0}]
jsp.error.unable.compile=无法为JSP编译类
jsp.error.unable.deleteClassFile=无法删除class文件[{0}]
jsp.error.unable.load=无法加载JSP的相关类
jsp.error.unable.renameClassFile=无法重命名类文件[{0}]为[{1}]
jsp.error.unable.to_find_method=不能为属性:[{0}]找到setter 方法.
jsp.error.unavailable=JSP已被标记为不可用
jsp.error.unbalanced.endtag=结束标签&lt;/{0}不对称
jsp.error.undeclared_namespace=使用未声明的命名空间[{0}]遇到自定义标记
jsp.error.unknown_attribute_type=属性[{0}]的未知属性类型[{1}]。
jsp.error.unsupported.encoding=不支持的编码：[{0}]
jsp.error.unterminated=未终止的[{0}]标记。
jsp.error.usebean.duplicate=useBean:重复的bean名称：[{0}]
jsp.error.usebean.noSession=当JSP页声明（通过page指令）useBean不参与会话时，useBean使用会话作用域是非法的
jsp.error.var_and_varReader=只有一个“var”或“varReader”能被指定
jsp.error.variable.alias=必须在variable指令中指定属性和别名属性中的name或none
jsp.error.variable.both.name=不能在变量指令的属性属性中同时指定给定的名称和名称
jsp.error.variable.either.name=必须在变量指令中指定 name-given 或 name-from-attribute 属性
jsp.error.xml.badStandardAction=无效、标准的action: [{0}]
jsp.error.xml.bad_tag=在与uri[{1}]关联的标记库中未定义标记[{0}]
jsp.exception=在 [{1}] 行处理 [{0}] 时发生异常
jsp.info.ignoreSetting=因为 SecurityManager 被启用，忽略 [{1}] 的 [{0}] 的设置
jsp.message.dont.modify.servlets=重要提示：不要修改生成的servlet
jsp.message.jsp_added=增加JSP 为路径[{0}]为上下文[{1}]的队列
jsp.message.jsp_queue_created=用长度[{0}]上下文[{1}]创建了jsp队列
jsp.message.jsp_queue_update=在上下文[{1}]队列中更新路径为[{0}]的JSP
jsp.message.jsp_removed_excess=从上下文[{1}]的队列中移除额外在路径[{0}]中JSP，
jsp.message.jsp_removed_idle=在[{2}]毫秒之后删除上下文[{1}]中路径[{0}]的空闲JSP
jsp.message.jsp_unload_check=在context[{0}]中检查未加载的jsp，jsp总共:[{1}]队列长度[{2}]
jsp.message.parent_class_loader_is=父类加载器是：[{0}]
jsp.message.scratch.dir.is=JSP引擎的Scratch目录是：[{0}]
jsp.tldCache.noTldInDir=在目录[{0}]中未找到TLD文件
jsp.tldCache.noTldInJar=在{0}中找不到TLD文件。考虑将JAR添加到CATALINA_BASE/conf/CATALINA.properties文件中的tomcat.util.scan.StandardJarScanFilter.jarsToSkip属性。
jsp.tldCache.noTldInResourcePath=在资源路径{0}中找不到TLD文件。
jsp.tldCache.noTldSummary=至少有一个JAR被扫描用于TLD但尚未包含TLD。 为此记录器启用调试日志记录，以获取已扫描但未在其中找到TLD的完整JAR列表。 在扫描期间跳过不需要的JAR可以缩短启动时间和JSP编译时间。
jsp.tldCache.tldInDir=在目录 [{0}]中找到了TLD文件。
jsp.tldCache.tldInJar=在JAR[{0}]中找到了TLD文件。
jsp.tldCache.tldInResourcePath=在资源路径{0}中找到TLD文件。
jsp.warning.bad.urlpattern.propertygroup=web.xml中url模式子元素中的值[{0}]错误
jsp.warning.checkInterval=警告：initParam checkInterval的值无效。将使用默认值“300”秒
jsp.warning.classDebugInfo=警告：initParam classdebuginfo的值无效。将使用默认值“false”
jsp.warning.classpathUrl=在类路径中找到无效的URL。此URL将被忽略
jsp.warning.compiler.classfile.delete.fail=未能删除生成的类文件[{0}]
jsp.warning.compiler.classfile.delete.fail.unknown=删除生成的class文件失败
jsp.warning.compiler.javafile.delete.fail=未能删除生成的Java文件[{0}]
jsp.warning.development=警告：initParam开发的值无效。将使用默认值“true”。
jsp.warning.displaySourceFragment=警告：displaySourceFragment初始化参数时参数值无效，将使用默认的值“true”
jsp.warning.dumpSmap=警告：初始化堆内存的值无效。将使用“false”的默认值
jsp.warning.enablePooling=警告：initParam enablePooling的值无效。将使用默认值“true”
jsp.warning.fork=警告：initParam的值无效。将使用“true”的默认值
jsp.warning.genchararray=警告：initParam genstringascharray的值无效。将使用默认值“false”
jsp.warning.jspIdleTimeout=警告：initParam jspIdleTimeout的值无效。将使用默认值“-1”
jsp.warning.keepgen=警告：initParam keepgenerated的值无效。将使用默认值“false”
jsp.warning.mappedFile=警告：initParam mappedFile的值无效。将使用默认值“false”
jsp.warning.maxLoadedJsps=警告：initParam maxLoadedJsps的值无效。将使用默认值“-1”。
jsp.warning.modificationTestInterval=警告：initParam modificationTestInterval的值无效。将使用默认值“4”秒
jsp.warning.noJarScanner=警告：ServletContext中没有设置org.apache.tomcat.JarScaner。回到默认的JarScaner实现。
jsp.warning.quoteAttributeEL=警告：initParam quoteattribeel的值无效。将使用默认值“false”
jsp.warning.recompileOnFail=警告：initParam recompileOnFail的值无效。将使用默认值“false”
jsp.warning.strictQuoteEscaping=警告：对initParam strictQuoteEscaping来说无效的值，将会使用默认值“true”
jsp.warning.suppressSmap=警告:suppressSmap的初始化参数无效。将使用默认值“false”
jsp.warning.tagPreDestroy=处理标记实例的preDestroy时出错[{0}]
jsp.warning.tagRelease=处理[{0}]的标记实例上的释放时出错
jsp.warning.unknown.sourceVM=忽略未知源VM[{0}]
jsp.warning.unknown.targetVM=忽略未知目标VM[{0}]
jsp.warning.unsupported.sourceVM=不支持的源VM[{0}]请求，使用[{1}]
jsp.warning.unsupported.targetVM=不支持请求的目标VM[{0}]，使用[{1}]
jsp.warning.xpoweredBy=警告：initParam xpoweredBy的值无效。将使用默认值“false”

jspc.delete.fail=无法删除文件 [{0}]
jspc.error.fileDoesNotExist=文件参数 [{0}] 不存在
jspc.error.generalException=错误文件[{0}]生成以下常规异常：
jspc.error.invalidFragment=由于web片段中的错误而中止预编译
jspc.error.invalidWebXml=由于web.xml中的错误而中止预编译
jspc.generation.result=生成在[{1}]毫秒内完成，出现[{0}]个错误
jspc.implicit.uriRoot=uriRoot隐式设置为[{0}]
jspc.webfrg.footer=\n\
</web-fragment>\n\
\n
jspc.webinc.footer=<!--\n\
由Apache Tomcat JSPC自动创建的内容结束。\n\
-->\n\
\n
jspc.webinc.header=\n\
<!--\n\
由 Apache Tomcat JspC 自动生成。\n\
-->\n\
\n
jspc.webinc.insertEnd=<!-- JSPC servlet 映射结束 -->
jspc.webinc.insertStart=<!-- JSPC servlet 映射开始 -->
jspc.webxml.footer=\n\
</web-app>\n\
\n

org.apache.jasper.compiler.ELParser.invalidQuotesForStringLiteral=字符串文本[{0}]无效。它必须包含在单引号或双引号中。
org.apache.jasper.compiler.ELParser.invalidQuoting=表达式[{0}]无效。在带引号的字符串中，只有[]，['']和[“]可以用[]转义。
org.apache.jasper.compiler.TldCache.servletContextNull=提供的 ServletContext 为 null
org.apache.jasper.servlet.JasperInitializer.onStartup=正在初始化上下文[{0}]的Jasper
org.apache.jasper.servlet.TldScanner.webxmlAdd=从资源路径[{0} ]加载URI[{1}]的TLD
org.apache.jasper.servlet.TldScanner.webxmlFailPathDoesNotExist=无法使用路径 [{0}] 和 URI [{1}] 处理TLD。指定的路径不存在。
org.apache.jasper.servlet.TldScanner.webxmlSkip=跳过从资源路径{0}加载URI[{1}]的TLD，因为它已在<jsp config>中定义

xmlParser.skipBomFail=解析XML输入流时，跳过BOM失败。
