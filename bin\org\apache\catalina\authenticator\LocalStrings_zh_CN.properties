# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

authenticator.certificates=此请求中没有客户端证书链
authenticator.changeSessionId=在身份验证时, 会话 ID 从 [{0} 更改为 [{1}]
authenticator.check.authorize=用户名[{0}]从连接器获得，并被信任为有效。从Tomcat领域获取此用户的角色。
authenticator.check.authorizeFail=领域无法识别用户[{0}]。创建具有该名称且没有角色的主体。
authenticator.check.found=已通过身份验证 [{0}]
authenticator.check.sso=未经过身份验证但找到了SSO会话ID [{0}]。尝试重新验证。
authenticator.formlogin=对表单登录页的直接引用无效
authenticator.jaspicCleanSubjectFail=清除 JASPIC 主题失败
authenticator.jaspicSecureResponseFail=在JASPIC处理期间无法保证响应
authenticator.jaspicServerAuthContextFail=失败的获取一个JASPIC  ServerAuthContext 实例
authenticator.loginFail=登录失败
authenticator.manager=初始化信任管理器异常
authenticator.noAuthHeader=客户端未发送授权请求头
authenticator.notContext=配置错误:必须被附属于一个上下文
authenticator.requestBodyTooBig=请求正文太大，无法在身份验证过程中进行缓存
authenticator.sessionExpired=已超出登录过程所允许的时间。 如果您希望继续，则必须单击两次后退并重新单击您请求的链接或先关闭然后重新打开浏览器
authenticator.tomcatPrincipalLogoutFail=使用TomcatPrincipal实例注销失败
authenticator.unauthorized=无法使用提供的凭据进行身份验证

basicAuthenticator.invalidCharset=只允许值为null、空字符串或UTF-8

digestAuthenticator.cacheRemove=已从客户端 nonce 缓存中删除有效条目，以便为新条目腾出空间。重播攻击现在是可能的。为防止重播攻击的可能性，请降低nonceValidity或增加nonceCacheSize。此类型的进一步警告将被抑制5分钟。

formAuthenticator.forwardErrorFail=转发到错误页时出现意外错误。
formAuthenticator.forwardLogin=使用请求方法GET将使用方法[{1}]发出的对[{0}]的请求转发到上下文[{3}]的登录页[{2}]
formAuthenticator.forwardLoginFail=转发到登录页时出现意外错误。
formAuthenticator.noErrorPage=没有为上下文[{0}]中的表单身份验证定义错误页
formAuthenticator.noLoginPage=在环境[{0}]中，未为FORM认证定义登录页面

singleSignOn.debug.associate=SSO将应用程序会话[{1}]与SSO会话[{0}]关联
singleSignOn.debug.associateFail=SSO无法关联应用程序会话{0}，因为SSO会话{1}不存在。
singleSignOn.debug.cookieCheck=SSO检查SSO cookie
singleSignOn.debug.cookieNotFound=SSO没有找到SSO cookie
singleSignOn.debug.deregister=与SSO会话[{1}]关联的SSO过期应用程序会话[{0}]
singleSignOn.debug.deregisterFail=SSO撤销登记SSO会话[{0}]失败，因为缓存中不包含这个SSO会话
singleSignOn.debug.deregisterNone=SSO注销了SSO会话[{0}]，但未找到关联的应用程序会话
singleSignOn.debug.hasPrincipal=找到以前经过身份验证的主体[{0}]
singleSignOn.debug.invoke=SSO为[{0}]处理请求
singleSignOn.debug.principalCheck=SSO为SSO会话[{0}]寻找缓存的Principal
singleSignOn.debug.principalFound=SSO 找到了带着认证类型的缓存代理
singleSignOn.debug.principalNotFound=SSO未找到缓存的Principal，为会话[{0}]擦除SSO cookie
singleSignOn.debug.register=使用身份验证类型[{2}]的用户[{1}]的SSO注册SSO会话[{0}]。
singleSignOn.debug.removeSession=SSO 从 SSO session [{1}] 中删除应用程序会话 [{0}]
singleSignOn.debug.sessionLogout=SSO正在处理SSO会话[{0}]和应用程序会话[{1}]的注销
singleSignOn.debug.sessionTimeout=SSO正在处理SSO会话[{0}]和应用程序会话[{1}]的超时
singleSignOn.debug.update=SSO 更新SSO 会话[{0}] 对认证 类型[{1}]
singleSignOn.sessionExpire.contextNotFound=SSO无法中止[{0}]，因为Context未找到
singleSignOn.sessionExpire.engineNull=SSO无法使会话[{0}]过期，因为引擎为空。
singleSignOn.sessionExpire.hostNotFound=由于无法找到主机，单点登录无法使会话[{0}]过期
singleSignOn.sessionExpire.managerError=由于会话管理器在检索会话时抛出异常，导致单点登录无法使会话[{0}]失效
singleSignOn.sessionExpire.managerNotFound=SSO无法使会话[{0}]过期，因为找不到管理器
singleSignOn.sessionExpire.sessionNotFound=SSO无法使会话[{0}]过期，因为找不到该会话

spnegoAuthenticator.authHeaderNoToken=客户端发送的协商授权 header 未包含 token
spnegoAuthenticator.authHeaderNotNego=客户端发送的授权头不是以协商开始的。
spnegoAuthenticator.serviceLoginFail=无法作为服务主体登录
spnegoAuthenticator.ticketValidateFail=无法验证客户端提供的票证
