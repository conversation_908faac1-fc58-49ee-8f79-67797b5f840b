# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cgiServlet.emptyEnvVarName=Empty environment variable name in initialisation parameter [environment-variable-]
cgiServlet.expandCloseFail=Failed to close input stream for script at path [{0}]
cgiServlet.expandCreateDirFail=Failed to create destination directory [{0}] for script expansion
cgiServlet.expandDeleteFail=Failed to delete file at [{0}] after IOException during expansion
cgiServlet.expandFail=Failed to expand script at path [{0}] to [{1}]
cgiServlet.expandNotFound=Unable to expand [{0}] as it could not be found
cgiServlet.expandOk=Expanded script at path [{0}] to [{1}]
cgiServlet.find.found=Found CGI: name [{0}], path [{1}], script name [{2}] and CGI name [{3}]
cgiServlet.find.location=Looking for a file at [{0}]
cgiServlet.find.path=CGI script requested at path [{0}] relative to CGI location [{1}]
cgiServlet.invalidArgumentDecoded=The decoded command line argument [{0}] did not match the configured cmdLineArgumentsDecoded pattern [{1}]
cgiServlet.invalidArgumentEncoded=The encoded command line argument [{0}] did not match the configured cmdLineArgumentsEncoded pattern [{1}]
cgiServlet.runBadHeader=Bad header line [{0}]
cgiServlet.runFail=I/O problems processing CGI
cgiServlet.runHeaderReaderFail=I/O problems closing header reader
cgiServlet.runInvalidStatus=Invalid status [{0}]
cgiServlet.runOutputStreamFail=I/O problems closing output stream
cgiServlet.runReaderInterrupt=Interrupted waiting for stderr reader thread
cgiServlet.runStdErr=stderr line: [{0}]
cgiServlet.runStdErrCount=Received [{0}] lines on stderr
cgiServlet.runStdErrFail=I/O problems with stderr

defaultServlet.blockExternalEntity=Blocked access to external entity with publicId [{0}] and systemId [{0}]
defaultServlet.blockExternalEntity2=Blocked access to external entity with name [{0}], publicId [{1}], baseURI [{2}] and systemId [{3}]
defaultServlet.blockExternalSubset=Blocked access to external subset with name [{0}] and baseURI [{1}]
defaultServlet.missingResource=The requested resource [{0}] is not available
defaultServlet.noResources=No static resources were found
defaultServlet.readerCloseFailed=Failed to close reader
defaultServlet.skipfail=Read failed because only [{0}] bytes were available but needed to skip [{1}] bytes to reach the start of the requested range
defaultServlet.xslError=XSL transformer error

directory.filename=Filename
directory.lastModified=Last Modified
directory.parent=Up To [{0}]
directory.size=Size
directory.title=Directory Listing For [{0}]

webdavservlet.externalEntityIgnored=The request included a reference to an external entity with PublicID [{0}] and SystemID [{1}] which was ignored
webdavservlet.inputstreamclosefail=Failed to close the inputStream of [{0}]
webdavservlet.jaxpfailed=JAXP initialization failed
