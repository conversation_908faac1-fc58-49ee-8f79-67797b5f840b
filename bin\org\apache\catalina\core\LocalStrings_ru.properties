# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

applicationContext.addJspFile.iae=Файл JSP [{0}] содержит ошибки
applicationContext.addListener.iae.cnfe=Невозможно создать экземпляр типа [{0}]
applicationContext.addListener.iae.wrongType=Указанный тип[{0}] не является одним из ожидаемых типов слушателей
applicationContext.setAttribute.namenull=Имя не может быть пустым

aprListener.initializingFIPS=Инициализируется режим FIPS...

filterChain.filter=При выполнении фильтра выброшено исключение

standardContext.securityConstraint.mixHttpMethod=Запрещено смешивать <http-method> и <http-method-omission> в одной и той же коллекции веб-ресурсов

standardWrapper.allocate=Ошибка при выделении экземпляра сервлета
standardWrapper.allocateException=Ошибка выделения для сервлета [{0}]
standardWrapper.deallocateException=Ошибка освобождения для сервлета [{0}]
standardWrapper.destroyException=Servlet.destroy() для сервлета [{0}] выбросил исключение
standardWrapper.destroyInstance=InstanceManager.destroy() для сервлета [{0}] выбросил исключение
standardWrapper.initException=Servlet.init() для сервлета [{0}] выбросил исключение
standardWrapper.instantiate=Ошибка создания экземпляра класса сервлета [{0}]
standardWrapper.isUnavailable=Сервлет [{0}] временно недоступен
standardWrapper.notChild=Контейнер может не иметь дочерних контейнеров
standardWrapper.notClass=Для сервлета [{0}] не указан специальный класс
standardWrapper.notContext=Родительский контейнер обертки должен быть контекстом
standardWrapper.notFound=Сервлет [{0}] недоступен
standardWrapper.notServlet=Класс [{0}] не является сервлетом
standardWrapper.serviceException=Servlet.service() для сервлета [{0}] в контексте с путем [{1}] выбросил исключение
standardWrapper.serviceExceptionRoot=Servlet.service() для сервлета [{0}] в контексте с путем [{1}] выбросил исключение [{2}] с первопричиной
standardWrapper.unavailable=Выделенный сервлет [{0}] недоступен
standardWrapper.unloadException=Сервлет [{0}] выбросил исключение unload()
standardWrapper.unloading=Невозможно выделить сервлет [{0}] потому что он еще заугружается
standardWrapper.waiting=Ожидание для [{0}] экземпляра(ов) которые нужно освободить для сервлета [{1}]
