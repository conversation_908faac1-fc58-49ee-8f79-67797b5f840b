package com.yq.cc.vxml;

/**
 * IVR 到 VXML 转换工具测试类
 */
public class IvrToVxmlUtilTest {
    
    public static void main(String[] args) {
        System.out.println("开始测试 IvrToVxmlUtil...");
        
        int totalTests = 0;
        int passedTests = 0;
        
        // 测试1：基本转换功能
        totalTests++;
        if (testBasicConversion()) {
            passedTests++;
            System.out.println("✓ 测试1：基本转换功能 - 通过");
        } else {
            System.out.println("✗ 测试1：基本转换功能 - 失败");
        }
        
        // 测试2：条件分支处理
        totalTests++;
        if (testConditionalBranching()) {
            passedTests++;
            System.out.println("✓ 测试2：条件分支处理 - 通过");
        } else {
            System.out.println("✗ 测试2：条件分支处理 - 失败");
        }
        
        // 测试3：XML 转义处理
        totalTests++;
        if (testXmlEscaping()) {
            passedTests++;
            System.out.println("✓ 测试3：XML 转义处理 - 通过");
        } else {
            System.out.println("✗ 测试3：XML 转义处理 - 失败");
        }
        
        // 测试4：空配置处理
        totalTests++;
        if (testEmptyConfiguration()) {
            passedTests++;
            System.out.println("✓ 测试4：空配置处理 - 通过");
        } else {
            System.out.println("✗ 测试4：空配置处理 - 失败");
        }
        
        // 测试5：错误处理
        totalTests++;
        if (testErrorHandling()) {
            passedTests++;
            System.out.println("✓ 测试5：错误处理 - 通过");
        } else {
            System.out.println("✗ 测试5：错误处理 - 失败");
        }
        
        // 输出测试结果
        System.out.println("\n测试结果：");
        System.out.println("总测试数：" + totalTests);
        System.out.println("通过测试：" + passedTests);
        System.out.println("失败测试：" + (totalTests - passedTests));
        System.out.println("通过率：" + (passedTests * 100 / totalTests) + "%");
        
        if (passedTests == totalTests) {
            System.out.println("\n🎉 所有测试通过！");
        } else {
            System.out.println("\n⚠️ 部分测试失败，请检查代码。");
        }
    }
    
    /**
     * 测试基本转换功能
     */
    private static boolean testBasicConversion() {
        try {
            String json = createBasicIvrJson();
            String vxml = IvrToVxmlUtil.convertJsonToVxml(json);
            
            // 验证基本结构
            return vxml.contains("<?xml version=\"1.0\" encoding=\"GBK\"?>") &&
                   vxml.contains("<vxml version=\"2.1\"") &&
                   vxml.contains("<form id=\"mainForm\">") &&
                   vxml.contains("</form>") &&
                   vxml.contains("</vxml>");
                   
        } catch (Exception e) {
            System.err.println("基本转换测试异常：" + e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试条件分支处理
     */
    private static boolean testConditionalBranching() {
        try {
            String json = createConditionalIvrJson();
            String vxml = IvrToVxmlUtil.convertJsonToVxml(json);
            
            // 验证条件分支结构
            return vxml.contains("<if cond=") &&
                   vxml.contains("== '1'") &&
                   vxml.contains("!= '1'");
                   
        } catch (Exception e) {
            System.err.println("条件分支测试异常：" + e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试 XML 转义处理
     */
    private static boolean testXmlEscaping() {
        try {
            String json = createXmlEscapeIvrJson();
            String vxml = IvrToVxmlUtil.convertJsonToVxml(json);
            
            // 验证特殊字符被正确转义
            return vxml.contains("&lt;") &&
                   vxml.contains("&gt;") &&
                   vxml.contains("&amp;");
                   
        } catch (Exception e) {
            System.err.println("XML转义测试异常：" + e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试空配置处理
     */
    private static boolean testEmptyConfiguration() {
        try {
            String json = createEmptyIvrJson();
            String vxml = IvrToVxmlUtil.convertJsonToVxml(json);
            
            // 验证空配置能正常处理
            return vxml.contains("<vxml") && vxml.contains("</vxml>");
                   
        } catch (Exception e) {
            System.err.println("空配置测试异常：" + e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试错误处理
     */
    private static boolean testErrorHandling() {
        try {
            // 测试无效 JSON
            try {
                IvrToVxmlUtil.convertJsonToVxml("invalid json");
                return false; // 应该抛出异常
            } catch (RuntimeException e) {
                // 预期的异常
            }
            
            // 测试空字符串
            try {
                IvrToVxmlUtil.convertJsonToVxml("");
                return false; // 应该抛出异常
            } catch (RuntimeException e) {
                // 预期的异常
            }
            
            return true;
            
        } catch (Exception e) {
            System.err.println("错误处理测试异常：" + e.getMessage());
            return false;
        }
    }
    
    /**
     * 创建基本 IVR JSON 配置
     */
    private static String createBasicIvrJson() {
        return "{\n" +
               "  \"ivrCode\": \"test01\",\n" +
               "  \"ivrName\": \"测试流程\",\n" +
               "  \"nodeList\": [\n" +
               "    {\n" +
               "      \"id\": \"START_001\",\n" +
               "      \"name\": \"开始\",\n" +
               "      \"type\": \"start\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"END_001\",\n" +
               "      \"name\": \"结束\",\n" +
               "      \"type\": \"end\"\n" +
               "    }\n" +
               "  ],\n" +
               "  \"lineList\": [\n" +
               "    {\n" +
               "      \"id\": \"LINE_001\",\n" +
               "      \"from\": \"START_001\",\n" +
               "      \"to\": \"END_001\"\n" +
               "    }\n" +
               "  ]\n" +
               "}";
    }
    
    /**
     * 创建带条件分支的 IVR JSON 配置
     */
    private static String createConditionalIvrJson() {
        return "{\n" +
               "  \"ivrCode\": \"test02\",\n" +
               "  \"ivrName\": \"条件测试流程\",\n" +
               "  \"nodeList\": [\n" +
               "    {\n" +
               "      \"id\": \"START_001\",\n" +
               "      \"name\": \"开始\",\n" +
               "      \"type\": \"start\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"SERVICETASK_001\",\n" +
               "      \"name\": \"用户输入\",\n" +
               "      \"type\": \"serviceTask\",\n" +
               "      \"paramName\": \"USER_INPUT\",\n" +
               "      \"paramMinSize\": \"1\",\n" +
               "      \"paramMaxSize\": \"1\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"END_001\",\n" +
               "      \"name\": \"结束\",\n" +
               "      \"type\": \"end\"\n" +
               "    }\n" +
               "  ],\n" +
               "  \"lineList\": [\n" +
               "    {\n" +
               "      \"id\": \"LINE_001\",\n" +
               "      \"from\": \"START_001\",\n" +
               "      \"to\": \"SERVICETASK_001\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"LINE_002\",\n" +
               "      \"from\": \"SERVICETASK_001\",\n" +
               "      \"to\": \"END_001\",\n" +
               "      \"expression\": {\n" +
               "        \"param\": \"USER_INPUT\",\n" +
               "        \"conditions\": \"eq\",\n" +
               "        \"val1\": \"1\"\n" +
               "      }\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"LINE_003\",\n" +
               "      \"from\": \"SERVICETASK_001\",\n" +
               "      \"to\": \"END_001\",\n" +
               "      \"expression\": {\n" +
               "        \"param\": \"USER_INPUT\",\n" +
               "        \"conditions\": \"neq\",\n" +
               "        \"val1\": \"1\"\n" +
               "      }\n" +
               "    }\n" +
               "  ]\n" +
               "}";
    }
    
    /**
     * 创建包含特殊字符的 IVR JSON 配置
     */
    private static String createXmlEscapeIvrJson() {
        return "{\n" +
               "  \"ivrCode\": \"test03\",\n" +
               "  \"ivrName\": \"XML转义测试\",\n" +
               "  \"nodeList\": [\n" +
               "    {\n" +
               "      \"id\": \"START_001\",\n" +
               "      \"name\": \"开始\",\n" +
               "      \"type\": \"start\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"USERTASK_001\",\n" +
               "      \"name\": \"特殊字符测试\",\n" +
               "      \"type\": \"userTask\",\n" +
               "      \"voicePlayContent\": \"测试<特殊>字符&转义\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"END_001\",\n" +
               "      \"name\": \"结束\",\n" +
               "      \"type\": \"end\"\n" +
               "    }\n" +
               "  ],\n" +
               "  \"lineList\": [\n" +
               "    {\n" +
               "      \"id\": \"LINE_001\",\n" +
               "      \"from\": \"START_001\",\n" +
               "      \"to\": \"USERTASK_001\"\n" +
               "    },\n" +
               "    {\n" +
               "      \"id\": \"LINE_002\",\n" +
               "      \"from\": \"USERTASK_001\",\n" +
               "      \"to\": \"END_001\"\n" +
               "    }\n" +
               "  ]\n" +
               "}";
    }
    
    /**
     * 创建空的 IVR JSON 配置
     */
    private static String createEmptyIvrJson() {
        return "{\n" +
               "  \"ivrCode\": \"test04\",\n" +
               "  \"ivrName\": \"空配置测试\",\n" +
               "  \"nodeList\": [],\n" +
               "  \"lineList\": []\n" +
               "}";
    }
}
