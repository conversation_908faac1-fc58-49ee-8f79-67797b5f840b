# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

accessLogValve.alreadyExists=无法将访问日志从{0}重命名为{1}，文件已存在
accessLogValve.closeFail=关闭访问日志文件失败
accessLogValve.deleteFail=未能删除旧的访问日志[{0}]
accessLogValve.invalidLocale=无法将区域设置设为 [{0}]
accessLogValve.invalidPortType=端口类型 [{0}] 无效，使用服务器（本地）端口
accessLogValve.openDirFail=无法为访问日志创建目录[{0}]。
accessLogValve.openFail=无法打开访问日志文件[{0}]。
accessLogValve.renameFail=无法将访问日志从[{0}]重命名为[{1}]。
accessLogValve.rotateFail=失败的循环切割访问日志.
accessLogValve.unsupportedEncoding=未能将编码设置为[{0}]，将使用系统默认字符集。
accessLogValve.writeFail=无法写入日志消息[{0}]

errorReportValve.description=描述
errorReportValve.exception=例外情况
errorReportValve.exceptionReport=异常报告
errorReportValve.message=消息
errorReportValve.noDescription=没有可用的描述
errorReportValve.note=):注意
errorReportValve.rootCause=根本原因。
errorReportValve.rootCauseInLogs=主要问题的全部 stack 信息可以在 server logs 里查看
errorReportValve.statusHeader=HTTP状态 {0} - {1}
errorReportValve.statusReport=状态报告
errorReportValve.type=类型
errorReportValve.unknownReason=未知的原因

extendedAccessLogValve.badXParam=无效的x参数格式，需要是'x-#（…）
extendedAccessLogValve.badXParamValue=Servlet请求[{0}]的x参数值无效
extendedAccessLogValve.decodeError=无法解码以[{0}]开头的其余字符
extendedAccessLogValve.emptyPattern=模式只是空的或空白的
extendedAccessLogValve.noClosing=未关闭）在解码中找到
extendedAccessLogValve.patternParseError=分析模式[{0}]时出错

http.400.desc=由于被认为是客户端对错误（例如：畸形的请求语法、无效的请求信息帧或者虚拟的请求路由），服务器无法或不会处理当前请求。
http.400.reason=错误的请求
http.401.desc=因为当前请求缺少对目标资源对有效的认证信息，所以它不会实施。
http.401.reason=未经授权的
http.402.desc=这个状态码时为未来使用预留的.
http.402.reason=需要支付
http.403.desc=服务器理解该请求但拒绝授权。
http.403.reason=被禁止
http.404.desc=源服务器未能找到目标资源的表示或者是不愿公开一个已经存在的资源表示。
http.404.reason=未找到
http.405.desc=请求行中接收的方法由源服务器知道，但目标资源不支持
http.405.reason=方法不允许
http.406.desc=根据请求中接收到的主动协商头字段，目标资源没有用户代理可以接受的当前表示，而且服务器不愿意提供缺省表示。
http.406.reason=不可接收
http.407.desc=状态码和401（未授权的）类似，但是表示客户端为了使用代理需要对它自身进行认证。
http.407.reason=代理需要认证
http.408.desc=在预分配的等待时间内，服务器未收到完整的请求信息。
http.408.reason=请求超时
http.409.desc=由于和目标资源对当前状态发生冲突，所以请求无法完成。
http.409.reason=冲突
http.410.desc=原始服务器上不再可以访问目标资源，并且此条件可能是永久性的。
http.410.reason=跑了。
http.411.desc=服务器拒绝接受没有定义内容长度的请求。
http.411.reason=所需长度
http.412.desc=在服务器上测试时，请求头字段中给出的一个或多个条件被评估为false。
http.412.reason=前置条件失败
http.413.desc=服务器拒绝处理请求，因为请求负载大于服务器愿意或能够处理的负载
http.413.reason=有效载荷过大
http.414.desc=服务器拒绝为请求提供服务，因为请求目标比服务器愿意解释的要长。
http.414.reason=URI太长
http.415.desc=源服务器拒绝服务请求，因为有效负载的格式在目标资源上此方法不支持。
http.415.reason=不支持的媒体类型
http.416.desc=(:请求的范围头字段中的任何范围都没有与选定资源的当前范围重叠，或者请求的范围集由于无效范围或小范围或重叠范围的过度请求而被拒绝。
http.416.reason=范围不满足
http.417.desc=(:至少有一个入站服务器无法满足请求的Expect头字段中给定的期望。
http.417.reason=期望的失败
http.421.desc=请求被定向到一台无法响应的服务器
http.421.reason=错误的请求。
http.422.desc=服务器了解请求实体的内容类型，请求实体的语法正确，但无法处理包含的指令。
http.422.reason=不可处理实体
http.423.desc=源或目标资源的方法被锁
http.423.reason=已锁定
http.424.desc=这个方法不能在这个资源上执行，因为请求操作依赖另一个操作，但是另一个操作失败了。
http.424.reason=失败的依赖项
http.426.desc=服务器拒绝使用当前协议执行请求，但可能愿意在客户端升级到其他协议后执行。
http.426.reason=需要升级
http.428.desc=原始服务器要求请求是有条件的。
http.428.reason=要求先决条件
http.429.desc=用户在给定的时间内发送了太多请求（“速率限制”）
http.429.reason=请求过多
http.431.desc=服务器不愿意处理该请求，因为它的头字段太大。
http.431.reason=请求头的字段太大
http.451.desc=服务器出于法律原因拒绝了此请求。
http.451.reason=因法律原因无法获得。
http.500.desc=服务器遇到一个意外的情况，阻止它完成请求。
http.500.reason=内部服务器错误
http.501.desc=服务器不支持完成请求所需的功能。
http.501.reason=未实现
http.502.desc=服务器在充当网关或代理时, 在尝试完成请求时, 从它访问的入站服务器收到无效响应。
http.502.reason=坏网关
http.503.desc=由于临时过载或计划维护，服务器当前无法处理请求，这可能会在一些延迟后得到缓解。
http.503.reason=服务不可用。
http.504.desc=服务器在充当网关或代理时，没有从上游服务器接收到完成请求所需访问的及时响应。
http.504.reason=网关超时
http.505.desc=服务器不支持或拒绝支持请求消息中使用的主要HTTP版本。
http.505.reason=HTTP 版本不支持
http.506.desc=服务器内部配置错误：选取的变体资源配置为自身去处理透明的内容协商，因此在协商进程中不是一个合适的终点。
http.506.reason=变体也协商
http.507.desc=无法对资源执行该方法，因为服务器无法存储成功完成请求所需的表示。
http.507.reason=存储空间.不足
http.508.desc=服务器终止了一个操作，因为它在处理“深度：无限”请求时遇到无限循环
http.508.reason=监测到循环回路
http.510.desc=请求中未满足访问资源的策略
http.510.reason=没有.扩展
http.511.desc=客户端需要进行身份验证才能获得网络访问权限。
http.511.reason=需要网络身份验证

jdbcAccessLogValve.close=无法关闭数据库。
jdbcAccessLogValve.exception=执行插入访问项时发生异常

persistentValve.filter.failure=无法编译filter=[{0}]

remoteCidrValve.invalid=为{0}提供的配置无效。有关详细信息，请参阅以前的消息
remoteCidrValve.noPort=请求不含有合法的服务器端口号。请求被拒绝。
remoteCidrValve.noRemoteIp=客户端没有IP地址。请求被拒绝。

remoteIpValve.invalidHostHeader=在HTTP请求头[{1}]中发现Host的无效值[{0}]
remoteIpValve.invalidHostWithPort=HTTP头[{1}]中的主机值[{0}]包含将被忽略的端口号
remoteIpValve.invalidPortHeader=HTTP标头[{1}]中的端口找到的值[{0}]无效
remoteIpValve.invalidRemoteAddress=无法确定远程主机，因为发布的远程地址[{0}]是非法的

requestFilterValve.configInvalid=为Remote [Addr | Host]阀门提供了一个或多个无效配置设置，阻止Valve及其父容器启动
requestFilterValve.deny=根据[{1}]配置拒绝[{0}]的请求

sslValve.certError=无法处理证书字符串[{0}]以创建java.security.cert.X509Certificate对象
sslValve.invalidProvider=与此{[0}]请求关联的连接器上指定的SSL提供程序无效。 无法处理证书数据。

stuckThreadDetectionValve.notifyStuckThreadCompleted=线程[{0}](id=[{3}])之前报告为卡住，但是已经完成。它活跃了大概[{1}]毫秒。{2,选择，0#|0< 仍有[{2}]个被Valve监控的线程可能卡住}
stuckThreadDetectionValve.notifyStuckThreadDetected=线程[{0}]（id=[{6}]）已处于活动状态[{1}]毫秒（自[{2}]起），以便为[{4}]提供相同的请求，并且可能被卡住（此StuckThreadDetectionValve的配置阈值为[{5}]秒）。总共有{3}个线程受此阀监视，可能被卡住。
stuckThreadDetectionValve.notifyStuckThreadInterrupted=线程[{0}]（id=[{5}]）已被中断，因为它在[{1}]毫秒（自[{2}]起）内处于活动状态，以便为[{3}]提供相同的请求，并且可能被卡住（此StuckThreadDetectionValve的配置中断阈值为[{4}]秒）。
