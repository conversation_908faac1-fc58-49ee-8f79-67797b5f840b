# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jasper.error.emptybodycontent.nonempty=D''après la TLD, le tag [{0}] doit être vide, mais ne l''est pas

jsp.engine.info=Moteur Jasper JSP {0}
jsp.error.action.isnottagfile=L''action [{0}] ne peut être utilisée que dans un fichier tag
jsp.error.action.istagfile=L''action [{0}] ne peut être utilisée dans un fichier tag
jsp.error.attempt_to_clear_flushed_buffer=Erreur : Tentative d'effacement d'un tampon qui a déjà été vidangé (flush)
jsp.error.attr.quoted=La valeur de l'attribut doit être entre guillemets
jsp.error.attribute.custom.non_rt_with_expr=D''après la TLD, l''attribut [{0}] n''accepte aucune expression
jsp.error.attribute.deferredmix=Impossible d'utiliser des expressions EL ${} et #{} dans la même valeur d'attribut
jsp.error.attribute.duplicate=Les noms qualifiés d’attributs doivent être uniques au sein d'un élément
jsp.error.attribute.invalidPrefix=Le préfixe d''attribut [{0}] ne correspond à aucune librairie de tags importée
jsp.error.attribute.noequal=Symbole égal (equal) attendu
jsp.error.attribute.noescape=La valeur d''attribut [{0}] est entre guillemets avec [{1}] qui doit être échappé quand il est utilisé dans la valeur
jsp.error.attribute.noquote=Symbole guillemet (quote) attendu
jsp.error.attribute.nowhitespace=La spécification JSP requiert un caractère d'espacement devant le nom d'un attribut
jsp.error.attribute.null_name=Le nom d'attribut est null
jsp.error.attribute.standard.non_rt_with_expr=L''attribut [{0}] de l''action standard [{1}] n''accepte pas d''expressions
jsp.error.attribute.unterminated=L''attribut pour [{0}] n''est pas correctement terminé
jsp.error.bad.scratch.dir=Le paramètre "scratchDir" que vous avez spécifié : [{0}] est inutilisable.
jsp.error.badStandardAction=L'action n'est pas reconnue comme une action standard.
jsp.error.bad_attribute=L''attribut [{0}] est incorrect pour le tag [{1}] d''après la TLD indiquée
jsp.error.bad_tag=Aucun tag [{0}] dans la bibliothèque de tag importée avec le préfixe [{1}]
jsp.error.beans.nomethod=Impossible de trouver une méthode pour lire la propriété [{0}] dans le bean de type [{1}]
jsp.error.beans.nomethod.setproperty=Impossible de trouver une méthode pour mettre à jour la propriété [{0}] de type [{1}]dans le bean de type [{2}]
jsp.error.beans.noproperty==Impossible de trouver de l''information sur la propriété [{0}] dans le bean de type [{1}]
jsp.error.beans.nullbean=Tentative d'opération bean sur un objet nul.
jsp.error.beans.property.conversion=Impossible de convertir la string [{0}] vers la classe [{1}] pour l''attribut [{2}] : [{3}]
jsp.error.beans.propertyeditor.notregistered=L'éditeur de propriétés n'est pas enregistré avec le PropertyEditorManager
jsp.error.beans.setproperty.noindexset=Impossible de renseigner la propriété indéxée
jsp.error.bug48498=Impossible d'afficher un extrait du JSP, ce qui peut être causé par un problème du parser XML (voir le bug 48498 de Tomcat)
jsp.error.classname=Impossible de déterminer le nom de classe d'après le fichier .class
jsp.error.coerce_to_type=Impossible de convertir la valeur [{2}] de l''attribut [{0}] vers le type [{1}]
jsp.error.compilation=Erreur de compilation du fichier : [{0}] [{1}]
jsp.error.compiler=Aucun compilateur Java disponible
jsp.error.compiler.config=Aucun compilateur Java disponible pour les options de configuration compilerClassName : [{0}] et compiler : [{1}]
jsp.error.config_pagedir_encoding_mismatch=L''encode de page (Page-encoding) indiqué dans le jsp-property-group [{0}] est différent de celui indiqué dans la directive de page [{1}]
jsp.error.corresponding.servlet=Erreur de servlet générée :
jsp.error.could.not.add.taglibraries=Impossible d'ajouter une ou plusieurs bibliothèques de tag.
jsp.error.data.file.processing=Erreur durant le traitement du fichier [{0}]
jsp.error.data.file.read=Erreur lors de la lecture du fichier [{0}]
jsp.error.data.file.write=Erreur lors de l'écriture du fichier de données
jsp.error.deferredmethodandvalue='deferredValue' et 'deferredMethod' ne peuvent être toutes deux 'true'
jsp.error.deferredmethodsignaturewithoutdeferredmethod=Impossible de sécifier une signature de méthode si 'deferredMethod' n'est pas 'true'
jsp.error.deferredvaluetypewithoutdeferredvalue=Impossible de spécifier un value type si 'deferredValue' n'est pas 'true'
jsp.error.directive.isnottagfile=La directive [{0}] ne peut être utilisée que dans un fichier tag
jsp.error.directive.istagfile=La directive [{0}] ne peut être utilisée dans un fichier tag
jsp.error.duplicate.name.jspattribute=L''attribut [{0}] indiqué dans l''action standard ou spécifique (custom) apparait aussi comme valeur de l''attribut de nom dans le jsp:attribute inclus
jsp.error.duplicateqname=Un attribut avec un nom qualifié [{0}] en double a été trouvé, ils doivent être uniques au sein d''un élément
jsp.error.dynamic.attributes.not.implemented=Le tag [{0}] indique qu''il accepte des attributs dynamics mais n''implémente pas l''interface requise
jsp.error.el.parse=[{0}] : [{1}]
jsp.error.el.template.deferred=#{...} n'est pas admis dans le texte d'un modèle (template)
jsp.error.el_interpreter_class.instantiation=Impossible de charger ou d''instancier la classe ELInterpreter [{0}]
jsp.error.fallback.invalidUse=jsp:fallback doit être un enfant direct de jsp:plugin
jsp.error.file.already.registered=Inclusion récursive du fichier [{0}]
jsp.error.file.cannot.read=Impossible de lire le fichier : [{0}]
jsp.error.file.not.found=Le fichier [{0}] n''a pas été trouvé
jsp.error.flush=Une exception s'est produite lors de l'envoi des données
jsp.error.fragmentwithtype=On ne peut indiquer à la fois les attributs ''fragment'' et ''type''.  Si ''fragment'' est présent, ''type'' est fixé comme ''{0}''
jsp.error.function.classnotfound=La classe [{0}] spécifiée dans la TLD pour la fonction [{1}] n''a pas été trouvée : [{2}]
jsp.error.include.exception=Impossible d''inclure (include) [{0}]
jsp.error.include.tag=Tag jsp:include incorrect
jsp.error.internal.filenotfound=Erreur interne : Fichier [{0}] introuvable
jsp.error.invalid.attribute=[{0}] : Attribut incorrect : [{1}]
jsp.error.invalid.bean=La valeur [{0}] de l''attribut de classe useBean est invalide
jsp.error.invalid.directive=Directive incorrecte
jsp.error.invalid.expression=[{0}] contient d''incorrecte(s) expression(s) : [{1}]
jsp.error.invalid.implicit=Le TLD implicite est invalide pour le fichier tag [{0}]
jsp.error.invalid.implicit.version=La version JSP déclarée dans le TLD implicite pour le fichier de tag à [{0}] est invalide
jsp.error.invalid.scope=La valeur de l''attribut "scope" est invalide : [{0}] (elle doit être "page", "request", "session" ou "application")
jsp.error.invalid.tagdir=Le répertoire du fichier Tag [{0}] ne commence pas par "/WEB-INF/tags"
jsp.error.invalid.version=Version JSP invalide pour le fichier tag [{0}]
jsp.error.ise_on_clear=Il est interdit d'utiliser clear() quand la taille de tampon== 0
jsp.error.java.line.number=Une erreur s''est produite à la ligne : [{0}] dans le fichier Java généré : [{1}]
jsp.error.javac=Exception javac
jsp.error.javac.env=Environnement :
jsp.error.jspbody.emptybody.only=Le tag [{0}] ne peut avoir que jsp:attribute dans son corps.
jsp.error.jspbody.invalidUse=Le jsp:body doit être un sous élément d'une action standard ou personnalisée
jsp.error.jspbody.required=Doit utiliser jsp:body pour indiqué le corps de tag body de [{0}] si jsp:attribute est utilisé.
jsp.error.jspc.missingTarget=Une cible manque, il faut spécifier -webapp, -uriroot, ou une ou plusieurs pages JSP
jsp.error.jspc.no_uriroot=uriroot n'est pas spécifié et ne peut être trouvé avec le(s) fichier(s) JSP spécifié(s)
jsp.error.jspc.uriroot_not_dir=L'option -uriroot doit indiquer un répertoire déjà existant
jsp.error.jspelement.missing.name=L'attribut obligatoire 'name' est absent de jsp:element
jsp.error.jspoutput.conflict=&lt;jsp:output&gt; : il est illégal d''avoir plusieurs occurrences de [{0}] avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.jspoutput.doctypenamesystem=&lt;jsp:output&gt; : les attributs "doctype-root-element" et "doctype-system" doivent apparaître conjointement
jsp.error.jspoutput.doctypepublicsystem=L'attribut &lt;jsp:output&gt; : 'doctype-system' doit être présent lorsque l'attribut 'doctype-public' l'est
jsp.error.jspoutput.invalidUse=&lt;jsp:output&gt; ne doit pas être utilisé en syntaxe standard
jsp.error.jspoutput.nonemptybody=&lt;jsp:output&gt; ne doit pas avoir de corps
jsp.error.jsproot.version.invalid=Le numéro de version [{0}] est invalide, il devrait être "1.2", "2.0", "2.1", "2.2" ou "2.3"
jsp.error.jsptext.badcontent=Quand '&lt;' apparaît dans le corps d'un &lt;jsp:text&gt;, il doit être encapsulé dans un CDATA\n
jsp.error.lastModified=Impossible de déterminer la date de dernière modification pour le fichier [{0}]
jsp.error.library.invalid=La page JSP page est incorrecte d''après la bibliothèque [{0}] : [{1}]
jsp.error.literal_with_void=Une valeur littérale a été spécifié pour l''attribut [{0}] qui est défini comme étant une méthode différée ne retournant pas d''objet, JSP.2.3.4 ne permet pas de valeur littérale dans ce cas
jsp.error.loadclass.taghandler=Impossible de charger la classe [{0}]
jsp.error.location=ligne : [{0}], colonne : [{1}]
jsp.error.mandatory.attribute=[{0}] : L''attribut obligatoire [{1}] est manquant
jsp.error.missing.tagInfo=L''objet TagInfo de [{0}] est absent de la TLD
jsp.error.missing_attribute=D''après le TLD l''attribut [{0}] est obligatoire pour le tag [{1}]
jsp.error.missing_var_or_varReader=L'attribut "var" ou "varReader" est manquant
jsp.error.namedAttribute.invalidUse=Le jsp:attribute doit être un sous élément d'une action standard ou personnalisée
jsp.error.needAlternateJavaEncoding=L''encodage java par défaut [{0}] est incorrect sur votre environnement java. Une alternative peut être indiquée via le paramêtre ''javaEncoding'' de la JspServlet.
jsp.error.nested.jspattribute=Une action standard jsp:attribute ne peut pas être nichée dans une autre
jsp.error.nested.jspbody=Une action standard jsp:body ne peut être incluse dans une autre action standard jsp:body ou jsp:attribute
jsp.error.nested_jsproot=&lt;jsp:root&gt; imbriqué
jsp.error.no.more.content=Fin de contenu alors que l'évalution n'était pas terminée : erreur de tags imbriqués ?
jsp.error.no.scratch.dir=Le moteur de JSP engine n'est pas configuré avec un répertoire de travail.\n\
\ Merci d'ajouter "jsp.initparams=scratchdir=<dir-name>" \n\
\ dans le fichier "servlets.properties" de ce contexte.
jsp.error.no.scriptlets=Les éléments de scripting ( <%!, <jsp:declaration, <%=, <jsp:expression, <%, <jsp:scriptlet ) ne sont pas autorisés ici.
jsp.error.noFunction=La fonction [{0}] ne peut être trouvée pour le préfixe spécifié
jsp.error.noFunctionMethod=La méthode [{0}] pour la fonction [{1}] n''a pas été trouvée dans la classe [{2}]
jsp.error.non_null_tei_and_var_subelems=Le tag [{0}] possède une ou plusieurs variables subelements et une classe TagExtraInfo qui retourne une ou plusieurs VariableInfo
jsp.error.not.in.template=[{0}] n''est pas autorisé dans le corps de texte de template.
jsp.error.outputfolder=Pas de répertoire de sortie
jsp.error.overflow=Erreur : Dépassement de capacité du tampon JSP
jsp.error.page.conflict.autoflush=Directive de page : il est illégal d''avoir plusieurs occurrences de "autoFlush" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.page.conflict.buffer=Directive de page : il est illégal d''avoir plusieurs occurrences de "buffer" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.page.conflict.contenttype=Directive de page : il est illégal d''avoir plusieurs occurrences de ''contentType'' avec des valeurs différentes (ancienne : [{0}], nouvelle : [{1}])
jsp.error.page.conflict.deferredsyntaxallowedasliteral=Directive de page : il est illégal d''avoir plusieurs occurrences de "deferedSyntaxAllowedAsLiteral" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.page.conflict.errorpage=Directive de page : il est illégal d''avoir plusieurs occurrences de "errorPage" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.page.conflict.extends=Directive de page : il est illégal d''avoir plusieurs occurrences de "extends" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.page.conflict.info=Directive de page : il est illégal d''avoir plusieurs occurrences de "info" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.page.conflict.iselignored=Directive de page : il est illégal d''avoir plusieurs occurrences de "isELIgnored" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.page.conflict.iserrorpage=Directive de page : il est illégal d''avoir plusieurs occurrences de "isErrorPage" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.page.conflict.isthreadsafe=Directive de page : il est illégal d''avoir plusieurs occurrences de "isThreadSafe" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.page.conflict.language=Directive de page : il est illégal d''avoir plusieurs occurrences de "language" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.page.conflict.session=Directive de page : il est illégal d''avoir plusieurs occurrences de "session" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.page.conflict.trimdirectivewhitespaces=Directive de page : il est illégal d''avoir plusieurs occurrences de "trimDirectiveWhitespaces" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.page.invalid.buffer=Directive de Page : valeur incorrecte pour "buffer"
jsp.error.page.invalid.deferredsyntaxallowedasliteral=Directive de page : valeur invalide pour deferredSyntaxAllowedAsLiteral
jsp.error.page.invalid.import=Directive de page : valeur invalide pour import
jsp.error.page.invalid.iselignored=Directive de page : valeur invalide pour isELIgnored
jsp.error.page.invalid.iserrorpage=Directive de Page : valeur incorrecte pour isErrorPage
jsp.error.page.invalid.isthreadsafe=Directive de Page : valeur incorrecte pour isThreadSafe
jsp.error.page.invalid.session=Directive de Page : valeur incorrecte pour session
jsp.error.page.invalid.trimdirectivewhitespaces=Directive de page : valeur invalide pour trimDirectiveWhitespaces
jsp.error.page.language.nonjava=Directive de page : l'attribut langage est invalide
jsp.error.page.multi.pageencoding=La directive de page ne doit pas avoir plusieurs occurrences de pageencoding
jsp.error.page.noSession=Impossible d'accéder à la portée session de la page car elle n'a pas de session asoociée
jsp.error.param.invalidUse=L'action jsp:param ne peut pas être utilisée en dehors d'éléments jsp:include, jsp:forward ou jsp:params
jsp.error.paramexpected=Le tag "param" est attendu avec les attributs "name" et "value" après le tag "params".
jsp.error.params.emptyBody=jsp:params doit inclure au moins un jsp:param
jsp.error.params.invalidUse=jsp:params doit être directement inclus dans jsp:plugin
jsp.error.parse.error.in.TLD=Erreur d''évaluation (parse) dans le descripteur de la bibliothèque de tag (TLD) : [{0}]
jsp.error.parse.xml=Erreur d''évaluation XML sur le fichier [{0}]
jsp.error.parse.xml.line=Erreur d''évaluation XML sur le fichier  [{0}] : (ligne [{1}], col [{2}])
jsp.error.parse.xml.scripting.invalid.body=Le corps de l''élément [{0}] ne doit contenir aucun éléments XML
jsp.error.plugin.badtype=Valeur invalide pour l'attribut 'type' dans jsp:plugin : la valeur doit être 'bean' ou 'applet'
jsp.error.plugin.nocode=code non déclaré dans jsp:plugin
jsp.error.plugin.notype=type non déclaré dans jsp:plugin
jsp.error.prefix.refined=Tentative de redéfinition du préfixe [{0}] en [{1}] alors qu''il a déjà été défini comme [{2}] dans la portée actuelle
jsp.error.prefix.use_before_dcl=Le préfixe [{0}] spécifié dans cette directive de tag a déjà été utilisé par une action dans le fichier [{1}] ligne [{2}]
jsp.error.prolog_config_encoding_mismatch=Le page-encoding spécifié dans le prologue XML [{0}] est différent de celui spécifié dans le jsp-property-group [{1}]
jsp.error.prolog_pagedir_encoding_mismatch=L''encodage spécifié dans le prologue XML [{0}] est différent de celui spécifié dans la directive de page [{1}]
jsp.error.quotes.unterminated=Guillemets non terminés
jsp.error.scripting.variable.missing_name=Incapable de déterminer le nom de variable scripting d''après l''attribut [{0}]
jsp.error.security=L'initialisation de la sécurité a échouée pour le contexte
jsp.error.servlet.destroy.failed=Erreur pendant le Servlet.destroy() de la page JSP
jsp.error.servlet.invalid.method=Les JSPs ne permettent que GET, POST ou HEAD. Jasper permet aussi OPTIONS
jsp.error.setLastModified=Impossible de fixer la date de dernière modification pour le fichier [{0}]
jsp.error.signature.classnotfound=La classe [{0}] spećifié dans la signature de la méthode dans la TLD pour la fonction [{1}] n''a pas pu être trouvée [{2}]
jsp.error.simpletag.badbodycontent=La TLD de la classe [{0}] spécifie un body-content (JSP) invalide pour un tag simple (SimpleTag).
jsp.error.single.line.number=Une erreur s''est produite à la ligne : [{0}] dans le fichier jsp : [{1}]
jsp.error.stream.close.failed=Erreur à la fermeture du flux
jsp.error.stream.closed=Flux fermé
jsp.error.tag.conflict.attr=Directive de tag : il est illégal d''avoir plusieurs occurrences de l''attribut [{0}] avec des valeurs différentes (ancienne : [{1}], nouvelle [{2}])
jsp.error.tag.conflict.deferredsyntaxallowedasliteral=Directive de tag : il est illégal d''avoir plusieurs occurrences de "deferredSyntaxAllowedAsLiteral" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.tag.conflict.iselignored=Directive de tag : il est illégal d''avoir plusieurs occurrences de "isELIgnored" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.tag.conflict.language=Directive de tag : il est illégal d''avoir plusieurs occurrences de "language" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.tag.conflict.trimdirectivewhitespaces=Directive de tag : il est illégal d''avoir plusieurs occurrences de "trimDirectiveWhitespaces" avec des valeurs différentes (ancienne : [{0}], nouvelle [{1}])
jsp.error.tag.invalid.deferredsyntaxallowedasliteral=Directive Tag : valeur invalide pour deferredSyntaxAllowedAsLiteral
jsp.error.tag.invalid.iselignored=Directive Tag : isELIgnored a une valeur invalide
jsp.error.tag.invalid.trimdirectivewhitespaces=Directive Tag : trimDirectiveWhitespaces a une valeur invalide
jsp.error.tag.language.nonjava=Directive de tag : attribut language invalide
jsp.error.tag.multi.pageencoding=La directive de tag ne doit pas avoir plusieurs occurences de pageencoding
jsp.error.tagdirective.badbodycontent=Contenu de corps (body-content) [{0}] invalide dans la directive tag
jsp.error.tagfile.badSuffix=Le suffixe ".tag"  est manquant dans le chemin du fichier tag [{0}]
jsp.error.tagfile.illegalPath=Le chemin du fichier de tag [{0}] est invalide, il doit commencer par "/WEB-INF/tags" ou "/META-INF/tags"
jsp.error.tagfile.missingPath=Le chemin n'est pas spécifié pour le fichier de tag
jsp.error.tagfile.nameFrom.badAttribute=La directive d''attribut (déclarée à la ligne [{1}] et dont le l''attribut nom est [{0}], la valeur de cet attribut name-from-attribute) doit être de type java.lang.String, est requise ("required") et ne doit pas être une expression évaluée à l''exécution ("rtexprvalue").
jsp.error.tagfile.nameFrom.noAttribute=La valeur [{0}] indiquée pour l''attribut "name-from-attribute" ne se réfère à aucun attribut de ce même nom.
jsp.error.tagfile.nameNotUnique=La valeur de [{0}] et la valeur de [{1}] à la ligne [{2}] sont les mêmes
jsp.error.taglibDirective.absUriCannotBeResolved=L''URI absolu : [{0}] ne peut être résolu ni dans le fichier web.xml ni dans les fichiers jar déployés avec cette application
jsp.error.taglibDirective.both_uri_and_tagdir=Les attributs 'uri' et 'tagdir' sont tous deux spécifiés
jsp.error.taglibDirective.missing.location=Ni l''uri' ni l'attribut 'tagdir' n''ont été indiqués dans la directive taglib
jsp.error.taglibDirective.uriInvalid=L''URI fourni pour la "tag library" [{0}] n''est pas un URI valide
jsp.error.tei.invalid.attributes=Message d''erreurs de validation provenant du TagExtraInfo pour [{0}]
jsp.error.teiclass.instantiation=Impossible de charger ou d''instancier la classe TagExtraInfo : [{0}]
jsp.error.text.has_subelement=&lt;jsp:text&gt; ne doit pas avoir de sous éléments
jsp.error.tld.fn.duplicate.name=Nom de fonction [{0}] dupliqué dans la bibliothèque de tags [{1}]
jsp.error.tld.fn.invalid.signature=Synthaxe invalide pour la signature de fonction dans la TLD.  Bibliothèque de Tag : [{0}], Fonction : [{1}]
jsp.error.tld.invalid_tld_file=Fichier TLD invalide : [{0}], voir la spécification JSP.7.3.1 pour plus de détails
jsp.error.tld.mandatory.element.missing=Élément [{0}] obligatoire manquant ou vide dans la définition de librairie de tags ("TLD") [{1}]
jsp.error.tld.missing=Impossible de trouver la bibliothèque de tags [{0}] pour l''URI [{1}]
jsp.error.tld.missing_jar=Ressource JAR manquante [{0}] containant un TLD
jsp.error.tld.unable_to_get_jar=Impossible d''obtenir la ressource [{0}] du JAR contenant le TLD : [{1}]
jsp.error.tlv.invalid.page=Message d''erreurs de validation provenant du TagLibraryValidator pour [{0}] en [{1}]
jsp.error.tlvclass.instantiation=Impossible de charger ou d''instancier la classe TagLibraryValidator : [{0}]
jsp.error.unable.compile=Impossible de compiler la classe pour la JSP
jsp.error.unable.deleteClassFile=Impossible de supprimer le fichier de classe [{0}]
jsp.error.unable.load=Impossible de charger la classe pour la JSP
jsp.error.unable.renameClassFile=Impossible de renommer le fichier de classe de [{0}] vers [{1}]
jsp.error.unable.to_find_method=Impossible de trouver une méthode de mise à jour pour l''attribut : [{0}]
jsp.error.unavailable=La JSP a été marquée comme non disponible
jsp.error.unbalanced.endtag=Le tag final "&lt;/{0}" n''est pas balancé
jsp.error.undeclared_namespace=Un tag personnalisé a été trouvé avec un espace de noms non déclaré [{0}]
jsp.error.unknown_attribute_type=Type d''attribut inconnu [{1}] pour l''attribut [{0}].
jsp.error.unsupported.encoding=Encodage non supporté : [{0}]
jsp.error.unterminated=Tag [{0}] non terminé
jsp.error.usebean.duplicate=useBean : Nom de bean dupliqué : [{0}]
jsp.error.usebean.noSession=Il est illégal pour useBean d'utiliser une portée de session (session scope) quand la page JSP indique (via la directive de page) qu'elle ne participe pas aux sessions
jsp.error.var_and_varReader=A la fois 'var' et 'varReader' sont indiqués
jsp.error.variable.alias=Aucun ou les deux parmi les attributs name-from-attribute et alias doivent 6etre spécifiés sur une directive de variable
jsp.error.variable.both.name=Impossible de spécifier à la fois les attributs name-given et name-from-attribute dans une directive de variable
jsp.error.variable.either.name=Un attribut "name-given" ou "name-from-attribute" doit être spécifié dans la directive "variable"
jsp.error.xml.badStandardAction=Action standard invalide : [{0}]
jsp.error.xml.bad_tag=Aucun tag [{0}] n''est défini dans la bibliothèque de tags associée à l''URI [{1}]
jsp.exception=Une exception s''est produite lors du traitement de [{0}] à la ligne [{1}]
jsp.info.ignoreSetting=Le paramètre [{0}] avec valeur [{1}] a été ignoré parce que le gestionnaire de sécurité est activé
jsp.message.dont.modify.servlets=IMPORTANT : Ne pas modifier les servlets générées
jsp.message.jsp_added=Ajout du JSP au chemin [{0}] à la queue du contexte [{1}]
jsp.message.jsp_queue_created=Création d''une queue de jsp avec une longueur de [{0}] pour le contexte [{1}]
jsp.message.jsp_queue_update=Mise à jour de la JSP pour le chemin [{0}] dans la file d''attente du contexte [{1}]
jsp.message.jsp_removed_excess=Enlevé un JSP excédentaire pour le chemin [{0}] de la file du contexte [{1}]
jsp.message.jsp_removed_idle=Le JSP inactif avec le chemin [{0}] dans le contexte [{1}] est enlevé après [{2}] secondes
jsp.message.jsp_unload_check=Vérification des JSPs lors du déchargement du contexte [{0}], nombre de jsp : [{1}] taille de la queue : [{2}]
jsp.message.parent_class_loader_is=Le chargeur de classe parent (class loader) est : [{0}]
jsp.message.scratch.dir.is=Le répertoire de travail (scratch dir) pour le moteur de JSP est : [{0}]
jsp.tldCache.noTldInDir=Aucun fichier TLD trouvé dans le répertoire [{0}]
jsp.tldCache.noTldInJar=Aucun TLD n''a été trouvé dans [{0}], ce JAR pourrait être ajouté dans la propriété tomcat.util.scan.StandardJarScanFilter.jarsToSkip de CATALINA_BASE/conf/catalina.properties
jsp.tldCache.noTldInResourcePath=Pas de TLD trouvé dans le chemin de ressources [{0}]
jsp.tldCache.noTldSummary=Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
jsp.tldCache.tldInDir=Des fichiers TLD ont été trouvés dans le répertoire [{0}]
jsp.tldCache.tldInJar=Des TLDs ont été trouvée dans le JAR [{0}]
jsp.tldCache.tldInResourcePath=Des fichiers TLD ont été trouvé dans le chemin de ressources [{0}]
jsp.warning.bad.urlpattern.propertygroup=Mauvaise valeur [{0}] dans le sous-élément (subelement) url-pattern du fichier web.xml
jsp.warning.checkInterval=WARNING : Valeur incorrecte pour le initParam checkInterval. Utilisation de la valeur par défaut "300" secondes
jsp.warning.classDebugInfo=WARNING : Valeur incorrecte pour le initParam classdebuginfo. Utilisation de la valeur par défaut "false"
jsp.warning.classpathUrl=Une URL invalide a été trouvée dans le chemin des classes, elle sera ignorée
jsp.warning.compiler.classfile.delete.fail=Impossible d''effacer le fichier classe généré [{0}]
jsp.warning.compiler.classfile.delete.fail.unknown=Impossible d'effacer le ou les fichier(s) de classe généré(s)
jsp.warning.compiler.javafile.delete.fail=Impossible d''effacer le fichier Java généré [{0}]
jsp.warning.development=WARNING : Valeur incorrecte pour le initParam development. Utilisation de la valeur par défaut "true"
jsp.warning.displaySourceFragment=Avertissement : valeur invalide pour le initParam "displaySourceFragment". Utilisant la valeur par défaut "true"
jsp.warning.dumpSmap=WARNING : Valeur invalide pour le paramètre d'initialisation dumpStack, la valeur par défaut "false" sera utilisée
jsp.warning.enablePooling=WARNING : Valeur incorrecte pour le initParam enablePooling. Utilisation de la valeur par défaut "true"
jsp.warning.fork=WARNING : Valeur invalide pour le paramètre d'initialisation fork, la valeur par défaut "true" sera utilisée
jsp.warning.genchararray=WARNING : Valeur invalide pour le paramètre d'initialisation genStringAsCharArray, la valeur par défaut "false" sera utilisée
jsp.warning.jspIdleTimeout=WARNING : Valeur invalide pour le paramètre d'initialisation jspIdleTimeout, la valeur par défaut "-1" sera utilisée
jsp.warning.keepgen=WARNING : Valeur incorrecte pour le initParam keepgenerated. Utilisation de la valeur par défaut "false"
jsp.warning.mappedFile=WARNING : Valeur incorrecte pour le initParam mappedFile. Utilisation de la valeur par défaut "false"
jsp.warning.maxLoadedJsps=WARNING : Valeur invalide pour le paramètre d'initialisation maxLoadedJsps, la valeur par défaut "-1" sera utilisée
jsp.warning.modificationTestInterval=WARNING : Valeur invalide pour le paramètre d'initialisation modificationTestInterval, la valeur par défaut "4" secondes sera utilisée
jsp.warning.noJarScanner=WARNING : Aucun org.apache.tomcat.JarScanner fixé sur ServletContext, utilisation de l'implémentation de JarScanner par défaut
jsp.warning.quoteAttributeEL=WARNING : Valeur invalide pour le paramètre d'initialisation quoteAttributeEL, la valeur par défaut "false" sera utilisée
jsp.warning.recompileOnFail=WARNING : Valeur invalide pour le paramètre d'initialisation recompileOnFail, la valeur par défaut "false" sera utilisée
jsp.warning.strictQuoteEscaping=WARNING : Valeur invalide pour le paramètre d'initialisation strictQuoteEscaping, la valeur par défaut "true" sera utilisée
jsp.warning.suppressSmap=WARNING : valeur invalide d' l'initParam suppressSmap. La valeur par défaut "false" sera utilisée
jsp.warning.tagPreDestroy=Erreur lors du traitement de preDestroy pour l''instance de tag [{0}]
jsp.warning.tagRelease=Erreur lors du traitement de release pour l''instance de tag [{0}]
jsp.warning.unknown.sourceVM=La VM source [{0}] inconnue a été ignorée
jsp.warning.unknown.targetVM=La VM cible [{0}] inconnue a été ignorée
jsp.warning.unsupported.sourceVM=La VM source [{0}] demandée n''est pas supportée, [{1}] sera utilisé
jsp.warning.unsupported.targetVM=La VM cible [{0}] demandée n''est pas supportée, [{1}] sera utilisé
jsp.warning.xpoweredBy=WARNING : Valeur invalide pour le paramètre d'initialisation xpoweredBy, la valeur par défaut "false" sera utilisée

jspc.delete.fail=Impossible d''effacer le fichier [{0}]
jspc.error.fileDoesNotExist=Le fichier [{0}] donné en argument n''existe pas
jspc.error.generalException=ERREUR-le fichier [{0}] a généré l''exception générale suivante :
jspc.error.invalidFragment=Arrêt de la précompilation à cause des erreurs dans les fragments web
jspc.error.invalidWebXml=Arrêt de la précompilation à cause d'erreurs dans web.xml
jspc.generation.result=La génération s''est terminée avec [{0}] erreurs en [{1}] millisecondes
jspc.implicit.uriRoot=uriRoot réglé implicitement à [{0}]
jspc.usage=Utilisation : jspc <options> [--] <fichiers jsp>\n\
où les fichiers jsp sont soit\n\
\    -webapp <dir>         Un répertoire contenant une application web, toutes les pages jsp\n\
\                          seront récursivement évaluées\n\
ou n'importe quel nombre de :\n\
\    <file>                Un fichier à évaluer comme page jsp\n\
et où les options sont :\n\
\    -help                 Afficher ce message d'aide\n\
\    -v                    Mode de débogage\n\
\    -d <dir>              Répertoire de sortie\n\
\    -l                    Affiche le nom de la page JSP lors d'un échec\n\
\    -s                    Affiche le nom la page JSPlors d'un succès\n\
\    -p <name>             Nom du paquet cible (par défaut org.apache.jsp)\n\
\    -c <name>             Nom de classe cible\n\
\                          (s'applique seulement à la première page JSP)\n\
\    -mapped               Génère des appels à write() séparés pour chaque ligne HTML dans la page JSP\n\
\    -die[#]               Génère un code d'erreur de retour (#) en cas d'erreur fatale (par défaut 1)\n\
\    -uribase <dir>        L'uri du répertoire auquel la compilation doit être relative\n\
\                          (par défaut "/")\n\
\    -uriroot <dir>        Identique à -webapp\n\
\    -compile              Compilation des Servlets générés\n\
\    -failFast             Arrêt à la première erreur de compilation\n\
\    -webinc <file>        Création d'association de Servlet partiel\n\
\    -webfrg <file>        Création d'un fichier web-fragment.xml complet\n\
\    -webxml <file>        Création d'un fichier web.xml complet\n\
\    -webxmlencoding <enc> Fixe l'encodage des caractères à utiliser pour lire et écrire le fichier\n\
\                          web.xml (par défaut UTF-8)\n\
\    -addwebxmlmappings    Fusionne le fragment de web.xml généré dans le web.xml\n\
\                          de l'application web dont les pages JSP sont générées\n\
\    -ieplugin <clsid>     Le classid du Plugin Java pour Internet Explorer\n\
\    -classpath <path>     Remplace la propriété système java.class.path\n\
\    -xpoweredBy           Ajoute l'en-tête de réponse X-Powered-By\n\
\    -trimSpaces           Enlève le texte de base qui est constitué entièrement d'espaces blancs\n\
\    -javaEncoding <enc>   Fixe le code de caractères pour les classes Java (par défaut UTF-8)\n\
\    -source <version>     Fixe l'argument -source pour le compilateur (par défaut 1.7)\n\
\    -target <version>     Fixe l'argument -target pour le compilateur (par défaut 1.7)\n\
\    -threadCount <count>  Nombre de threads à utiliser pour la compilation\n\
\                          ("2.0C" veut dire deux threads par cœur)\n
jspc.webfrg.footer=\n\
</web-fragment>\n\
\n
jspc.webfrg.header=<?xml version="1.0" encoding="{0}"?>\n\
<web-fragment xmlns="http://xmlns.jcp.org/xml/ns/javaee"\n\
\              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"\n\
\              xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee\n\
\                      http://xmlns.jcp.org/xml/ns/javaee/web-fragment_3_1.xsd"\n\
\              version="3.1"\n\
\              metadata-complete="true">\n\
\  <name>org_apache_jasper.jspc</name>\n\
\  <distributable/>\n\
<!--\n\
Crée automatiquement par JspC de Apache Tomcat.\n\
-->\n\
\n
jspc.webinc.footer=<!--\n\
Fin du contenu généré automatiquement par JspC de Apache Tomcat.\n\
-->\n\
\n
jspc.webinc.header=\n\
<!--\n\
Créé automatiquement par JspC de Apache Tomcat.\n\
-->\n\
\n
jspc.webinc.insertEnd=<!-- Fin des mappings des Servlets JSPC -->
jspc.webinc.insertStart=<!-- Début des mappings des Servlets JSPC -->
jspc.webxml.footer=\n\
</web-app>\n\
\n
jspc.webxml.header=<?xml version="1.0" encoding="{0}"?>\n\
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"\n\
\         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"\n\
\         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee\n\
\                 http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd"\n\
\         version="3.1"\n\
\         metadata-complete="false">\n\
<!--\n\
Crée automatiquement par JspC de Apache Tomcat.\n\
-->\n\
\n

org.apache.jasper.compiler.ELParser.invalidQuotesForStringLiteral=Le littéral chaîne [{0}] est invalide, il doit être contenu entre guillemets simples ou doubles
org.apache.jasper.compiler.ELParser.invalidQuoting=L''expression [{0}] n''est pas valide, dans une chaîne entre guillemets seuls [], [''] et ["] peuvent être échappés avec []
org.apache.jasper.compiler.TldCache.servletContextNull=Le ServletContext fourni est égal à null
org.apache.jasper.servlet.JasperInitializer.onStartup=Initialisation de Jasper pour le contexte [{0}]
org.apache.jasper.servlet.TldScanner.webxmlAdd=Chargement de la TLD pour l''URI [{1}] à partir du chemin de ressource [{0}]
org.apache.jasper.servlet.TldScanner.webxmlFailPathDoesNotExist=Echec du traitement de la TLD du cheming [{0}] avec l''URI [{1}], le chemin spécifié n''existe pas
org.apache.jasper.servlet.TldScanner.webxmlSkip=Le chargement du TLD à l''URI [{1}] pour le chemin de ressource [{0}] ne sera pas effectué car il a déjà été défini dans <jsp-config>

xmlParser.skipBomFail=Echec pour passer le BOM lors du traitement du flux d'entrée XML
