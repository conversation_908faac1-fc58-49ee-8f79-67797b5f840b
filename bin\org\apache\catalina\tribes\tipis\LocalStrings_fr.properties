# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractReplicatedMap.broadcast.noReplies=La diffusion (broadcast) n'a pas recu de réponse, probablement un dépassement du temps imparti
abstractReplicatedMap.heartbeat.failed=Impossible d'envoyer le message AbstractReplicatedMap.ping
abstractReplicatedMap.init.completed=L''initialisation de l''AbstractReplicatedMap[{0}] s''est terminé en [{1}] ms
abstractReplicatedMap.init.start=Initialisation de l''AbstractReplicatedMap avec le nom de contexte [{0}]
abstractReplicatedMap.leftOver.ignored=Le message [{0}] a été ignoré.
abstractReplicatedMap.leftOver.pingMsg=Le délai d''attente a été dépassé pour le message PING, le membre [{0}] a pu être enlevé de la structure du registre de membres
abstractReplicatedMap.mapMember.unavailable=Le membre [{0}] n''est pas encore disponible
abstractReplicatedMap.mapMemberAdded.added=Le membre de la structure répliquée a été ajouté : [{0}]
abstractReplicatedMap.mapMemberAdded.nullMember=Notifié que le membre n''est pas disponible dans le registre de membres : [{0}]
abstractReplicatedMap.member.disappeared=Le membre [{0}] a disparu, les entrées correspondantes seront déplacées sur le nouveau nœud
abstractReplicatedMap.ping.stateTransferredMember=L''état du membre [{0}] est transféré mais il n''est pas encore disponible
abstractReplicatedMap.ping.timeout=Le membre [{0}] dans la carte répliquée [{1}] a fait un timeout lors du traitement d''un ping
abstractReplicatedMap.relocate.complete=La relocation des entrées de la structure répliquée a été finie en [{0}] ms
abstractReplicatedMap.transferState.noReplies=État de transfert, 0 réponses, probablement un timeout
abstractReplicatedMap.unable.deserialize.MapMessage=Impossible de désérialiser MapMessage
abstractReplicatedMap.unable.diffObject=Impossible d'obtenir les différences de l'objet, il sera entièrement répliqué
abstractReplicatedMap.unable.get=Impossible de répliquer les données de sortie pour une opération AbstractReplicatedMap.get
abstractReplicatedMap.unable.put=Impossible de répliquer les données de sortie pour une opération AbstractReplicatedMap.put
abstractReplicatedMap.unable.relocate=Impossible de déplacer [{0}] sur un nouveau nœud de sauvegarde
abstractReplicatedMap.unable.remove=Impossible de répliquer les données de sortie pour une opération AbstractReplicatedMap.remove
abstractReplicatedMap.unable.replicate=Impossible de répliquer les données
abstractReplicatedMap.unable.retrieve=Impossible de récupérer les objets distants pour les clés : [{0}]
abstractReplicatedMap.unable.transferState=Impossible de transférer l'état de la AbstractReplicatedMap
abstractReplicatedMap.unableApply.diff=Impossible d''appliquer le diff à la clé : [{0}]
abstractReplicatedMap.unableSelect.backup=Impossible de choisir un nœud de sauvegarde
abstractReplicatedMap.unableSend.startMessage=Impossible d'envoyer le message de démarrage de la structure répliquée
abstractReplicatedMap.unableStart=Impossible de démarrer la structure répliquée

lazyReplicatedMap.unableReplicate.backup=Impossible de répliquer la clé de sauvegarde : [{0}]
lazyReplicatedMap.unableReplicate.proxy=Impossible de copier la clé de proxy : [{0}] sur la sauvegarde (backup) : [{1}]. Raison : [{2}]

mapMessage.deserialize.error.key=Erreur de désérialisation de la clé du MapMessage
mapMessage.deserialize.error.value=Erreur de désérialisation de la valeur du MapMessage

replicatedMap.member.disappeared=Le membre [{0}] a disparu, les entrées correspondantes seront déplacées sur un nouveau nœud
replicatedMap.relocate.complete=La relocation des entrées de la structure répliquée a été accomplie en [{0}] ms
replicatedMap.unable.relocate=Impossible de déplacer [{0}] sur un nouveau noeud auxiliaire.
replicatedMap.unableReplicate.completely=Impossible de répliquer la clé de sauvegarde : [{0}], succès pour les nœuds : [{1}], échec pour les nœuds : [{2}]
