# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

chunkedInputFilter.eos=요청의 body를 읽는 동안 예기치 않은 스트림의 끝
chunkedInputFilter.eosTrailer=Trailer 헤더들을 읽는 중 예기치 않은 스트림의 끝
chunkedInputFilter.error=이전 오류로 인하여 데이터가 가용하지 않습니다.
chunkedInputFilter.invalidCrlf=유효하지 않은 행의 끝 시퀀스입니다. (CR 또는 LF가 아닌 다른 문자가 발견됨)
chunkedInputFilter.invalidCrlfCRCR=유효하지 않은 행의 끝 시퀀스 (CRCR)
chunkedInputFilter.invalidCrlfNoCR=유효하지 않은 라인 끝 시퀀스 (CR 바이트가 LF 바이트 전에 존재하지 않음)
chunkedInputFilter.invalidCrlfNoData=유효하지 않은 행의 끝 시퀀스 (더 이상 읽을 데이터가 없음)
chunkedInputFilter.invalidHeader=유효하지 않은 chunk 헤더
chunkedInputFilter.maxExtension=maxExtensionSize 값을 초과했습니다.
chunkedInputFilter.maxTrailer=maxTrailerSize 값을 초과했습니다.

inputFilter.maxSwallow=maxSwallowSize 값을 초과했습니다.
