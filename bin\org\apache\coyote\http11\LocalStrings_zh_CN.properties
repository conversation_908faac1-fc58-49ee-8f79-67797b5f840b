# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractHttp11Protocol.alpnConfigured=[{0}]连接器已配置为支持通过ALPN与[{1}]协商。
abstractHttp11Protocol.alpnWithNoAlpn=[{1}]的升级处理程序[{0}]仅支持通过ALPN升级，但已为不支持ALPN的[{2}]连接器配置
abstractHttp11Protocol.httpUpgradeConfigured=[{0}]连接器已配置为支持HTTP升级到[{1}]
abstractHttp11Protocol.upgradeJmxRegistrationFail=JMX升级协议注册失败

http11processor.fallToDebug=\n\
\ 注意：HTTP请求解析错误的进一步发生将记录在DEBUG级别。
http11processor.header.parse=解析 HTTP 请求 header 错误
http11processor.request.finish=完成请求时出错
http11processor.request.inconsistentHosts=请求行中指定的主机与主机头不一致。
http11processor.request.invalidScheme=HTTP请求包含具有无效方案的绝对URL
http11processor.request.invalidTransferEncoding=HTTP请求包含了一个无效的Transfer-Encoding头
http11processor.request.invalidUri=HTTP请求包含一个无效的URI
http11processor.request.invalidUserInfo=HTTP 请求包含带有无效 userinfo 的绝对 URI
http11processor.request.multipleContentLength=请求包含了多个content-length请求头参数
http11processor.request.multipleHosts=请求包含多个主机头
http11processor.request.noHostHeader=http/1.1请求没有提供主机头
http11processor.request.nonNumericContentLength=请求包含一个具有非数字值的内容长度头
http11processor.request.prepare=准备请求时出错
http11processor.request.process=错误的处理请求
http11processor.response.finish=错误完成相应
http11processor.sendfile.error=使用sendfile发送数据时出错。可能是由于起点/终点的请求属性无效引起的
http11processor.socket.info=获取socket信息异常

iib.available.readFail=尝试确定数据是否可用时，非阻塞读取失败
iib.eof.error=套接字读取到意外的EOF
iib.failedread.apr=读取失败，APR/本机错误代码为[{0}]
iib.filter.npe=你不能添加空过滤器(null)
iib.invalidHttpProtocol=在HTTP协议中发现无效字符
iib.invalidPhase=无效的请求行解析阶段[{0}]
iib.invalidRequestTarget=在请求目标中找到无效字符。有效字符在RFC 7230和RFC 3986中定义
iib.invalidheader=HTTP header行 [{0}] 不符合RFC 7230并且已被忽略。
iib.invalidmethod=在方法名称中发现无效的字符串, HTTP 方法名必须是有效的符号.
iib.parseheaders.ise.error=意外状态：已解析标头。 缓冲池不回收？
iib.readtimeout=从套接字读取数据超时
iib.requestheadertoolarge.error=请求头太大

iob.failedwrite=写入.失败
iob.failedwrite.ack=无法发送HTTP 100继续响应
iob.responseheadertoolarge.error=尝试将更多数据写入响应标头，而不是缓冲区中有可用空间。 增加连接器上的maxHttpHeaderSize或将更少的数据写入响应头。
