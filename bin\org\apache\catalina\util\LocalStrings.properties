# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

SecurityUtil.doAsPrivilege=An exception occurs when running the PrivilegedExceptionAction block.

customObjectInputStream.logRequired=A valid logger is required for class name filtering with logging
customObjectInputStream.nomatch=The class [{0}] did not match the regular expression [{1}] for classes allowed to be deserialized

extensionValidator.extension-not-found-error=ExtensionValidator[{0}][{1}]: Required extension [{2}] not found.
extensionValidator.extension-validation-error=ExtensionValidator[{0}]: Failed to find [{1}] required extension(s).
extensionValidator.failload=Failure loading extension [{0}]
extensionValidator.web-application-manifest=Web Application Manifest

hexUtil.bad=Bad hexadecimal digit
hexUtil.odd=Odd number of hexadecimal digits

introspection.classLoadFailed=Failed to load class [{0}]

lifecycleBase.alreadyDestroyed=The destroy() method was called on component [{0}] after destroy() had already been called. The second call will be ignored.
lifecycleBase.alreadyStarted=The start() method was called on component [{0}] after start() had already been called. The second call will be ignored.
lifecycleBase.alreadyStopped=The stop() method was called on component [{0}] after stop() had already been called. The second call will be ignored.
lifecycleBase.destroyFail=Failed to destroy component [{0}]
lifecycleBase.destroyStopFail=Called stop() on failed component [{0}] to trigger clean-up but it failed too
lifecycleBase.initFail=Failed to initialize component [{0}]
lifecycleBase.invalidTransition=An invalid Lifecycle transition was attempted ([{0}]) for component [{1}] in state [{2}]
lifecycleBase.setState=Setting state for [{0}] to [{1}]
lifecycleBase.startFail=Failed to start component [{0}]
lifecycleBase.stopFail=Failed to stop component [{0}]

lifecycleMBeanBase.registerFail=Failed to register object [{0}] with name [{1}] during component initialisation
lifecycleMBeanBase.unregisterFail=Failed to unregister MBean with name [{0}] during component destruction
lifecycleMBeanBase.unregisterNoServer=No MBean server was available to unregister the MBean [{0}]

netmask.cidrNegative=The CIDR [{0}] is negative
netmask.cidrNotNumeric=The CIDR [{0}] is not numeric
netmask.cidrTooBig=The CIDR [{0}] is greater than the address length [{1}]
netmask.invalidAddress=The address [{0}] is not valid
netmask.invalidPort=The port part in the pattern [{0}] is not valid

parameterMap.locked=No modifications are allowed to a locked ParameterMap

resourceSet.locked=No modifications are allowed to a locked ResourceSet

sessionIdGeneratorBase.createRandom=Creation of SecureRandom instance for session ID generation using [{0}] took [{1}] milliseconds.
sessionIdGeneratorBase.random=Exception initializing random number generator of class [{0}]. Falling back to java.secure.SecureRandom
sessionIdGeneratorBase.randomAlgorithm=Exception initializing random number generator using algorithm [{0}]
sessionIdGeneratorBase.randomProvider=Exception initializing random number generator using provider [{0}]
