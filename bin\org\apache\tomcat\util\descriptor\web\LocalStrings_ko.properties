# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

filterDef.invalidFilterName=유효하지 않은 <filter-name>. [{0}]이(가) 필터 정의에 포함되어 있습니다.

securityConstraint.uncoveredHttpMethod=URL 패턴 [{0}]와(과) 함께 설정되는 security constraint들에 대해, HTTP 메소드들 [{1}]만이 커버됩니다. 다른 모든 메소드들은 커버되지 않습니다.
securityConstraint.uncoveredHttpMethodFix=[{1}] 중의 하나가 아닌, 커버될 수 없는 HTTP 메소드들을 사용한 접근을 거부하기 위하여, URL 패턴이 [{0}]인 security constraint들을 추가합니다.
securityConstraint.uncoveredHttpOmittedMethod=URL 패턴 [{0}]와(과) 함께 설정된 security constraint들에 대하여, HTTP 메소드들 [{1}]은(는) 커버되지 않습니다.
securityConstraint.uncoveredHttpOmittedMethodFix=커버될 수 없는 HTTP 메소드들 [{1}]에 대한 접근을 거부하기 위하여, URL 패턴 [{0}]와(과) 함께 security constraint를 추가합니다.

servletDef.invalidServletName=서블릿 정의에서 유효하지 않은 <servlet-name> [{0}]

webRuleSet.absoluteOrdering=<absolute-ordering> 엘리먼트는 web-fragment.xml 내에서 유효하지 않으므로, 무시될 것입니다.
webRuleSet.absoluteOrderingCount=<absolute-ordering> 엘리먼트는 1회만 나타나도록 제한됩니다.
webRuleSet.nameCount=<name> 엘리먼트는 단 한번만 나타나도록 제한됩니다.
webRuleSet.postconstruct.duplicate=클래스 [{0}]에 PostConstruct 메소드가 중복 정의되어 있습니다.
webRuleSet.predestroy.duplicate=클래스 [{0}]에 중복된 @PreDestroy 메소드 정의
webRuleSet.relativeOrdering=web.xml 내에서 유효하지 않아서, 해당 <ordering> 엘리먼트는 무시될 것입니다.
webRuleSet.relativeOrderingCount=<ordering> 엘리먼트는 오직 한번만 나타나도록 제한됩니다.

webXml.duplicateEnvEntry=중복된 env-entry 이름 [{0}]
webXml.duplicateFilter=중복된 필터 이름: [{0}]
webXml.duplicateFragment=이름이 [{0}]인, 둘 이상의 fragment들이 발견되었습니다. 이는 상대적 순서배열에서 불허됩니다. 상세 정보는 서블릿 스펙 8.2.2 2c 장을 참조하십시오. 절대적 순서배열을 사용하는 것을 고려해 보십시오.
webXml.duplicateMessageDestination=중복된 message-destination 이름 [{0}]
webXml.duplicateMessageDestinationRef=중복된 message-destination-ref 이름 [{0}]
webXml.duplicateResourceEnvRef=중복된 resource-env-ref 이름 [{0}]입니다.
webXml.duplicateResourceRef=중복된 resource-ref 이름: [{0}]
webXml.duplicateServletMapping=이름이 [{0}]과 [{1}]인 두 서블릿들 모두 url-pattern [{2}]에 매핑되어 있는데, 이는 허용되지 않습니다.
webXml.duplicateTaglibUri=중복된 태그 라이브러리 URI [{0}]
webXml.mergeConflictDisplayName=표시 이름이, 위치가 [{1}]이고 이름이 [{0}]인 fragment를 포함하여, 여러 개의 fragment들에 다른 값들로 정의되었습니다.
webXml.mergeConflictFilter=필터 [{0}]이(가), [{2}]에 위치한 [{1}](이)라는 이름을 가진 fragment를 포함하여, 여러 개의 fragment들에서 일관되지 않게 정의되었습니다.
webXml.mergeConflictLoginConfig=LoginConfig가, [{1}]에 위치하고 이름이 [{0}]인 fragment를 포함하여, 여러 개의 fragment들에 일관되지 않게 정의되었습니다.
webXml.mergeConflictOrder=Fragment의 상대적 순서들이 순환 참조를 포함하고 있습니다. 이 문제는 web.xml에서 절대 순서를 사용함으로써 해결될 수 있습니다.
webXml.mergeConflictResource=리소스 [{0}]은(는), 이름이 [{1}](이)고 [{2}]에 위치한 fragment를 포함하여, 여러 개의 fragment들에서 일관되지 않게 정의되었습니다.
webXml.mergeConflictServlet=서블릿 [{0}]이(가), [{2}]에 위치하고 [{1}](이)라는 이름을 가진 fragment를 포함하여, 여러 개의 fragment들에서 일관되지 않게 정의되었습니다.
webXml.mergeConflictSessionCookieComment=세션 쿠키 주석이, [{1}]에 위치하고 [{0}](이)라는 이름의 fragment를 포함하여, 여러 개의 fragment들에서 다른 값들로 비일관되게 정의되었습니다.
webXml.mergeConflictSessionCookieDomain=세션 쿠키 도메인이, [{1}]에 위치하고 이름이 [{0}]인 fragment를 포함하여, 여러 개의 fragment들에서 다른 값들로 일관되지 않게 정의되었습니다.
webXml.mergeConflictSessionCookieHttpOnly=세션 쿠키의 http-only 플래그가, [{1}]에 위치하고 이름이 [{0}]인 fragment를 포함하여, 여러 개의 fragment들에 다른 값들로 일관되지 않게 정의되었습니다.
webXml.mergeConflictSessionCookieMaxAge=세션 쿠키의 max-age가, [{1}]에 위치하고 이름이 [{0}]인 fragment를 포함하여, 여러 개의 fragment들에서 다른 값들로 일관되지 않게 정의되었습니다.
webXml.mergeConflictSessionCookieName=세션 쿠키 이름이, [{1}]에 위치하고 [{0}](이)라는 이름을 가진 fragment를 포함하여, 여러 개의 fragment들에서 다른 값들로 일관되지 않게 정의되었습니다.
webXml.mergeConflictSessionCookiePath=세션 쿠기 경로가, [{1}]에 위치하고 이름이 [{0}]인 fragment를 포함하여, 여러 개의 fragment들에서 다른 값들로 일관되지 않게 정의되었습니다.
webXml.mergeConflictSessionCookieSecure=세션 쿠키의 secure 플래그가, [{1}]에 위치하고 이름이 [{0}]인 fragment를 포함하여, 여러 개의 fragment들에서 다른 값들로 일관되지 않게 정의되었습니다.
webXml.mergeConflictSessionTimeout=세션 제한 시간 초과 값이, [{1}]에 위치하고 이름이 [{0}]인 fragment를 포함하여, 여러 개의 fragment들에 다른 값들로 일관되지 않게 정의되었습니다.
webXml.mergeConflictSessionTrackingMode=세션 트랙킹 모드 설정들이, [{1}]에 위치하고 [{0}](이)라는 이름의 fragment를 포함하여, 여러 fragment들에 일관되지 않게 정의되었습니다.
webXml.mergeConflictString=이름이 [{1}]인 [{0}]이(가), [{3}]에 위치하고 이름이 [{2}]인 fragment를 포함하여, 여러 개의 fragment들에서 일관되지 않게 정의되었습니다.
webXml.multipleOther=<ordering> 엘리먼트 안에 내재된, 여러 개의 <others> 엔트리들
webXml.reservedName=예약된 이름 [{0}]을(를) 사용한 web.xml이 탐지되었습니다. name 엘리먼트는 이 fragment를 위해 무시될 것입니다.
webXml.unrecognisedPublicId=public ID [{0}]이(가), web.xml 파일들을 위해 알려진 public ID들 중 어떤 것과도 부합되지 않아, 해당 버전을 알아낼 수 없습니다.
webXml.version.unknown=알 수 없는 버전 문자열 [{0}]. 기본 버전이 사용될 것입니다.
webXml.wrongFragmentName=web.xml의 absolute-ordering 태그에서 잘못된 fragment 이름, [{0}]이(가) 사용되었습니다!

webXmlParser.applicationParse=[{0}]에 위치한 애플리케이션 web.xml 내에서 파싱 오류 발생
webXmlParser.applicationPosition=행 [{0}], 열 [{1}]에서 발생했음
webXmlParser.applicationStart=[{0}]에 위치한 애플리케이션 web.xml을 파싱합니다.
