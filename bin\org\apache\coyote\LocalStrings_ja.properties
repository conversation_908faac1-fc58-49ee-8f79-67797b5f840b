# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractConnectionHandler.connectionsGet=ソケット[{1}]のプロセッサ[{0}]が見つかりました。
abstractConnectionHandler.error=リクエストの読み取り中にエラーが発生しました。無視します。
abstractConnectionHandler.ioexception.debug=IOExceptionは正常で無視されます。
abstractConnectionHandler.negotiatedProcessor.fail=ネゴシエートされたプロトコル[{0}]のプロセッサの作成に失敗しました。
abstractConnectionHandler.oome=リクエストの完全な処理に失敗しました
abstractConnectionHandler.process=ステータス[{1}]のソケット[{0}]を処理しています。
abstractConnectionHandler.processorCreate=新しいプロセッサ [{0}] を生成しました
abstractConnectionHandler.processorPop=キャッシュからプロセッサー [{0}] を取得しました。
abstractConnectionHandler.protocolexception.debug=ProtocolExceptionsは正常です。無視します。
abstractConnectionHandler.socketexception.debug=SocketExceptionsは正常です。無視します。
abstractConnectionHandler.upgradeCreate=SocketWrapper [{1}]のアップグレードプロセッサ[{0}]が作成されました。

abstractProcessor.fallToDebug=\n\
\ 注: 以降のリクエスト構文解析エラーの発生はDEBUGレベルでログに出力されます。
abstractProcessor.hostInvalid=ホスト名 [{0}] は不正です。
abstractProcessor.httpupgrade.notsupported=このプロトコルは HTTP アップグレードに対応していません。
abstractProcessor.pushrequest.notsupported=このプロトコルはサーバープッシュの要求に対応していません。
abstractProcessor.socket.ssl=SSL属性取得時の例外

abstractProtocol.mbeanDeregistrationFailed=MBeanサーバー[{1}]から[{0}]という名前のMBeanの登録を解除できませんでした。
abstractProtocol.processorRegisterError=リクエストプロセッサ登録中のエラー
abstractProtocol.processorUnregisterError=リクエストプロセッサ登録解除中のエラー
abstractProtocol.waitingProcessor.add=待機中のプロセッサに [{0}] を追加しました
abstractProtocol.waitingProcessor.remove=待機中のプロセッサから [{0}] を除去しました

abstractProtocolHandler.destroy=ProtocolHandler [{0}] を破棄します。
abstractProtocolHandler.init=プロトコルハンドラ [{0}] を初期化します。
abstractProtocolHandler.pause=ProtocolHandler [{0}] を一時停止します。
abstractProtocolHandler.resume=プロトコルハンドラー [{0}] を再開します。
abstractProtocolHandler.setAttribute=属性[{0}]に値[{1}]を設定する
abstractProtocolHandler.start=プロトコルハンドラー [{0}] を開始しました。
abstractProtocolHandler.stop=ProtocolHandler [{0}]の停止中

asyncStateMachine.invalidAsyncState=非同期状態[{1}]のリクエストに対して[{0}]を呼び出すことはできません

compressionConfig.ContentEncodingParseFail=圧縮が使用済みか確認する際に、Content-Encoding ヘッダの解析に失敗しました

request.notAsync=非同期処理またはHTTPアップグレード処理内のノンブロッキングIOに切り替えることは有効です。
request.nullReadListener=setReadListener() には null を指定できません。
request.readListenerSet=ノンブロッキングリードリスナーは既に設定されています。

response.encoding.invalid=JRE は文字エンコーディング [{0}] を認識しません。
response.notAsync=非同期処理、あるいは、HTTP アップグレード処理の途中でのみノンブロッキング IO へ切り替えることができます。
response.notNonBlocking=ノンブロッキングモードにしなかったレスポンスの isReady() を呼び出すことはできません。
response.nullWriteListener=setWriteListener() には null を指定できません。
response.writeListenerSet=ノンブロッキング書き込みリスナーが設定済みです
