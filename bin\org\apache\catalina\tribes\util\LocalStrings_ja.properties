# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

arrays.keyoffset.outOfBounds=keyoffset が領域外を指しています。
arrays.length.outOfBounds=キー配列のデータ要素が不足しています。length が境界を超えました。
arrays.malformed.arrays=バイト配列は{1,3,4,5,6}のような表現でなければなりません。
arrays.srcoffset.outOfBounds=srcoffsetが範囲外です。

executorFactory.not.running=エグゼキュータが実行されていないので、キューにコマンドを強制することはできません
executorFactory.queue.full=キューの容量がいっぱいです。

uuidGenerator.createRandom=[{0}]を使用してUUID生成用のSecureRandomインスタンスを作成すると、[{1}]ミリ秒かかりました。
uuidGenerator.unable.fit=[{0}] バイトを配列に合わせることができません。現在のサイズは [{1}] で必要なサイズは [{2}] です。
