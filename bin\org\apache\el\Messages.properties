# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# General Errors
error.convert=Cannot convert [{0}] of type [{1}] to [{2}]
error.compare=Cannot compare [{0}] to [{1}]
error.function=Problems calling function [{0}]
error.unreachable.base=Target Unreachable, identifier [{0}] resolved to null
error.unreachable.property=Target Unreachable, [{0}] returned null
error.resolver.unhandled=ELResolver did not handle type: [{0}] with property of [{1}]
error.resolver.unhandled.null=ELResolver cannot handle a null base Object with identifier [{0}]
error.invoke.wrongParams=The method [{0}] was called with [{1}] parameter(s) when it expected [{2}]
error.invoke.tooFewParams=The method [{0}] was called with [{1}] parameter(s) when it expected at least [{2}]

# ValueExpressionLiteral
error.value.literal.write=ValueExpression is a literal and not writable: [{0}]

# ExpressionFactoryImpl
error.null=Expression cannot be null
error.mixed=Expression cannot contain both '#{...}' and '${...}' : [{0}]
error.method=Not a valid MethodExpression : [{0}]
error.method.nullParms=Parameter types cannot be null
error.value.expectedType=Expected type cannot be null

# ExpressionBuilder
error.parseFail=Failed to parse the expression [{0}]

# ValueSetVisitor
error.syntax.set=Illegal Syntax for Set Operation

# ReflectionUtil
error.method.notfound=Method not found: {0}.{1}({2})
error.method.ambiguous=Unable to find unambiguous method: {0}.{1}({2})

# ValidatingVisitor
error.fnMapper.null=Expression uses functions, but no FunctionMapper was provided
error.fnMapper.method=Function [{0}] not found
error.fnMapper.paramcount=Function [{0}] specifies [{1}] params, but [{2}] were declared

# ExpressionImpl
error.context.null=ELContext was null

# Parser
error.function.tooManyMethodParameterSets=There are multiple sets of parameters specified for function [{0}]
error.identifier.notjava=The identifier [{0}] is not a valid Java identifier as required by section 1.19 of the EL specification (Identifier ::= Java language identifier). This check can be disabled by setting the system property org.apache.el.parser.SKIP_IDENTIFIER_CHECK to true.
error.lambda.tooManyMethodParameterSets=There are more sets of method parameters specified than there are nested lambda expressions

# Stream
stream.compare.notComparable=Stream elements must implement Comparable
stream.optional.empty=It is illegal to call get() on an empty optional
stream.optional.paramNotLambda=The parameter for the method [{0}] should be a lambda expression
