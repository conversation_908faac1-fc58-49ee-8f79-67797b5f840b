# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

bufferPool.created=最大サイズ[{1}]のタイプの[{0}]バイトのバッファプールを作成しました。

objectReader.retrieveFailed.socketReceiverBufferSize=TCP ソケットのレシーバーバッファサイズを取得できなかったため既定値として 43800 byte を設定します。

replicationStream.conflict=public でないインターフェイスのクラスローダーが複数存在します。

xByteBuffer.discarded.invalidHeader=ヘッダーが不正なためパッケージを破棄します。
xByteBuffer.no.package=XByteBuffer にパッケージがありません。
xByteBuffer.size.larger.buffer=バッファ長より大きな値がサイズとして指定されました。
xByteBuffer.unableCreate=バッファーが小さすぎてデータパッケージを作成できません。
xByteBuffer.unableTrim=利用可能なバイト数より多くのバイトをトリムすることはできません。 長さ：[{0}]トリム：[{1}]
xByteBuffer.wrong.class=メッセージのClassが間違っています。 それはSerializableを実装するはずですが、代わりに[{0}]です。
