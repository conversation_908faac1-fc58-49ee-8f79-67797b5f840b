# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cookie.fallToDebug=\n\
\ 注意：此错误的进一步出现将记录在调试级别。
cookie.invalidCookieValue=收到包含无效cookie的cookie头[{0}]。将忽略该cookie。
cookie.invalidCookieVersion=使用无法识别的[{0}] cookie版本接收到cookie标头。标头及其包含的cookie将被忽略。
cookie.valueNotPresent=<不存在>

http.closingBracket=在非IPv6主机名中找到了右括号']'。
http.illegalAfterIpv6=不允许字符[{0}]跟随主机名中的IPv6地址。
http.illegalCharacterDomain=字符[{0}]在域名中永远无效。
http.illegalCharacterIpv4=字符[{0}]为非法的IPv4地址。
http.illegalCharacterIpv6=字符[{0}]为非法的IPv6地址。
http.invalidCharacterDomain.afterColon=字符 [{0}] 在域名中的冒号后无效。
http.invalidCharacterDomain.afterHyphen=字符 [{0}] 在域名中的连字符后无效。
http.invalidCharacterDomain.afterLetter=字符 [{0}] 在域名中的字母后无效。
http.invalidCharacterDomain.afterNumber=字符 [{0}] 在域名中的数字后无效。
http.invalidCharacterDomain.afterPeriod=字符 [{0}] 在域名中的句号后无效。
http.invalidCharacterDomain.atEnd=字符 [{0}] 在域名末尾无效。
http.invalidCharacterDomain.atStart=字符 [{0}] 在域名开头无效。
http.invalidHextet=hextet无效。 hextet必须包含4个或更少的十六进制字符。
http.invalidIpv4Location=IPv6地址在无效位置包含嵌入的IPv4地址。
http.invalidLeadingZero=非零的IPv4字符可能不包含前导零。
http.invalidOctet=无效字符[{0}].IPv4字符的有效范围为0～255。
http.invalidSegmentEndState=状态[{0}]对于段的结尾无效。
http.noClosingBracket=ipv6 地址缺失一个闭合的圆括号
http.noOpeningBracket=IPv6地址缺少开括号(
http.singleColonEnd=IPv6地址不能以单个“.”结尾。
http.singleColonStart=一个IPv6地址也许不是以单个冒号":"开头的。
http.tooFewHextets=一个IPv6地址必须包含8个16进制数，但是这个IP地址包含了[(0)]个16进制数，并且无“::”序列表示一个或多个0个16进制数
http.tooManyColons=IPv6地址不能包含超过2个连续冒号字符。
http.tooManyDoubleColons=一个IPv6地址只能包含一个 '::' 序列。
http.tooManyHextets=IPv6地址包含[{0}]个十六进制数，但有效的IPv6地址不能超过8个。
