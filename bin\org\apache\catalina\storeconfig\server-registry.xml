<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<Registry name="Tomcat" version="6.0.35" encoding="UTF-8" >
     <Description
        tag="Server"
        standard="true"
        default="true"
        externalAllowed="true"
        children="true"
        tagClass="org.apache.catalina.core.StandardServer"
        storeFactoryClass="org.apache.catalina.storeconfig.StandardServerSF">
     </Description>
     <Description
        tag="Service"
        standard="true"
        default="true"
        children="true"
        tagClass="org.apache.catalina.core.StandardService"
        storeFactoryClass="org.apache.catalina.storeconfig.StandardServiceSF">
     </Description>
     <Description
        tag="Engine"
        standard="true"
        default="true"
        children="true"
        tagClass="org.apache.catalina.core.StandardEngine"
        storeFactoryClass="org.apache.catalina.storeconfig.StandardEngineSF">
        <TransientAttribute>domain</TransientAttribute>
     </Description>
     <Description
        tag="Host"
        standard="true"
        default="true"
        children="true"
        tagClass="org.apache.catalina.core.StandardHost"
        storeFactoryClass="org.apache.catalina.storeconfig.StandardHostSF">
        <TransientAttribute>domain</TransientAttribute>
     </Description>
     <Description
        tag="Context"
        standard="true"
        default="true"
        externalAllowed="true"
        externalOnly="true"
        storeSeparate="true"
        backup="true"
        children="true"
        tagClass="org.apache.catalina.core.StandardContext"
        storeFactoryClass="org.apache.catalina.storeconfig.StandardContextSF"
        storeAppenderClass="org.apache.catalina.storeconfig.StoreContextAppender">
        <TransientAttribute>available</TransientAttribute>
        <TransientAttribute>configFile</TransientAttribute>
        <TransientAttribute>configured</TransientAttribute>
        <TransientAttribute>displayName</TransientAttribute>
        <TransientAttribute>distributable</TransientAttribute>
        <TransientAttribute>domain</TransientAttribute>
        <TransientAttribute>name</TransientAttribute>
        <TransientAttribute>publicId</TransientAttribute>
        <TransientAttribute>originalDocBase</TransientAttribute>
        <TransientAttribute>replaceWelcomeFiles</TransientAttribute>
        <TransientAttribute>sessionTimeout</TransientAttribute>
        <TransientAttribute>startupTime</TransientAttribute>
        <TransientAttribute>tldScanTime</TransientAttribute>
        <TransientAttribute>effectiveMajorVersion</TransientAttribute>
        <TransientAttribute>effectiveMinorVersion</TransientAttribute>
        <TransientAttribute>webappVersion</TransientAttribute>
        <TransientAttribute>ignoreAnnotations</TransientAttribute>
     </Description>
     <Description
        id="org.apache.catalina.deploy.NamingResourcesImpl.[GlobalNamingResources]"
        tag="GlobalNamingResources"
        standard="true"
        default="false"
        attributes="false"
        children="true"
        tagClass="org.apache.catalina.deploy.NamingResourcesImpl"
        storeFactoryClass="org.apache.catalina.storeconfig.GlobalNamingResourcesSF">
     </Description>
     <Description
        tag="Connector"
        standard="true"
        default="true"
        tagClass="org.apache.catalina.connector.Connector"
        children="true"
        storeFactoryClass="org.apache.catalina.storeconfig.ConnectorSF"
        storeAppenderClass="org.apache.catalina.storeconfig.ConnectorStoreAppender">
        <!-- All attribute duplicated from the SSLHostConfig, removed in Tomcat 10 -->
        <TransientAttribute>SSLProtocol</TransientAttribute>
        <TransientAttribute>sslEnabledProtocols</TransientAttribute>
        <TransientAttribute>SSLCipherSuite</TransientAttribute>
        <TransientAttribute>ciphers</TransientAttribute>
        <TransientAttribute>SSLCertificateChainFile</TransientAttribute>
        <TransientAttribute>SSLCertificateFile</TransientAttribute>
        <TransientAttribute>keyAlias</TransientAttribute>
        <TransientAttribute>SSLCertificateKeyFile</TransientAttribute>
        <TransientAttribute>keyPass</TransientAttribute>
        <TransientAttribute>SSLPassword</TransientAttribute>
        <TransientAttribute>keystoreFile</TransientAttribute>
        <TransientAttribute>keystorePass</TransientAttribute>
        <TransientAttribute>keystoreProvider</TransientAttribute>
        <TransientAttribute>keystoreType</TransientAttribute>
        <TransientAttribute>SSLCACertificateFile</TransientAttribute>
        <TransientAttribute>SSLCACertificatePath</TransientAttribute>
        <TransientAttribute>crlFile</TransientAttribute>
        <TransientAttribute>SSLCARevocationFile</TransientAttribute>
        <TransientAttribute>SSLCARevocationPath</TransientAttribute>
        <TransientAttribute>SSLDisableCompression</TransientAttribute>
        <TransientAttribute>SSLDisableSessionTickets</TransientAttribute>
        <TransientAttribute>SSLDisableCompression</TransientAttribute>
        <TransientAttribute>SSLHonorCipherOrder</TransientAttribute>
        <TransientAttribute>useServerCipherSuitesOrder</TransientAttribute>
        <TransientAttribute>algorithm</TransientAttribute>
        <TransientAttribute>sslContext</TransientAttribute>
        <TransientAttribute>sessionCacheSize</TransientAttribute>
        <TransientAttribute>sessionTimeout</TransientAttribute>
        <TransientAttribute>sslProtocol</TransientAttribute>
        <TransientAttribute>trustManagerClassName</TransientAttribute>
        <TransientAttribute>truststoreAlgorithm</TransientAttribute>
        <TransientAttribute>truststoreFile</TransientAttribute>
        <TransientAttribute>truststorePass</TransientAttribute>
        <TransientAttribute>truststoreProvider</TransientAttribute>
        <TransientAttribute>truststoreType</TransientAttribute>
        <!-- All attribute duplicated from the AbstractHttp11Protocol, removed in Tomcat 10 -->
        <TransientAttribute>clientAuth</TransientAttribute>
        <TransientAttribute>SSLVerifyClient</TransientAttribute>
        <TransientAttribute>trustMaxCertLength</TransientAttribute>
        <TransientAttribute>SSLVerifyDepth</TransientAttribute>
        <TransientAttribute>useServerCipherSuitesOrder</TransientAttribute>
        <TransientAttribute>SSLHonorCipherOrder</TransientAttribute>
     </Description>
     <Description
        tag="UpgradeProtocol"
        standard="false"
        default="false"
        tagClass="org.apache.coyote.UpgradeProtocol"
        children="false"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="SSLHostConfig"
        standard="true"
        default="true"
        tagClass="org.apache.tomcat.util.net.SSLHostConfig"
        children="true"
        storeFactoryClass="org.apache.catalina.storeconfig.SSLHostConfigSF">
        <TransientAttribute>openSslContext</TransientAttribute>
        <TransientAttribute>openSslConfContext</TransientAttribute>
        <!-- All attribute duplicated from the Certificate, may be removed in Tomcat 10 -->
        <TransientAttribute>certificateChainFile</TransientAttribute>
        <TransientAttribute>certificateFile</TransientAttribute>
        <TransientAttribute>certificateKeyAlias</TransientAttribute>
        <TransientAttribute>certificateKeyFile</TransientAttribute>
        <TransientAttribute>certificateKeyPassword</TransientAttribute>
        <TransientAttribute>certificateKeystoreFile</TransientAttribute>
        <TransientAttribute>certificateKeystorePassword</TransientAttribute>
        <TransientAttribute>certificateKeystoreProvider</TransientAttribute>
        <TransientAttribute>certificateKeystoreType</TransientAttribute>
     </Description>
     <Description
        tag="Certificate"
        standard="true"
        default="true"
        tagClass="org.apache.tomcat.util.net.SSLHostConfigCertificate"
        children="false"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase"
        storeAppenderClass="org.apache.catalina.storeconfig.CertificateStoreAppender">
     </Description>
     <Description
        tag="OpenSSLConf"
        standard="true"
        default="true"
        tagClass="org.apache.tomcat.util.net.openssl.OpenSSLConf"
        children="true"
        storeFactoryClass="org.apache.catalina.storeconfig.OpenSSLConfSF">
     </Description>
     <Description
        tag="OpenSSLConfCmd"
        standard="true"
        default="true"
        tagClass="org.apache.tomcat.util.net.openssl.OpenSSLConfCmd"
        children="false"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="NamingResources"
        standard="true"
        default="false"
        attributes="false"
        children="true"
        tagClass="org.apache.catalina.deploy.NamingResourcesImpl"
        storeFactoryClass="org.apache.catalina.storeconfig.NamingResourcesSF">
     </Description>
     <Description
        tag="Manager"
        standard="false"
        default="false"
        children="true"
        tagClass="org.apache.catalina.Manager"
        storeFactoryClass="org.apache.catalina.storeconfig.ManagerSF">
        <TransientAttribute>domain</TransientAttribute>
    </Description>
    <Description
        tag="Manager"
        standard="false"
        default="false"
        children="true"
        tagClass="org.apache.catalina.session.PersistentManager"
        storeFactoryClass="org.apache.catalina.storeconfig.PersistentManagerSF">
     </Description>
     <Description
        tag="Store"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.session.FileStore"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="Store"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.session.JDBCStore"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="Cluster"
        standard="false"
        default="false"
        children="true"
        tagClass="org.apache.catalina.ha.CatalinaCluster"
        storeFactoryClass="org.apache.catalina.storeconfig.CatalinaClusterSF">
        <TransientAttribute>clusterName</TransientAttribute>
     </Description>
     <Description
        tag="Realm"
        standard="false"
        default="false"
        children="true"
        tagClass="org.apache.catalina.Realm"
        storeFactoryClass="org.apache.catalina.storeconfig.RealmSF">
        <TransientAttribute>domain</TransientAttribute>
        <TransientAttribute>realmPath</TransientAttribute>
     </Description>
     <Description
        tag="CredentialHandler"
        standard="false"
        default="false"
        children="true"
        tagClass="org.apache.catalina.CredentialHandler"
        storeFactoryClass="org.apache.catalina.storeconfig.CredentialHandlerSF">
     </Description>
     <Description
        tag="Parameter"
        standard="true"
        default="false"
        tagClass="org.apache.tomcat.util.descriptor.web.ApplicationParameter"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="Loader"
        standard="true"
        default="false"
        tagClass="org.apache.catalina.loader.WebappLoader"
        storeFactoryClass="org.apache.catalina.storeconfig.LoaderSF">
        <TransientAttribute>domain</TransientAttribute>
     </Description>
     <Description
        tag="Listener"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.LifecycleListener"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
       <TransientChild>org.apache.catalina.mbeans.ServerLifecycleListener</TransientChild>
       <TransientChild>org.apache.catalina.core.NamingContextListener</TransientChild>
       <TransientChild>org.apache.catalina.startup.ContextConfig</TransientChild>
       <TransientChild>org.apache.catalina.startup.ContextConfig$HostWebXmlCacheCleaner</TransientChild>
       <TransientChild>org.apache.catalina.startup.EngineConfig</TransientChild>
       <TransientChild>org.apache.catalina.startup.HostConfig</TransientChild>
       <TransientChild>org.apache.catalina.core.StandardHost$MemoryLeakTrackingListener</TransientChild>
       <TransientChild>org.apache.catalina.mapper.MapperListener</TransientChild>
       <TransientChild>org.apache.catalina.core.StandardEngine$AccessLogListener</TransientChild>
     </Description>
     <Description
        id="org.apache.catalina.core.StandardServer.[ServerLifecycleListener]"
        tag="Listener"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.mbeans.ServerLifecycleListener"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="Valve"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.Valve"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
         <TransientChild>org.apache.catalina.authenticator.BasicAuthenticator</TransientChild>
         <TransientChild>org.apache.catalina.authenticator.DigestAuthenticator</TransientChild>
         <TransientChild>org.apache.catalina.authenticator.FormAuthenticator</TransientChild>
         <TransientChild>org.apache.catalina.authenticator.NonLoginAuthenticator</TransientChild>
         <TransientChild>org.apache.catalina.authenticator.SSLAuthenticator</TransientChild>
         <TransientChild>org.apache.catalina.core.StandardContextValve</TransientChild>
         <TransientChild>org.apache.catalina.core.StandardEngineValve</TransientChild>
         <TransientChild>org.apache.catalina.core.StandardHostValve</TransientChild>
         <TransientChild>org.apache.catalina.valves.CertificatesValve</TransientChild>
         <TransientChild>org.apache.catalina.valves.ErrorReportValve</TransientChild>
         <TransientChild>org.apache.catalina.valves.RequestListenerValve</TransientChild>
     </Description>
     <Description
        tag="Environment"
        standard="true"
        default="false"
        tagClass="org.apache.tomcat.util.descriptor.web.ContextEnvironment"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="EJB"
        standard="true"
        default="false"
        tagClass="org.apache.tomcat.util.descriptor.web.ContextEjb"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="LocalEjb"
        standard="true"
        default="false"
        tagClass="org.apache.tomcat.util.descriptor.web.ContextLocalEjb"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="Resource"
        standard="true"
        default="false"
        tagClass="org.apache.tomcat.util.descriptor.web.ContextResource"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
    <Description
        tag="Resources"
        standard="false"
        default="false"
        children="true"
        tagClass="org.apache.catalina.WebResourceRoot"
        storeFactoryClass="org.apache.catalina.storeconfig.WebResourceRootSF">
        <TransientAttribute>domain</TransientAttribute>
     </Description>
    <Description
        id="org.apache.catalina.WebResourceSet.[PreResources]"
        tag="PreResources"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.WebResourceSet"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
    <Description
        id="org.apache.catalina.WebResourceSet.[JarResources]"
        tag="JarResources"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.WebResourceSet"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
    <Description
        id="org.apache.catalina.WebResourceSet.[PostResources]"
        tag="PostResources"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.WebResourceSet"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="ResourceEnvRef"
        standard="true"
        default="false"
        tagClass="org.apache.tomcat.util.descriptor.web.ContextResourceEnvRef"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="ResourceLink"
        standard="true"
        default="false"
        tagClass="org.apache.tomcat.util.descriptor.web.ContextResourceLink"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="Transaction"
        standard="true"
        default="false"
        tagClass="org.apache.tomcat.util.descriptor.web.ContextTransaction"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        id="org.apache.catalina.core.StandardContext.[WrapperLifecycle]"
        tag="WrapperLifecycle"
        standard="true"
        default="false"
        attributes="false"
        storeFactoryClass="org.apache.catalina.storeconfig.WrapperLifecycleSF">
     </Description>
     <Description
        id="org.apache.catalina.core.StandardContext.[WrapperListener]"
        tag="WrapperListener"
        standard="true"
        default="false"
        attributes="false"
        storeFactoryClass="org.apache.catalina.storeconfig.WrapperListenerSF">
     </Description>
     <Description
        id="org.apache.catalina.core.StandardContext.[WatchedResource]"
        tag="WatchedResource"
        standard="true"
        default="false"
        attributes="false"
        storeFactoryClass="org.apache.catalina.storeconfig.WatchedResourceSF">
     </Description>
     <Description
        tag="Sender"
        standard="false"
        default="false"
        children="true"
        tagClass="org.apache.catalina.tribes.ChannelSender"
        storeFactoryClass="org.apache.catalina.storeconfig.SenderSF">
     </Description>
     <Description
        tag="Transport"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.tribes.transport.DataSender"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="Receiver"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.tribes.ChannelReceiver"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
        <TransientAttribute>bind</TransientAttribute>
        <TransientAttribute>host</TransientAttribute>
     </Description>
     <Description
        tag="Membership"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.tribes.MembershipService"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="Deployer"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.ha.ClusterDeployer"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="ClusterListener"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.ha.ClusterListener"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="Channel"
        standard="false"
        default="false"
        children="true"
        tagClass="org.apache.catalina.tribes.Channel"
        storeFactoryClass="org.apache.catalina.storeconfig.ChannelSF">
     </Description>
     <Description
        tag="Interceptor"
        standard="false"
        default="false"
        children="false"
        tagClass="org.apache.catalina.tribes.ChannelInterceptor"
        storeFactoryClass="org.apache.catalina.storeconfig.InterceptorSF">
     </Description>
     <Description
        tag="Member"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.tribes.Member"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="Executor"
        standard="false"
        default="false"
        tagClass="org.apache.catalina.core.StandardThreadExecutor"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="JarScanner"
        standard="true"
        default="false"
        children="true"
        tagClass="org.apache.tomcat.util.scan.StandardJarScanner"
        storeFactoryClass="org.apache.catalina.storeconfig.JarScannerSF">
     </Description>
     <Description
        tag="JarScanFilter"
        standard="true"
        default="false"
        tagClass="org.apache.tomcat.util.scan.StandardJarScanFilter"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="CookieProcessor"
        standard="false"
        default="false"
        tagClass="org.apache.tomcat.util.http.CookieProcessor"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
     <Description
        tag="SessionIdGenerator"
        standard="true"
        default="false"
        tagClass="org.apache.catalina.util.StandardSessionIdGenerator"
        storeFactoryClass="org.apache.catalina.storeconfig.StoreFactoryBase">
     </Description>
</Registry>

