# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

authenticator.certificates=이 요청에 클라인트 인증서 체인이 없습니다.
authenticator.changeSessionId=인증 처리 시, 세션 ID를 [{0}]에서 [{1}](으)로 변경했습니다.
authenticator.check.authorize=사용자 이름 [{0}]을(를) Connector로부터 얻었으며, 이는 유효한 것으로 신뢰되었습니다. Tomcat Realm으로부터, 이 사용자를 위한 역할들을 구합니다.
authenticator.check.authorizeFail=Realm이 사용자 [{0}]을(를) 인식하지 못했습니다. 해당 사용자명에 대해 아무런 역할 없이 Principal을 생성합니다.
authenticator.check.found=[{0}]은(는) 이미 인증되었습니다.
authenticator.check.sso=인증되지 않았는데, SSO 세션 ID [{0}]이(가) 발견되었습니다. 다시 인증을 시도합니다.
authenticator.formlogin=폼 로그인 페이지에 대한 유효하지 않은 직접 참조
authenticator.jaspicCleanSubjectFail=JASPIC subject를 제거하지 못했습니다.
authenticator.jaspicSecureResponseFail=JASPIC 처리 중 응답을 보안처리 하지 못했습니다.
authenticator.jaspicServerAuthContextFail=JASPIC ServerAuthContext 인스턴스를 획득하지 못했습니다.
authenticator.loginFail=로그인 실패
authenticator.manager=Trust 매니저들을 초기화하는 중 예외 발생
authenticator.noAuthHeader=클라이언트가 authorization 헤더를 보내지 않았습니다.
authenticator.notContext=설정 오류: 컨텍스트에 설정되어야만 합니다.
authenticator.requestBodyTooBig=요청의 body가 너무 커서, 인증 처리 과정에서 캐시에 저장될 수 없습니다.
authenticator.sessionExpired=로그인 처리 허용 시간이 초과되었습니다. 계속하시려면 뒤로 가기를 두번 클릭한 후, 요청했던 링크를 다시 클릭하거나, 브라우저를 닫았다가 다시 시작해야 합니다.
authenticator.tomcatPrincipalLogoutFail=TomcatPrincipal 인스턴스를 사용한 로그아웃 시도가 실패했습니다.
authenticator.unauthorized=제공된 credentials를 사용하여 인증할 수 없습니다.

basicAuthenticator.invalidCharset=허용된 값들은 오직 널, 빈 문자열, 또는 UTF-8 문자열입니다.

digestAuthenticator.cacheRemove=새로운 엔트리들을 위한 공간을 만들기 위해, client nonce cache로부터 유효한 엔트리를 제거했습니다. 리플레이 공격이 가능해진 상태입니다. 가능성 있는 리플레이 공격들을 방지하려면, nonceValidity를 감소 시키거나, nonceCacheSize를 증가 시키십시오. 더 이상 이러한 종류의 경고 메시지들은 향후 5분 동안 나오지 않을 것입니다.

formAuthenticator.forwardErrorFail=오류 페이지로 forward하는 중 예기치 않은 오류 발생
formAuthenticator.forwardLogin=메소드 [{1}]을(를) 사용한 [{0}]에 대한 요청을, 컨텍스트 [{3}]의 로그인 페이지 [{2}](으)로, GET 요청 메소드를 사용하여 forward 합니다.
formAuthenticator.forwardLoginFail=로그인 페이지로 forward하는 중 예기치 않은 오류 발생
formAuthenticator.noErrorPage=컨텍스트 [{0}]에서 폼 기반 인증을 위한 오류 페이지가 정의되지 않았습니다.
formAuthenticator.noLoginPage=컨텍스트 [{0}]에서, 폼 기반 인증을 위한 로그인 페이지가 정의되지 않았습니다.

singleSignOn.debug.associate=SSO가, 애플리케이션 세션 [{1}]을(를) SSO 세션 [{0}]와(과) 연관시킵니다.
singleSignOn.debug.associateFail=SSO 세션 [{1}]이(가) 존재하지 않기 때문에, SSO가 애플리케이션 세션 [{0}]을(를) 연관시키지 못했습니다.
singleSignOn.debug.cookieCheck=SSO가, SSO 쿠키가 존재하는지 점검합니다.
singleSignOn.debug.cookieNotFound=SSO가, SSO 쿠키를 찾지 못했습니다.
singleSignOn.debug.deregister=SSO가, SSO 세션 [{1}]와(과) 연관된 애플리케이션 세션 [{0}]을(를) 만료시킵니다.
singleSignOn.debug.deregisterFail=SSO 세션 [{0}]이(가) 캐시에 존재하지 않기 때문에, SSO가 해당 SSO 세션에 대한 등록을 제거하지 못했습니다.
singleSignOn.debug.deregisterNone=SSO가 SSO 세션 [{0}]의 등록을 제거했으나, 연관된 애플리케이션 세션들을 찾지 못했습니다.
singleSignOn.debug.hasPrincipal=SSO가 이전에 인증된 Principal [{0}]을(를) 발견했습니다.
singleSignOn.debug.invoke=SSO가 [{0}]을(를) 위해 요청을 처리합니다.
singleSignOn.debug.principalCheck=SSO 세션 [{0}]을(를) 위하여, SSO가 캐시된 Principal을 찾습니다.
singleSignOn.debug.principalFound=인증 타입이 [{1}]인, 캐시된 Principal [{0}]을(를), SSO가 발견했습니다.
singleSignOn.debug.principalNotFound=SSO가 캐시된 Principal을 찾지 못했습니다. 세션 [{0}]을(를) 위한 SSO 쿠키를 지웁니다.
singleSignOn.debug.register=사용자 [{1}]을(를) 위해, 인증 타입 [{2}]을 사용하여, SSO가 SSO 세션 [{0}]을(를) 등록합니다.
singleSignOn.debug.removeSession=SSO가 SSO 세션 [{1}](으)로부터 세션 [{0}]을(를) 제거합니다.
singleSignOn.debug.sessionLogout=SSO 세션 [{0}]와(과) 애플리케이션 세션 [{1}]을(를) 위해, SSO가 로그아웃을 처리 중
singleSignOn.debug.sessionTimeout=SSO 세션 [{0}]와(과) 애플리케이션 세션 [{1}]을(를) 위한, SSO 처리가 제한 시간 초과되었습니다.
singleSignOn.debug.update=SSO가, SSO 세션 [{0}]의 인증 타입을 [{1}](으)로 변경합니다.
singleSignOn.sessionExpire.contextNotFound=컨텍스트를 찾을 수 없기 때문에, SSO가 세션 [{0}]을(를) 만료시킬 수 없습니다.
singleSignOn.sessionExpire.engineNull=엔진이 널이기 때문에 SSO가 세션 [{0}]을(를) 만료시킬 수 없습니다.
singleSignOn.sessionExpire.hostNotFound=호스트를 찾을 수 없어서, SSO가 세션 [{0}]을(를) 만료시킬 수 없습니다.
singleSignOn.sessionExpire.managerError=세션을 찾는 동안 매니저가 예외를 발생시켜, SSO가 세션 [{0}]을(를) 만료시킬 수 없습니다.
singleSignOn.sessionExpire.managerNotFound=매니저를 찾을 수 없기 때문에, SSO가 세션 [{0}]을(를) 만료시킬 수 없습니다.
singleSignOn.sessionExpire.sessionNotFound=세션을 찾을 수 없기 때문에, SSO가 세션 [{0}]을(를) 만료시킬 수 없습니다.

spnegoAuthenticator.authHeaderNoToken=클라이언트에 의해 전송된 Negotiate authorization 헤더가 토큰을 포함하지 않았습니다.
spnegoAuthenticator.authHeaderNotNego=클라이언트가 보낸 Authorization 헤더가 Negotiate로 시작하지 않았습니다.
spnegoAuthenticator.serviceLoginFail=서비스 Principal로서 로그인 할 수 없습니다.
spnegoAuthenticator.ticketValidateFail=클라이언트에 의해 제공된 티켓이 유효한지를 확인하지 못했습니다.
