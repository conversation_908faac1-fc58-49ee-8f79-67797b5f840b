<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<antlib>
  <typedef
        name="open"
        classname="org.apache.catalina.ant.jmx.JMXAccessorTask" />
  <typedef
        name="set"
        classname="org.apache.catalina.ant.jmx.JMXAccessorSetTask" />
  <typedef
        name="get"
        classname="org.apache.catalina.ant.jmx.JMXAccessorGetTask" />
  <typedef
        name="invoke"
        classname="org.apache.catalina.ant.jmx.JMXAccessorInvokeTask" />
  <typedef
        name="query"
        classname="org.apache.catalina.ant.jmx.JMXAccessorQueryTask" />
  <typedef
        name="create"
        classname="org.apache.catalina.ant.jmx.JMXAccessorCreateTask" />
  <typedef
        name="unregister"
        classname="org.apache.catalina.ant.jmx.JMXAccessorUnregisterTask" />
  <typedef
        name="equals"
        classname="org.apache.catalina.ant.jmx.JMXAccessorEqualsCondition" />
  <typedef
        name="condition"
        classname="org.apache.catalina.ant.jmx.JMXAccessorCondition" />
</antlib>