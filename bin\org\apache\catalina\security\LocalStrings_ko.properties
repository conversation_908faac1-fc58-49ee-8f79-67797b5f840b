# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

SecurityListener.checkUmaskFail=umask 설정 [{0}]을(를) 사용하여 시작을 시도했습니다. 적어도 umask를 [{1}] 만큼 제한적으로 설정하지 않고 Tomcat을 시작하는 것은, Lifecycle 리스너인 org.apache.catalina.security.SecurityListener (통상 CATALINA_BASE/conf/server.xml에서 설정)에 의해 차단되었습니다.
SecurityListener.checkUmaskNone=시스템 프로퍼티 [{0}]에 umask 설정이 없습니다. 하지만, Tomcat은 umask를 지원하는 플랫폼에서 실행 중인 것으로 보입니다. 해당 시스템 프로퍼티는 보통 CATALINA_HOME/bin/catalina.sh에서 설정됩니다. Lifecycle 리스너인 org.apache.catalina.security.SecurityListener(통상 CATALINA_BASE/conf/server.xml에서 설정)는, umask 값이 적어도 [{1}] 만큼 제한적으로 설정되기를 요구합니다.
SecurityListener.checkUmaskParseFail=값 [{0}]이(가) 유효한 umask 값이 아니어서, 파싱하지 못했습니다.
SecurityListener.checkUmaskSkip=umask를 결정할 수 없습니다. Tomcat이 Windows에서 실행되는 것으로 보이므로, umask 점검을 건너뜁니다.
SecurityListener.checkUserWarning=사용자 [{0}](으)로서 실행하면서, 프로그램 시작이 시도 되었습니다. 이 사용자로서 Tomcat을 실행하는 것은, Lifecycle 리스너인 org.apache.catalina.security.SecurityListener (보통 CATALINA_BASE/conf/server.xml에서 설정)에 의해 차단되었습니다.

SecurityUtil.doAsPrivilege=PrivilegedExceptionAction 블록을 실행하는 중 예외가 발생했습니다.
