# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

serverContainer.addNotAllowed=一旦尝试使用以前注册的终结点之一，就不能再注册其他终结点
serverContainer.configuratorFail=无法为[{1}]类型的POJO创建类型[{0}]的配置程序
serverContainer.duplicatePaths=多个端点可能不能发不到同一个路径[{0}]：已经存在的端点[{1}]和新的端点[{2}]
serverContainer.encoderFail=无法创建[{0}]类型的编码器
serverContainer.failedDeployment=由于以前的部署失败，不允许将WebSocket终结点部署到主机[{1}]中路径为[{0}]的Web应用程序
serverContainer.missingAnnotation=无法部署POJO类[{0}]，因为它没有用@ServerEndpoint进行注释
serverContainer.servletContextMissing=没有指定ServletContext

upgradeUtil.incompatibleRsv=指定扩展名具有不兼容的RSV位使用

uriTemplate.duplicateParameter=参数[{0}]在不允许的路径中出现多次
uriTemplate.emptySegment=路径[{0}]包含一个或多个不允许的空段
uriTemplate.invalidPath=路径 [{0}] 无效。
uriTemplate.invalidSegment=段[{0}]在提供的路径[{1}]中无效

wsFrameServer.bytesRead=将[{0}]个字节读入输入缓冲区，准备进行处理
wsFrameServer.illegalReadState=意外的读取状态[{0}]
wsFrameServer.onDataAvailable=进入方法

wsHttpUpgradeHandler.closeOnError=由于错误关闭WebSocket连接
wsHttpUpgradeHandler.destroyFailed=销毁WebSocket HttpUpgradeHandler时无法关闭WebConnection
wsHttpUpgradeHandler.noPreInit=在容器调用init()之前，必须调用preinit()方法来配置WebSocket HttpUpgradeHandler。通常，这意味着创建WsHttpUpgradeHandler 实例的servlet也应该调用preinit()
wsHttpUpgradeHandler.serverStop=服务器正在停止

wsRemoteEndpointServer.closeFailed=无法完全关闭ServletOutputStream 连接
