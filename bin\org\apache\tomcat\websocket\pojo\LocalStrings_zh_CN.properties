# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

pojoEndpointBase.closeSessionFail=在错误处理期间无法关闭WebSocket会话
pojoEndpointBase.onCloseFail=):无法为类型为[{0}]的POJO调用POJO端点的onClose方法
pojoEndpointBase.onError=在错误发生后，没有为[{0}]配置错误处理
pojoEndpointBase.onErrorFail=无法为类型为[{0}]的POJO调用POJO端点的onError方法
pojoEndpointBase.onOpenFail=无法为类型为[{0}]的POJO调用POJO端点的onOpen方法

pojoEndpointServer.getPojoInstanceFail=创建类型为 [{0}] 的 POJO 实例失败

pojoMessageHandlerWhole.decodeIoFail=解码消息时出现IO错误
pojoMessageHandlerWhole.maxBufferSize=此实现支持的最大消息大小为Integer.MAX_VALUE

pojoMethodMapping.decodePathParamFail=未能将路径参数值[{0}]解码为预期的类型[{1}]
pojoMethodMapping.duplicateAnnotation=类[{1}]上存在的重复注释[{0}]
pojoMethodMapping.duplicateLastParam=用OnMessage注释的类[{1}]的方法[{0}]上存在多个布尔参数（最后的）
pojoMethodMapping.duplicateMessageParam=类[{1}]的方法[{0}]上存在多个消息参数，该方法已用OnMessage进行了注释。
pojoMethodMapping.duplicatePongMessageParam=使用OnMessage注释的类[{1}]的方法[{0}]上存在多个PongMessage参数
pojoMethodMapping.duplicateSessionParam=类[{1}]的方法[{0}]上存在多个会话参数，该方法使用OnMessage进行了注释
pojoMethodMapping.invalidDecoder=这个特定类型的解码器[{0}]无法被实例化
pojoMethodMapping.methodNotPublic=注解方法[{0}]不为公共方法
pojoMethodMapping.noDecoder=在用onMessage注释的类[{1}]的方法[{0}]上找不到消息参数的解码器。
pojoMethodMapping.noPayload=用OnMessage注释的类{1}的方法{0}上不存在有效负载参数
pojoMethodMapping.onErrorNoThrowable=):类[{1}]的方法[{0}]上不存在用OnError注释的抛出参数
pojoMethodMapping.paramWithoutAnnotation=在类[{2}]的方法[{1}]上找到了类型为[{0}]的参数，该方法没有@PathParam 注释
pojoMethodMapping.partialInputStream=类[{1}]的方法[{0}]中存在无效的InputStream和boolean参数，该方法使用OnMessage进行了批注
pojoMethodMapping.partialObject=用onMessage注释的类[{1}]的方法[{0}]中存在无效的对象和布尔参数
pojoMethodMapping.partialPong=用OnMessage注释的类[{1}]的方法[{0}]上存在无效的PongMessage和布尔参数
pojoMethodMapping.partialReader=用OnMessage注释的类[{1}]的方法[{0}]中存在无效的读取器和布尔参数
pojoMethodMapping.pongWithPayload=类[{1}]的方法[{0}]中存在无效的PongMessage 和消息参数，该方法是用onMessage注释的

pojoPathParam.wrongType=不允许将类型[{0}]作为路径参数。用@PathParam注释的参数只能是字符串、Java原语或盒装版本。
