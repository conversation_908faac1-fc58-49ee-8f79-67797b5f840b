# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

backupManager.noCluster=no cluster associated with this context: [{0}]
backupManager.startFailed=Failed to start BackupManager: [{0}]
backupManager.startUnable=Unable to start BackupManager: [{0}]
backupManager.stopped=Manager [{0}] is stopping

clusterSessionListener.noManager=Context manager doesn''t exist:[{0}]

deltaManager.createMessage.access=Manager [{0}]: create session access message for session [{1}]
deltaManager.createMessage.accessChangePrimary=Manager [{0}]: create change primary node message for session [{1}]
deltaManager.createMessage.allSessionData=Manager [{0}] sent all session data.
deltaManager.createMessage.allSessionTransferred=Manager [{0}] sent all session data transferred
deltaManager.createMessage.delta=Manager [{0}]: create delta request message for session [{1}]
deltaManager.createMessage.expire=Manager [{0}]: create session expire message for session [{1}]
deltaManager.createMessage.unableCreateDeltaRequest=Unable to serialize delta request for sessionid [{0}]
deltaManager.createSession.newSession=Created a new DeltaSession with Id [{0}] Total count=[{1}]
deltaManager.dropMessage=Manager [{0}]: Drop message [{1}] inside GET_ALL_SESSIONS sync phase start date [{2}] message date [{3}]
deltaManager.expireSessions=Manager [{0}] expiring sessions upon shutdown
deltaManager.foundMasterMember=Found for context [{0}] the replication master member [{1}]
deltaManager.loading.cnfe=ClassNotFoundException while loading persisted sessions: [{0}]
deltaManager.loading.existing.session=overload existing session [{0}]
deltaManager.loading.ioe=IOException while loading persisted sessions: [{0}]
deltaManager.managerLoad=Exception loading sessions from persistent storage
deltaManager.noCluster=Starting... no cluster associated with this context: [{0}]
deltaManager.noContextManager=Manager [{0}]: In reply to the ''Get all session data'' message sent at [{1}], a ''No matching context manager'' message was received after [{2}] ms.
deltaManager.noMasterMember=Starting... with no other member for context [{0}] at domain [{1}]
deltaManager.noMembers=Manager [{0}]: skipping state transfer. No members active in cluster group.
deltaManager.noSessionState=Manager [{0}]: No session state sent at [{1}] received, timing out after [{2}] ms.
deltaManager.receiveMessage.accessed=Manager [{0}]: received session accessed message for session [{1}]
deltaManager.receiveMessage.allSessionDataAfter=Manager [{0}]: all session state deserialized
deltaManager.receiveMessage.allSessionDataBegin=Manager [{0}]: received all session state data
deltaManager.receiveMessage.createNewSession=Manager [{0}]: received session created message for session [{1}]
deltaManager.receiveMessage.delta=Manager [{0}]: received session delta message for session [{1}]
deltaManager.receiveMessage.delta.unknown=Manager [{0}]: received session delta for unknown session [{1}]
deltaManager.receiveMessage.error=Manager [{0}]: Unable to receive message through TCP channel
deltaManager.receiveMessage.eventType=Manager [{0}]: Received SessionMessage of type=[{1}] from [{2}]
deltaManager.receiveMessage.expired=Manager [{0}]: received session expired message for session [{1}]
deltaManager.receiveMessage.noContextManager=Manager [{0}] received from node [{1}:{2}] no context manager.
deltaManager.receiveMessage.transfercomplete=Manager [{0}] received from node [{1}:{2}] session state transferred.
deltaManager.receiveMessage.unloadingAfter=Manager [{0}]: unloading sessions complete
deltaManager.receiveMessage.unloadingBegin=Manager [{0}]: start unloading sessions
deltaManager.registerCluster=Register manager [{0}] to cluster element [{1}] with name [{2}]
deltaManager.sendMessage.newSession=Manager [{0}] send new session [{1}]
deltaManager.sessionReceived=Manager [{0}]; session state sent at [{1}] received in [{2}] ms.
deltaManager.startClustering=Starting clustering manager at [{0}]
deltaManager.stopped=Manager [{0}] is stopping
deltaManager.unableSerializeSessionID=Unable to serialize sessionID [{0}]
deltaManager.unloading.ioe=IOException while saving persisted sessions: [{0}]
deltaManager.waitForSessionState=Manager [{0}], requesting session state from [{1}]. This operation will timeout if no session state has been received within [{2}] seconds.

deltaRequest.invalidAttributeInfoType=Invalid attribute info type=[{0}]
deltaRequest.removeUnable=Unable to remove element:
deltaRequest.showPrincipal=Principal [{0}] is set to session [{1}]
deltaRequest.ssid.mismatch=Session id mismatch, not executing the delta request
deltaRequest.ssid.null=Session Id is null for setSessionId
deltaRequest.wrongPrincipalClass=ClusterManager only support GenericPrincipal. Your realm used principal class [{0}].

deltaSession.notifying=Notifying cluster of session expiration: manager [{0}], primary [{1}], sessionId [{2}]
deltaSession.readSession=readObject() loading session [{0}]
deltaSession.writeSession=writeObject() storing session [{0}]

jvmRoute.cannotFindSession=Cannot find session [{0}]
jvmRoute.changeSession=Changed session from [{0}] to [{1}]
jvmRoute.failover=Detected a failover with different jvmRoute - orginal route: [{0}] new one: [{1}] at session id [{2}]
jvmRoute.foundManager=Found Cluster Manager [{0}] at [{1}]
jvmRoute.missingJvmRouteAttribute=No engine jvmRoute attribute configured!
jvmRoute.noCluster=The JvmRouterBinderValve is configured, but clustering is not being used. Fail over will still work, providing a PersistentManager is used.
jvmRoute.notFoundManager=Not found Cluster Manager at [{0}]
jvmRoute.set.originalsessionid=Set Orginal Session id at request attribute [{0}] value: [{1}]
jvmRoute.turnoverInfo=Turnover Check time [{0}] msec
jvmRoute.valve.started=JvmRouteBinderValve started
jvmRoute.valve.stopped=JvmRouteBinderValve stopped

standardSession.notSerializable=Cannot serialize session attribute [{0}] for session [{1}]
standardSession.removeAttribute.ise=removeAttribute: Session already invalidated
standardSession.setAttribute.namenull=setAttribute: name parameter cannot be null
