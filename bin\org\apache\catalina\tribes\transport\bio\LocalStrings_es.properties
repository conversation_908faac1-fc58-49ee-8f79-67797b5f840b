# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

bioReceiver.socket.closeFailed=Fallo al cerrar el socket

bioReplicationTask.reader.closeFailed=Fallo al cerrar el lector

bioSender.ack.eof=EOF alcanzado en puerto local [{0}:{1,number,integer}]
bioSender.ack.missing=No puedo leer reconocimiento desde [{0}:{1,number,integer}] en {2,number,integer} ms. Desconectando conector e intentando otra vez.
bioSender.ack.wrong=Falta ACK correcto tras 10 bytes leídos en puerto local [{0}:{1,number,integer}]
bioSender.closeSocket=El remitente cerró el conector con [{0}:{1,number,integer}] (contador de cierre {2,number,integer})
bioSender.disconnect=Remitente desconectado de [{0}:{1,number,integer}] (contador de desconexión {2,number,integer})
bioSender.openSocket=Remitente abrió conector con [{0}:{1,number,integer}] (contador de apertura {2,number,integer})
bioSender.openSocket.failure=¡No pude abrir conector de remitente [{0}:{1,number,integer}]! (contador de fallo de apertura {2,number,integer})
bioSender.send.again=Enviar datos de nuevo a [{0}:{1,number,integer}]
