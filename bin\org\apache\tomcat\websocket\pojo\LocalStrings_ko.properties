# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

pojoEndpointBase.closeSessionFail=오류 처리 중 웹소켓 세션을 닫지 못했습니다.
pojoEndpointBase.onCloseFail=타입이 [{0}]인 POJO를 위한 POJO 엔드포인트의 onClose 메소드를 호출하지 못했습니다.
pojoEndpointBase.onError=[{0}]을(를) 위한 오류 핸들링이 설정되지 않았으며, 다음과 같은 오류가 발생했습니다.
pojoEndpointBase.onErrorFail=타입이 [{0}]인 POJO를 위한 POJO 엔드포인트의 onError를 호출하지 못했습니다.
pojoEndpointBase.onOpenFail=타입이 [{0}]인 POJO를 위한, POJO 엔드포인트의 onOpen 메소드를 호출하지 못했습니다.

pojoEndpointServer.getPojoInstanceFail=타입이 [{0}]인 POJO 인스턴스를 생성하지 못했습니다.

pojoMessageHandlerWhole.decodeIoFail=메시지를 디코딩하는 중 IO 오류 발생
pojoMessageHandlerWhole.maxBufferSize=이 구현을 위해 지원되는 최대 메시지 크기는 Integer.MAX_VALUE입니다.

pojoMethodMapping.decodePathParamFail=경로 파라미터 값 [{0}]을(를), 요구되는 타입 [{1}](으)로 디코드하지 못했습니다.
pojoMethodMapping.duplicateAnnotation=중복된 [{0}] annotation들이 클래스 [{1}]에 존재합니다.
pojoMethodMapping.duplicateLastParam=OnMessage로 annotate된 클래스 [{1}]의 메소드 [{0}]에, 여러 개의 boolean 타입의 (마지막) 파라미터들이 존재합니다.
pojoMethodMapping.duplicateMessageParam=OnMessage로 annotate된 클래스 [{1}]의 메소드 [{0}]에 여러 개의 메시지 파라미터들이 존재합니다.
pojoMethodMapping.duplicatePongMessageParam=OnMessage로 annotate된 클래스 [{1}]의 메소드 [{0}]에 여러 개의 PongMessage 파라미터들이 존재합니다.
pojoMethodMapping.duplicateSessionParam=OnMessage로 annotate된 클래스 [{1}]의 메소드 [{0}]에 여러 개의 세션 파라미터들이 존재합니다.
pojoMethodMapping.invalidDecoder=지정된 타입 [{0}]의 디코더를 생성할 수 없었습니다.
pojoMethodMapping.methodNotPublic=Annotate된 메소드 [{0}]이(가) public 메소드가 아닙니다.
pojoMethodMapping.noDecoder=클래스 [{1}]에서, OnMessage로 annotate된 메소드 [{0}]에 존재하는 메시지 파라미터들을 위한 디코더를 찾을 수 없습니다.
pojoMethodMapping.noPayload=OnMessage로 annotate된 클래스 [{1}]의 메소드 [{0}]에, payload 파라미터가 존재하지 않습니다.
pojoMethodMapping.onErrorNoThrowable=OnError로 annotate된 클래스 [{1}]의 메소드 [{0}]에 Throwable 파라미터가 없습니다.
pojoMethodMapping.paramWithoutAnnotation=@PathParam으로 annotate 되지 않은 클래스 [{2}]의 메소드 [{1}]에서, 타입이 [{0}]인 파라미터가 발견되었습니다.
pojoMethodMapping.partialInputStream=OnMessage로 annotate된 클래스 [{1}]의 메소드 [{0}]에 유효하지 않은 InputStream과 boolean 파라미터들이 존재합니다.
pojoMethodMapping.partialObject=OnMessage로 annotate된 클래스 [{1}]의 메소드 [{0}]에 유효하지 않은 Object와 boolean 파라미터들이 존재합니다.
pojoMethodMapping.partialPong=OnMessage로 annotate된 클래스 [{1}]의 메소드 [{0}]에 존재하는 PongMessage와 boolean 파라미터들은 유효하지 않습니다.
pojoMethodMapping.partialReader=OnMessage로 annotate된 클래스 [{1}]의 메소드 [{0}]에, 유효하지 않은 Reader와 boolean 파라미터들이 존재합니다.
pojoMethodMapping.pongWithPayload=OnMessage로 annotate된 클래스 [{1}]의 메소드 [{0}]에 존재하는 PongMessage와 Message 파라미터들은 유효하지 않습니다.

pojoPathParam.wrongType=타입 [{0}]은(는) 경로 파라미터로서 허용되지 않습니다. @PathParam으로 annotate된 파라미터들은 오직 문자열들, 또는 자바 원시타입들 또는 그것들의 박싱된 버전들이어야 합니다.
