# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

channel.nio.interrupted=当前线程被中断。
channel.nio.ssl.appInputNotEmpty=应用程序输入缓冲区仍然包含数据。数据将会丢失。
channel.nio.ssl.appOutputNotEmpty=应用程序输出缓冲区仍包含数据。数据可能会丢失。
channel.nio.ssl.closeSilentError=如所料，尝试完全关闭连接时出现异常。
channel.nio.ssl.closing=通道处于关闭状态
channel.nio.ssl.eofDuringHandshake=握手期间EOF
channel.nio.ssl.expandNetInBuffer=增加网络入口缓冲到[{0}]字节
channel.nio.ssl.expandNetOutBuffer=正在将网络输出缓冲区扩展到[{0}]字节。
channel.nio.ssl.foundHttp=找到一个明文HTTP请求，它应该是一个加密的TLS连接
channel.nio.ssl.handshakeError=握手错误。
channel.nio.ssl.incompleteHandshake=握手未完成，您必须在读取数据之前完成握手。
channel.nio.ssl.invalidCloseState=无效的关闭状态，不会发送网络数据。
channel.nio.ssl.invalidStatus=意外状态[{0}]。
channel.nio.ssl.netInputNotEmpty=网络输入缓冲区仍然包含数据。握手会失败的。
channel.nio.ssl.netOutputNotEmpty=网络输出缓冲区仍然包含数据。握手会失败的。
channel.nio.ssl.notHandshaking=握手认证期间NOT_HANDSHAKING
channel.nio.ssl.pendingWriteDuringClose=挂起写入，因此网络缓冲区中的剩余数据无法发送SSL close消息，套接字已关闭
channel.nio.ssl.remainingDataDuringClose=网络缓冲区中的剩余数据，无法发送SSL关闭消息，套接字仍将关闭
channel.nio.ssl.sniDefault=无法缓冲足够的数据来确定请求的SNI主机名。使用默认值
channel.nio.ssl.sniHostName=连接[{0}]中提取的SNI主机名称是[{1}]
channel.nio.ssl.timeoutDuringHandshake=握手期间超时。
channel.nio.ssl.unexpectedStatusDuringUnwrap=握手展开期间出现意外状态[{0}]。
channel.nio.ssl.unexpectedStatusDuringWrap=握手WRAP期间出现意外状态[{0}]。
channel.nio.ssl.unwrapFail=无法解包数据，无效状态 [{0}]
channel.nio.ssl.unwrapFailResize=由于缓冲区太小无法解包数据，无效状态 [{0}]
channel.nio.ssl.wrapException=包装期间握手失败
channel.nio.ssl.wrapFail=无法包装数据，状态无效[{0}]

endpoint.accept.fail=套接字接受失败
endpoint.alpn.fail=无法使用[{0}]为ALPN配置终结点。
endpoint.alpn.negotiated=使用ALPN协商的[{0}]协议
endpoint.apr.applyConf=正将OpenSSLConfCmd应用在SSL Context上。
endpoint.apr.checkConf=检查配置OpenSSLConf
endpoint.apr.errApplyConf=不能对SSL上下文应用OpenSSLConf  配置
endpoint.apr.errCheckConf=检查OpenSSLConf错误
endpoint.apr.errMakeConf=无法创建OpenSSLConf 上下文
endpoint.apr.failSslContextMake=无法创建SSLContext。检查AprLifecycleListener中的SSLEngine 是否已启用，AprLifecycleListener是否已正确初始化，以及是否已指定有效的SSLProtocol
endpoint.apr.invalidSslProtocol=为SSLProtocol属性提供了无效值[{0}]
endpoint.apr.maxConnections.running=):APR终结点在运行时不支持MaxConnections的设置。[{0}]的现有值将继续使用。
endpoint.apr.maxConnections.unlimited=APR终结点不支持无限连接。[{0}]的现有值将继续使用。
endpoint.apr.noSendfileWithSSL=启用SSL时，APR/native连接器不支持Sendfile
endpoint.apr.pollAddInvalid=无效企图向一个轮询器中添加一个套接字[{0}]
endpoint.apr.pollError=Poller失败，异常[{0}]：[{1}]
endpoint.apr.pollMergeEvents=为套接字[{0}]创建合并事件[{2}]的合并轮询器事件[{1}]
endpoint.apr.pollUnknownEvent=从轮询器返回的套接字具有无法识别的事件[{0}]
endpoint.apr.remoteport=APR套接字[{0}]已打开，远程端口为[{1}]
endpoint.apr.tooManyCertFiles=证书文件配置超过了
endpoint.debug.channelCloseFail=关闭频道失败
endpoint.debug.destroySocket=将销毁socket [{0}]
endpoint.debug.pollerAdd=添加到addList socket[{0}]，timeout[{1}]，flags[{2}]
endpoint.debug.pollerAddDo=添加到轮询器套接字[{0}]
endpoint.debug.pollerProcess=为事件[{1}]处理套接字[{0}]
endpoint.debug.pollerRemove=正在尝试从轮询器中删除[{0}]。
endpoint.debug.pollerRemoved=已从轮询器中删除[{0}]
endpoint.debug.registerRead=[{0}]的注册读兴趣
endpoint.debug.registerWrite=[{0}]的注册写利息
endpoint.debug.socket=socket [{0}]
endpoint.debug.socketCloseFail=关闭 socket 失败
endpoint.debug.socketTimeout=超时 [{0}]
endpoint.debug.unlock.fail=尝试在端口[{0}]上解除接受锁定时捕获到异常
endpoint.debug.unlock.localFail=无法确定[{0}]的本地地址
endpoint.debug.unlock.localNone=无法解除 [{0}] 的接受器，因为本地地址不可用
endpoint.duplicateSslHostName=为主机名[{0}]提供了多个SSLHostConfig元素。主机名必须唯一
endpoint.err.close=抓住异常试图关闭socket
endpoint.err.handshake=握手失败
endpoint.err.unexpected=处理套接字时意外错误
endpoint.executor.fail=执行器拒绝了用于处理的套接字[{0}]
endpoint.getAttribute=[{0}] 是 [{1}]
endpoint.init.bind=套接字绑定失败: [{0}] [{1}]
endpoint.init.bind.inherited=连接器配置为使用继承通道时没有继承通道
endpoint.init.listen=套接字侦听失败：[{0}][{1}]
endpoint.init.notavail=APR.不可用
endpoint.invalidJmxNameSslHost=无法为与主机[{0}]关联的SSLHostConfig生成有效的JMX对象名
endpoint.invalidJmxNameSslHostCert=对于SSLHostConfigCertificate关联的主机[{0}]和证书类型[{1}]，无法生成有效的XML对象名称
endpoint.jmxRegistrationFailed=未能使用名称[{0}]注册JMX对象
endpoint.jsse.noSslContext=):找不到主机名[{0}]的SSLContext
endpoint.launch.fail=无法启动新的可运行文件
endpoint.nio.registerFail=无法用轮询器中的选择器注册套接字。
endpoint.nio.selectorCloseFail=关闭轮询器时未能关闭选择器
endpoint.nio.stopLatchAwaitFail=在预期的时间内，pollers未停止
endpoint.nio.stopLatchAwaitInterrupted=在等待轮询器停止时，该线程被中断
endpoint.nio.timeoutCme=处理超时异常. 这段代码已经被重复检查并且没有并发修改发现。如果你能重现这个错误，请提交一个tomcat bug, 提供重现步骤.
endpoint.nio2.exclusiveExecutor=NIO2连接器需要一个独占的执行器才能在关机时正常运行
endpoint.noSslHostConfig=没有找到带有hostName[{0}]的SSLHostConfig元素，以匹配连接器[{1}]的默认SSLHostConfigName
endpoint.noSslHostName=SSL主机中没有被提供主机名
endpoint.poll.error=意外的轮询器错误
endpoint.poll.fail=严重轮询器故障（重新启动轮询器）[{0}] [{1}]
endpoint.poll.initfail=轮询器创建失败。
endpoint.poll.limitedpollsize=创建轮询器失败，大小：[{0}]
endpoint.process.fail=分配 socket 处理器出错
endpoint.processing.fail=运行.套接字处理器出错
endpoint.removeDefaultSslHostConfig=默认SSLHostConfig(名为[{0}])可能未被移除
endpoint.sendfile.addfail=发送文件(Sendfile)失败: [{0}] [{1}]
endpoint.sendfile.error=未知的sendfile异常。
endpoint.serverSocket.closeFailed=无法为 [{0}] 关闭服务器 socket
endpoint.setAttribute=设置. [{0}] 到 [{1}]
endpoint.timeout.err=处理套接字超时出错
endpoint.unknownSslHostName=此终结点无法识别SSL主机名[{0}]
endpoint.warn.executorShutdown=与线程池[{0}]关联的执行程序尚未完全关闭。 某些应用程序线程可能仍在运行。
endpoint.warn.incorrectConnectionCount=连接数不正确，在同一个套接字上调用多个socket.close。
endpoint.warn.noLocalAddr=无法确定套接字 [{0}] 的本地地址
endpoint.warn.noLocalName=无法确定 socket [{0}] 的本地主机名
endpoint.warn.noLocalPort=无法确定套接字 [{0}] 的本地端口
endpoint.warn.noRemoteAddr=无法确定套接字[{0}]的远程地址
endpoint.warn.noRemoteHost=无法确定套接字[{0}]的远程主机名
endpoint.warn.noRemotePort=无法确定 socket [{0}] 的远程端口
endpoint.warn.unlockAcceptorFailed=接收器线程[{0}]解锁失败。强制硬套接字关闭。

sniExtractor.clientHelloInvalid=ClientHello信息未正常格式化
sniExtractor.clientHelloTooBig=):ClientHello 没有出现在单个TLS记录中，因此无法提取SNI信息
sniExtractor.tooEarly=在客户端问候被解析之前调用这个方法是非法的

socket.apr.clientAbort=客户端中止连接。
socket.apr.closed=与该链接所关联的 socket [{0}] 被关闭
socket.apr.read.error=从APR/native socket[{1}]中读取带有包装器[{2}]的数据时出现意外错误[{0}]。
socket.apr.write.error=将数据写入APR/native socket[{1}]时出现意外错误[{0}]，包装器为[{2}]。
socket.closed=与此连接关联的套接字已关闭。
socket.sslreneg=重新协商SSL连接时出现异常

socketWrapper.readTimeout=读取超时
socketWrapper.writeTimeout=写入超时

sslHostConfig.certificate.notype=指定了多个证书，并且至少有一个证书缺少必需的属性类型
sslHostConfig.certificateVerificationInvalid=证书认证值[{0}]未识别
sslHostConfig.fileNotFound=配置文件 [{0}] 不存在
sslHostConfig.invalid_truststore_password=提供的信任存储密码无法用于解锁和/或验证信任存储。正在重试使用空密码访问信任存储，该密码将跳过验证。
sslHostConfig.mismatch=属性[{0}]是在名为[{1}]的SSLHostConfig 上设置的，用于[{2}]配置语法，但SSLHostConfig 正与[{3}]配置语法一起使用
sslHostConfig.opensslconf.null=(:忽略设置空OpenSSLConf 的尝试
sslHostConfig.prefix_missing=协议[{0}]已添加到名为[{1}]的SSLHostConfig 上的协议列表中。检查是否缺少一个+/-前缀。

sslHostConfigCertificate.mismatch=属性[{0}]是在名为[{1}]的SSLHostConfigCertificate上设置的，用于证书存储类型[{2}]，但该证书正与类型为[{3}]的存储一起使用。

sslImplementation.cnfe=无法为类 [{0}] 创建SSLImplementation

sslUtilBase.active=活跃的[{0}]是：[{1}]
sslUtilBase.aliasIgnored=FIPS已启用所以别名[{0}]将被忽略。如果key存储中的key大于一个，使用的key将取决于key存储的实现
sslUtilBase.alias_no_key_entry=别名[{0}]不标识密钥项
sslUtilBase.invalidTrustManagerClassName=提供的trustManagerClassName[{0}]未实现javax.net.ssl.TrustManager
sslUtilBase.keystore_load_failed=由于{2}，无法加载路径为{1}]的密钥库类型{0}]
sslUtilBase.noCertFile=使用SSL连接器时必须定义SSLHostConfig属性certificateFile
sslUtilBase.noCrlSupport=truststoreProvider [{0}]不支持certificateRevocationFile配置选项
sslUtilBase.noKeys=在密钥存储中找不到私钥的别名
sslUtilBase.noVerificationDepth=truststoreProvider[{0}]不支持CertificationDepth配置选项
sslUtilBase.noneSupported=SSL引擎不支持指定的[{0}]：[{1}]
sslUtilBase.skipped=某些指定的[{0}]不受SSL引擎支持，已被跳过：[{1}]
sslUtilBase.ssl3=SSLv3 已显式启用。 已知该协议是不安全。
sslUtilBase.tls13.auth=JSSE TLS 1.3实现不支持初始握手后的身份验证，因此与可选的客户端身份验证不兼容
sslUtilBase.trustedCertNotChecked=未检查别名为{0}的受信任证书的有效日期，因为该证书属于未知类型
sslUtilBase.trustedCertNotValid=由于[{2}]，别名为[{0}]且DN [{1}]的可信证书无效。 将接受由此可信证书签署的证书
