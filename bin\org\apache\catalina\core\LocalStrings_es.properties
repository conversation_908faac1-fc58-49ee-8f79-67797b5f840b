# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

applicationContext.addFilter.ise=No se pueden añadir filtros al contexto [{0}] ya que éste ha sido inicializado
applicationContext.addJspFile.iae=El archivo JSP [{0}] no es válido
applicationContext.addListener.iae.cnfe=No puedo crear una instancia del tipo [{0}]
applicationContext.addListener.iae.sclNotAllowed=Una vez que el primer ServletContextListener ha sido llamado, no se pueden añadir más ServletContextListeners.
applicationContext.addListener.iae.wrongType=El tipo especificado [{0}] no es uno de los tipos de escuchador esperados
applicationContext.addListener.ise=No se pueden añadir escuchadores al contexto [{0}], una vez que ha sido inicializado.
applicationContext.addRole.ise=No se pueden añadir roles al contexto [{0}], una vez que ha sido inicializado.
applicationContext.addServlet.ise=No se pueden añadir servlets al contexto [{0}], una vez que ha sido inicializado.
applicationContext.attributeEvent=Excepción lanzada por escuchador de eventos de atributos
applicationContext.invalidServletName=Incapaz de añadir la definición servlet devido a que el nombre servlet no es válido [{0}].
applicationContext.lookup.error=No pude localizar el recurso [{0}] en el contexto [{1}]
applicationContext.mapping.error=Error durante mapeo
applicationContext.requestDispatcher.iae=La Trayectoria [{0}] no comienza con carácter "/"
applicationContext.resourcePaths.iae=La Trayectoria [{0}] no comienza con carácter "/"
applicationContext.role.iae=Un rol individual que se ha de declarar para el contexto [{0}] no puede ser nulo o cadena vacía
applicationContext.roles.iae=Un arreglo de roles que se ha de declarar para el contexto [{0}] no puede ser nulo o cadena vacía
applicationContext.setAttribute.namenull=El nombre no puede ser nulo
applicationContext.setSessionTimeout.ise=El timeout de la sesión no pudo ser fijado para el contexto [{0}] debido a que el contexto ha sido inicializado
applicationContext.setSessionTracking.iae.invalid=El modo de seguimiento de sesión [{0}] requerido para el contexto [{1}] no está soportado por este contexto
applicationContext.setSessionTracking.iae.ssl=Los modos de seguimiento de sesión requeridos para el contexto [{0}], incluyó SSL y al menos otro modo. SSL no se puede configurar con otros modos.
applicationContext.setSessionTracking.ise=No se pueden poner los modos de seguimiento de sesión para el contexto [{0}] mientras el contexto se está ejecutando.

applicationDispatcher.allocateException=Excepción de reserva de espacio para servlet [{0}]
applicationDispatcher.deallocateException=Excepción de recuperación de espacio para servlet [{0}]
applicationDispatcher.forward.ise=No puedo reenviar después de que la respuesta se haya llevado a cabo.
applicationDispatcher.isUnavailable=El Servlet [{0}] no está disponible en este momento
applicationDispatcher.serviceException=El Servlet.service() para servlet [{0}] lanzó una excepción
applicationDispatcher.specViolation.request=SevletRequest original o ServletRequest original arropado no pasó a RequestDispatcher en violación de SRV.8.2 y SRV.********
applicationDispatcher.specViolation.response=SevletResponse original o ServletResponse original arropado no pasó a RequestDispatcher en violación de SRV.8.2 y SRV.********

applicationFilterConfig.jmxRegisterFail=Ha fallado el registro JMX para el filtro del tipo [{0}] y nombre [{1}]
applicationFilterConfig.jmxUnregister=Se ha completado el desregistro JMX para el filtro del tipo [{0}] y nombre [{1}]
applicationFilterConfig.jmxUnregisterFail=Ha fallado el desregistro JMX para el filtro del tipo [{0}] y nombre [{1}]

applicationFilterRegistration.nullInitParam=No puedo poner el parámetro de inicialización para el filtro debido a un nombre nulo y/o valor. Nombre [{0}], Valor [{1}]
applicationFilterRegistration.nullInitParams=No puedo poner los parámetros de inicialización para el filtro debido a un nombre nulo y/o valor. Nombre [{0}], Valor [{1}]

applicationServletRegistration.setServletSecurity.iae=Se ha especificado restricción Null para el servlet [{0}] desplegado en el contexto con el nombre [{1}]
applicationServletRegistration.setServletSecurity.ise=No se pueden añadir restricciones de seguridad al servlet [{0}] desplegado en el contexto con el nombre [{1}] ya que el contexto ya ha sido inicializado.

aprListener.aprDestroy=No pude apagar la biblioteca nativa de Apache Tomcat
aprListener.aprInit=La biblioteca nativa de Apache Tomcat basada en ARP que permite un rendimiento óptimo en entornos de desarrollo no ha sido hallada en java.library.path: [{0}]
aprListener.flags=Capacidades APR: IPv6 [{0}], enviar fichero [{1}], aceptar filtros [{2}], aleatorio [{3}].
aprListener.initializedOpenSSL=OpenSSL inicializado correctamente [{0}]
aprListener.initializingFIPS=Inicializando modo FIPS...
aprListener.sslInit=No pude inicializar el SSLEngine (Motor SSL)
aprListener.tcnInvalid=Se encuentra instalada una versión incompatible [{0}] de la biblioteca nativa APR de Apache Tomcat, mientras que Tomcat necesita la versión [{1}]
aprListener.tcnValid=Cargada la biblioteca nativa APR de Apache Tomcat [{0}] con la versión APR [{1}].
aprListener.tcnVersion=Se encuentra instalada una versión muy vieja [{0}] de la biblioteca nativa APR de Apache Tomcat, mientras que Tomcat recomienda una versión mayor de [{1}]
aprListener.tooLateForFIPSMode=No se pudo fijar  setFIPSMode: SSL ya ha sido inicializado

asyncContextImpl.requestEnded=El requerimiento asociado con AsyncContext ya ha completado su procesamiento.

containerBase.backgroundProcess.cluster=Excepción al procesar clúster [{0}] de proceso en segundo plano
containerBase.backgroundProcess.realm=Excepción al procesar reino [{0}] de proceso en segundo plano
containerBase.backgroundProcess.unexpectedThreadDeath=Muerte inesperada de un hilo en segudo plano [{0}]\n
containerBase.backgroundProcess.valve=Excepción al procesar válvula [{0}] de proceso en segundo plano

defaultInstanceManager.invalidInjection=Método inválido para el recurso de inserción de anotación
defaultInstanceManager.restrictedContainerServlet=El acceso a la clase [{0}] esta prohibido. Esta es una clase restringida  ( implementa la interface ContainerServlet ). Una applicación web debe ser configurada como privilegiada para ser capaz de cargarla
defaultInstanceManager.restrictedFiltersResource=No se ha hallado el fichero de propiedades restringidas de filtros
defaultInstanceManager.restrictedListenersResource=No se ha hallado el fichero de propiedades restringidas de escuchadores
defaultInstanceManager.restrictedServletsResource=No se ha hallado el fichero de propiedades restringidas de servlets

filterChain.filter=La ejecución del Filtro lanzó una excepción
filterChain.servlet=La ejecución del Servlet lanzó una excepción

jreLeakListener.authPolicyFail=Error mientras intentaba prevenir fallos de memoria en javax.security.auth.Policy class
jreLeakListener.gcDaemonFail=No pude disparar la creación del hilo Daemon GC durante el arranque de Tomcat para prevenir posibles fallos de memoria. Esto es lo esperado en JVMs no Sun.
jreLeakListener.jarUrlConnCacheFail=No pude desactivar la caché de conexión URL de Jar por defecto
jreLeakListener.ldapPoolManagerFail=No pude disparar la creación de la clase com.sun.jndi.ldap.LdapPoolManager durante el arranque de Tomcat para prevenir posibles fallos de memoria. Esto es lo que se espera si las JVMs no son Sun.
jreLeakListener.xmlParseFail=Error mientras intentaba prevenir fallos de memoria durante el análisis XML

naming.addEnvEntry=Adicionando entrada de ambiente [{0}]\n
naming.bindFailed=No pude cambiar (bind) objeto: [{0}]
naming.invalidEnvEntryType=La entrada de Entorno [{0}] tiene un tipo inválido
naming.invalidEnvEntryValue=La entrada de Entorno [{0}] tiene un valor inválido
naming.jmxRegistrationFailed=No pude registrar en JMX: [{0}]
naming.namingContextCreationFailed=Falló la creación del contexto de nombres (naming): [{0}]
naming.unbindFailed=No pude descambiar (unbind) objecto: [{0}]
naming.wsdlFailed=No pude hallar fichero wsdl: [{0}]

standardContext.applicationListener=Error configurando escuchador de aplicación de clase [{0}]
standardContext.applicationSkipped=Se ha saltado la instalación de escuchadores de aplicación debido a error(es) previo(s)
standardContext.backgroundProcess.loader=Excepción al procesar cargador [{0}] de proceso en segundo plano
standardContext.backgroundProcess.manager=Excepción al procesar gestor [{0}] de proceso en segundo plano
standardContext.cluster.noManager=No se ha hallado el gestor. Revisando si hay que usar el gestor de clúster. Clúster configurado: [{0}], Aplicación distribuíble: [{1}]
standardContext.duplicateListener=El escuchador [{0}] ya está configurado para este contexto. La definición duplicada ha sido ignorada.
standardContext.errorPage.error=La localización de la página de error [{0}] debe de comenzar con ''/''
standardContext.errorPage.required=ErrorPage no puede ser nulo
standardContext.errorPage.warning=AVISO: La localización de la página de error [{0}] debe de comenzar con ''/'' en Servlet 2.4
standardContext.filterMap.either=El mapeo de filtro debe de especificar o un <url-pattern> o un <servlet-name>
standardContext.filterMap.name=El mapeo de filtro especifica un nombre desconocido de filtro [{0}]
standardContext.filterMap.pattern=<url-pattern> [{0}] inválido en mapeo de filtro
standardContext.filterStart=Excepción arrancando filtro [{0}]
standardContext.invalidWrapperClass=[{0}] no es una subclase de StandardWrapper
standardContext.isUnavailable=Esta aplicación no está disponible en este momento
standardContext.listenerStart=Excepción al enviar evento inicializado de contexto a instancia de escuchador de clase [{0}]
standardContext.listenerStop=Excepción enviando evento de contexto destruído a instancia de escuchador de clase [{0}]
standardContext.loginConfig.errorPage=La Página de error de Formulario [{0}] debe de comenzar con ''/''
standardContext.loginConfig.errorWarning=AVISO: La página de error de Formulario [{0}] debe de comenzar con ''/'' en Servlet 2.4
standardContext.loginConfig.loginPage=La página de login de Formulario [{0}] debe de comenzar con ''/''
standardContext.loginConfig.loginWarning=AVISO: La página de login de Formulario [{0}] debe de comenzar con ''/'' en Servlet 2.4
standardContext.loginConfig.required=LoginConfig no puede ser nula
standardContext.manager=Configurado un gestor de la clase [{0}]
standardContext.managerFail=El manejador de sesiones falló al iniciar
standardContext.namingResource.destroy.fail=No pude destruir recursos de viejo nombre
standardContext.namingResource.init.fail=No pude inicializar recursos de nuevo nombre
standardContext.notStarted=Aún no se ha arrancado el Contexto [{0}]
standardContext.notWrapper=El Hijo de un Contexto debe de ser un Arropador (Wrapper)
standardContext.parameter.duplicate=Duplicado parámetro de inicialización de contexto [{0}]
standardContext.parameter.required=Es necesario poner nombre de parámetro y valor de parámetro
standardContext.pathInvalid=Una ruta de contexto debe de ser o una cadena vacía o comenzar con "/". La ruta [{0}] no cumple con estos criterios y ha sido cambiada por [{1}]
standardContext.predestroy.duplicate=Definición duplicada de método @PreDestroy para clase {0}]
standardContext.reloadingCompleted=Se ha completado la recarga de este Contexto
standardContext.reloadingStarted=Ha comenzado la recarga de Contexto [{0}]
standardContext.requestListener.requestInit=Una excepción durante el envío de requerimiento ha iniciado un evento de ciclo de vida (lifecycle event) para la instancia de clase a la escucha (listener) [{0}]
standardContext.resourcesStart=Error arrancando Recursos estáticos
standardContext.securityConstraint.mixHttpMethod=No está permitido mezclar <http-method> y <http-method-omission> en la misma  colección de recursos web
standardContext.securityConstraint.pattern=<url-pattern> [{0}] inválida en restricción de seguridad
standardContext.servletMap.name=El mapeo de Servlet especifica un nombre de servlet desconocido [{0}]
standardContext.servletMap.pattern=<url-pattern> [{0}] inválida en mapeo de servlet
standardContext.startFailed=Falló en arranque del Contexto [{0}] debido a errores previos
standardContext.startingContext=Excepción arrancando Contexto con nombre [{0}]
standardContext.stoppingContext=Excepción parando Context [{0}]
standardContext.urlPattern.patternWarning=AVISO: el patrón URL [{0}] debe de comenzar con ''/'' en Servlet 2.4
standardContext.workCreateException=No pude determinar directorio absoluto de trabajo a partir del directorio [{0}] y  CATALINA_HOME [{1}] para el contexto [{2}]
standardContext.workCreateFail=No pude crear el directorio de trabajo [{0}] para el contexto [{1}]
standardContext.workPath=Excepción obteniendo ruta de trabajo para el contexto [{0}]

standardContextValve.acknowledgeException=No pude reconocer el requerimiento con una respuesta 100 (Continuar)

standardEngine.jvmRouteFail=No pude poner el atributo jvmRoute del Motor para la propiedad del sistema
standardEngine.notHost=El Hijo de un Motor debe de ser un Máquina
standardEngine.notParent=El Motor no puede tener un Contenedor padre

standardHost.clientAbort=El Cliente Remoto Abortó el Requerimiento, IOException: [{0}]
standardHost.invalidErrorReportValveClass=No pude cargar clase especifiada de válvula de informe de error: [{0}]
standardHost.noContext=No se ha configurado Contexto para procesar este requerimiento
standardHost.notContext=El Hijo de una Máquina debe de ser un Contexto
standardHost.nullName=Es necesario poner el nombre de Máquina

standardServer.shutdownViaPort=Se ha recibido un comando de apagado a través del puerto de apagado. Parando la instancia del Servidor.
standardServer.storeConfig.notAvailable=No se registró ninguna implementación de StoreConfig registrada con nombre n MBean  [{0}] por eso no se pudo salvar la configuración. Un MBean adecuado es normalmente registrado via el StoreConfigLifecycleListener.

standardService.connector.initFailed=No pude inicializar el conector [{0}]
standardService.connector.pauseFailed=No pude pausar el conector [{0}]
standardService.connector.startFailed=No pude arrancar el conector [{0}]
standardService.connector.stopFailed=No pude parar el conector [{0}]
standardService.start.name=Arrancando servicio [{0}]
standardService.stop.name=Parando servicio [{0}]

standardWrapper.allocate=Error reservando espacio para una instancia de servlet
standardWrapper.allocateException=Excepción de reserva de espacio para servlet [{0}]
standardWrapper.deallocateException=Excepción de recuperación de espacio para servlet [{0}]
standardWrapper.destroyException=Servlet.destroy() para servlet [{0}] lanzó excepción
standardWrapper.initException=Servlet.init() para servlet [{0}] lanzó excepción
standardWrapper.instantiate=Error instanciando clase de servlet [{0}]
standardWrapper.isUnavailable=El Servlet [{0}] no está disponible en este momento
standardWrapper.notChild=El contenedor de Arropador (Wrapper) no puede tener contenedores hijo
standardWrapper.notClass=No se ha especificado clase de servlet para servlet [{0}]
standardWrapper.notContext=El contenedor padre para un Arropador (Wrapper) debe de ser un Contexto
standardWrapper.notFound=No está disponible el Servlet [{0}]
standardWrapper.notServlet=La Clase [{0}] no es un Servlet
standardWrapper.serviceException=Servlet.service() para servlet [{0}] lanzó excepción
standardWrapper.serviceExceptionRoot=El Servlet.service() para el servlet [{0}] en el contexto con ruta [{1}] lanzó la excepción [{2}] con causa raíz
standardWrapper.unavailable=Marcando el servlet [{0}] como no disponible
standardWrapper.unloadException=El Servlet [{0}] lanzó excepción unload()
standardWrapper.unloading=No se puede reservar espacio para servlet [{0}] porque está siendo descargado
standardWrapper.waiting=Esperando por [{0}] instancia(s) para recuperar su espacio reservado

threadLocalLeakPreventionListener.containerEvent.error=Excepción procesando evento de contenedor [{0}]
threadLocalLeakPreventionListener.lifecycleEvent.error=Excepción procesando evento de ciclo de vida [{0}]
