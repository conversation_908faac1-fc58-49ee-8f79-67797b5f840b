# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jsseSupport.certTranslationError=인증서 [{0}]에 대한 인증서 변환을 하는 중 오류 발생
jsseSupport.clientCertError=클라이언트로부터 인증서를 구하려 시도하는 중 오류 발생

jsseUtil.excludeProtocol=이 JRE에서 지원되는 해당 SSL 프로토콜 [{0}]이(가), Tomcat의 가용 프로토콜 목록에서 제외되어 있습니다.
jsseUtil.noDefaultProtocols=sslEnabledProtocols의 기본값을 결정할 수 없습니다. Connector가 제대로 시작되는지 보증하려면 명시적으로 값을 설정하십시오.

pemFile.noMultiPrimes=해당 PKCS#1 인증서는 multi-prime 포맷으로 되어 있는데, 자바는 해당 포맷으로부터 RSA 개인 키 객체를 생성할 API를 제공하지 않습니다.
pemFile.notValidRFC5915=제공된 키는 RFC 5915를 따르지 않습니다
pemFile.parseError=[{0}](으)로부터 키를 파싱할 수 없습니다.
