# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

SecurityUtil.doAsPrivilege=Une exception s'est produite lors de l'execution du bloc PrivilegedExceptionAction.

customObjectInputStream.logRequired=Un enregistreur ("logger") valide est requis pour filtrer par nom de classe
customObjectInputStream.nomatch=La classe [{0}] n''est pas acceptée par l''expression régulière [{1}] qui autorise la désérialisation

extensionValidator.extension-not-found-error=ExtensionValidator[{0}][{1}] : L''extension requise [{2}] est introuvable.
extensionValidator.extension-validation-error=ExtensionValidator[{0}] : Impossible de trouver [{1}] extension(s) requise(s).
extensionValidator.failload=Erreur de chargement de l''extension [{0}]
extensionValidator.web-application-manifest=Web Application Manifest

hexUtil.bad=Mauvais digit hexadecimal
hexUtil.odd=Nombre impair de digits hexadecimaux

introspection.classLoadFailed=Echec du chargement de la classe [{0}]

lifecycleBase.alreadyDestroyed=La méthode destroy() a été appelée sur le composant [{0}] après que destroy() ait déjà été appelé, le deuxième appel sera ignoré
lifecycleBase.alreadyStarted=La méthode start() a été appelée sur le composant [{0}] après que start() ait déjà été appelé, le deuxième appel sera ignoré
lifecycleBase.alreadyStopped=La méthode stop() a été appelée sur le composant [{0}] après que stop() ait déjà été appelé, le deuxième appel sera ignoré
lifecycleBase.destroyFail=Echec de la destruction du composant [{0}]
lifecycleBase.destroyStopFail=L''appel de stop() sur le composant en échec [{0}] pour causer un nettoyage a échoué
lifecycleBase.initFail=Echec d''initialisation du composant [{0}]
lifecycleBase.invalidTransition=Un transition de Lifecycle invalide a été tentée ([{0}]) pour le composant [{1}] dans l''état [{2}]
lifecycleBase.setState=Fixe l''état pour [{0}] à [{1}]
lifecycleBase.startFail=Echec de démarrage du composant [{0}]
lifecycleBase.stopFail=Echec de l''arrêt du composant [{0}]

lifecycleMBeanBase.registerFail=Echec de l''enregistrement de l''objet [{0}] avec le nom [{1}] pendant l''initialisation du composant

netmask.cidrNegative=Le CIDR [{0}] est négatif
netmask.cidrNotNumeric=Le CIDR [{0}] n''est pas un nombre
netmask.cidrTooBig=Le CIDR [{0}] est plus grand que la longueur de l''adresse [{1}]
netmask.invalidAddress=L''adresse [{0}] est invalide
netmask.invalidPort=La portion concernant le port dans le modèle [{0}] est invalide

parameterMap.locked=Aucune modification n'est autorisée sur un ParameterMap verrouillé

resourceSet.locked=Aucune modification n'est autorisée sur un ResourceSet verrouillé

sessionIdGeneratorBase.createRandom=La création de l''instance de SecureRandom pour le générateur d''id de session en utilisant [{0}] a pris [{1}] millisecondes
sessionIdGeneratorBase.random=Exception durant l''initialisation de la classe du générateur de nombre aléatoire [{0}]
sessionIdGeneratorBase.randomAlgorithm=Erreur lors de l''initialisation du générateur de nombres aléatoires en utilisant l''algorithme [{0}]
sessionIdGeneratorBase.randomProvider=Exception lors de l''initialisation du générateur de nombres aléatoires utilisant le fournisseur [{0}]
