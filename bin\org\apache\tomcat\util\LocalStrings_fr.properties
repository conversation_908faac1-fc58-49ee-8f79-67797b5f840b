# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

diagnostics.threadDumpTitle=Traces complètes des threads
diagnostics.vmInfoClassCompilation=Compilation de la classe
diagnostics.vmInfoClassLoading=Chargeur de classes
diagnostics.vmInfoGarbageCollectors=Garbage Collector [{0}]
diagnostics.vmInfoLogger=Information sur le journal
diagnostics.vmInfoMemory=Information mémoire
diagnostics.vmInfoMemoryManagers=Gestionnaire de mémoire [{0}]
diagnostics.vmInfoMemoryPools=Pool de mémoire [{0}]
diagnostics.vmInfoOs=Information sur l'OS
diagnostics.vmInfoPath=Imformation de chemin
diagnostics.vmInfoRuntime=Information sur l'environnement d'exécution
diagnostics.vmInfoStartup=Arguments de démarrage
diagnostics.vmInfoSystem=Paramètres système
diagnostics.vmInfoThreadCounts=Nombre de fils d'exécution (threads)
diagnostics.vmInfoThreadMxBean=Capacités de ThreadMXBean
