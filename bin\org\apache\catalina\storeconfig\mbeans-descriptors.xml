<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE mbeans-descriptors PUBLIC
   "-//Apache Software Foundation//DTD Model MBeans Configuration File"
   "http://jakarta.apache.org/commons/dtds/mbeans-descriptors.dtd">
<mbeans-descriptors>

  <mbean         name="StoreConfig"
            className="org.apache.catalina.mbeans.ClassNameMBean"
          description="Implementation of a store server.xml config"
               domain="Catalina"
                group="StoreConfig"
                 type="org.apache.catalina.storeconfig.StoreConfig">
    <operation name="storeConfig"
               description="Store Server"
               impact="ACTION" returnType="void" />
    <operation name="storeServer"
               description="Store Server from ObjectName"
               impact="ACTION" returnType="void" >
         <parameter name="objectname"
                 description="Objectname from Server"
                 type="java.lang.String"/>
         <parameter name="backup"
                 description="store Context with backup"
                 type="boolean"/>
         <parameter name="externalAllowed"
                 description="store all Context external that have a configFile"
                 type="boolean"/>
    </operation>

<!--
   Catalina:j2eeType=WebModule,name=//localhost/manager,J2EEApplication=none,J2EEServer=none
-->
   <operation name="storeContext"
               description="Store Context from ObjectName"
               impact="ACTION" returnType="void" >
         <parameter name="objectname"
                 description="ObjectName from Context"
                 type="java.lang.String"/>
         <parameter name="backup"
                 description="store with Backup"
                 type="boolean"/>
         <parameter name="externalAllowed"
                 description="store all or store only internal server.xml context (configFile == null)"
                 type="boolean"/>
    </operation>
    <operation name="store"
               description="Store Server"
               impact="ACTION" returnType="void" >
          <parameter name="server"
                 description="Server"
                 type="org.apache.catalina.Server"
                 />
    </operation>
    <operation name="store"
               description="Store Context"
               impact="ACTION" returnType="void" >
          <parameter name="context"
                 description="Context"
                 type="org.apache.catalina.Context"/>
    </operation>
    <operation name="store"
               description="Store Host"
               impact="ACTION" returnType="void" >
          <parameter name="host"
                 description="Host"
                 type="org.apache.catalina.Host"/>
    </operation>
    <operation name="store"
               description="Store Service"
               impact="ACTION" returnType="void" >
          <parameter name="service"
                 description="service"
                 type="org.apache.catalina.Service"/>
    </operation>

  </mbean>

</mbeans-descriptors>


