# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

digesterFactory.missingSchema=XML模型[{0}]未找到，如果XML校验功能开启了的话，这很可能终止XML校验

localResolver.unresolvedEntity=不能解决XML资源[{0}]与公共ID[{1}],系统ID[{2}]和基础URI[{3}]到一个已知,当地的实体。

xmlErrorHandler.error=非致命错误[{0}]报告正在处理[{1}]。
xmlErrorHandler.warning=警告[{0}]报告处理[{1}]。
