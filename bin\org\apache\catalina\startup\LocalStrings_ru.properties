# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

catalina.serverStartFail=Томкат не смог запуститься из-за того что обязательный компонент не смог запуститься

contextConfig.applicationUrl=Не возможно определить URL для web.xml приложения
contextConfig.defaultPosition=Произошло в строке [{0}] столбце [{1}]

hostConfig.deployDir=Установка веб приложения в папку [{0}]

userConfig.database=Ошибка при загрузке базы данных пользователей

versionLoggerListener.catalina.base=CATALINA_BASE:         {0}
versionLoggerListener.catalina.home=CATALINA_HOME:         {0}
versionLoggerListener.os.arch=Архитектура:           {0}
versionLoggerListener.os.version=Версия ОС:             {0}
versionLoggerListener.vm.version=Версия JVM:            {0}
