<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE mbeans-descriptors PUBLIC
   "-//Apache Software Foundation//DTD Model MBeans Configuration File"
   "http://jakarta.apache.org/commons/dtds/mbeans-descriptors.dtd">
<mbeans-descriptors>

  <mbean         name="JspMonitor"
          description="JSP Monitoring"
               domain="Catalina"
                group="Monitoring"
                 type="org.apache.jasper.servlet.JspServlet">

    <attribute   name="jspCount"
          description="The number of JSPs that have been loaded into a webapp"
                 type="int"
                 writeable="false"/>

    <attribute   name="jspReloadCount"
          description="The number of JSPs that have been reloaded"
                 type="int"/>

    <attribute   name="jspUnloadCount"
          description="The number of JSPs that have been unloaded"
                 type="int"/>

    <attribute   name="jspQueueLength"
          description="The length of the JSP queue (if enabled via maxLoadedJsps)"
                 type="int"/>

  </mbean>

</mbeans-descriptors>
