# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

elProcessor.defineFunctionInvalidMethod=El método [{0}] en la clase [{1}] no es un método estático público

importHandler.ambiguousStaticImport=La importación estática [{0}] no puede ser procesada pues entra en conflicto con [{1}] la cual ya ha sido importada
importHandler.classNotFound=La clase [{0}] no puede ser importada debido a que no fué encontrada
importHandler.invalidClassNameForStatic=La clase [{0}] especificada para importación estática  [{1}] no es valida
importHandler.staticNotFound=La importación estática [{0}] no se pudo encontrar en la clase [{1}] para importar [{2}]

objectNotAssignable=No puedo añadir un objeto del tipo [{0}] a un arreglo de objetos del tipo [{1}]
propertyNotFound=Propiedad [{1}] no hallada en el tipo [{0}]
propertyNotReadable=Propiedad [{1}] no legible para el tipo [{0}]
propertyNotWritable=Propiedad [{1}] no grabable para el tipo [{0}]
propertyReadError=Error reading [{1}] en el tipo [{0}]
propertyWriteError=Error writing [{1}] en el tipo [{0}]
