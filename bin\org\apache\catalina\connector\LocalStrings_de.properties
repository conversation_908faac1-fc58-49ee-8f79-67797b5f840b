# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

coyoteAdapter.checkRecycled.response=Eine nicht recycelte Antwort wurde erkannt und recylet
coyoteAdapter.debug=Die Variable [{0}] hat den Wert [{1}]

coyoteConnector.invalidEncoding=Das Encoding [{0}] wird von der JRE nicht erkannt. Der Konnektor wird weiterhin [{1}] nutzen
coyoteConnector.invalidPort=Der Konnektor kann nicht starten, da der als Port angegebene Wert [{0}] nicht gültig ist.
coyoteConnector.protocolHandlerStartFailed=Der Start des Protokoll-Handlers ist fehlgeschlagen

coyoteRequest.filterAsyncSupportUnknown=Es konnte nicht ermittelt werden ob einer der Filter asyncrone Bearbeitung nicht unterstützt
coyoteRequest.gssLifetimeFail=Die verbleibende Lebenzeit für den Principal [{0}] konnte nicht ermittelt werden.

responseFacade.nullResponse=Das Response Objekt ist wiederverwendet worden und nicht mehr mit der Facade verknüpft.
