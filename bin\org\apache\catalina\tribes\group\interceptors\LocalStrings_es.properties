# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

domainFilterInterceptor.message.refused=El mensaje [{0}] recibido del cluster fue rechazado

encryptInterceptor.decrypt.error.short-message=Fallo al descifrar el mensaje: fin-de-mensaje prematuro

messageDispatchInterceptor.queue.full=La cola asincrónica esta llena, se alcanzó el limite de  [{0}] bytes, actualmente:[{1}] bytes.\n

nonBlockingCoordinator.memberAlive.failed=No se puede verificar si el miembro esta vivo, asumiendo que el miembro esta inactivo.
nonBlockingCoordinator.processCoordinationMessage.failed=Error procesando el mensaje de coordinación. Puede ser fatal.\n

staticMembershipInterceptor.no.pingInterceptor=No existe TcpPingInterceptor. El verificador de estado de miembros estáticos no trabaja correctamente. Al definir el TcpPingInterceptor, el verificador de estado de miembros estáticos trabajará correctamente.
staticMembershipInterceptor.sendShutdown.failed=El aviso de apagado falló.

tcpFailureDetector.failureDetection.failed=No se pudo realizar la verificación de detección de fallos, se asume que el membro esta abajo.[{0}]
tcpFailureDetector.heartbeat.failed=Imposible ejecutar heartbeat en el TcpFailureDetector.
tcpFailureDetector.member.disappeared=Verificación completada. Miembro desaparecido[{0}]
tcpFailureDetector.still.alive=Verificación completa. El miembro aun esta vivo [{0}]
tcpFailureDetector.suspectMember.alive=Se confima que esta vivo el miembro.[{0}]\n

tcpPingInterceptor.ping.failed=Imposible enviar ping TCP

throughputInterceptor.report=ThroughputInterceptor Reporte[\n\
\tTx Msg:{0} mensajes\n\
\tEnviados:{2} MB (aplicación)\n\
\tTiempo:{3} segundos\n\
\tTx Speed:{4} MB/seg(total)\n\
\tTx Speed::{5} MB/seg(aplicación)\n\
\tMsg error:{6}\n\
\tRx Msg:{7} mensajes\n\
\tRx Speed:{8} MB/sec (desde 1er msg)\n\
\tRecivido:{9} MB]

twoPhaseCommitInterceptor.heartbeat.failed=Incapáz de ejecutar heartbeat en el interceptor TwoPhaseCommit
