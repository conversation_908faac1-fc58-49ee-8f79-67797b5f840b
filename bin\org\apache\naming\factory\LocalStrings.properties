# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

dataSourceLinkFactory.badWrapper=Not a wrapper for type [{0}]

factoryBase.factoryClassError=Could not load resource factory class
factoryBase.factoryCreationError=Could not create resource factory instance
factoryBase.instanceCreationError=Could not create resource instance

lookupFactory.circularReference=Found a circular reference involving [{0}]
lookupFactory.createFailed=Could not create instance of JNDI lookup factory class
lookupFactory.loadFailed=Could not load JNDI lookup factory class
lookupFactory.typeMismatch=The JNDI reference [{0}] was expected to be of type [{1}] but the lookup [{2}] return an object of type [{3}]

resourceFactory.factoryCreationError=Could not create resource factory instance

resourceLinkFactory.invalidGlobalContext=Caller provided invalid global context
resourceLinkFactory.nullType=The local resource link [{0}] that refers to global resource [{1}] does not specify the required attribute type
resourceLinkFactory.unknownType=The local resource link [{0}] that refers to global resource [{1}] specified the unknown type [{2}]
resourceLinkFactory.wrongType=The local resource link [{0}] that refers to global resource [{1}] was expected to return an instance of [{2}] but returned an instance of [{3}]
