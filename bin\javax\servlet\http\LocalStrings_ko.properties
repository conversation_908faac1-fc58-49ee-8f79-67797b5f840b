# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

err.cookie_name_blank=쿠키 이름이 널이거나 길이가 0인 문자열이어서는 안됩니다.
err.cookie_name_is_token=쿠키 이름 [{0}]은(는) 예약된 토큰입니다.
err.io.indexOutOfBounds=크기 [{2}]인 배열에 대하여, 유효하지 않은 offset [{0}] 그리고/또는 길이 [{1}].
err.io.nullArray=write 메소드에 널인 바이트 배열이 전달되었습니다.
err.io.short_read=Short Read

http.method_delete_not_supported=HTTP 메소드 DELETE는 이 URL에 의해 지원되지 않습니다.
http.method_get_not_supported=HTTP 메소드 GET은 이 URL에 의해 지원되지 않습니다.
http.method_not_implemented=이 URI를 위한 서블릿은 메소드 [{0}]을(를) 구현하지 않았습니다.
http.method_post_not_supported=HTTP 메소드인 POST는 이 URL에 의해 지원되지 않습니다.
http.method_put_not_supported=HTTP 메소드 PUT은 이 URL에 의해 지원되지 않습니다.
http.non_http=HTTP 요청이 아니거나, HTTP 응답이 아닙니다.
