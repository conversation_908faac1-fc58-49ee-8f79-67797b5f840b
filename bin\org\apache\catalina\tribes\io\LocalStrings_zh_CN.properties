# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

bufferPool.created=已创建缓冲池，最大大小为：[{0}]字节，类型为：[{1}]

objectReader.retrieveFailed.socketReceiverBufferSize=无法检索套接字接收器缓冲区大小，设置为默认43800字节。

replicationStream.conflict=和非公开接口类加载器冲突

xByteBuffer.discarded.invalidHeader=丢弃了包，头无效。
xByteBuffer.no.package=XByteBuffer中不存在数据包
xByteBuffer.size.larger.buffer=大小比现有缓冲区大。
xByteBuffer.unableCreate=不能创建数据包， buffer 太小
xByteBuffer.unableTrim=修剪的字节数不能超过可用字节数。长度：[{0}]修剪：[{1}]
xByteBuffer.wrong.class=消息对应类不符合要求。 它应该实现Serializable，而不是：[{0}]。
