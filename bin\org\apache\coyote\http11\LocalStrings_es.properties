# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

http11processor.header.parse=Error analizando cabecera de requerimiento HTTP
http11processor.request.finish=Error acabando requerimiento
http11processor.request.prepare=Error preparando solicitud
http11processor.request.process=Error procesando requerimiento
http11processor.response.finish=Error acabando respuesta
http11processor.socket.info=Excepción obteniendo información de conector

iib.eof.error=Inesperado Fin De Archivo (EOF) leído en el enchufe (socket)
iib.invalidmethod=Se encontró un carácter inválido en el nombre del método. Los nombres de métodos HTTP deben ser tokens
iib.parseheaders.ise.error=Estado inesperado: las cabeceras ya fueron parseadas. No se ha reciclado el buffer??
iib.requestheadertoolarge.error=La cabecera del requerimiento es demasido grande

iob.failedwrite=Fallo de escritura
iob.responseheadertoolarge.error=Un intento para escribir más datos en las cabeceras de respuesta escribir fue hecho, sin embargo no hay más espacio disponible en el bufer. Aumente el parámetro maxHttpHeaderSize en el conector o escribe menos datos en las respuestas de las cabeceras.\n
