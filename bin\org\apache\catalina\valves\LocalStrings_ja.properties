# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

accessLogValve.alreadyExists=[{0}]から[{1}]へのアクセスログの名前の変更に失敗しました。ファイルはすでに存在しています。
accessLogValve.closeFail=アクセスログのクローズに失敗しました
accessLogValve.deleteFail=古いアクセスログ[{0}]を削除できませんでした。
accessLogValve.invalidLocale=[{0}] をロケールに設定できませんでした。
accessLogValve.invalidPortType=不正なポート種類 [{0}] の代わりにサーバーのローカルポートを使用します。
accessLogValve.openDirFail=アクセスログのディレクトリ[{0}]の作成に失敗しました
accessLogValve.openFail=アクセスログファイル [{0}] を開けません。
accessLogValve.renameFail=[{0}]から[{1}]へのアクセスログの名前の変更に失敗しました。
accessLogValve.rotateFail=アクセスログのローテーションに失敗しました
accessLogValve.unsupportedEncoding=文字エンコーディングに [{0}] を指定できません。システムの既定値を使用します。
accessLogValve.writeFail=ログメッセージ[{0}]の書き込みに失敗しました

errorReportValve.description=説明
errorReportValve.exception=例外
errorReportValve.exceptionReport=例外報告
errorReportValve.message=メッセージ
errorReportValve.noDescription=説明はありません
errorReportValve.note=注意
errorReportValve.rootCause=根本原因
errorReportValve.rootCauseInLogs=原因のすべてのスタックトレースは、サーバのログに記録されています
errorReportValve.statusHeader=HTTPステータス {0} – {1}
errorReportValve.statusReport=ステータスレポート
errorReportValve.type=タイプ
errorReportValve.unknownReason=未知の理由

extendedAccessLogValve.badXParam=無効なxパラメータフォーマットです。 'x-#(...)にする必要があります。
extendedAccessLogValve.badXParamValue=サーブレットリクエスト[{0}]の無効なxパラメータ値
extendedAccessLogValve.decodeError=[{0}]で始まる残りの文字をデコードできません
extendedAccessLogValve.emptyPattern=パターン文字列が空です。もしくは空白だけで構成されています。
extendedAccessLogValve.noClosing=終了）がデコードで見つかりません。
extendedAccessLogValve.patternParseError=パターン文字列 [{0}] を解析できませんでした。

http.400.desc=サーバは、クライアントエラー（例えば、不正なリクエスト構文、無効なリクエストメッセージフレーミング、または不正なリクエストルーティング）であると考えられるために、リクエストを処理できない、または処理しません。
http.400.reason=Bad Request
http.401.desc=リクエストには対象リソースの有効な認証資格がないため、適用されていません。
http.401.reason=Unauthorized
http.402.desc=このステータスコードは、将来の使用のために予約されています
http.402.reason=Payment Required
http.403.desc=サーバーはリクエストの認証を拒否しました。
http.403.reason=Forbidden
http.404.desc=オリジンサーバーは、ターゲットリソースの現在の表現を見つけられなかったか、またはそれが存在することを開示するつもりはありません。
http.404.reason=見つかりません。
http.405.desc=リクエストラインで受信されたメソッドは、オリジンサーバーによって認識されますが、ターゲットリソースによってサポートされていません。
http.405.reason=Method Not Allowed
http.406.desc=ターゲットリソースは、リクエストで受け取ったプロアクティブなネゴシエーションヘッダフィールドに従って、ユーザエージェントが受け入れられる現在の表現を持たず、サーバはデフォルトの表現を提供することを望ましくありません。
http.406.reason=Not Acceptable
http.407.desc=このステータスコードは401（Unauthorized）に似ていますが、クライアントがプロキシを使用するために自身を認証する必要があることを示します。
http.407.reason=プロキシ認証が必要です。
http.408.desc=サーバーは、待機用に準備された時間内に完全なリクエストメッセージを受信しませんでした。
http.408.reason=リクエストタイムアウト
http.409.desc=ターゲットリソースの現在の状態との競合のためにリクエストを完了できませんでした。
http.409.reason=Conflict
http.410.desc=オリジンサーバーでターゲットリソースへのアクセスが利用できなくなり、この状態が永続的になる可能性があります。
http.410.reason=Gone
http.411.desc=サーバーは、定義されたContent-Lengthなしでリクエストを受け入れることを拒否します。
http.411.reason=Length Required
http.412.desc=リクエストヘッダーフィールドに指定された1つ以上の条件が、サーバー上でテストされたときにfalseに評価されました。
http.412.reason=前提条件失敗
http.413.desc=リクエストペイロードがサーバーが処理できる、または処理できるよりも大きいため、サーバーはリクエストの処理を拒否しています。
http.413.reason=Payload Too Large
http.414.desc=リクエストの対象がサーバーが解釈しようとするよりも長いため、サーバーはリクエストのサービスを拒否しています。
http.414.reason=URI Too Long
http.415.desc=ペイロードがターゲットリソース上のこのメソッドでサポートされていない形式であるため、オリジンサーバーはリクエストを処理することを拒否しています。
http.415.reason=未対応のメディアタイプです。
http.416.desc=リクエストのRangeヘッダーフィールドの範囲のいずれも、選択されたリソースの現在のエクステントと重複しないか、無効な範囲または小さすぎる範囲または重複する範囲の過剰なリクエストのためにリクエストされた範囲の集合が拒否されました。
http.416.reason=Range Not Satisfiable
http.417.desc=リクエストのExpectヘッダーフィールドで指定された期待値が、少なくとも1つのインバウンドサーバーで満たされていない可能性があります。
http.417.reason=Expectation Failed
http.421.desc=リクエストはレスポンスを生成できないサーバーに向けられました。
http.421.reason=Misdirected Request
http.422.desc=サーバーはリクエストエンティティのコンテンツタイプを理解しており、リクエストエンティティの構文は正しいものの、含まれている命令を処理できませんでした。
http.422.reason=Unprocessable Entity
http.423.desc=メソッド呼び出しの依頼元リソース、あるいは依頼先リソースはロックされています。
http.423.reason=ロックされています
http.424.desc=要求されたアクションが別のアクションに依存し、そのアクションが失敗したため、このメソッドはリソース上で実行できませんでした。
http.424.reason=Failed Dependency
http.426.desc=サーバーは現在のプロトコルを使用してリクエストを実行することを拒否しますが、クライアントが別のプロトコルにアップグレードした後にその要求を実行する可能性があります。
http.426.reason=アップグレードが必要です。
http.428.desc=オリジンサーバーは、要求が条件付きであることを要求します。
http.428.reason=Precondition Required
http.429.desc=ユーザーが指定した時間内に多くのリクエストを送信しました（レート制限）。
http.429.reason=大量のリクエストが発生しています。
http.431.desc=ヘッダーフィールドが大きすぎるため、サーバーはリクエストをすすんで処理しません。
http.431.reason=リクエストヘッダフィールドが大き過ぎます。
http.451.desc=サーバーはこのリクエストを法的理由で拒否しました。
http.451.reason=Unavailable For Legal Reasons
http.500.desc=サーバーは予期しない条件に遭遇しました。それはリクエストの実行を妨げます。
http.500.reason=Internal Server Error
http.501.desc=サーバーは、リクエストを実行するために必要な機能をサポートしていません。
http.501.reason=Not Implemented
http.502.desc=ゲートウェイあるいはプロキシサーバーからリクエストを試みた内部サーバーから不正なレスポンスを受信しました。
http.502.reason=Bad Gateway
http.503.desc=サーバーは、一時的な過負荷または定期保守のために現在リクエストを処理できません。遅れて緩和される可能性があります。
http.503.reason=Service Unavailable
http.504.desc=ゲートウェイまたはプロキシとして機能しているサーバーは、リクエストを完了するためにアクセスする必要のある上流のサーバーからタイムリーなレスポンスを受信しませんでした。
http.504.reason=ゲートウェイタイムアウト
http.505.desc=サーバーは、リクエストメッセージで使用されたメジャーバージョンのHTTPをサポートしていないか、またはサポートを拒否しています。
http.505.reason=サポートされていないHTTPバージョン
http.506.desc=サーバーには内部構成エラーがあります。選択された可変リソースは透過的なコンテンツネゴシエーション自体に関与するように構成されているため、ネゴシエーションプロセスの適切なエンドポイントではありません。
http.506.reason=Variant Also Negotiates
http.507.desc=サーバーがリクエストを正常に完了するのに必要な表現を保管できないため、このメソッドをリソースに対して実行できませんでした。
http.507.reason=ストレージに充分な空き容量がありません。
http.508.desc=サーバーは、 "Depth：infinity"でリクエストを処理している間に無限ループを検出したため、操作を終了しました。
http.508.reason=Loop Detected
http.510.desc=リクエストにリソースにアクセスするためのポリシーが満たされていません。
http.510.reason=Not Extended
http.511.desc=クライアントはネットワークアクセスを取得するために認証する必要があります。
http.511.reason=Network Authentication Required

jdbcAccessLogValve.close=データベースのクローズに失敗しました。
jdbcAccessLogValve.exception=アクセスエントリの挿入を実行中の例外です

remoteCidrValve.invalid="[{0}]" に不正な値が指定されました。詳細は前のメッセージを参照してください。
remoteCidrValve.noRemoteIp=クライアントの IP アドレスを取得できません。リクエストを拒否します。

remoteIpValve.invalidHostHeader=HTTP ヘッダ [{1}] 中の Host に無効な値 [{0}] が見つかりました
remoteIpValve.invalidHostWithPort=HTTP ヘッダ [{1}] 中の Host の値 [{0}] はポート番号を含んでいますが無視されます
remoteIpValve.invalidPortHeader=HTTP ヘッダー [{1}] に不正なポート番号 [{0}] が指定されました。

requestFilterValve.configInvalid=Valveとその親コンテナの起動を妨げたRemote [Addr | Host] Valveに1つ以上の無効な構成設定が提供されました。
requestFilterValve.deny=プロパティ [{1}] により [{0}] へのリクエストを拒否しました。

sslValve.certError=java.security.cert.X509Certificateオブジェクトを生成するための証明書文字列[{0}]の処理に失敗しました。
sslValve.invalidProvider=リクエスト [{0}] に関連付けられたコネクターに指定された SSL プロバイダーは不正です。証明書データを処理できません。

stuckThreadDetectionValve.notifyStuckThreadCompleted=スレッド[{0}]（id = [{3}]）は以前にスタックされていると報告されましたが完了しました。それはおよそ[{1}]ミリ秒の間アクティブだった。\n\
\ {2,choice,0#|0< このバルブによって監視されているスレッド [{2}] は残っていますが、スタックされている可能性があります。}
stuckThreadDetectionValve.notifyStuckThreadDetected=スレッド[{0}]（ID = [{6}]）は[{1}]ミリ秒（[{2}以降）から[{4}]に対する同じリクエストを処理するためにアクティブであり、スタックされている可能性があります(このStuckThreadDetectionValveの設定されたしきい値(threshold )は[{5}]秒です）。このValveによって監視されているスレッドは合計で[{3}]個あり、スタックされている可能性があります。
stuckThreadDetectionValve.notifyStuckThreadInterrupted=スレッド [{0}] (id=[{5}]) に割り込みが発生しました。[{3}] に対するリクエストの処理時間が [{1}] ミリ秒 ([{2}] から開始) を超過したため処理が進まなくなっている可能性があります。StuckThreadDetectionValve には割り込みが発生するまでの時間 [{4}] 秒が設定されています。
