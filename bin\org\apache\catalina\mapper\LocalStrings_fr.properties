# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

mapper.addHost.sameHost=L''enregistrement en double de l''hôte [{0}] est ignoré
mapper.addHost.success=Enregistré l''hôte [{0}]
mapper.addHostAlias.sameHost=L''enregistrement en double de l''alias [{0}] de l''hôte [{1}] est ignoré
mapper.addHostAlias.success=L''alias [{0}] pour le hôte [{1}] a été enregistré
mapper.duplicateHost=L''hôte [{0}] est en double et sera ignoré, le nom est déjà utilisé par l''hôte [{1}]
mapper.duplicateHostAlias=L''alias [{0}] de l''hôte [{1}] est en double et sera ignoré, le nom est déjà utilisé par l''hôte [{2}]
mapper.removeWrapper=Retire l''enrobeur du contexte [{0}] avec le chemin [{1}]

mapperListener.pauseContext=Enregistrement du contexte [{0}] comme étant en cours de rechargement dans le service [{1}]
mapperListener.registerContext=Enregistrement du contexte [{0}] pour le service [{1}]
mapperListener.registerHost=Enregistrement de l''hôte [{0}] dans le domaine [{1}] pour le service [{2}]
mapperListener.registerWrapper=Enregistrement du wrapper [{0}] dans le contexte [{1}] pour le service [{2}]
mapperListener.unknownDefaultHost=L''hôte par défaut [{0}] est inconnu dans le service [{1}], Tomcat ne sera pas capable de traiter les requêtes HTTP/1.0 qui ne spécifient pas de nom d''hôte
mapperListener.unregisterContext=Retrait du Context [{0}] pour le service [{1}]
mapperListener.unregisterHost=Retrait de l''hôte [{0}] dans le domaine [{1}] pour le service [{2}]
mapperListener.unregisterWrapper=Désenregitrement du wrapper [{0}] dans le contexte [{1}] pour le service [{2}]
