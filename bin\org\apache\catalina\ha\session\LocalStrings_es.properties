# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

backupManager.startUnable=Imposible de iniciar BackupManager: [{0}]\n

deltaManager.createMessage.access=Gestor [{0}]: creado mensaje de sesión [{1}] acceso.
deltaManager.createMessage.accessChangePrimary=Gestor [{0}]: creado mensaje de sesión [{1}] acceso para cambiar el primario.
deltaManager.createMessage.allSessionData=Gestor [{0}] envía todos los datos de sesión.
deltaManager.createMessage.allSessionTransferred=Gestor [{0}] envía todos los datos de sesión transferidos
deltaManager.createMessage.delta=Gestor [{0}]: crea mensaje de sesión [{1}] de requerimiento delta.
deltaManager.createMessage.expire=Gestor [{0}]: crea mensaje de sesión [{1}] de expiración.
deltaManager.createMessage.unableCreateDeltaRequest=No puedo serializar requerimiento delta para la id de sesión [{0}]
deltaManager.createSession.newSession=Creada una DeltaSession con Id [{0}] Total contador=[{1}]
deltaManager.dropMessage=Gestor [{0}]: Quita mensaje [{1}] dentro de fase sincronizada GET_ALL_SESSIONS fecha inicio [{2}] fecha mensaje [{3}]
deltaManager.expireSessions=Gestor [{0}] expirando sesiones al apagar
deltaManager.foundMasterMember=Hallado para contexto [{0}] el miembro maestro de réplica [{1}]
deltaManager.loading.cnfe=ClassNotFoundException al cargar sesiones persistentes: [{0}]
deltaManager.loading.existing.session=sobrecarga en sesión existente [{0}]
deltaManager.loading.ioe=IOException al cargar sesiones persistentes: [{0}]
deltaManager.managerLoad=Excepción cargando sesiones desde almacenaje persistente
deltaManager.noCluster=Arrancando... no hay clúster asociado con este contexto: [{0}]
deltaManager.noContextManager=Manejador [{0}]: En respuesta al mensaje ''Tomar todos los datos de sesión (Get all session data)'' enviado a [{1}], recibión el mensaje ''El manejador no machea con ningún contexto (No matching context manager)'' luego de [{2}] ms.\n
deltaManager.noMasterMember=Arrancando... sin otro miembro para el contexto [{0}] en dominio [{1}]
deltaManager.noMembers=Gestor [{0}]: saltando estado de transferencia. No hay miembros activos en grupo de clúster.
deltaManager.noSessionState=Gestor [{0}]: No se ha recibido estado de sesión a las [{1}], agotando tiempo tras [{2}] ms.
deltaManager.receiveMessage.accessed=Gestor [{0}]: accedida sesión [{1}] recibida.
deltaManager.receiveMessage.allSessionDataAfter=Gestor [{0}]: estado de sesión deserializado
deltaManager.receiveMessage.allSessionDataBegin=Gestor [{0}]: recibidos datos de estado de sesión
deltaManager.receiveMessage.createNewSession=Gestor [{0}]: creada sesión [{1}] recibida.
deltaManager.receiveMessage.delta=Gestor [{0}]: delta sesión [{1}] recibida.
deltaManager.receiveMessage.delta.unknown=Manejador [{0}]: recibió una diferencia de sesión para una sesión desconocida [{1}]
deltaManager.receiveMessage.error=Gestor [{0}]: No puedo recibir mensaje a través del canal TCP
deltaManager.receiveMessage.eventType=Gestor [{0}]: recibido SessionMessage de tipo=[{1}] desde [{2}]
deltaManager.receiveMessage.expired=Gestor [{0}]: expirada sesión [{1}] recibida.
deltaManager.receiveMessage.transfercomplete=Gestor [{0}] recibido desde nodo [{1}:{2}] estado de sesión transferido.
deltaManager.receiveMessage.unloadingAfter=Gestor [{0}]: completada la descarga de sesiones
deltaManager.receiveMessage.unloadingBegin=Gestor [{0}]: iniciada descarga de sesiones
deltaManager.registerCluster=Registrar gestor [{0}] a elemento de clúster [{1}] con nombre [{2}]
deltaManager.sendMessage.newSession=El gestor [{0}] envía nueva sesión [{1}]
deltaManager.sessionReceived=Gestor [{0}]; estado de sesión enviado a las [{1}] recibido en [{2}] ms.
deltaManager.startClustering=Iniciando gestor de clúster a las [{0}]
deltaManager.stopped=El gestor [{0}] se está parando
deltaManager.unableSerializeSessionID=No puedo seriallizar la ID de sesión [{0}]
deltaManager.unloading.ioe=IOException al grabar sesiones persistentes: [{0}]
deltaManager.waitForSessionState=Gestor [{0}], requiriendo estado de sesión desde [{1}]. Esta operación se agotará si no se recibe estado de sesión dentro de [{2}] segundos.

deltaRequest.removeUnable=Imposible eliminar elemento:
deltaRequest.showPrincipal=El Principal [{0}] está puesto a sesión [{1}]
deltaRequest.wrongPrincipalClass=DeltaManager sólo soporta GenericPrincipal. Tu reino utilizó clase principal [{0}].

deltaSession.notifying=Notificando clúster de expiración manager [{0}], primary [{1}], sessionId [{2}]
deltaSession.readSession=readObject() cargando sesión [{0}]
deltaSession.writeSession=writeObject() guardando sesión [{0}]

jvmRoute.cannotFindSession=No puedo hallar sesión [{0}]
jvmRoute.changeSession=Cambiada sesión desde [{0}] a [{1}]
jvmRoute.failover=Detectada una caída con diferente jvmRoute - ruta original: [{0}] nueva: [{1}] en id de sesión [{2}]
jvmRoute.foundManager=Hallado Clúster DeltaManager [{0}] en [{1}]
jvmRoute.missingJvmRouteAttribute=¡No se ha configurado atributo de motor jvmRoute!
jvmRoute.noCluster=La válvula JvmRouterBinderValve se encuentra configurada, pero no se usa el clúster. Aún funcionará la tolerancia a fallos, siempre que se esté usando PersistentManager.
jvmRoute.notFoundManager=No hallado Clúster DeltaManager [{0}] en [{1}]
jvmRoute.set.originalsessionid=Puesta id Orginal de Sesión en atributo de requerimiento [{0}] valor: [{1}]
jvmRoute.turnoverInfo=Ajustado tiempo de Chequeo a [{0}] mseg
jvmRoute.valve.started=JvmRouteBinderValve arrancada
jvmRoute.valve.stopped=JvmRouteBinderValve parada

standardSession.notSerializable=No puedo serializar atributo de sesión [{0}] para sesión [{1}]
standardSession.removeAttribute.ise=removeAttribute: Sesión ya invalidada
standardSession.setAttribute.namenull=setAttribute: parámetro de nombre no puede ser nulo
