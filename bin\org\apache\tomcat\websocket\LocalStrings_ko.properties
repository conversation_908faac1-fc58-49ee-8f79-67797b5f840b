# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

asyncChannelGroup.createFail=JavaEE 컨테이너들과 같은 복잡한 클래스로더 환경에서 메모리 누수를 방지하기 위해 필수적이며 웹소켓 클라이언트들을 위한, 전용 AsynchronousChannelGroup를 생성할 수 없습니다.

asyncChannelWrapperSecure.check.notOk=TLS handshake가 예기치 않은 상태값 [{0}]을(를) 반환했습니다.
asyncChannelWrapperSecure.check.unwrap=데이터를 읽는 동안, 바이트들이 출력으로 쓰여졌습니다.
asyncChannelWrapperSecure.check.wrap=쓰기 작업을 하는 동안, 입력으로부터 바이트들이 소비되었습니다.
asyncChannelWrapperSecure.closeFail=채널을 깨끗하게 닫지 못했습니다.
asyncChannelWrapperSecure.concurrentRead=동시 발생적인 읽기 오퍼레이션들은 허용되지 않습니다.
asyncChannelWrapperSecure.concurrentWrite=동시적인 쓰기 오퍼레이션들은 허용되지 않습니다.
asyncChannelWrapperSecure.eof=예기치 않은 스트림의 끝
asyncChannelWrapperSecure.notHandshaking=TLS handshake 과정 중 예기치 않은 상태 [NOT_HANDSHAKING]입니다.
asyncChannelWrapperSecure.statusUnwrap=unwrap() 오퍼레이션 후에, SSLEngineResult의 예기치 않은 상태입니다.
asyncChannelWrapperSecure.statusWrap=wrap() 오퍼레이션 수행 이후, SSLEngineResult의 예기치 않은 상태입니다.
asyncChannelWrapperSecure.tooBig=결과 [{0}]이(가) 너무 커서, 정수로서 표현될 수 없습니다.
asyncChannelWrapperSecure.wrongStateRead=읽기 오퍼레이션을 완료하려 시도할 때에, 읽기 진행 중임을 표시하는 플래그가 false인 것으로 (true였어만 했음에도) 밝혀졌습니다.
asyncChannelWrapperSecure.wrongStateWrite=쓰기 오퍼레이션을 완료하려 시도할 때, 쓰기 진행 중이라는 플래그가 false로 (true였어야 함에도) 밝혀졌습니다.

backgroundProcessManager.processFailed=백그라운드 프로세스가 실패했습니다.

caseInsensitiveKeyMap.nullKey=널인 키들은 허용되지 않습니다.

futureToSendHandler.timeout=[{0}] [{1}]이(가) 완료되기를 기다린 후, 작업 제한 시간을 초과했습니다.

perMessageDeflate.alreadyClosed=해당 transformer가 이미 닫혔으므로 더이상 사용될 수 없습니다.
perMessageDeflate.deflateFailed=압축된 웹소켓 프레임의 압축을 풀지 못했습니다.
perMessageDeflate.duplicateParameter=[{0}] 확장 파라미터가 중복 정의되어 있습니다.
perMessageDeflate.invalidState=유효하지 않은 상태
perMessageDeflate.invalidWindowSize=크기가 [{1}]인 유효하지 않은 윈도우들이 [{0}]을 위해 지정되었습니다. 유효한 값들의 범위는 8에서 15까지의 모든 숫자들입니다.
perMessageDeflate.unknownParameter=알 수 없는 확장 파라미터 [{0}]은(는), 정의되지 않았습니다.

transformerFactory.unsupportedExtension=Extension [{0}]은(는) 지원되지 않습니다.

util.invalidMessageHandler=제공된 메시지 핸들러에 onMessage(Object) 메소드가 없습니다.
util.invalidType=[{0}] 값을 타입 [{1}](으)로 강제 변환시킬 수 없습니다. 해당 타입은 지원되지 않습니다.
util.notToken=허용되지 않는 확장 파라미터가 지정되었습니다. 이름: [{0}], 값: [{1}].
util.unknownDecoderType=해당 디코더 타입 [{0}]은(는) 인식되지 않습니다.

wsFrame.alreadyResumed=메시지 수신이 이미 재개되었습니다.
wsFrame.alreadySuspended=메시지 수신이 이미 일시 정지되었습니다.
wsFrame.bufferTooSmall=비동기 메시지를 지원할 수 없습니다. 버퍼가 너무 작습니다. 버퍼 크기: [{0}], 메시지 크기: [{1}]
wsFrame.byteToLongFail=너무 많은 바이트들([{0}])이 제공되어, long으로 변환될 수 없었습니다.
wsFrame.closed=Control 프레임을 닫은 이후에 새로운 프레임을 받았습니다.
wsFrame.controlFragmented=단편화된(fragmented) Control 프레임을 받았지만, Control 프레임은 단편화될 수 없습니다.
wsFrame.controlNoFin=fin 비트셋을 포함하지 않은 control 프레임이 전송되었습니다. Control 프레임들에 continuation 프레임들이 사용되는 것이 허용되지 않습니다.
wsFrame.controlPayloadTooBig=Control 프레임이, 크기가 [{0}]인 payload와 함께 전송되었는데, 이는 최대 허용치인 125바이트를 초과합니다.
wsFrame.illegalReadState=예기치 않은 읽기 상태: [{0}]
wsFrame.invalidOpCode=인식되지 않는 opCode [{0}]와(과) 함께, 웹소켓 프레임이 전송되었습니다.
wsFrame.invalidUtf8=웹소켓 텍스트 프레임을 받았는데, 유효하지 않은 바이트 시퀀스를 포함하고 있기 때문에, UTF-8로 디코딩될 수 없었습니다.
wsFrame.invalidUtf8Close=웹소켓 닫기 프레임을 접수하였는데, 닫기 사유는 유효하지 않은 UTF-8 바이트 시퀀스들을 포함했다는 것입니다.
wsFrame.ioeTriggeredClose=복구될 수 없는 IOException이 발생하여 연결이 닫혔습니다.
wsFrame.messageTooBig=메시지가 [{0}] 바이트의 길이로 되어 있으나, MessageHandler는 [{1}] 바이트의 제한값을 가지고 있습니다.
wsFrame.noContinuation=Continuation 프레임이 요구될 때에, 새로운 메시지가 시작되었습니다.
wsFrame.notMasked=클라이언트 프레임이 마스크 되어 있지 않습니다. 모든 클라이언트 프레임들은 반드시 마스크 되어야 합니다.
wsFrame.oneByteCloseCode=클라이언트가 단일 바이트의 payload를 가진 닫기 프레임을 보냈는데, 이는 유효하지 않습니다.
wsFrame.partialHeaderComplete=웹소켓 프레임을 받았습니다. fin [{0}], rsv [{1}], OpCode [{2}], payload 길이 [{3}]
wsFrame.payloadMsbInvalid=유효하지 않은 웹소켓 프레임이 접수되었습니다 - 64 비트의 payload에, 허용되지 않는 최상위 비트가 설정되었습니다.
wsFrame.readFailed=비동기 클라이언트의 읽기 실패
wsFrame.sessionClosed=해당 세션이 이미 닫혔기 때문에, 클라이언트 데이터가 처리될 수 없습니다.
wsFrame.suspendRequested=메시지 수신의 일시 정지가 이미 요청되었습니다.
wsFrame.textMessageTooBig=디코드된 텍스트 메시지가 출력 버퍼에 비해 너무 크며, 해당 엔드포인트는 partial 메시지들을 지원하지 않습니다.
wsFrame.wrongRsv=클라이언트 프레임이, opCode [{1}]을(를) 포함한 메시지를 위해, reserved 비트들을 [{0}](으)로 설정했는데, 이는 이 엔드포인트에 의해 지원되지 않습니다.

wsFrameClient.ioe=서버가 전송한 데이터를 읽는 중 실패

wsHandshakeRequest.invalidUri=문자열 [{0}]은(는) 유효한 URI를 구성하는 데 사용될 수 없습니다.
wsHandshakeRequest.unknownScheme=요청의 스킴 [{0}]이(가) 인식되지 않는 스킴입니다.

wsRemoteEndpoint.acquireTimeout=지정된 제한 시간 내에, 현재 메시지가 완전히 전송되지 않았습니다.
wsRemoteEndpoint.changeType=단편화된(fragmented) 메시지를 전송할 때, 모든 fragment들은 반드시 동일한 타입이어야 합니다.
wsRemoteEndpoint.closed=웹소켓 세션이 이미 닫혔기 때문에, 메시지가 전달되지 않을 것입니다.
wsRemoteEndpoint.closedDuringMessage=웹소켓 세션이 이미 닫혔기 때문에, 메시지의 나머지 부분은 전달되지 않을 것입니다.
wsRemoteEndpoint.closedOutputStream=OutputStream이 이미 닫혀 있으므로, 이 메소드는 호출될 수 없습니다.
wsRemoteEndpoint.closedWriter=Writer가 이미 닫혔기 때문에, 이 메소드는 호출될 수 없습니다.
wsRemoteEndpoint.flushOnCloseFailed=세션이 이미 종료된 이후에도, 메시지들이 배치(batch)에 포함되어 있습니다. 배치에 남아있는 메시지들을 배출할 수 없습니다.
wsRemoteEndpoint.invalidEncoder=지정된 타입 [{0}]의 Encoder의 인스턴스를 생성할 수 없었습니다.
wsRemoteEndpoint.noEncoder=클래스 [{0}]의 객체를 위한 인코더가 지정되지 않았습니다.
wsRemoteEndpoint.nullData=유효하지 않은 널 데이터 아규먼트
wsRemoteEndpoint.nullHandler=유효하지 않은 널 핸들러 아규먼트
wsRemoteEndpoint.sendInterrupt=현재 쓰레드가, blocking 전송이 완료되기를 기다리던 중 중단되었습니다.
wsRemoteEndpoint.tooMuchData=Ping 또는 pong은 125 바이트를 초과한 데이터를 보낼 수 없습니다.
wsRemoteEndpoint.writeTimeout=Blocking 쓰기가 제한 시간 초과되었습니다.
wsRemoteEndpoint.wrongState=호출된 메소드에 대해, 원격 엔드포인트가 유효하지 않은 상태 [{0}]에 있습니다.

wsSession.closed=웹소켓 세션 [{0}]은(는) 이미 닫혔으며, (close()를 제외한) 어떤 메소드도 닫힌 세션에 호출되어서는 안됩니다.
wsSession.created=웹소켓 세션 [{0}]을(를) 생성했습니다.
wsSession.doClose=웹소켓 세션 [{0}]을(를) 닫습니다.
wsSession.duplicateHandlerBinary=바이너리 메시지 핸들러가 이미 설정되었습니다.
wsSession.duplicateHandlerPong=Pong 메시지 핸들러가 이미 설정되었습니다.
wsSession.duplicateHandlerText=텍스트 메시지 핸들러가 이미 설정되어 있습니다.
wsSession.flushFailOnClose=세션이 닫힐 때, 배치에 쌓인 메시지들을 배출하지 못했습니다.
wsSession.instanceNew=엔드포인트 인스턴스 등록 실패
wsSession.invalidHandlerTypePong=Pong 메시지 핸들러는 반드시 MessageHandler.Whole을 구현해야 합니다.
wsSession.messageFailed=웹소켓 연결이 이미 닫혔기 때문에, 완전한 메시지를 쓸 수 없습니다.
wsSession.removeHandlerFailed=핸들러 [{0}]이(가), 이 세션과 함께 등록되지 않았었기 때문에, 제거될 수 없습니다.
wsSession.sendCloseFail=세션 [{0}]을(를) 위해, 원격 엔드포인트로 세션 닫기 메시지를 보내지 못했습니다.
wsSession.timeout=웹소켓 세션 [{0}]이(가) 제한 시간 초과로 만료되었습니다.
wsSession.timeoutRead=웹소켓 세션 [{0}]이(가) 읽기 유휴 시간 타임아웃으로 만료되었습니다.
wsSession.timeoutWrite=웹소켓 세션 [{0}]이(가) 쓰기 유휴 시간 타임아웃으로 만료되었습니다.
wsSession.unknownHandler=인식되지 않는 타입 [{1}]을(를) 위한 것이었기에, 해당 메시지 핸들러 [{0}]을(를) 추가할 수 없습니다.
wsSession.unknownHandlerType=메시지 핸들러 [{0}]이(가) 인식되지 않는 타입 [{1}](으)로 wrap 되어 있어, 추가할 수 없습니다.

wsWebSocketContainer.asynchronousSocketChannelFail=서버에 대한 연결을 열 수 없습니다.
wsWebSocketContainer.connect.entry=타입이 [{0}]인 엔드포인트 인스턴스를 [{1}]에 연결합니다.
wsWebSocketContainer.connect.write=로컬 주소 [{2}](으)로부터, 시작 위치 [{0}], 최대 길이 [{1}]의 데이터를 버퍼에 씁니다.
wsWebSocketContainer.defaultConfiguratorFail=기본 Configurator를 생성하지 못했습니다.
wsWebSocketContainer.endpointCreateFail=타입이 [{0}]인 로컬 엔드포인트를 생성하지 못했습니다.
wsWebSocketContainer.failedAuthentication=HTTP 응답 코드 [{0}]을(를) 처리하지 못했습니다. 인증 헤더가 서버에 의해 받아들여지지 않았습니다.
wsWebSocketContainer.httpRequestFailed=웹소켓 연결을 초기화하기 위한 HTTP 요청이 실패했습니다.
wsWebSocketContainer.invalidExtensionParameters=서버가, 클라이언트가 지원할 수 없는 확장 파라미터들과 함께 응답했습니다.
wsWebSocketContainer.invalidHeader=[{0}] 내에서, 헤더 이름과 헤더 값을 구분하기 위한 콜론('':'')이 존재하지 않기에, HTTP 헤더를 파싱할 수 없습니다. 해당 헤더를 건너뛰었습니다.
wsWebSocketContainer.invalidStatus=서버 [{0}](으)로부터의 HTTP 응답은, 웹소켓으로 HTTP 업그레이드를 허용하지 않았습니다.
wsWebSocketContainer.invalidSubProtocol=웹소켓 서버가, 해당 Sec-WebSocket-Protocol 헤더를 위해 여러 값들을 반환했습니다.
wsWebSocketContainer.maxBuffer=이 구현은 버퍼의 최대 크기를 Integer.MAX_VALUE로 제한합니다.
wsWebSocketContainer.missingAnnotation=@ClientEndpoint에 의해 annotate되지 않았기에, POJO 클래스 [{0}]을(를) 사용할 수 없습니다.
wsWebSocketContainer.missingLocationHeader=HTTP 응답 코드 [{0}]을(를) 처리하지 못했습니다. 응답에 Location 헤더가 없습니다.
wsWebSocketContainer.missingWWWAuthenticateHeader=HTTP 응답 코드 [{0}]을(를) 처리하지 못했습니다. 응답 헤더 WWW-Authenticate가 없습니다.
wsWebSocketContainer.pathNoHost=URI 내에 호스트가 지정되지 않았습니다.
wsWebSocketContainer.pathWrongScheme=스킴 [{0}]은(는) 지원되지 않습니다. 지원되는 스킴들은 ws와 wss입니다.
wsWebSocketContainer.proxyConnectFail=설정된 프록시 [{0}](으)로 연결하지 못했습니다. HTTP 응답 코드는 [{1}]이었습니다.
wsWebSocketContainer.redirectThreshold=순환 Location 헤더 [{0}]이(가) 탐지되었고, 최대 redirect 회수에 도달했습니다. 최대 [{2}]회 중 [{1}]회.
wsWebSocketContainer.responseFail=웹소켓으로의 HTTP 업그레이드가 실패했습니만, 일부 데이터가 접수되었을 수 있습니다: 상태 코드 [{0}], HTTP 헤더들 [{1}]
wsWebSocketContainer.sessionCloseFail=ID가 [{0}]인 세션이 깨끗하게 닫히지 않았습니다.
wsWebSocketContainer.shutdown=웹 애플리케이션이 중지되고 있습니다.
wsWebSocketContainer.sslEngineFail=SSL/TLS 연결들을 지원하는 SSLEngine을 생성할 수 없습니다.
wsWebSocketContainer.unsupportedAuthScheme=HTTP 응답 코드 [{0}]을(를) 처리하지 못했습니다. 지원되지 않는 인증 스킴 [{1}]이(가) 응답에서 반환되었습니다.
