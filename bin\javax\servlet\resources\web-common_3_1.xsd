<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns="http://www.w3.org/2001/XMLSchema"
            targetNamespace="http://xmlns.jcp.org/xml/ns/javaee"
            xmlns:javaee="http://xmlns.jcp.org/xml/ns/javaee"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            elementFormDefault="qualified"
            attributeFormDefault="unqualified"
            version="3.1">
  <xsd:annotation>
    <xsd:documentation>

      DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

      Copyright (c) 2009-2013 Oracle and/or its affiliates. All rights reserved.

      The contents of this file are subject to the terms of either the GNU
      General Public License Version 2 only ("GPL") or the Common Development
      and Distribution License("CDDL") (collectively, the "License").  You
      may not use this file except in compliance with the License.  You can
      obtain a copy of the License at
      https://glassfish.dev.java.net/public/CDDL+GPL_1_1.html
      or packager/legal/LICENSE.txt.  See the License for the specific
      language governing permissions and limitations under the License.

      When distributing the software, include this License Header Notice in each
      file and include the License file at packager/legal/LICENSE.txt.

      GPL Classpath Exception:
      Oracle designates this particular file as subject to the "Classpath"
      exception as provided by Oracle in the GPL Version 2 section of the License
      file that accompanied this code.

      Modifications:
      If applicable, add the following below the License Header, with the fields
      enclosed by brackets [] replaced by your own identifying information:
      "Portions Copyright [year] [name of copyright owner]"

      Contributor(s):
      If you wish your version of this file to be governed by only the CDDL or
      only the GPL Version 2, indicate your decision by adding "[Contributor]
      elects to include this software in this distribution under the [CDDL or GPL
      Version 2] license."  If you don't indicate a single choice of license, a
      recipient has the option to distribute your version of this file under
      either the CDDL, the GPL Version 2 or to extend the choice of license to
      its licensees as provided above.  However, if you add GPL Version 2 code
      and therefore, elected the GPL Version 2 license, then the option applies
      only if the new code is made subject to such option by the copyright
      holder.

    </xsd:documentation>
  </xsd:annotation>

  <xsd:annotation>
    <xsd:documentation>
      The Apache Software Foundation elects to include this software under the
      CDDL license.
    </xsd:documentation>
  </xsd:annotation>

  <xsd:annotation>
    <xsd:documentation>
      <![CDATA[[
      This is the common XML Schema for the Servlet 3.1 deployment descriptor.
      This file is in turn used by web.xml and web-fragment.xml
      web application's war file.  All Servlet deployment descriptors
      must indicate the web common schema by using the Java EE
      namespace:

      http://xmlns.jcp.org/xml/ns/javaee

      and by indicating the version of the schema by
      using the version element as shown below:

      <web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="..."
      version="3.1">
      ...
      </web-app>

      The instance documents may indicate the published version of
      the schema using the xsi:schemaLocation attribute for Java EE
      namespace with the following location:

      http://xmlns.jcp.org/xml/ns/javaee/web-common_3_1.xsd

      ]]>
    </xsd:documentation>
  </xsd:annotation>

  <xsd:annotation>
    <xsd:documentation>

      The following conventions apply to all Java EE
      deployment descriptor elements unless indicated otherwise.

      - In elements that specify a pathname to a file within the
      same JAR file, relative filenames (i.e., those not
      starting with "/") are considered relative to the root of
      the JAR file's namespace.  Absolute filenames (i.e., those
      starting with "/") also specify names in the root of the
      JAR file's namespace.  In general, relative names are
      preferred.  The exception is .war files where absolute
      names are preferred for consistency with the Servlet API.

    </xsd:documentation>
  </xsd:annotation>

  <xsd:include schemaLocation="javaee_7.xsd"/>

  <xsd:include schemaLocation="jsp_2_3.xsd"/>

  <xsd:group name="web-commonType">
    <xsd:choice>
      <xsd:group ref="javaee:descriptionGroup"/>
      <xsd:element name="distributable"
                   type="javaee:emptyType"/>
      <xsd:element name="context-param"
                   type="javaee:param-valueType">
        <xsd:annotation>
          <xsd:documentation>

            The context-param element contains the declaration
            of a web application's servlet context
            initialization parameters.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="filter"
                   type="javaee:filterType"/>
      <xsd:element name="filter-mapping"
                   type="javaee:filter-mappingType"/>
      <xsd:element name="listener"
                   type="javaee:listenerType"/>
      <xsd:element name="servlet"
                   type="javaee:servletType"/>
      <xsd:element name="servlet-mapping"
                   type="javaee:servlet-mappingType"/>
      <xsd:element name="session-config"
                   type="javaee:session-configType"/>
      <xsd:element name="mime-mapping"
                   type="javaee:mime-mappingType"/>
      <xsd:element name="welcome-file-list"
                   type="javaee:welcome-file-listType"/>
      <xsd:element name="error-page"
                   type="javaee:error-pageType"/>
      <xsd:element name="jsp-config"
                   type="javaee:jsp-configType"/>
      <xsd:element name="security-constraint"
                   type="javaee:security-constraintType"/>
      <xsd:element name="login-config"
                   type="javaee:login-configType"/>
      <xsd:element name="security-role"
                   type="javaee:security-roleType"/>
      <xsd:group ref="javaee:jndiEnvironmentRefsGroup"/>
      <xsd:element name="message-destination"
                   type="javaee:message-destinationType"/>
      <xsd:element name="locale-encoding-mapping-list"
                   type="javaee:locale-encoding-mapping-listType"/>
    </xsd:choice>
  </xsd:group>

  <xsd:attributeGroup name="web-common-attributes">
    <xsd:attribute name="version"
                   type="javaee:web-app-versionType"
                   use="required"/>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
    <xsd:attribute name="metadata-complete"
                   type="xsd:boolean">
      <xsd:annotation>
        <xsd:documentation>

          The metadata-complete attribute defines whether this
          deployment descriptor and other related deployment
          descriptors for this module (e.g., web service
          descriptors) are complete, or whether the class
          files available to this module and packaged with
          this application should be examined for annotations
          that specify deployment information.

          If metadata-complete is set to "true", the deployment
          tool must ignore any annotations that specify deployment
          information, which might be present in the class files
          of the application.

          If metadata-complete is not specified or is set to
          "false", the deployment tool must examine the class
          files of the application for annotations, as
          specified by the specifications.

        </xsd:documentation>
      </xsd:annotation>
    </xsd:attribute>
  </xsd:attributeGroup>


<!-- **************************************************** -->

  <xsd:complexType name="auth-constraintType">
    <xsd:annotation>
      <xsd:documentation>

        The auth-constraintType indicates the user roles that
        should be permitted access to this resource
        collection. The role-name used here must either correspond
        to the role-name of one of the security-role elements
        defined for this web application, or be the specially
        reserved role-name "*" that is a compact syntax for
        indicating all roles in the web application. If both "*"
        and rolenames appear, the container interprets this as all
        roles.  If no roles are defined, no user is allowed access
        to the portion of the web application described by the
        containing security-constraint.  The container matches
        role names case sensitively when determining access.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="description"
                   type="javaee:descriptionType"
                   minOccurs="0"
                   maxOccurs="unbounded"/>
      <xsd:element name="role-name"
                   type="javaee:role-nameType"
                   minOccurs="0"
                   maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="auth-methodType">
    <xsd:annotation>
      <xsd:documentation>

        The auth-methodType is used to configure the authentication
        mechanism for the web application. As a prerequisite to
        gaining access to any web resources which are protected by
        an authorization constraint, a user must have authenticated
        using the configured mechanism. Legal values are "BASIC",
        "DIGEST", "FORM", "CLIENT-CERT", or a vendor-specific
        authentication scheme.

        Used in: login-config

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="javaee:string"/>
    </xsd:simpleContent>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="dispatcherType">
    <xsd:annotation>
      <xsd:documentation>

        The dispatcher has five legal values: FORWARD, REQUEST,
        INCLUDE, ASYNC, and ERROR.

        A value of FORWARD means the Filter will be applied under
        RequestDispatcher.forward() calls.
        A value of REQUEST means the Filter will be applied under
        ordinary client calls to the path or servlet.
        A value of INCLUDE means the Filter will be applied under
        RequestDispatcher.include() calls.
        A value of ASYNC means the Filter will be applied under
        calls dispatched from an AsyncContext.
        A value of ERROR means the Filter will be applied under the
        error page mechanism.

        The absence of any dispatcher elements in a filter-mapping
        indicates a default of applying filters only under ordinary
        client calls to the path or servlet.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="javaee:string">
        <xsd:enumeration value="FORWARD"/>
        <xsd:enumeration value="INCLUDE"/>
        <xsd:enumeration value="REQUEST"/>
        <xsd:enumeration value="ASYNC"/>
        <xsd:enumeration value="ERROR"/>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="error-codeType">
    <xsd:annotation>
      <xsd:documentation>

        The error-code contains an HTTP error code, ex: 404

        Used in: error-page

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="javaee:xsdPositiveIntegerType">
        <xsd:pattern value="\d{3}"/>
        <xsd:attribute name="id"
                       type="xsd:ID"/>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="error-pageType">
    <xsd:annotation>
      <xsd:documentation>

        The error-pageType contains a mapping between an error code
        or exception type to the path of a resource in the web
        application.

        Error-page declarations using the exception-type element in
        the deployment descriptor must be unique up to the class name of
        the exception-type. Similarly, error-page declarations using the
        error-code element must be unique in the deployment descriptor
        up to the status code.

        If an error-page element in the deployment descriptor does not
        contain an exception-type or an error-code element, the error
        page is a default error page.

        Used in: web-app

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:choice minOccurs="0"
                  maxOccurs="1">
        <xsd:element name="error-code"
                     type="javaee:error-codeType"/>
        <xsd:element name="exception-type"
                     type="javaee:fully-qualified-classType">
          <xsd:annotation>
            <xsd:documentation>

              The exception-type contains a fully qualified class
              name of a Java exception type.

            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:choice>
      <xsd:element name="location"
                   type="javaee:war-pathType">
        <xsd:annotation>
          <xsd:documentation>

            The location element contains the location of the
            resource in the web application relative to the root of
            the web application. The value of the location must have
            a leading `/'.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="filterType">
    <xsd:annotation>
      <xsd:documentation>

        The filterType is used to declare a filter in the web
        application. The filter is mapped to either a servlet or a
        URL pattern in the filter-mapping element, using the
        filter-name value to reference. Filters can access the
        initialization parameters declared in the deployment
        descriptor at runtime via the FilterConfig interface.

        Used in: web-app

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:group ref="javaee:descriptionGroup"/>
      <xsd:element name="filter-name"
                   type="javaee:filter-nameType"/>
      <xsd:element name="filter-class"
                   type="javaee:fully-qualified-classType"
                   minOccurs="0"
                   maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>

            The fully qualified classname of the filter.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="async-supported"
                   type="javaee:true-falseType"
                   minOccurs="0"/>
      <xsd:element name="init-param"
                   type="javaee:param-valueType"
                   minOccurs="0"
                   maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation>

            The init-param element contains a name/value pair as
            an initialization param of a servlet filter

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="filter-mappingType">
    <xsd:annotation>
      <xsd:documentation>

        Declaration of the filter mappings in this web
        application is done by using filter-mappingType.
        The container uses the filter-mapping
        declarations to decide which filters to apply to a request,
        and in what order. The container matches the request URI to
        a Servlet in the normal way. To determine which filters to
        apply it matches filter-mapping declarations either on
        servlet-name, or on url-pattern for each filter-mapping
        element, depending on which style is used. The order in
        which filters are invoked is the order in which
        filter-mapping declarations that match a request URI for a
        servlet appear in the list of filter-mapping elements.The
        filter-name value must be the value of the filter-name
        sub-elements of one of the filter declarations in the
        deployment descriptor.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="filter-name"
                   type="javaee:filter-nameType"/>
      <xsd:choice minOccurs="1"
                  maxOccurs="unbounded">
        <xsd:element name="url-pattern"
                     type="javaee:url-patternType"/>
        <xsd:element name="servlet-name"
                     type="javaee:servlet-nameType"/>
      </xsd:choice>
      <xsd:element name="dispatcher"
                   type="javaee:dispatcherType"
                   minOccurs="0"
                   maxOccurs="5"/>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="nonEmptyStringType">
    <xsd:annotation>
      <xsd:documentation>

        This type defines a string which contains at least one
        character.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="javaee:string">
        <xsd:minLength value="1"/>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="filter-nameType">
    <xsd:annotation>
      <xsd:documentation>

        The logical name of the filter is declare
        by using filter-nameType. This name is used to map the
        filter.  Each filter name is unique within the web
        application.

        Used in: filter, filter-mapping

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="javaee:nonEmptyStringType"/>
    </xsd:simpleContent>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="form-login-configType">
    <xsd:annotation>
      <xsd:documentation>

        The form-login-configType specifies the login and error
        pages that should be used in form based login. If form based
        authentication is not used, these elements are ignored.

        Used in: login-config

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="form-login-page"
                   type="javaee:war-pathType">
        <xsd:annotation>
          <xsd:documentation>

            The form-login-page element defines the location in the web
            app where the page that can be used for login can be
            found.  The path begins with a leading / and is interpreted
            relative to the root of the WAR.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="form-error-page"
                   type="javaee:war-pathType">
        <xsd:annotation>
          <xsd:documentation>

            The form-error-page element defines the location in
            the web app where the error page that is displayed
            when login is not successful can be found.
            The path begins with a leading / and is interpreted
            relative to the root of the WAR.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>

  <xsd:simpleType name="http-methodType">
    <xsd:annotation>
      <xsd:documentation>

        An HTTP method type as defined in HTTP 1.1 section 2.2.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:token">
      <xsd:pattern value="[!-~-[\(\)&#60;&#62;@,;:&#34;/\[\]?=\{\}\\\p{Z}]]+"/>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="load-on-startupType">
    <xsd:union memberTypes="javaee:null-charType xsd:integer"/>
  </xsd:simpleType>

  <xsd:simpleType name="null-charType">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value=""/>
    </xsd:restriction>
  </xsd:simpleType>


<!-- **************************************************** -->

  <xsd:complexType name="login-configType">
    <xsd:annotation>
      <xsd:documentation>

        The login-configType is used to configure the authentication
        method that should be used, the realm name that should be
        used for this application, and the attributes that are
        needed by the form login mechanism.

        Used in: web-app

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="auth-method"
                   type="javaee:auth-methodType"
                   minOccurs="0"/>
      <xsd:element name="realm-name"
                   type="javaee:string"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            The realm name element specifies the realm name to
            use in HTTP Basic authorization.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="form-login-config"
                   type="javaee:form-login-configType"
                   minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="mime-mappingType">
    <xsd:annotation>
      <xsd:documentation>

        The mime-mappingType defines a mapping between an extension
        and a mime type.

        Used in: web-app

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:annotation>
        <xsd:documentation>

          The extension element contains a string describing an
          extension. example: "txt"

        </xsd:documentation>
      </xsd:annotation>
      <xsd:element name="extension"
                   type="javaee:string"/>
      <xsd:element name="mime-type"
                   type="javaee:mime-typeType"/>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="mime-typeType">
    <xsd:annotation>
      <xsd:documentation>

        The mime-typeType is used to indicate a defined mime type.

        Example:
        "text/plain"

        Used in: mime-mapping

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="javaee:string">
        <xsd:pattern value="[^\p{Cc}^\s]+/[^\p{Cc}^\s]+"/>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="security-constraintType">
    <xsd:annotation>
      <xsd:documentation>

        The security-constraintType is used to associate
        security constraints with one or more web resource
        collections

        Used in: web-app

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="display-name"
                   type="javaee:display-nameType"
                   minOccurs="0"
                   maxOccurs="unbounded"/>
      <xsd:element name="web-resource-collection"
                   type="javaee:web-resource-collectionType"
                   maxOccurs="unbounded"/>
      <xsd:element name="auth-constraint"
                   type="javaee:auth-constraintType"
                   minOccurs="0"/>
      <xsd:element name="user-data-constraint"
                   type="javaee:user-data-constraintType"
                   minOccurs="0"/>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="servletType">
    <xsd:annotation>
      <xsd:documentation>

        The servletType is used to declare a servlet.
        It contains the declarative data of a
        servlet. If a jsp-file is specified and the load-on-startup
        element is present, then the JSP should be precompiled and
        loaded.

        Used in: web-app

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:group ref="javaee:descriptionGroup"/>
      <xsd:element name="servlet-name"
                   type="javaee:servlet-nameType"/>
      <xsd:choice minOccurs="0"
                  maxOccurs="1">
        <xsd:element name="servlet-class"
                     type="javaee:fully-qualified-classType">
          <xsd:annotation>
            <xsd:documentation>

              The servlet-class element contains the fully
              qualified class name of the servlet.

            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="jsp-file"
                     type="javaee:jsp-fileType"/>
      </xsd:choice>
      <xsd:element name="init-param"
                   type="javaee:param-valueType"
                   minOccurs="0"
                   maxOccurs="unbounded"/>
      <xsd:element name="load-on-startup"
                   type="javaee:load-on-startupType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            The load-on-startup element indicates that this
            servlet should be loaded (instantiated and have
            its init() called) on the startup of the web
            application. The optional contents of these
            element must be an integer indicating the order in
            which the servlet should be loaded. If the value
            is a negative integer, or the element is not
            present, the container is free to load the servlet
            whenever it chooses. If the value is a positive
            integer or 0, the container must load and
            initialize the servlet as the application is
            deployed. The container must guarantee that
            servlets marked with lower integers are loaded
            before servlets marked with higher integers. The
            container may choose the order of loading of
            servlets with the same load-on-start-up value.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="enabled"
                   type="javaee:true-falseType"
                   minOccurs="0"/>
      <xsd:element name="async-supported"
                   type="javaee:true-falseType"
                   minOccurs="0"/>
      <xsd:element name="run-as"
                   type="javaee:run-asType"
                   minOccurs="0"/>
      <xsd:element name="security-role-ref"
                   type="javaee:security-role-refType"
                   minOccurs="0"
                   maxOccurs="unbounded"/>
      <xsd:element name="multipart-config"
                   type="javaee:multipart-configType"
                   minOccurs="0"
                   maxOccurs="1"/>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="servlet-mappingType">
    <xsd:annotation>
      <xsd:documentation>

        The servlet-mappingType defines a mapping between a
        servlet and a url pattern.

        Used in: web-app

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="servlet-name"
                   type="javaee:servlet-nameType"/>
      <xsd:element name="url-pattern"
                   type="javaee:url-patternType"
                   minOccurs="1"
                   maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="servlet-nameType">
    <xsd:annotation>
      <xsd:documentation>

        The servlet-name element contains the canonical name of the
        servlet. Each servlet name is unique within the web
        application.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="javaee:nonEmptyStringType"/>
    </xsd:simpleContent>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="session-configType">
    <xsd:annotation>
      <xsd:documentation>

        The session-configType defines the session parameters
        for this web application.

        Used in: web-app

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="session-timeout"
                   type="javaee:xsdIntegerType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            The session-timeout element defines the default
            session timeout interval for all sessions created
            in this web application. The specified timeout
            must be expressed in a whole number of minutes.
            If the timeout is 0 or less, the container ensures
            the default behaviour of sessions is never to time
            out. If this element is not specified, the container
            must set its default timeout period.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cookie-config"
                   type="javaee:cookie-configType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            The cookie-config element defines the configuration of the
            session tracking cookies created by this web application.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="tracking-mode"
                   type="javaee:tracking-modeType"
                   minOccurs="0"
                   maxOccurs="3">
        <xsd:annotation>
          <xsd:documentation>

            The tracking-mode element defines the tracking modes
            for sessions created by this web application

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="cookie-configType">
    <xsd:annotation>
      <xsd:documentation>

        The cookie-configType defines the configuration for the
        session tracking cookies of this web application.

        Used in: session-config

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="name"
                   type="javaee:cookie-nameType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            The name that will be assigned to any session tracking
            cookies created by this web application.
            The default is JSESSIONID

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="domain"
                   type="javaee:cookie-domainType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            The domain name that will be assigned to any session tracking
            cookies created by this web application.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="path"
                   type="javaee:cookie-pathType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            The path that will be assigned to any session tracking
            cookies created by this web application.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="comment"
                   type="javaee:cookie-commentType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            The comment that will be assigned to any session tracking
            cookies created by this web application.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="http-only"
                   type="javaee:true-falseType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            Specifies whether any session tracking cookies created
            by this web application will be marked as HttpOnly

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="secure"
                   type="javaee:true-falseType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            Specifies whether any session tracking cookies created
            by this web application will be marked as secure.
            When true, all session tracking cookies must be marked
            as secure independent of the nature of the request that
            initiated the corresponding session.
            When false, the session cookie should only be marked secure
            if the request that initiated the session was secure.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="max-age"
                   type="javaee:xsdIntegerType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            The lifetime (in seconds) that will be assigned to any
            session tracking cookies created by this web application.
            Default is -1

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="cookie-nameType">
    <xsd:annotation>
      <xsd:documentation>

        The name that will be assigned to any session tracking
        cookies created by this web application.
        The default is JSESSIONID

        Used in: cookie-config

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="javaee:nonEmptyStringType"/>
    </xsd:simpleContent>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="cookie-domainType">
    <xsd:annotation>
      <xsd:documentation>

        The domain name that will be assigned to any session tracking
        cookies created by this web application.

        Used in: cookie-config

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="javaee:nonEmptyStringType"/>
    </xsd:simpleContent>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="cookie-pathType">
    <xsd:annotation>
      <xsd:documentation>

        The path that will be assigned to any session tracking
        cookies created by this web application.

        Used in: cookie-config

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="javaee:nonEmptyStringType"/>
    </xsd:simpleContent>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="cookie-commentType">
    <xsd:annotation>
      <xsd:documentation>

        The comment that will be assigned to any session tracking
        cookies created by this web application.

        Used in: cookie-config

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="javaee:nonEmptyStringType"/>
    </xsd:simpleContent>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="tracking-modeType">
    <xsd:annotation>
      <xsd:documentation>

        The tracking modes for sessions created by this web
        application

        Used in: session-config

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="javaee:string">
        <xsd:enumeration value="COOKIE"/>
        <xsd:enumeration value="URL"/>
        <xsd:enumeration value="SSL"/>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="transport-guaranteeType">
    <xsd:annotation>
      <xsd:documentation>

        The transport-guaranteeType specifies that the communication
        between client and server should be NONE, INTEGRAL, or
        CONFIDENTIAL. NONE means that the application does not
        require any transport guarantees. A value of INTEGRAL means
        that the application requires that the data sent between the
        client and server be sent in such a way that it can't be
        changed in transit. CONFIDENTIAL means that the application
        requires that the data be transmitted in a fashion that
        prevents other entities from observing the contents of the
        transmission. In most cases, the presence of the INTEGRAL or
        CONFIDENTIAL flag will indicate that the use of SSL is
        required.

        Used in: user-data-constraint

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="javaee:string">
        <xsd:enumeration value="NONE"/>
        <xsd:enumeration value="INTEGRAL"/>
        <xsd:enumeration value="CONFIDENTIAL"/>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="user-data-constraintType">
    <xsd:annotation>
      <xsd:documentation>

        The user-data-constraintType is used to indicate how
        data communicated between the client and container should be
        protected.

        Used in: security-constraint

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="description"
                   type="javaee:descriptionType"
                   minOccurs="0"
                   maxOccurs="unbounded"/>
      <xsd:element name="transport-guarantee"
                   type="javaee:transport-guaranteeType"/>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="war-pathType">
    <xsd:annotation>
      <xsd:documentation>

        The elements that use this type designate a path starting
        with a "/" and interpreted relative to the root of a WAR
        file.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="javaee:string">
        <xsd:pattern value="/.*"/>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>

  <xsd:simpleType name="web-app-versionType">
    <xsd:annotation>
      <xsd:documentation>

        This type contains the recognized versions of
        web-application supported. It is used to designate the
        version of the web application.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:token">
      <xsd:enumeration value="3.1"/>
    </xsd:restriction>
  </xsd:simpleType>


<!-- **************************************************** -->

  <xsd:complexType name="web-resource-collectionType">
    <xsd:annotation>
      <xsd:documentation>

        The web-resource-collectionType is used to identify the
        resources and HTTP methods on those resources to which a
        security constraint applies. If no HTTP methods are specified,
        then the security constraint applies to all HTTP methods.
        If HTTP methods are specified by http-method-omission
        elements, the security constraint applies to all methods
        except those identified in the collection.
        http-method-omission and http-method elements are never
        mixed in the same collection.

        Used in: security-constraint

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="web-resource-name"
                   type="javaee:string">
        <xsd:annotation>
          <xsd:documentation>

            The web-resource-name contains the name of this web
            resource collection.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="description"
                   type="javaee:descriptionType"
                   minOccurs="0"
                   maxOccurs="unbounded"/>
      <xsd:element name="url-pattern"
                   type="javaee:url-patternType"
                   maxOccurs="unbounded"/>
      <xsd:choice minOccurs="0"
                  maxOccurs="1">
        <xsd:element name="http-method"
                     type="javaee:http-methodType"
                     minOccurs="1"
                     maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>

              Each http-method names an HTTP method to which the
              constraint applies.

            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element name="http-method-omission"
                     type="javaee:http-methodType"
                     minOccurs="1"
                     maxOccurs="unbounded">
          <xsd:annotation>
            <xsd:documentation>

              Each http-method-omission names an HTTP method to
              which the constraint does not apply.

            </xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:choice>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="welcome-file-listType">
    <xsd:annotation>
      <xsd:documentation>

        The welcome-file-list contains an ordered list of welcome
        files elements.

        Used in: web-app

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="welcome-file"
                   type="xsd:string"
                   maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation>

            The welcome-file element contains file name to use
            as a default welcome file, such as index.html

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>

  <xsd:simpleType name="localeType">
    <xsd:annotation>
      <xsd:documentation>

        The localeType defines valid locale defined by ISO-639-1
        and ISO-3166.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[a-z]{2}(_|-)?([\p{L}\-\p{Nd}]{2})?"/>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="encodingType">
    <xsd:annotation>
      <xsd:documentation>

        The encodingType defines IANA character sets.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="[^\s]+"/>
    </xsd:restriction>
  </xsd:simpleType>


<!-- **************************************************** -->

  <xsd:complexType name="locale-encoding-mapping-listType">
    <xsd:annotation>
      <xsd:documentation>

        The locale-encoding-mapping-list contains one or more
        locale-encoding-mapping(s).

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="locale-encoding-mapping"
                   type="javaee:locale-encoding-mappingType"
                   maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="locale-encoding-mappingType">
    <xsd:annotation>
      <xsd:documentation>

        The locale-encoding-mapping contains locale name and
        encoding name. The locale name must be either "Language-code",
        such as "ja", defined by ISO-639 or "Language-code_Country-code",
        such as "ja_JP".  "Country code" is defined by ISO-3166.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="locale"
                   type="javaee:localeType"/>
      <xsd:element name="encoding"
                   type="javaee:encodingType"/>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="ordering-othersType">
    <xsd:annotation>
      <xsd:documentation>

        This element indicates that the ordering sub-element in which
        it was placed should take special action regarding the ordering
        of this application resource relative to other application
        configuration resources.
        See section 8.2.2 of the specification for details.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="multipart-configType">
    <xsd:annotation>
      <xsd:documentation>

        This element specifies configuration information related to the
        handling of multipart/form-data requests.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="location"
                   type="javaee:string"
                   minOccurs="0"
                   maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>

            The directory location where uploaded files will be stored

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="max-file-size"
                   type="xsd:long"
                   minOccurs="0"
                   maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>

            The maximum size limit of uploaded files

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="max-request-size"
                   type="xsd:long"
                   minOccurs="0"
                   maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>

            The maximum size limit of multipart/form-data requests

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="file-size-threshold"
                   type="xsd:integer"
                   minOccurs="0"
                   maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>

            The size threshold after which an uploaded file will be
            written to disk

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>

</xsd:schema>
