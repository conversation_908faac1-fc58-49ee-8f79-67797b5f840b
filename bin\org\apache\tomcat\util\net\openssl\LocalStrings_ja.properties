# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

engine.ciphersFailure=暗号リストを取得できません。
engine.emptyCipherSuite=暗号スイートがありません。
engine.engineClosed=エンジンが閉じられています
engine.failedCipherSuite=暗号スイート[{0}]を有効にできませんでした。
engine.inboundClose=ピアのclose_notifyを受信する前のインバウンドクローズ
engine.invalidBufferArray=オフセット: [{0}], 長さ: [{1}] (期待値: offset <= offset + length <= srcs.length [{2}])
engine.noRestrictSessionCreation=OpenSslEngine では既存セッションのレジュームのみにエンジンを制限することはできません
engine.noSSLContext=SSLコンテキストがありません
engine.noSession=SSLセッションIDが利用可能ではありません
engine.nullBuffer=Null バッファ
engine.nullBufferInArray=配列内のNull バッファ
engine.nullCipherSuite=Null 暗号スイート
engine.nullName=Null値名
engine.nullValue=Null値
engine.openSSLError=OpenSSLエラー：[{0}] メッセージ：[{1}]
engine.oversizedPacket=暗号化パケットのサイズが超過しています。
engine.unsupportedCipher=サポートされていない暗号スイート：[{0}] [{1}]
engine.unsupportedProtocol=プロトコル [{0}] には対応していません。
engine.unverifiedPeer=未確認のピア
engine.writeToSSLFailed=SSLへの書き込みに失敗しました。返却値：[{0}]

openssl.X509FactoryError=X509ファクトリインスタンスの取得エラー
openssl.addedClientCaCert=クライアント CA 証明書を登録しました: [{0}]
openssl.applyConf=OpenSSLConfCmdをSSLコンテキストに適用します。
openssl.certificateVerificationFailed=証明書確認に失敗しました
openssl.checkConf=OpenSSLConfの確認
openssl.doubleInit=SSLコンテキストが既に初期化されています。無視します。
openssl.errApplyConf=OpenSSLConfをSSLコンテキストに適用できませんでした。
openssl.errCheckConf=OpenSSLConfチェック中のエラー
openssl.errMakeConf=OpenSSLConfコンテキストを作成できませんでした。
openssl.errorSSLCtxInit=SSL コンテキスト初期化中のエラー
openssl.keyManagerMissing=キーマネージャーが見つかりません。
openssl.makeConf=OpenSSLConfコンテキストの作成
openssl.nonJsseCertificate=証明書 [{0}] またはその秘密鍵 [{1}] は JSSE キーマネージャを用いて処理することが出来ませんでした。OpenSSL に直接渡されます
openssl.nonJsseChain=証明書チェイン [{0}] が指定されていないか、または有効ではありません。JSSE は有効な証明書チェインを必要とするため、OpenSSL の直接使用が試行されます
openssl.trustManagerMissing=トラストマネージャが見つかりません

opensslconf.applyCommand=OpenSSLConfはコマンド（名前[{0}]、値[{1}]）を適用しています。
opensslconf.applyFailed=OpenSSLConfをSSLコンテキストに適用する際の失敗
opensslconf.checkCommand=OpenSSLConfチェックコマンド（名前[{0}]、値[{1}]）
opensslconf.checkFailed=OpenSSLConf のチェックが失敗しました。
opensslconf.failedCommand=結果[{2}]でOpenSSLConfがコマンド（名前[{0}]、値[{1}]）に失敗しました。無視されます。
opensslconf.finishFailed=結果[{0}]でOpenSSLConfのfinish処理が失敗しました
opensslconf.noCommandName=OpenSSLConfコマンド名なし - 無視されます（コマンド値[{0}]）
opensslconf.resultCommand=OpenSSLConfコマンド（名前[{0}]、値[{1}]）が[{2}]を返しました。

sessionContext.nullTicketKeys=Null キー
