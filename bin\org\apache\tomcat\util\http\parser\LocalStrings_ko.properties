# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cookie.fallToDebug=\n\
\ 비고: 이 오류가 더 발생하는 경우 DEBUG 레벨 로그로 기록될 것입니다.
cookie.invalidCookieValue=유효하지 않은 쿠키가 포함된 쿠키 헤더 [{0}]을(를) 받았습니다. 이 쿠키는 무시될 것입니다.
cookie.invalidCookieVersion=인식되지 않는 쿠키 버전 [{0}]을(를) 사용한 쿠키 헤더를 받았습니다. 해당 헤더와 그에 포함된 쿠키들은 무시될 것입니다.
cookie.valueNotPresent=<not present>

http.closingBracket=닫는 대괄호(']')가 IPv6 이름이 아닌 호스트 이름에서 발견되었습니다.
http.illegalAfterIpv6=호스트 이름 내에서, IPv6 주소 이후에 문자 [{0}]은(는) 허용되지 않습니다.
http.illegalCharacterDomain=문자 [{0}]은(는) 도메인 이름 내에서 유효하지 않은 문자입니다.
http.illegalCharacterIpv4=문자 [{0}]은(는) IPv4 주소에서 절대 유효하지 않은 것입니다.
http.illegalCharacterIpv6=문자 [{0}]은(는) IPv6 주소 내에서 유효하지 않은 것입니다.
http.invalidCharacterDomain.afterColon=도메인 이름 내에서, 콜론 이후의 문자 [{0}]은(는) 유효하지 않습니다.
http.invalidCharacterDomain.afterHyphen=도메인 이름 내에서, 붙임표(하이픈) 이후의 문자 [{0}]은(는) 유효하지 않습니다.
http.invalidCharacterDomain.afterLetter=도메인 이름 내에서, 한 글자 이후의 문자 [{0}]은(는) 유효하지 않습니다.
http.invalidCharacterDomain.afterNumber=도메인 이름 내에서, 숫자 이후의 문자 [{0}]은(는) 유효하지 않습니다.
http.invalidCharacterDomain.afterPeriod=도메인 이름 내에서, 마침표 이후의 문자 [{0}]은(는) 유효하지 않습니다.
http.invalidCharacterDomain.atEnd=도메인 이름의 끝 위치에, 문자 [{0}]은(는) 유효하지 않습니다.
http.invalidCharacterDomain.atStart=도메인 이름의 시작 위치에, 문자 [{0}]은(는) 유효하지 않습니다.
http.invalidHextet=유효하지 않은 헥스텟(hextet)입니다. 헥스텟은 반드시 네 개 이하의 문자들이어야 합니다.
http.invalidIpv4Location=IPv6 주소가, 유효하지 않은 위치에 내장 IPv4 주소를 포함하고 있습니다.
http.invalidLeadingZero=IPv4 옥텟(octet)은, 값이 0이 아닌 이상, 0으로 시작해서는 안됩니다.
http.invalidOctet=유효하지 않은 옥텟(octet) [{0}]. IPv4 옥텟의 유효한 범위는 0에서 255까지입니다.
http.invalidSegmentEndState=상태 [{0}]은(는) segment의 끝으로 유효하지 않습니다.
http.noClosingBracket=IPv6 주소에 닫는 대괄호가 없습니다.
http.noOpeningBracket=IPv6 주소에 여는 대괄호가 없습니다.
http.singleColonEnd=IPv6 주소는 단일 ':' 문자로 끝나서는 안됩니다.
http.singleColonStart=IPv6 주소는 단일의 ':'으로 시작할 수 없습니다.
http.tooFewHextets=IPv6 주소는 반드시 8개의 헥스텟(hextet)들로 이루어져야 하지만, 이 주소는 [{0}] 개의 헥스텟들으로 이루어져 있고, 하나 이상의 0 헥스텟들을 표시하기 위한 ''::'' 시퀀스도 존재하지 않습니다.
http.tooManyColons=IPv6 주소는 연속으로 두 개를 초과한 콜론 문자('':'')들을 포함할 수 없습니다.
http.tooManyDoubleColons=IPv6 주소는 단일한 '::' 시퀀스만을 포함해야 합니다.
http.tooManyHextets=IPv6 주소가 [{0}]개의 헥스텟(hextet)들을 포함하고 있지만, 유효한 IPv6 주소는 8개를 초과할 수 없습니다.
