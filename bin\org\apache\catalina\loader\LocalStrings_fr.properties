# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

webappClassLoader.addExportsJavaIo=Lorsque Java 9 ou ultérieur est utilisé, "--add-opens=java.base/java.io={0}" doit être ajouté aux arguments de la ligne de commande de la JVM pour activer la protection contre les fuites de mémoire du cache de ObjectStream; cet avertissement peut aussi être supprimé en désactivant cette protection
webappClassLoader.addExportsRmi=Quand Java 9 est utilisé, il faut utiliser "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED" sur la ligne de commande de la JVM pour activer la détection de fuites de mémoire des cibles RMI ; sinon cet avertissement peut être supprimé en désactivant cette détection
webappClassLoader.addExportsThreadLocal=Quand Java 9 est utilisé, il faut utiliser "--add-opens=java.base/java.lang=ALL-UNNAMED" sur la ligne de commande de la JVM pour activer la détection de fuites de mémoire des ThreadLocal ; sinon cet avertissement peut être supprimé en désactivant cette détection
webappClassLoader.addPermissionNoCanonicalFile=Impossible d’obtenir le chemin de fichier canonique pour l''URL [{0}]
webappClassLoader.addPermissionNoProtocol=Le protocole [{0}] dans l''URL [{1}] n''est pas supporté donc aucune permission de lecture n''a été assignée pour les ressources situées à cette URL
webappClassLoader.addTransformer=Ajout d''un transformateur de fichier de class [{0}] pour l''application web [{1}]
webappClassLoader.addTransformer.duplicate=Ajout en double ignoré du transformateur de fichiers de classe [{0}] à l''application web [{1}]
webappClassLoader.addTransformer.illegalArgument=L''application web [{0}] a essayé d''ajouter un transformateur de fichiers de classe null
webappClassLoader.checkThreadLocalsForLeaks=L''application web [{0}] a crée un ThreadLocal avec une clé de type [{1}] (valeur [{2}]) et une valeur de type [{3}] (valeur [{4}]) mais ne l''a pas supprimé lorsqu''elle a été arrêtée, les threads seront graduellement renouvelés pour éviter une probable fuite de mémoire
webappClassLoader.checkThreadLocalsForLeaks.badKey=Impossible de déterminer une représentation sous forme de chaîne de caractères d''une clé du type [{0}]
webappClassLoader.checkThreadLocalsForLeaks.badValue=Impossible de déterminer la représentation sous forme de chaîne de caractère de la valeur du type [{0}]
webappClassLoader.checkThreadLocalsForLeaks.unknown=Inconnu
webappClassLoader.checkThreadLocalsForLeaksFail=Echec de vérfication des références ThreadLocal pour l''application web [{0}]
webappClassLoader.checkThreadLocalsForLeaksNone=L''application web [{0}] a crée un ThreadLocal avec une clé de type [{1}] (valeur [{2}]) et une valeur de type [{3}] (valeur [{4}]) mais comme les clés n''ont que des références faibles dans la structure des ThreadLocal cela ne causera pas de fuite de mémoire
webappClassLoader.checkThreadLocalsForLeaksNull=L''application web [{0}] a crée un ThreadLocal avec une clé de type [{1}] (valeur [{2}]), ce ThreadLocal a correctement été fixé à null et la valeur sera enlevée par la GC
webappClassLoader.checkThreadsHttpClient=Trouvé une thread utilisé par HttpClient pour maintenir les connections actives, corrigé en associant le thread avec le chargeur de classe parent
webappClassLoader.clearJdbc=L''application web [{0}] a enregistré un pilote JDBC [{1}], mais ne l''a pas désenregistré avant l''arrêt de l''application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
webappClassLoader.clearObjectStreamClassCachesFail=Impossible d''effacer les références faibles de ObjectStreamClass$Caches pour l''application web [{0}]
webappClassLoader.clearRmi=Trouvé une cible RMI avec une classe squelette de classe [{0}] et une valeur [{1}], celle ci a été enlevée de force pour prévenir ue fuite de mémoir
webappClassLoader.clearRmiFail=Impossible d''effacer le chargeur de classes de contexte référencé depuis sun.rmi.transport.Target pour l''application web [{0}]
webappClassLoader.clearRmiInfo=Impossible de trouver la classe sun.rmi.transport.Target pour nettoyer le chargeur de classes du contexte pour l''application web [{0}], c''est normal pour les JVMs non Oracle
webappClassLoader.getThreadGroupError=Impossible d''obtenir le parent pour le ThreadGroup [{0}], il ne sera pas possible de vérifier tous les threads pour des fuites de mémoire
webappClassLoader.jarsAdded=Un ou plusieurs JARs ont été ajoutés à l''application web [{0}]
webappClassLoader.jarsModified=Un ou plusieurs JARs ont été modifiés dans l''application web [{0}]
webappClassLoader.jarsRemoved=Un ou plusieurs JARs ont été enlevés de l''application web [{0}]
webappClassLoader.javaseClassLoaderNull=L'attribut j2seClassLoader ne doit pas être null
webappClassLoader.jdbcRemoveFailed=Le désenregistrement du pilote JDBC a échoué pour l''application web [{0}]
webappClassLoader.loadedByThisOrChildFail=Impossible de vérifier complètement les entrées d''une instance de [{0}] pour des fuites de mémoire potentielles dans le contexte [{1}]
webappClassLoader.readError=Erreur lors de la lecture de la resource : impossible de charger [{0}].
webappClassLoader.removeTransformer=Enlevé le transformateur de fichiers de classe [{0}] de l''application web [{1}]
webappClassLoader.resourceModified=La ressource [{0}] a été modifiée, la date de dernière modification était [{1}] et est désormais [{2}]
webappClassLoader.restrictedPackage=Violation de sécurité en essayant d''utiliser à une classe à accès restreint [{0}]
webappClassLoader.securityException=Exception de sécurité en essayant de trouver la classe [{0}] dans findClassInternal [{1}]
webappClassLoader.stackTrace=L''application web [{0}] semble avoir démarré un thread nommé [{1}] mais ne l''a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : {2}
webappClassLoader.stackTraceRequestThread=Une requête de l''application web [{0}] est toujours en cours, ce qui causera certainement une fuite de mémoire, vous pouvez contrôler le temps alloué en utilisant l''attribut unloadDelay de l''implémentation standard de Context ; trace du fil d’exécution de la requête : [{2}]
webappClassLoader.stopThreadFail=Impossible de terminer le thread nommé [{0}] pour l''application [{1}]
webappClassLoader.stopTimerThreadFail=Echec de l''arrêt du TimerThread nommé [{0}] pour l''application web [{1}]
webappClassLoader.stopped=Impossible de charger [{0}], ce chargeur de classes a déjà été arrêté
webappClassLoader.superCloseFail=Echec lors de l'appel de close() dans la superclasse
webappClassLoader.transformError=Erreur d''instrumentation : impossible de transformer la classe [{0}] parce que son format est illégal
webappClassLoader.warnTimerThread=L''application [{0}] semble avoir démarré un TimerThread nommé [{1}] en utilisant java.util.Timer mais ne l''a pas stoppé, le timer ainsi que le thread associé ont été arrêtés pour éviter une fuite de mémoire
webappClassLoader.wrongVersion=(impossible de charger la classe [{0}])

webappClassLoaderParallel.registrationFailed=L'enregistrement de org.apache.catalina.loader.ParallelWebappClassLoader comme pouvant charger des classes en parallèle a échoué

webappLoader.deploy=Déploiement des classes des réceptacles (class repositories) vers le répertoire de travail [{0}]
webappLoader.reloadable=Impossible de mettre la propriété rechargeable à [{0}]
webappLoader.setContext.ise=Il est interdit de définir le Context lorsque le chargeur de classes a déjà été démarré
webappLoader.starting=Démarrage de ce chargeur (loader)
webappLoader.stopping=Arrêt de ce chargeur (loader)
