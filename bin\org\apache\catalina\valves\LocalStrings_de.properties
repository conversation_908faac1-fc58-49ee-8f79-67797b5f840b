# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

accessLogValve.invalidLocale=Konnte Locale nicht auf [{0}] setzen
accessLogValve.openFail=Konnte Access Logfile [{0}] nicht öffnen
accessLogValve.rotateFail=Rotieren des Zugriffslogs ist fehlgeschlagen

errorReportValve.description=Beschreibung
errorReportValve.note=Hinweis
errorReportValve.rootCauseInLogs=Der komplette Stacktrace der Ursache ist in den Server logs zu finden
errorReportValve.unknownReason=Unbekannter Grund

http.401.reason=Unautorisiert
http.402.desc=Dieser Status-Code ist für eine zukünftige Nutzung reserviert
http.403.desc=Der Server hat die Anfrage verstanden, verbietet aber eine Autorisierung.
http.404.reason=nicht gefunden
http.406.reason=Nicht annehmbar
http.407.reason=Authentisierung für Proxy benötigt
http.411.reason=Länge benötigt
http.412.reason=Vorbedingung nicht erfüllt
http.415.reason=Nicht unterstützter Media-Type
http.421.desc=Die Anfrage wurde an einen Server gestellt der keine Antwort erzeugen konnte.
http.423.desc=Die Quell- oder Zielressource einer Methode ist gesperrt.
http.423.reason=Gesperrt
http.426.reason=Upgrade nötig
http.429.reason=Zu viele Anfragen
http.504.reason=Gateway-Zeitüberschreitung
http.505.reason=HTTP Version nicht unterstützt
http.507.reason=Nicht genügend Speicherplatz
http.510.reason=Nicht erweitert
http.511.desc=Um Netzwerk Zugriff zu erlangen muss sich der Client authentifizieren.

remoteCidrValve.noRemoteIp=Client verfügt über keine IP Adresse. Zugriff verweigert.

remoteIpValve.invalidPortHeader=Ungültiger Wert [{0}] für Port im HTTP Header [{1}] gefunden
