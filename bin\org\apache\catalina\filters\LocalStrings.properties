# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

addDefaultCharset.unsupportedCharset=Specified character set [{0}] is not supported

corsFilter.invalidPreflightMaxAge=Unable to parse preflightMaxAge
corsFilter.invalidSupportsCredentials=It is not allowed to configure supportsCredentials=[true] when allowedOrigins=[*]
corsFilter.nullRequest=HttpServletRequest object is null
corsFilter.nullRequestType=CORSRequestType object is null
corsFilter.onlyHttp=CORS doesn't support non-HTTP request or response
corsFilter.wrongType1=Expects an HttpServletRequest object of type [{0}]
corsFilter.wrongType2=Expects an HttpServletRequest object of type [{0}] or [{1}]

csrfPrevention.invalidRandomClass=Unable to create Random source using class [{0}]

expiresFilter.exceptionProcessingParameter=Exception processing configuration parameter [{0}]:[{1}]
expiresFilter.expirationHeaderAlreadyDefined=Request [{0}] with response status [{1}] content-type [{2}], expiration header already defined
expiresFilter.filterInitialized=Filter initialized with configuration [{0}]
expiresFilter.invalidDurationNumber=Invalid duration (number) [{0}] in directive [{1}]
expiresFilter.invalidDurationUnit=Invalid duration unit (years|months|weeks|days|hours|minutes|seconds) [{0}] in directive [{1}]
expiresFilter.noDurationFound=Duration not found in directive [{0}]
expiresFilter.noDurationUnitAfterAmount=Duration unit not found after amount [{0}] in directive [{1}]
expiresFilter.noExpirationConfigured=Request [{0}] with response status [{1}] content-type [{2}], no expiration configured
expiresFilter.noExpirationConfiguredForContentType=No Expires configuration found for content-type [{0}]
expiresFilter.numberError=Exception parsing number at position [{0}] (zero based) in comma delimited list [{1}]
expiresFilter.responseAlreadyCommitted=Request [{0}], cannot apply ExpiresFilter on already committed response.
expiresFilter.setExpirationDate=Request [{0}] with response status [{1}] content-type [{2}], set expiration date [{3}]
expiresFilter.skippedStatusCode=Request [{0}] with response status [{1}] content-type [{1}], skip expiration header generation for given status
expiresFilter.startingPointInvalid=Invalid starting point (access|now|modification|a<seconds>|m<seconds>) [{0}] in directive [{1}]
expiresFilter.startingPointNotFound=Starting point (access|now|modification|a<seconds>|m<seconds>) not found in directive [{0}]
expiresFilter.unknownParameterIgnored=Unknown parameter [{0}] with value [{1}] is ignored !
expiresFilter.unsupportedStartingPoint=Unsupported startingPoint [{0}]
expiresFilter.useDefaultConfiguration=Use default [{0}] for content-type [{1}] returns [{2}]
expiresFilter.useMatchingConfiguration=Use [{0}] matching [{1}] for content-type [{2}] returns [{3}]

filterbase.noSuchProperty=The property [{0}] is not defined for filters of type [{1}]

http.403=Access to the specified resource [{0}] has been forbidden.

httpHeaderSecurityFilter.clickjack.invalid=An invalid value [{0}] was specified for the anti click-jacking header
httpHeaderSecurityFilter.committed=Unable to add HTTP headers since response is already committed on entry to the HTTP header security Filter

remoteCidrFilter.invalid=Invalid configuration provided for [{0}]. See previous messages for details.
remoteCidrFilter.noRemoteIp=Client does not have an IP address. Request denied.

remoteIpFilter.invalidHostHeader=Invalid value [{0}] found for Host in HTTP header [{1}]
remoteIpFilter.invalidHostWithPort=Host value [{0}] in HTTP header [{1}] included a port number which will be ignored
remoteIpFilter.invalidNumber=Illegal number for parameter [{0}]: [{1}]
remoteIpFilter.invalidRemoteAddress=Unable to determine the remote host because the reported remote address [{0}] is not valid

requestFilter.deny=Denied request for [{0}] based on property [{1}]

restCsrfPreventionFilter.invalidNonce=CSRF nonce validation failed

webDavFilter.xpProblem=WebdavFixFilter: the XP-x64-SP2 client is known not to work with WebDAV Servlet
webDavFilter.xpRootContext=WebdavFixFilter: the XP-x64-SP2 client will only work with the root context
