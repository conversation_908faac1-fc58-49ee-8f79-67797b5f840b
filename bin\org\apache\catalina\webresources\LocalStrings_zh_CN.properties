# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractArchiveResourceSet.setReadOnlyFalse=基于存档的WebResourceSets 如基于jar的WebResourceSets 硬编码为只读，并且不能配置为读写

abstractResource.getContentFail=无法把[{0}]作为byte数组返回
abstractResource.getContentTooLarge=无法返回[{0}]作为字节数组，因为资源的大小[[1]]个字节大于字节数组的最大大小

abstractResourceSet.checkPath=请求的路径[{0}]无效。必须以“/”开头。

cache.addFail=无法将位于[{0}]的资源添加到Web应用程序[{1}]的缓存中，因为在清除过期缓存条目后可用空间仍不足 - 请考虑增加缓存的最大空间。
cache.backgroundEvictFail=后台缓存收回进程无法释放上下文[{1}的缓存的[{0}]%-请考虑增加缓存的最大大小。在逐出之后，缓存中大约保留了[{2}]KB的数据。
cache.objectMaxSizeTooBig=objectMaxSize的值[{0}]kB大于maxSize/20的限制，因此已缩减为[{1}]kB
cache.objectMaxSizeTooBigBytes=为要缓存的最大对象大小[{0}] kB指定的值大于Integer.MAX_VALUE字节，后者是可以缓存的最大大小。该限制将设置为Integer.MAX_VALUE字节。

cachedResource.invalidURL=无法创建CachedResourceURLStreamHandler实例，因为URL[{0}]畸形

classpathUrlStreamHandler.notFound=无法使用线程上下文类加载器或当前类的类加载器加载资源{0}

dirResourceSet.manifestFail=从[{0}]读取manifest失败
dirResourceSet.notDirectory=基本和内部路径[{0}] {1} [{2}]指定的目录不存在。
dirResourceSet.writeNpe=输入流不能为空

extractingRoot.jarFailed=解压JAR文件[{0}]失败
extractingRoot.targetFailed=无法为提取的 JAR 文件创建目录 [{0}]

fileResource.getCanonicalPathFail=不能判断资源的标准路径[{0}]
fileResource.getCreationFail=无法确定资源 [{0}] 的创建时间
fileResource.getUrlFail=不能决定一个url 为资源[{0}]

fileResourceSet.notFile=由基路径和内部路径[{0}]{1}[{2}]指定的文件不存在。

jarResource.getInputStreamFail=无法获取JAR[{1}]中的资源文件[{0}]的一个InputStream

jarResourceRoot.invalidWebAppPath=此资源总是引用一个目录，因此提供的webAppPath必须以/结尾，但提供的webAppPath是[{0}]

jarWarResourceSet.codingError=编码错误

standardRoot.checkStateNotStarted=如果当前未启动资源，则可能无法访问这些资源
standardRoot.createInvalidFile=无法从[{0}]创建WebResourceSet。
standardRoot.createUnknownType=无法为未知类型[{0}]创建WebResourceSet。
standardRoot.invalidPath=资源路径[{0}]无效
standardRoot.invalidPathNormal=资源路径[{0}]已规范化为无效的[{1}]
standardRoot.lockedFile=Web应用程序[{0}]无法关闭通过以下堆栈跟踪打开的文件[{1}]
standardRoot.noContext=尚未为WebResourceRoot配置上下文
standardRoot.startInvalidMain=指定的主资源集 [{0}] 无效
standardRoot.unsupportedProtocol=此web资源实现不支持URL协议[{0}]
