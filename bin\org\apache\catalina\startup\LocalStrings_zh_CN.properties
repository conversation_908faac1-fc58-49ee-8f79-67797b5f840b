# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

catalina.configFail=无法从[{0}]加载服务器配置
catalina.noCluster=由于[{0}]未找到群集Ruleset。已禁用群集配置。
catalina.noNaming=命名环境已禁用
catalina.serverStartFail=所必需的服务组件启动失败，所以无法启动Tomcat
catalina.shutdownHookFail=关闭挂钩在尝试停止服务器时遇到错误
catalina.stopServer=未配置关闭端口。通过OS信号关闭服务器。服务器未关闭。

connector.noSetExecutor=连接器[{0}]不支持外部执行器。找不到方法setExecutor（java.util.concurrent.Executor）
connector.noSetSSLImplementationName=连接器[{0}]不支持更改SSL实现。找不到方法setslimplementationname（String）。

contextConfig.altDDNotFound=未找到 alt-dd 文件 [{0}]
contextConfig.annotationsStackOverflow=由于StackOverflower错误，无法完成对web应用程序[{0}]的批注的扫描。可能的根本原因包括-Xss的设置过低和非法的循环继承依赖项。正在处理的类层次结构是[{1}]
contextConfig.applicationMissing=web.xml文件丢失，只使用默认。
contextConfig.applicationParse=解析应用程序的web.xml错误，位置：[{0}]
contextConfig.applicationPosition=发生在第[{0}]行，属性[{1}]
contextConfig.applicationStart=正在解析应用程序 web.xml 文件 [{0}]
contextConfig.applicationUrl=无法确定应用程序 web.xml 的URL
contextConfig.authenticatorConfigured=为方法 [{0}] 配置了验证器
contextConfig.authenticatorInstantiate=无法实例化认证类[{0}]
contextConfig.authenticatorMissing=不能配置一个认证为方法[{0}]
contextConfig.authenticatorResources=无法加载认证者映射列表
contextConfig.badUrl=不能处理上下文描述符[{0}]
contextConfig.cce=生命周期事件数据对象[{0}] 不是一个上下文
contextConfig.contextClose=关闭context.xml错误
contextConfig.contextMissing=缺少 context.xml：[{0}]
contextConfig.contextParse=解析context.xml错误，[{0}]
contextConfig.defaultError=处理默认web.xml错误，名称：[{0}]，位置：[{1}]
contextConfig.defaultMissing=未找到全局 web.xml
contextConfig.defaultPosition=发生在 [{0}] 行 [{1}] 列
contextConfig.destroy=ContextConfig：正在销毁
contextConfig.fileUrl=无法从URL[{0}]创建文件对象
contextConfig.fixDocBase=上下文[{0}]的异常修复docBase
contextConfig.init=上下文配置: 正在初始化
contextConfig.inputStreamFile=无法为注解处理文件[{0}]
contextConfig.inputStreamJar=无法处理Jar实体[{0}]的注解，Jar：[{1}]
contextConfig.inputStreamWebResource=不能处理注释的Web资源[{0}]
contextConfig.invalidSciHandlesTypes=无法为核对一个或多个ServletContentInitializers的注解@HandlesTypes而加载类[{0}]
contextConfig.jarFile=无法处理Jar[{0}]的注解
contextConfig.jspFile.error=JSP文件[{0}]必须以''/''开头。
contextConfig.jspFile.warning=警告：在Servlet 2.4 中，JSP文件[{0}]必须以‘/’开头
contextConfig.missingRealm=对应的认证领域未配置
contextConfig.noAntiLocking=配置 java.io.tmpdir的值[{0}]未指向有效路径。Web应用[{1}]antiResourceLocking配置将被忽略
contextConfig.processAnnotationsDir.debug=使用注解 [{0}]扫描目录中的类文件
contextConfig.processAnnotationsJar.debug=扫描jar文件中注解[{0}]的类文件
contextConfig.processAnnotationsWebDir.debug=扫描 web 应用程序目录下含有 [{0}] 注解的 class 文件
contextConfig.resourceJarFail=无法处理在URL[{0}]处找到的JAR，以便将静态资源包含在名为[{1}]的上下文中
contextConfig.role.auth=在标签<auth-constraint>的子标签<security-role>中没有定义角色名[{0}]
contextConfig.role.link=<role-link>中使用的安全角色[{0}]未被定义在<security-role>中
contextConfig.role.runas=<run-as> 中使用的安全角色名 [{0}]，未在 <security-role> 中定义
contextConfig.sci.debug=无法为[{0}]处理ServletContainerInitializer，很可能是由于丢失了一个使用@HandlesTypes注解定义的类
contextConfig.sci.info=无法处理[{0}]的ServletContainerInitializer。这很可能是由于@handleTypes注释中定义的类丢失所致。为完整堆栈跟踪启用调试级别日志记录。
contextConfig.servletContainerInitializerFail=无法检测到上下文名称为[{0}]的ServletContainerInitializers
contextConfig.start=ContextConfig：开始处理
contextConfig.stop=ContextConfig：停止处理
contextConfig.unavailable=由于之前的错误，标记当前应用程序不可用
contextConfig.unknownUrlProtocol=注解解析过程中，URL协议[{0}]未识别。忽略URL[{1}]。
contextConfig.urlPatternValue=类文件[{1}]的urlPatterns和值属性上同时设置了注解[{0}]
contextConfig.xmlSettings=上下文[{0}]将解析web.xml和web-fragment.xml文件，验证为：[{1}]，命名空间感知为：[{2}]

engineConfig.cce=生命周期事件数据对象[{0}]不是一个引擎(Engine)
engineConfig.start=EngineConfig:处理开始。
engineConfig.stop=引擎配置：处理进程停止

expandWar.copy=错误的拷贝[{0}] to [{1}]
expandWar.createFailed=无法创建文件夹[{0}]。
expandWar.createFileFailed=无法创建文件[{0}]
expandWar.deleteFailed=[{0}] 无法被彻底删除。其余残留文件可能会导致问题
expandWar.deleteOld=发现一个展开的目录[{0}],它的最后修改时间与关联的WAR不一致.它将被删除.
expandWar.illegalPath=归档[{0}]格式错误，将被忽略：条目包含非扩展到[{2}]的非法路径[{1}]，因为它超出了定义的docBase [{3}]
expandWar.lastModifiedFailed=无法为[{0}]设置上次修改时间
expandWar.missingJarEntry=无法获得 JarEntry [{0}] 的输入流 - WAR 文件是否已损坏?

failedContext.start=无法处理全局，每个主机或特定于上下文的context.xml文件，因此无法启动[{0}]上下文。

hostConfig.appBase=主机[{0}]的应用程序基础[{1}]不存在或不是目录。deployOnStartUp和autoDebug已设置为false，以防止部署错误。其他错误仍然可能发生。
hostConfig.canonicalizing=试图取消部署[{1}]时，无法确定[{0}]的规范路径
hostConfig.cce=生命周期事件数据对象[{0}]不是主机
hostConfig.context.remove=移除上下文[{0}]错误
hostConfig.context.restart=上下文[{0}]重新启动时出错。
hostConfig.createDirs=无法为部署创建目录：[{0}]
hostConfig.deploy.error=部署web应用程序目录[{0}]时发生异常
hostConfig.deployDescriptor=正在部署部署描述符[{0}]。
hostConfig.deployDescriptor.blocked=未部署上下文路径为[{0}]的Web应用程序，因为它包含一个部署描述符[{1}]，该描述符可能包含安全部署应用程序所需的配置，但此主机的DeployXML设置阻止了部署描述符的处理。应该在[{2}]创建适当的描述符来部署此应用程序。
hostConfig.deployDescriptor.error=部署描述符[{0}]时出错
hostConfig.deployDescriptor.finished=部署描述符[{0}]的部署已在[{1}]ms内完成
hostConfig.deployDescriptor.localDocBaseSpecified=在主机appBase 中指定了docBase [{0}]，将被忽略
hostConfig.deployDescriptor.threaded.error=等待部署描述符的多线程部署完成时出错
hostConfig.deployDir=把web 应用程序部署到目录 [{0}]
hostConfig.deployDir.error=无法部署应用目录 [{0}]
hostConfig.deployDir.finished=Web应用程序目录[{0}]的部署已在[{1}]毫秒内完成
hostConfig.deployDir.threaded.error=等待目录的多线程部署完成时出错
hostConfig.deployWar=正在部署web应用程序存档文件[{0}]
hostConfig.deployWar.error=部署 Web 应用程序 archive [{0}] 时出错
hostConfig.deployWar.finished=web应用程序存档文件[{0}]的部署已在[{1}]ms内完成
hostConfig.deployWar.hiddenDir=将忽略目录[{0}]，因为WAR [{1}]优先，unpackWAR为false
hostConfig.deployWar.threaded.error=等待WAR文件的多线程部署完成时出错
hostConfig.deploying=部署发现的web应用
hostConfig.docBaseUrlInvalid=所提供的部署目录无法用URL来表示
hostConfig.expand=正在扩展web应用程序存档文件[{0}]
hostConfig.expand.error=解压WEB应用程序文件[{0}]时异常
hostConfig.ignorePath=忽略appBase中的路径[{0}]以进行自动部署
hostConfig.illegalWarName=war名称[{0}]无效。存档将被忽略
hostConfig.jmx.register=注册上下文[{0}]失败。
hostConfig.jmx.unregister=移除注册上下文[{0}]失败
hostConfig.reload=重新加载上下文[{0}]
hostConfig.resourceNotAbsolute=无法从上下文[{0}]中删除资源，因为[{1}]不是绝对路径
hostConfig.start=HostConfig: 开始处理
hostConfig.stop=Host配置:停止处理
hostConfig.undeploy=正在取消部署上下文[{0}]
hostConfig.undeployVersion=正在取消部署没有活动会话的旧版本上下文[{0}]

passwdUserDatabase.readFail=无法从/etc/passwd获取完整的用户集

tomcat.addWebapp.conflictChild=无法在[{0}]处部署到上下文路径[{1}]，因为存在上下文[{2}]
tomcat.addWebapp.conflictFile=由于现有文件[{2}]的存在，无法在[{0}]将war部署到上下文路径[{1}]
tomcat.baseDirMakeFail=无法创建用作基本目录的目录{0}
tomcat.baseDirNotDir=基本目录指定的位置[{0}]不是一个目录
tomcat.defaultMimeTypeMappingsFail=无法加载默认MIME类型
tomcat.homeDirMakeFail=无法创建用作主目录的目录 [{0}]

userConfig.database=加载用户数据库异常
userConfig.deploy=正在为用户[{0}]部署web应用程序
userConfig.deploy.threaded.error=等待用户目录的多线程部署完成时出错
userConfig.deploying=正在部署用户 web 应用程序
userConfig.error=为用户 [{0}]部署web应用发生错误
userConfig.start=用户配置：处理开始
userConfig.stop=UserConfig:处理停止

versionLoggerListener.arg=命令行参数：       {0}
versionLoggerListener.catalina.base=CATALINA_BASE:     {0}
versionLoggerListener.catalina.home=CATALINA_HOME:     {0}
versionLoggerListener.env=环境变量：         {0} = {1}
versionLoggerListener.java.home=Java 环境变量:     {0}
versionLoggerListener.os.arch=架构:              {0}
versionLoggerListener.os.name=操作系统名称:      {0}
versionLoggerListener.os.version=OS.版本:           {0}
versionLoggerListener.prop=系统属性:          {0} = {1}
versionLoggerListener.serverInfo.server.built=服务器构建:        {0}
versionLoggerListener.serverInfo.server.number=服务器版本号:      {0}
versionLoggerListener.serverInfo.server.version=Server.服务器版本: {0}
versionLoggerListener.vm.vendor=JVM.供应商:        {0}
versionLoggerListener.vm.version=Java虚拟机版本:    {0}

webAnnotationSet.invalidInjection=方法资源注入注解无效。
