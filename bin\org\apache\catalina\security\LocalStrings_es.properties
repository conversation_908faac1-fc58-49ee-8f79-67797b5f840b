# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

SecurityListener.checkUmaskFail=Intentado arranque con valor de umask de [{0}]. Ejecutando Tomcat sin umask al menos tan restrictivo como [{1}] ha sido bloqueado por el oyente de Ciclo de Vida  org.apache.catalina.security.SecurityListener (normalmente configurado en  CATALINA_BASE/conf/server.xml)
SecurityListener.checkUmaskNone=No se ha hallado valor de umask en propiedad de sistema [{0}]. Si embargo, parece que Tomcat está siendo ejecutado en una plataforma que soporta umask. La propiedad del sistema se pone normalmente en CATALINA_HOME/bin/catalina.sh. El oyente de Ciclo de Vida org.apache.catalina.security.SecurityListener (normalmente configurado en CATALINA_BASE/conf/server.xml) espera un umask al menos tan restrictivo como [{1}]
SecurityListener.checkUmaskParseFail=No pude anallizar el valor [{0}] como in válido umask.
SecurityListener.checkUmaskSkip=No pude determinar umask. Parece que Tomcat se está ejecutando en Windows, por lo que se salta el chequeo de umsak.
SecurityListener.checkUserWarning=Se ha intentado arrancar mientras se ejecutaba como usuario [{0}]. Ejecutando Tomcat como este usuario user ha sido bloqueado por el oyente de Ciclos de Vida org.apache.catalina.security.SecurityListener (normalmente configurado en  CATALINA_BASE/conf/server.xml)

SecurityUtil.doAsPrivilege=Ha tenido lugar una excepción al ejecutar el bloque PrivilegedExceptionAction.
