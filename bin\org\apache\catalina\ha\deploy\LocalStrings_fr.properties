# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

farmWarDeployer.alreadyDeployed=l''application web [{0}] est déjà déployée
farmWarDeployer.deleteFail=Pas réussi à supprimer [{0}]
farmWarDeployer.deployEnd=Le déploiement de [{0}] est terminé
farmWarDeployer.fileCopyFail=Impossible de copier depuis [{0}] vers [{1}]
farmWarDeployer.hostOnly=Le FarmWarDeployer ne fonctionne qu'en tant que sous-élément d'un "host cluster" !
farmWarDeployer.hostParentEngine=FarmWarDeployer peut fonctionner uniquement si le parent de [{0}] est un moteur
farmWarDeployer.mbeanNameFail=Impossible de construire le nom d''objet du mbean pour le moteur [{0}] et l''hôte [{1}]
farmWarDeployer.modInstall=Installation en cours pour la webapp [{0}] depuis [{1}]
farmWarDeployer.modInstallFail=Incapable d'installer le fichier WAR
farmWarDeployer.msgIoe=Incapable de lire le message de déploiement dans la ferme
farmWarDeployer.msgRxDeploy=Recu le chemin de déploiement [{0}] du cluster, war [{1}]
farmWarDeployer.msgRxUndeploy=Réception d''un retrait de cluster ("cluster undeployment") du chemin [{0}]
farmWarDeployer.removeFailLocal=Impossible d''enlever localement de [{0}]
farmWarDeployer.removeFailRemote=La suppression locale de [{0}] a échouée, l''autre gestionnaire (manager) a l''app en fonction !
farmWarDeployer.removeLocal=Retrait de l''application web [{0}]
farmWarDeployer.removeLocalFail=Impossible d'enlever le fichier WAR
farmWarDeployer.removeStart=Retrait de l''application web [{0}] dans tout le cluster
farmWarDeployer.removeTxMsg=Envoi à tout le cluster du déploiement à partir de [{0}]
farmWarDeployer.renameFail=Echec du renommage de [{0}] en [{1}]
farmWarDeployer.sendEnd=Envoi du chemin de déploiement du war au cluster, war [{1}] terminé
farmWarDeployer.sendFragment=Envoi du chemin du fragment du war du cluster [{0}], war [{1}] vers [{2}]
farmWarDeployer.sendStart=Envoi du déploiement war en cluster chemin [{0}], war [{1}] démarré
farmWarDeployer.servicingDeploy=L''application [{0}] est en cours de maintenance, mettez de nouveau à jour la date du fichier war [{1}]
farmWarDeployer.servicingUndeploy=L''application [{0}] est en maintenance et ne peut être retirée du node backup du cluster
farmWarDeployer.started=Le FarmWarDeployer du cluster a démarré
farmWarDeployer.stopped=Le FarmWarDeployer du cluster a été arrêté
farmWarDeployer.undeployEnd=Retrait de [{0}] terminé
farmWarDeployer.undeployLocal=Le contexte local [{0}] est retiré
farmWarDeployer.watchDir=Le déploiement du cluster surveille [{0}] pour des modifications

fileMessageFactory.cannotRead=Impossible de lire un message, cette fabrique est en train d'écrire
fileMessageFactory.cannotWrite=Impossible d'écrire un message, cette fabrique est en train de lire
fileMessageFactory.closed=La fabrique a été fermée
fileMessageFactory.deleteFail=Impossible de supprimer [{0}]
fileMessageFactory.duplicateMessage=Réception de message en double, le délai d''attente maximum de l''expéditeur pourrait être trop court ; contexte : [{0}] nom de fichier : [{1}] données : [{2}] longueur des données : [{3}]

fileNewFail=Impossible de créer [{0}]

warWatcher.cantListWatchDir=Incapacité de lister les fichiers dans le répertoire WatchDir [{0}] : vérifiez qu''il s''agit d''un répertoire et qu''il a la permission de lecture.
warWatcher.checkWarResult=WarInfo.check() a retourné [{0}] pour [{1}]
warWatcher.checkingWar=Vérification du fichier WAR [{0}]
warWatcher.checkingWars=Vérification des WARs dans [{0}]
warWatcher.listedFileDoesNotExist=[{0}] a été détecté dans [{1}] mais n''existe pas, les permissions du répertoire pourraient être incorrectes
