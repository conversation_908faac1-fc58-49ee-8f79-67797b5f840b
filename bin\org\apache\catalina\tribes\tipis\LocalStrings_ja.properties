# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractReplicatedMap.broadcast.noReplies=恐らくタイムアウトが発生したため、ブロードキャストの返信は 0 でした。
abstractReplicatedMap.heartbeat.failed=AbstractReplicatedMap.pingメッセージを送信できません。
abstractReplicatedMap.init.completed=AbstractReplicatedMap [{0}]の初期化は[{1}] msで完了しました。
abstractReplicatedMap.init.start=コンテキスト名:[{0}]を使用してAbstractReplicatedMapを初期化しています。
abstractReplicatedMap.leftOver.ignored=メッセージ [{0}] は無視します。
abstractReplicatedMap.leftOver.pingMsg=PINGメッセージがタイムアウト期間を超えて受信されました。 マップメンバー[{0}]はマップメンバーシップから削除されている可能性があります。
abstractReplicatedMap.mapMember.unavailable=メンバー[{0}]はまだ利用できません。
abstractReplicatedMap.mapMemberAdded.added=マップメンバーが追加されました：[{0}]
abstractReplicatedMap.mapMemberAdded.nullMember=通知されたメンバーはメンバーシップに登録されていません：[{0}]
abstractReplicatedMap.member.disappeared=メンバー[{0}]が消滅しました。 関連するマップエントリは、新しいノードに再配置されます。
abstractReplicatedMap.ping.stateTransferredMember=メンバー [{0}] は状態を転送中で利用可能ではありません。
abstractReplicatedMap.ping.timeout=マップ[{1}]のメンバー[{0}]がping処理でタイムアウトしました。
abstractReplicatedMap.relocate.complete=マップ要素の再配置は [{0}] ミリ秒で完了しました。
abstractReplicatedMap.transferState.noReplies=転送状態、0 レスポンス、おそらくタイムアウトです。
abstractReplicatedMap.unable.deserialize.MapMessage=MapMessageのデシリアライズが出来ません。
abstractReplicatedMap.unable.diffObject=オブジェクトのdiffを取れません。 代わりにオブジェクト全体を複製します。
abstractReplicatedMap.unable.get=AbstractReplicatedMap.get オペレーションでデータの複製ができません
abstractReplicatedMap.unable.put=AbstractReplicatedMap.putオペレーションのデータをレプリケートできません
abstractReplicatedMap.unable.relocate=[{0}] を新しいバックアップノードへ再配置できません。
abstractReplicatedMap.unable.remove=AbstractReplicatedMap.remove オペレーションのデータをレプリケートできません。
abstractReplicatedMap.unable.replicate=データを複製できません。
abstractReplicatedMap.unable.retrieve=キー：[{0}]のリモートオブジェクトを取得できません。
abstractReplicatedMap.unable.transferState=AbstractReplicatedMapの状態を転送できません。
abstractReplicatedMap.unableApply.diff=キー [{0}] の差分を計算できません。
abstractReplicatedMap.unableSelect.backup=バックアップノードを選択できません。
abstractReplicatedMap.unableSend.startMessage=マップ開始メッセージを送信できません。
abstractReplicatedMap.unableStart=レプリケーションマップの起動が出来ません。

lazyReplicatedMap.unableReplicate.backup=[{2}] により、バックアップキー [{0}] をメンバー [{1}] へ複製できません。
lazyReplicatedMap.unableReplicate.proxy=[{2}] のため、プロキシキーを [{0}] からバックアップの [{1}] へ複製できません。

mapMessage.deserialize.error.key=MapMessage キーのデシリアライズに失敗しました。
mapMessage.deserialize.error.value=MapMessageの値のデシリアライズに失敗しました。

replicatedMap.member.disappeared=メンバー[{0}]が消滅しました。 関連するマップエントリは、新しいノードに再配置されます。
replicatedMap.relocate.complete=マップ要素の再配置は [{0}] ms で完了しました。
replicatedMap.unable.relocate=[{0}]を新しいバックアップノードに再配置できません
replicatedMap.unableReplicate.completely=バックアップキー[{0}]を複製できません。 成功ノード：[{1}]。 障害ノード：[{2}]。
