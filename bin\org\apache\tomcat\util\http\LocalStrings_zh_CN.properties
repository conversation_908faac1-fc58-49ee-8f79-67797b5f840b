# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cookies.fallToDebug=\n\
\ 注意：将在调试级别记录进一步出现的Cookie错误。
cookies.invalidCookieToken=Cookie：cookie无效。值不是令牌或引用值
cookies.invalidSameSiteCookies=未知设置[{0}]，必须是以下之一：unset、none、lax、strict。默认值为unset。
cookies.invalidSpecial=Cookies：未知特殊的Cookie
cookies.maxCountFail=检测到超过Cookie最大允许的数量[{0}]

headers.maxCountFail=检测到超过了允许设置的最大header 数[{0}]

parameters.bytes=开始处理输入[{0}]
parameters.copyFail=无法创建以调试日志记录为目的的原始参数值的副本
parameters.decodeFail.debug=字符解码失败.参数 [{0}]和值 [{1}]被忽略
parameters.decodeFail.info=字符解码失败。值为[{1}]的参数[{0}]已被忽略。请注意，此处引用的名称和值可能由于解码失败而损坏。使用调试级别日志记录查看原始的、未损坏的值。
parameters.emptyChunk=忽略空参数块
parameters.fallToDebug=\n\
\ 注：更多的参数错误将以DEBUG级别日志进行记录。
parameters.invalidChunk=从字节[{0}]开始到字节[{1}]结束的无效块，忽略值[{2}]
parameters.maxCountFail=检测到单个请求（[{0}]）的最大请求参数数（GET加POST）。 超出此限制的任何参数都被忽略。 要更改此限制，请在Connector上设置maxParameterCount属性。
parameters.maxCountFail.fallToDebug=\n\
\ 注意:更多的错误信息只在debug级别日志中记录
parameters.multipleDecodingFail=字符解码失败。总共检测到[{0}]个失败，但只记录了第一个失败。为此记录器启用调试级别日志记录以记录所有故障。
parameters.noequal=):参数从位置[{0}]开始，到位置[{1}]结束，值为[{2}]，后面没有“=”字符

rfc6265CookieProcessor.invalidCharInValue=Cookie值中存在无效字符[{0}]
rfc6265CookieProcessor.invalidDomain=为此cookie指定的域[{0}]无效
rfc6265CookieProcessor.invalidPath=这个cookie被指定了一个无效的路径 [{0}]
