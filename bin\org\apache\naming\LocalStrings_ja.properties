# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

contextBindings.noContextBoundToCL=Naming Contextはこのクラスローダにバインドされていません
contextBindings.noContextBoundToThread=名前付けコンテキストはこのスレッドにバインドされていません
contextBindings.unknownContext=未知のコンテキスト名です: [{0}]

namingContext.alreadyBound=名前 [{0}] は既にこのコンテキストにバインドされています
namingContext.contextExpected=名前がコンテキストにバインドされていません
namingContext.failResolvingReference=参照の解決中に予測しない例外が発生しました
namingContext.invalidName=名前は無効です
namingContext.nameNotBound=名前 [{0}] はこのコンテキストにバインドされていません
namingContext.noAbsoluteName=この名前空間に絶対名を生成できません
namingContext.readOnly=コンテキストはリードオンリーです

selectorContext.methodUsingName=オブジェクト名 [{1}] に対してメソッド [{0}] を呼び出します。
selectorContext.methodUsingString=メソッド[{0}]を[{1}]の文字列で呼び出します。
selectorContext.noJavaUrl=このコンテキストにはjava: URLを用いてアクセスされねばいけません
