# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

combinedRealm.addRealm=Add [{0}] realm, making a total of [{1}] realms
combinedRealm.authFail=Failed to authenticate user [{0}] with realm [{1}]
combinedRealm.authStart=Attempting to authenticate user [{0}] with realm [{1}]
combinedRealm.authSuccess=Authenticated user [{0}] with realm [{1}]
combinedRealm.getPassword=The getPassword() method should never be called
combinedRealm.getPrincipal=The getPrincipal() method should never be called
combinedRealm.realmStartFail=Failed to start [{0}] realm
combinedRealm.setCredentialHandler=A CredentialHandler was set on an instance of the CombinedRealm (or a sub-class of CombinedRealm). CombinedRealm doesn't use a configured CredentialHandler. Is this a configuration error?
combinedRealm.unexpectedMethod=An unexpected call was made to a method on the combined realm

credentialHandler.invalidStoredCredential=The invalid stored credential string [{0}] was provided by the Realm to match with the user provided credentials

dataSourceRealm.authenticateFailure=Username [{0}] NOT successfully authenticated
dataSourceRealm.authenticateSuccess=Username [{0}] successfully authenticated
dataSourceRealm.close=Exception closing database connection
dataSourceRealm.exception=Exception performing authentication
dataSourceRealm.getPassword.exception=Exception retrieving password for [{0}]
dataSourceRealm.getRoles.exception=Exception retrieving roles for [{0}]

jaasCallback.username=Returned username [{0}]

jaasRealm.accountExpired=Username [{0}] NOT authenticated due to expired account
jaasRealm.authenticateFailure=Username [{0}] NOT successfully authenticated
jaasRealm.authenticateSuccess=Username [{0}] successfully authenticated as Principal [{1}] -- Subject was created too
jaasRealm.beginLogin=JAASRealm login requested for username [{0}] using LoginContext for application [{1}]
jaasRealm.checkPrincipal=Checking Principal [{0}] [{1}]
jaasRealm.credentialExpired=Username [{0}] NOT authenticated due to expired credential
jaasRealm.failedLogin=Username [{0}] NOT authenticated due to failed login
jaasRealm.loginContextCreated=JAAS LoginContext created for username [{0}]
jaasRealm.loginException=Login exception authenticating username [{0}]
jaasRealm.rolePrincipalAdd=Adding role Principal [{0}] to this user Principal''s roles
jaasRealm.rolePrincipalFailure=No valid role Principals found.
jaasRealm.unexpectedError=Unexpected error
jaasRealm.userPrincipalFailure=No valid user Principal found
jaasRealm.userPrincipalSuccess=Principal [{0}] is a valid user class. We will use this as the user Principal.

jdbcRealm.authenticateFailure=Username [{0}] NOT successfully authenticated
jdbcRealm.authenticateSuccess=Username [{0}] successfully authenticated
jdbcRealm.close=Exception closing database connection
jdbcRealm.exception=Exception performing authentication
jdbcRealm.open=Exception opening database connection
jdbcRealm.open.invalidurl=Driver [{0}] does not support the url [{1}]

jndiRealm.authenticateFailure=Username [{0}] NOT successfully authenticated
jndiRealm.authenticateSuccess=Username [{0}] successfully authenticated
jndiRealm.cipherSuites=Enable [{0}] as cipher suites for tls connection.
jndiRealm.close=Exception closing directory server connection
jndiRealm.emptyCipherSuites=Empty String for cipher suites given. Using default cipher suites.
jndiRealm.exception=Exception performing authentication
jndiRealm.exception.retry=Exception performing authentication. Retrying...
jndiRealm.invalidHostnameVerifier=[{0}] not a valid class name for a HostnameVerifier
jndiRealm.invalidSslProtocol=Given protocol [{0}] is invalid. It has to be one of [{1}]
jndiRealm.invalidSslSocketFactory=[{0}] not a valid class name for an SSLSocketFactory
jndiRealm.multipleEntries=User name [{0}] has multiple entries
jndiRealm.negotiatedTls=Negotiated tls connection using protocol [{0}]
jndiRealm.open=Exception opening directory server connection
jndiRealm.tlsClose=Exception closing tls response

lockOutRealm.authLockedUser=An attempt was made to authenticate the locked user [{0}]
lockOutRealm.removeWarning=User [{0}] was removed from the failed users cache after [{1}] seconds to keep the cache size within the limit set

mdCredentialHandler.unknownEncoding=The encoding [{0}] is not supported so the current setting of [{1}] will still be used

memoryRealm.authenticateFailure=Username [{0}] NOT successfully authenticated
memoryRealm.authenticateSuccess=Username [{0}] successfully authenticated
memoryRealm.loadExist=Memory database file [{0}] cannot be read
memoryRealm.loadPath=Loading users from memory database file [{0}]
memoryRealm.readXml=Exception while reading memory database file
memoryRealm.xmlFeatureEncoding=Exception configuring digester to permit java encoding names in XML files. Only IANA encoding names will be supported.

pbeCredentialHandler.invalidKeySpec=Unable to generate a password based key

realmBase.algorithm=Invalid message digest algorithm [{0}] specified
realmBase.authenticateFailure=Username [{0}] NOT successfully authenticated
realmBase.authenticateSuccess=Username [{0}] successfully authenticated
realmBase.cannotGetRoles=Cannot get roles from principal [{0}]
realmBase.createUsernameRetriever.ClassCastException=Class [{0}] is not an X509UsernameRetriever.
realmBase.createUsernameRetriever.newInstance=Cannot create object of type [{0}].
realmBase.credentialNotDelegated=Credential for user [{0}] has not been delegated though storing was requested
realmBase.delegatedCredentialFail=Unable to obtain delegated credential for user [{0}]
realmBase.digest=Error digesting user credentials
realmBase.forbidden=Access to the requested resource has been denied
realmBase.gotX509Username=Got user name from X509 certificate: [{0}]
realmBase.gssContextNotEstablished=Authenticator implementation error: the passed security context is not fully established
realmBase.gssNameFail=Failed to extract name from established GSSContext
realmBase.hasRoleFailure=Username [{0}] does NOT have role [{1}]
realmBase.hasRoleSuccess=Username [{0}] has role [{1}]

userDatabaseRealm.lookup=Exception looking up UserDatabase under key [{0}]
userDatabaseRealm.noDatabase=No UserDatabase component found under key [{0}]
