# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

applicationContext.addFilter.ise=无法将筛选器添加到上下文[{0}]，因为该上下文已初始化
applicationContext.addJspFile.iae=JSP 文件 [{0}] 不可用
applicationContext.addListener.iae.cnfe=无法创建类型为 [{0}] 的实例
applicationContext.addListener.iae.init=无法将类型为[{0}]的实例添加为侦听器
applicationContext.addListener.iae.sclNotAllowed=一旦调用了第一个ServletContextListener，就不能再添加ServletContextListener。
applicationContext.addListener.iae.wrongType=指定的类型[{0}]不是预期的侦听器类型之一
applicationContext.addListener.ise=无法将侦听器添加到上下文[{0}]中，因为该上下文已初始化
applicationContext.addRole.ise=上下文被初始化后，角色不能再被添加到[{0}]中
applicationContext.addServlet.ise=无法将servlet添加到上下文[{0}]中，因为该上下文已初始化
applicationContext.attributeEvent=属性事件监听器引发的异常
applicationContext.illegalDispatchPath=应用程序试图获取具有非法路径[{0}]的请求分派器，但该路径被拒绝，因为它包含一个编码的目录遍历尝试
applicationContext.invalidFilterName=由于筛选器名称[{0}]无效，无法添加筛选器定义。
applicationContext.invalidServletName=由于servlet名称[{0}]无效，无法添加对应servlet的定义。
applicationContext.lookup.error=在上下文[{1}]中找不到资源[{0}]
applicationContext.mapping.error=映射中.的错误
applicationContext.requestDispatcher.iae=路径[{0}]不以“/”字符开头
applicationContext.resourcePaths.iae=路径[{0}]不以“/”字符开头
applicationContext.role.iae=要为上下文[{0}]声明的单个角色不能为空，也不能为空字符串
applicationContext.roles.iae=要为上下文[{0}]声明的角色数组不能为空
applicationContext.setAttribute.namenull=Name 不能为 null
applicationContext.setInitParam.ise=初始化上下文后无法设置初始化参数
applicationContext.setRequestEncoding.ise=无法为上下文[{0}]设置请求编码，因为该上下文已初始化
applicationContext.setResponseEncoding.ise=无法为上下文[{0}]设置响应编码，因为该上下文已初始化
applicationContext.setSessionTimeout.ise=(:无法为上下文[{0}]设置会话超时，因为该上下文已初始化
applicationContext.setSessionTracking.iae.invalid=该上下文不支持为上下文[{1}]请求的会话跟踪模式[{0}]
applicationContext.setSessionTracking.iae.ssl=为上下文 [{0}] 请求的 session 跟踪模式包括 SSL 和至少一种其他模式。 SSL可能未配置其他模式。
applicationContext.setSessionTracking.ise=当上下文正在运行，无法设置上下文[{0}]的会话跟踪模式

applicationDispatcher.allocateException=为servlet[{0}]分配异常
applicationDispatcher.deallocateException=servlet[{0}]的解除分配异常
applicationDispatcher.forward.ise=提交响应后无法转发
applicationDispatcher.isUnavailable=Servlet[{0}]当前不可用
applicationDispatcher.serviceException=Servlet[{0}]的Servlet.service()抛出异常
applicationDispatcher.specViolation.request=原始ServletRequest或包装的原始ServletRequest未传递给RequestDispatcher，这违反了SRV.8.2和SRV.14.2.5.1
applicationDispatcher.specViolation.response=原始的ServletResponse或包装后的ServletResponse未传递给RequestDispatcher，原因：违反了SRV.8.2和SRV.14.2.5.1\n\
\n

applicationFilterConfig.jmxRegisterFail=类型为[{0}]且名称为[{1}]的筛选器的JMX注册失败
applicationFilterConfig.jmxUnregister=已完成对类型为[{0}]且名称为[{1}]的筛选器的JMX取消注册。
applicationFilterConfig.jmxUnregisterFail=类型为[{0}]且名称为[{1}]的筛选器的JMX取消注册失败
applicationFilterConfig.preDestroy=):为类型为[{1}]的名为[{0}]的筛选器调用preDestroy 失败
applicationFilterConfig.release=失败的销毁过滤器类型为[{1}]名称为[{0}]

applicationFilterRegistration.nullInitParam=由于名称和/或值为空，无法为筛选器设置初始化参数。名称[{0}]，值[{1}]
applicationFilterRegistration.nullInitParams=由于name和(或)value为null，无法为过滤器设置初始化参数。name为 [{0}]，value为 [{1}]

applicationHttpRequest.fragmentInDispatchPath=调度路径[{0}]中的片段已被删除

applicationPushBuilder.methodInvalid=推送请求的HTTP方法必须既可缓存又安全，但是[{0}]不是
applicationPushBuilder.methodNotToken=HTTP方法必须是令牌(token)，但 [{0}] 包含非令牌字符

applicationServletRegistration.setServletSecurity.iae=为部署到名为[{1}]的上下文的Servlet[{0}]指定的空约束
applicationServletRegistration.setServletSecurity.ise=无法将安全性约束添加到已部署到名称为[{1}]的上下文的servlet [{0}]中，因为上下文已被初始化

applicationSessionCookieConfig.ise=无法将属性{0}添加到上下文{1}的sessioncokieconfig中，因为该上下文已初始化

aprListener.aprDestroy=无法关闭基于APR的Apache Tomcat本机库
aprListener.aprInit=在java.library.path:[{0}]上找不到基于APR的Apache Tomcat本机库，该库允许在生产环境中获得最佳性能
aprListener.aprInitDebug=在java.library.path[{1}]上使用名称[{0}]找不到基于APR的ApacheTomcat本机库。报告的错误是[{2}]
aprListener.aprInitError=基于APR的本地库加载失败.错误报告为[{0}]
aprListener.config=APR/OpenSSL配置：useAprConnector[{0}]，useOpenSSL[{1}]
aprListener.currentFIPSMode=当前FIPS模式：[{0}]。
aprListener.enterAlreadyInFIPSMode=AprLifecycleListener 配置为强制进入FIPS模式，但库已处于FIPS模式[{0}]
aprListener.flags=APR功能：IPv6[{0}]、sendfile[{1}]、accept filters[{2}]、random[{3}]。
aprListener.initializeFIPSFailed=进入FIPS模式失败
aprListener.initializeFIPSSuccess=成功的进入FIPS 模式
aprListener.initializedOpenSSL=OpenSSL成功初始化 [{0}]
aprListener.initializingFIPS=初始化FIPS模式...
aprListener.requireNotInFIPSMode=AprLifecycleListener配置为要求库已处于FIPS模式，但它未处于FIPS模式
aprListener.skipFIPSInitialization=已经处于FIPS模式，跳过FIPS初始化
aprListener.sslInit=无法初始化SSLEngine
aprListener.tcnInvalid=安装了不兼容的APR（基于Apache Tomcat原生库）版本[{0}]，而Tomcat要求版本[{1}]
aprListener.tcnValid=使用APR版本[{1}]加载了基于APR的Apache Tomcat本机库[{0}]。
aprListener.tcnVersion=安装了基于APR的Apache Tomcat Native库的旧版本[{0}]，而Tomcat建议使用[{1}]的最低版本
aprListener.tooLateForFIPSMode=无法设置FIPSMode：SSL已初始化
aprListener.tooLateForSSLEngine=无法设置引擎：SSL已初始化
aprListener.tooLateForSSLRandomSeed=无法设置 SSLRandomSeed：SSL已经初始化
aprListener.wrongFIPSMode=AprLifecycleListener的FIPSMode选项的意外值：[{0}]

asyncContextImpl.asyncDispatchError=异步调度时出错
asyncContextImpl.asyncRunnableError=通过AsyncContext.start（）处理异步运行时出错
asyncContextImpl.dispatchingStarted=异步调度操作已经被调用。不允许在同一异步周期内进行其他异步调度操作。
asyncContextImpl.fireOnComplete=为任何异步侦听器触发onComplete（）事件
asyncContextImpl.fireOnError=为任何异步侦听器触发onError（）事件
asyncContextImpl.fireOnStartAsync=为任何异步侦听器启动onStartAsync（）事件
asyncContextImpl.fireOnTimeout=为任何异步侦听器触发onTimeout（）事件
asyncContextImpl.noAsyncDispatcher=从ServletContext 返回的调度程序不支持异步调度
asyncContextImpl.onCompleteError=对类型为[{0}]的侦听器的onComplete（）调用失败
asyncContextImpl.onErrorError=对类型为[{0}]的侦听器的onError（）调用失败
asyncContextImpl.onStartAsyncError=对类型为[{0}]的侦听器的onStartAsync（）调用失败
asyncContextImpl.onTimeoutError=对类型为[{0}]的侦听器的onTimeout（）调用失败
asyncContextImpl.request.ise=调用方法complete()后或者任意一个dispatch()方法调用后，调用getRequest()方法是非法的
asyncContextImpl.requestEnded=AsyncContext关联的请求已经完成处理。
asyncContextImpl.response.ise=调用complete()或者任何的dispatch()方法后，调用getResponse()方法是非法的。

containerBase.backgroundProcess.cluster=异常处理集群[{0}]后台进程
containerBase.backgroundProcess.realm=异常处理领域[{0}]后台进程
containerBase.backgroundProcess.unexpectedThreadDeath=后台线程[{0}]意外结束
containerBase.backgroundProcess.valve=处理阀门[{0}]后台进程异常
containerBase.nullName=容器名称不能为null
containerBase.threadedStartFailed=子容器启动失败
containerBase.threadedStopFailed=停止期间子容器失败

defaultInstanceManager.invalidInjection=方法资源注入注解无效
defaultInstanceManager.restrictedClass=禁止访问类[{0}]。这是一个限制类。必须将web应用程序配置为具有特权才能加载它
defaultInstanceManager.restrictedContainerServlet=禁止访问类 [{0}]。 它是一个受限制的类（实现了 ContainerServlet 接口）。 必须将 Web 应用程序配置为特权才能加载它
defaultInstanceManager.restrictedFiltersResource=找不到受限制的过滤器属性文件[{0}]
defaultInstanceManager.restrictedListenersResource=无法找到RestrictedListener的配置文件[{0}]
defaultInstanceManager.restrictedServletsResource=找不到受限制的servlets属性文件[{0}]
defaultInstanceManager.restrictedWrongValue=类名为{1}的受限类属性文件{0}中的值错误。期望值：[restricted]，实际值：[{2}]

filterChain.filter=Filter 执行抛出一个异常
filterChain.servlet=Servlet执行抛出一个异常

jreLeakListener.classToInitializeFail=在tomcat启动期间未能加载类[{0}]，以防止可能的内存泄漏。
jreLeakListener.gcDaemonFail=Tomcat开始预防潜在的内存溢出期间，触发GC守护线程失败，在非Sun JVMs上这是正常的。
jreLeakListener.jarUrlConnCacheFail=默认情况下无法禁用Jar URL连接缓存。
jreLeakListener.ldapPoolManagerFail=在Tomcat启动期间未能触发com.sun.jndi.ldap.LdapPoolManager类的创建以防止可能的内存泄漏。这在非Sun jvm上是预期的。
jreLeakListener.xmlParseFail=尝试在XML解析期间防止内存泄漏时出错。

naming.addEnvEntry=添加环境条目 [{0}]
naming.addResourceEnvRef=添加资源环境引用 [{0}]
naming.bindFailed=无法绑定对象：[{0}]
naming.invalidEnvEntryType=环境条目[{0}]没有一个有效哦的类型
naming.invalidEnvEntryValue=环境项[{0}]的值无效。
naming.jmxRegistrationFailed=注册到JMX失败：[{0}]
naming.namingContextCreationFailed=创建上下文名称失败
naming.unbindFailed=解绑对象[{0}]失败
naming.wsdlFailed=未找到 wsdl 文件：[{0}]

noPluggabilityServletContext.notAllowed=Servlet 3.0规范的第4.4节不允许从未在web.xml，web-fragment.xml文件中定义或未用@WebListener注释的ServletContextListener调用此方法。

pushBuilder.noPath=在设置路径之前调用push（）是非法的

standardContext.applicationListener=配置应用程序监听器[{0}]错误
standardContext.applicationSkipped=由于以前的错误，已跳过安装应用程序侦听器
standardContext.backgroundProcess.instanceManager=异常处理实例管理器[{0}]后台进程
standardContext.backgroundProcess.loader=异常处理加载程序[{0}]后台进程
standardContext.backgroundProcess.manager=异常处理管理器[{0}]后台进程。
standardContext.backgroundProcess.resources=异常处理资源[{0}] 后台进程
standardContext.cluster.noManager=未发现管理器。检查是否需要集群管理器。集群配置：[{0}]，应用程序分配：[{1}]
standardContext.configurationFail=一个或多个组件将上下文标记为未正确配置
standardContext.cookieProcessor.null=不允许将上下文的CookieProcessor 设置为null
standardContext.duplicateListener=当前上下文已经配置了监听器[{0}]，重复的定义将被忽略。
standardContext.errorPage.error=错误页面位置[{0}]必须以“ /”开头
standardContext.errorPage.required=ErrorPage不能为null
standardContext.errorPage.warning=警告：在Servlet 2.4中，错误页位置 [{0}] 必须以"/"开头
standardContext.extensionValidationError=尝试校验必需的应用程序扩展时发生错误
standardContext.filterFail=一个或多个筛选器启动失败。完整的详细信息将在相应的容器日志文件中找到
standardContext.filterMap.either=过滤器映射必须指定 <url-pattern> 或 <servlet-name>
standardContext.filterMap.name=Filter mapping 指定了一个未知的 filter名称 [{0}]
standardContext.filterMap.pattern=过滤器映射中的<url-pattern> [{0}] 无效
standardContext.filterStart=启动过滤器异常
standardContext.invalidWrapperClass=[{0}] 不是StandardWrapper的子类
standardContext.isUnavailable=此应用程序目前不可用
standardContext.listenerFail=一个或多个listeners启动失败，更多详细信息查看对应的容器日志文件
standardContext.listenerStart=异常将上下文初始化事件发送到类的侦听器实例.[{0}]
standardContext.listenerStop=例外情况发送上下文删除事件[{0}]，以便列表实例
standardContext.loadOnStartup.loadException=web应用程序[{0}]中的Servlet[{1}]引发了load（）异常
standardContext.loginConfig.errorPage=表单错误页[{0}]必须以"/"开始
standardContext.loginConfig.errorWarning=警告：Servlet 2.4中，表单错误页[{0}]必须以"/"开始
standardContext.loginConfig.loginPage=表单登录页面 [{0}] 必须以''/''开头
standardContext.loginConfig.loginWarning=警告：在Servlet 2.4 中 Form 登录页[{0}] 必须以 "/" 开头
standardContext.loginConfig.required=LoginConfig不能为空
standardContext.manager=配置类为[{0}]的管理器
standardContext.managerFail=会话管理器无法启动
standardContext.namingResource.destroy.fail=无法销毁旧的命名资源
standardContext.namingResource.init.fail=未能初始化新的命名资源
standardContext.notStarted=名称为[{0}]的上下文还没有启动
standardContext.notWrapper=上下文的子级必须是包装器
standardContext.parameter.duplicate=重复的上下文初始化参数[{0}]
standardContext.parameter.required=参数名和参数值都是必需的
standardContext.pathInvalid=上下文路径必须是空字符串或以''/''开头，而不能以''/''结尾。路径[{0}]不符合这些条件，已更改为[{1}]
standardContext.postconstruct.duplicate=类[{0}]的构造后方法定义重复
standardContext.postconstruct.required=完全限定的类名和方法名都是必需的
standardContext.predestroy.duplicate=类 [{0}] 的 @PreDestroy 方法定义重复
standardContext.predestroy.required=完全合格的类名和方法名都是必需的
standardContext.reloadingCompleted=已完成重新加载名为{0}的上下文
standardContext.reloadingStarted=已开始重新加载名为[{0}]的上下文
standardContext.requestListener.requestInit=向类[{0}]的侦听器实例发送请求初始化的生命周期事件的异常
standardContext.resourcesInit=初始化静态变量错误
standardContext.resourcesStart=启动静态资源出错
standardContext.resourcesStop=停止静态资源时出错
standardContext.sciFail=ServletContainerInitializer处理期间出错
standardContext.securityConstraint.mixHttpMethod=在相同的web资源集合中不允许混用: <http-method> 和 <http-method-omission>
standardContext.securityConstraint.pattern=安全约束中的<url-pattern> [{0}] 无效
standardContext.servletFail=启动时无法加载一个或多个Servlet。 全部的详细信息可在相应的容器日志文件中找到
standardContext.servletMap.name=Servlet映射指定未知的Servlet名称[{0}]
standardContext.servletMap.pattern=servlet映射中的<url pattern>[{0}]无效
standardContext.startFailed=由于之前的错误，Context[{0}]启动失败
standardContext.startingContext=启动Context[{0}]出现异常
standardContext.stop.asyncWaitInterrupted=等待卸载延迟毫秒以完成飞行中的异步请求时收到中断。上下文停止将继续，不会有进一步的延迟。
standardContext.stoppingContext=异常停止的上下文使用名为[{0}]
standardContext.threadBindingListenerError=上下文[{0}]配置的线程绑定监听器发生错误
standardContext.urlPattern.patternWarning=警告：在Servlet 2.4中，URL模式[{0}]必须以“/”开头
standardContext.webappClassLoader.missingProperty=无法将web应用程序类加载器属性[{0}]设置为[{1}]，因为该属性不存在。
standardContext.workCreateException=无法从目录[{0}]和catalina_home[{1}]中为上下文[{2}]确定绝对工作目录
standardContext.workCreateFail=无法为上下文[{1}]创建工作目录[{0}]
standardContext.workPath=获取上下文[{0}]的工作路径时发生异常

standardContextValve.acknowledgeException=以100（继续）响应确认请求失败

standardEngine.jvmRouteFail=无法从系统属性设置引擎的jvmRoute 属性
standardEngine.notHost=Engine的子节点必须是一个Host
standardEngine.notParent=引擎不能有父容器

standardHost.clientAbort=远程客户端中止请求，IOException:[{0}]。
standardHost.invalidErrorReportValveClass=无法加载指定的错误报告阀类：[{0}]
standardHost.noContext=没有配置上下文来处理此请求
standardHost.notContext=主机的子节点必须有上下文
standardHost.nullName=主机名是必需的
standardHost.problematicAppBase=在主机[{0}]上为appBase使用空字符串会将其设置为CATALINA_BASE，这是一个坏主意

standardHostValue.customStatusFailed=无法正确调度自定义错误页[{0}]

standardPipeline.basic.start=启动新基本阀时出错
standardPipeline.basic.stop=停止旧基本阀时出错
standardPipeline.valve.destroy=破坏阀门错误
standardPipeline.valve.start=错误启动阀
standardPipeline.valve.stop=错误截止阀

standardServer.accept.error=尝试在侦听shutdown命令的套接字上接受IO异常
standardServer.accept.readError=尝试读取关机命令时发生IO异常
standardServer.accept.security=试图在侦听shutdown命令的套接字上接受时发生安全错误
standardServer.accept.timeout=在调用accept()方法之后，侦听shutdown命令的套接字经历了意外的超时[{0}]毫秒。 这是bug 56684的一个例子？
standardServer.invalidShutdownCommand=收到无效的关闭命令[{0}]
standardServer.shutdownViaPort=通过关闭端口接收到有效的关闭命令。正在停止服务器实例。
standardServer.storeConfig.contextError=存储上下文[{0}]配置时出错
standardServer.storeConfig.error=存储服务器配置时出错
standardServer.storeConfig.notAvailable=没有将StoreConfig实现注册为名为[{0}]的MBean，因此无法保存配置。合适的MBean通常通过StoreConfigLifecycleListener注册。

standardService.engine.startFailed=启动关联的Engine失败
standardService.engine.stopFailed=失败停止关联的引擎
standardService.mapperListener.startFailed=无法启动关联的MapperListener
standardService.mapperListener.stopFailed=无法停止关联的MapperListener
standardService.start.name=正在启动服务[{0}]
standardService.stop.name=正在停止服务[{0}]

standardWrapper.allocate=分配一个servlet实例错误
standardWrapper.allocateException=分配异常的servlet [{0}]
standardWrapper.deallocateException=servlet[{0}]的解除分配异常
standardWrapper.destroyException=Servlet[{0}]的Servlet.destroy（）引发异常
standardWrapper.destroyInstance=servlet[{0}]实例管理销毁(destroy) 抛出异常
standardWrapper.initException=Servlet[{0}]的Servlet.init（）引发异常
standardWrapper.instantiate=实例化Servlet类[{0}]异常
standardWrapper.isUnavailable=Servlet [{0}]当前不可用。
standardWrapper.notChild=Wrapper容器内部不允许有子容器。
standardWrapper.notClass=未为servlet[{0}]指定servlet类
standardWrapper.notContext=包装的父容器必须是上下文
standardWrapper.notFound=Servlet [{0}] 不可用
standardWrapper.notServlet=类{0}不是Servlet
standardWrapper.serviceException=在路径为[{1}]的上下文中，servlet[{0}]的Servlet.service()引发异常
standardWrapper.serviceExceptionRoot=在路径为{1}的上下文中，Servlet[{0}]的Servlet.service（）引发了具有根本原因的异常{2}
standardWrapper.unavailable=将servlet[{0}]标记为不可用
standardWrapper.unloadException=Servlet[{0}]引发unload（）异常
standardWrapper.unloading=无法分配servlet [{0}]，因为它没有被加载
standardWrapper.waiting=正在等待为Servlet[{1}]释放[{0}]实例

threadLocalLeakPreventionListener.containerEvent.error=异常处理容器事件[{0}]
threadLocalLeakPreventionListener.lifecycleEvent.error=处理生命周期事件[{0}]时发生异常
