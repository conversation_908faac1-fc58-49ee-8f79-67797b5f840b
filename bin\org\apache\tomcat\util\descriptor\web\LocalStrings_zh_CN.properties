# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

filterDef.invalidFilterName=过滤器定义中的<filter-name> [{0}] 无效。

securityConstraint.uncoveredHttpMethod=对于URL模式为[{0}]的安全约束，只包括HTTP方法[{1}]。所有其他方法都没有涉及。
securityConstraint.uncoveredHttpMethodFix=添加URL模式为[{0}]的安全约束，以拒绝使用未覆盖的HTTP方法进行访问，这些方法不是下列方法之一[{1}]。
securityConstraint.uncoveredHttpOmittedMethod=对于URL模式[{0}]的安全性约束，将发现HTTP方法[{1}]。
securityConstraint.uncoveredHttpOmittedMethodFix=添加url模式为[{0}]的安全约束以拒绝使用未覆盖的http方法[{1}]的访问

servletDef.invalidServletName=servlet定义中的<servlet name>[{0}]无效。

webRuleSet.absoluteOrdering=<绝对值排序>元素在web片段xml中无效，将被忽略。
webRuleSet.absoluteOrderingCount=<absolute ordering>元素限制为1次出现
webRuleSet.nameCount=<name>元素只能出现1次
webRuleSet.postconstruct.duplicate=class [{0}] 有重复的 post 构造方法声明
webRuleSet.predestroy.duplicate=类[{0}]的@PreDestroy方法定义重复
webRuleSet.relativeOrdering=<ordering>元素在web.xml中无效，将被忽略
webRuleSet.relativeOrderingCount=<ordering>元素限制为1次出现。

webXml.duplicateEnvEntry=重复的env-entry 名 [{0}]
webXml.duplicateFilter=重复的过滤器名称 [{0}]
webXml.duplicateFragment=找到多个名为{0}的片段。这是不合法的相对排序。有关详细信息，请参阅Servlet规范的第8.2.2 2c节。考虑使用绝对排序。
webXml.duplicateMessageDestination=重复的邮件目标名称[{0}]。
webXml.duplicateMessageDestinationRef=重复的消息目标引用名称[{0}]
webXml.duplicateResourceEnvRef=重复的资源env ref name[{0}]
webXml.duplicateResourceRef=重复的资源引用名称[{0}]
webXml.duplicateServletMapping=名为 [{0}]和 [{1}] 的servlet不能映射为一个url模式(url-pattern) [{2}]
webXml.duplicateTaglibUri=重复的标记库URI[{0}]
webXml.mergeConflictDisplayName=显示名称在多个片段中被定义，这些片段包含不同的值，包括位于[{1}]的[{0}]的片段。
webXml.mergeConflictFilter=筛选器[{0}]在多个片段中定义不一致，包括位于[{2}]的名为[{1}]的片段
webXml.mergeConflictLoginConfig=在多个片段中定义的LoginConfig不一致，其中包括位于[{1}]的名为[{0}]的片段
webXml.mergeConflictOrder=片段相对顺序包含循环引用。这可以通过在web.xml中使用绝对排序来解决。
webXml.mergeConflictResource=资源[{0}]在多个片段中定义不一致，包括位于[{2}]的名为[{1}]的片段。
webXml.mergeConflictServlet=Servlet[{0}]在多个片段中的定义不一致，包括位于[{2}]的名为[{1}]的片段
webXml.mergeConflictSessionCookieComment=会话cookie注释在多个具有不同值的片段中定义不一致，包括位于{1}的名为{0}]的片段
webXml.mergeConflictSessionCookieDomain=会话cookie域在多个具有不同值的片段中定义不一致，包括位于[{1}]的名为[{0}]的片段
webXml.mergeConflictSessionCookieHttpOnly=会话cookie http only标志在多个具有不同值的片段中定义不一致，包括位于[{1}]的名为[{0}]的片段
webXml.mergeConflictSessionCookieMaxAge=会话cookie max age在多个具有不同值的片段中定义不一致，包括位于[{1}]的名为[{0}]的片段
webXml.mergeConflictSessionCookieName=会话cookie名称在多个具有不同值的片段中定义不一致，包括位于 [{1}] 的片段 [{0}]
webXml.mergeConflictSessionCookiePath=会话cookie路径在多个具有不同值的片段中定义不一致，包括位于[{1}]的名为[{0}]的片段
webXml.mergeConflictSessionCookieSecure=会话cookie安全标志在多个具有不同值的片段中定义不一致，包括位于{1}的名为{0}]的片段
webXml.mergeConflictSessionTimeout=会话超时以不同值的多个片段不一致地定义，这些片段包括位于[{1}]的具有名称[{0}]的片段。
webXml.mergeConflictSessionTrackingMode=会话跟踪模式在多个片段中定义不一致，包括位于[{1}]的名称为[{0}]的片段
webXml.mergeConflictString=名称为{1}的{0}在多个片段中定义不一致，包括位于{3}的名称为{2}的片段
webXml.multipleOther=嵌套在<ordering>元素中的多个<others>条目
webXml.reservedName=使用保留名称[{0}]检测到web.xml文件。 此片段将忽略name元素。
webXml.unrecognisedPublicId=对于web.xml文件，公共ID[{0}]不匹配任何已知的公共ID‘，因此无法识别版本。
webXml.version.unknown=未知版本字符串 [{0}]。将使用默认版本。
webXml.wrongFragmentName=在web.xml绝对排序标签上使用了错误的片段名[{0}]！

webXmlParser.applicationParse=解析应用web.xml错误，路径：[{0}]
webXmlParser.applicationPosition=出现在第 [{0}] 行 第 [{1}] 列
webXmlParser.applicationStart=正在分析位于[{0}]的应用程序web.xml文件
