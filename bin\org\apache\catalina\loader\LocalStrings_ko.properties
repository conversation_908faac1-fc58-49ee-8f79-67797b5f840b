# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

webappClassLoader.addExportsJavaIo=Java 9 또는 이후 버전을 사용할 때에는, "--add-opens=java.base/java.io={0}" 을(를) JVM 명령행 아규먼트들에 추가해서, ObjectStream 캐시 메모리 릭 방지 옵션을 활성화할 필요가 있습니다. 또는 원하시는 경우, ObjectStream 클래스 캐시 메모리 릭 방지 옵션을 비활성화해서 이 경고 메시지를 없앨 수도 있습니다.
webappClassLoader.addExportsRmi=Java 9에서 실행하면서 RMI Target 메모리 누수 탐지를 사용 가능하게 하려면, "--add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED"를 JVM 명령 행 아규먼트에 추가해야 합니다. 또는, RMI Target 메모리 누수 탐지를 사용불능 상태로 설정함으로써, 이 경고를 없앨 수도 있습니다.
webappClassLoader.addExportsThreadLocal=Java 9 환경에서 실행할 때에는, ThreadLocal 메모리 누수 탐지를 위하여, "--add-opens=java.base/java.lang=ALL-UNNAMED"를 JVM 명령 행 아규먼트들에 추가해야 합니다. 또는, ThreadLocal 메모리 누수 탐지를 사용불능 상태로 설정함으로써, 이 경고 메시지를 없앨 수도 있습니다.
webappClassLoader.addPermissionNoCanonicalFile=URL [{0}](으)로부터 canonical 파일 경로를 얻을 수 없습니다.
webappClassLoader.addPermissionNoProtocol=URL [{1}] 내의 프로토콜 [{0}]은(는) 지원되지 않으므로, 이 URL의 리소스에 대한 읽기가 승인되지 않았습니다.
webappClassLoader.addTransformer=클래스 파일 Transformer [{0}]을(를) 웹 애플리케이션 [{1}]에 추가했습니다.
webappClassLoader.addTransformer.duplicate=웹 애플리케이션 [{1}]에 클래스 파일 변환기 [{0}]을(를) 추가하기 위한, 중복된 호출을 무시합니다.
webappClassLoader.addTransformer.illegalArgument=웹 애플리케이션 [{0}]이(가) 널인, 클래스 파일 Transformer을 추가하려 시도했습니다.
webappClassLoader.checkThreadLocalsForLeaks=웹 애플리케이션 [{0}]이(가), 타입 [{1}]인 키와 (값: [{2}]) 타입 [{3}]인 값을 (값: [{4}]) 사용하여 ThreadLocal 객체를 생성했지만, 웹 애플리케이션이 중지될 때 그것을 제거하지 못했습니다. 혹시 있을 법한 메모리 누수를 방지하기 위하여, 시간을 두고 쓰레드들을 재생성할 것입니다.
webappClassLoader.checkThreadLocalsForLeaks.badKey=타입이 [{0}]인 키의 문자열 representation을 결정할 수 없습니다.
webappClassLoader.checkThreadLocalsForLeaks.badValue=타입이 [{0}]인 값의 문자열 representation을 결정할 수 없습니다.
webappClassLoader.checkThreadLocalsForLeaks.unknown=알 수 없음
webappClassLoader.checkThreadLocalsForLeaksFail=웹 애플리케이션 [{0}]을(를) 위한 ThreadLocal 참조들에 대한 점검이 실패했습니다.
webappClassLoader.checkThreadLocalsForLeaksNone=웹 애플리케이션 [{0}]이(가), 타입 [{1}]의 키(값: [{2}])와 타입 [{3}]의 값(값: [{4}])을 사용하여, ThreadLocal Map을 생성했습니다. 해당 ThreadLocal Map은 weak 키들을 유지하고 있기 때문에, 이는 메모리 누수가 아닙니다.
webappClassLoader.checkThreadLocalsForLeaksNull=웹 애플리케이션 [{0}]이(가), 타입 [{1}]인 키를 (값: [{2}]) 사용하여 ThreadLocal 객체를 생성했습니다. 해당 ThreadLocal 객체가 올바르게 널로 설정되었으므로, 해당 키는 GC에 의해 제거 될 것입니다.
webappClassLoader.checkThreadsHttpClient=웹 애플리케이션 클래스로더를 사용하는 HttpClient keep-alive 쓰레드를 발견했습니다. 쓰레드의 클래스로더를 부모 클래스로더로 전환시켰습니다.
webappClassLoader.clearJdbc=웹 애플리케이션 [{0}]이(가) JDBC 드라이버 [{1}]을(를) 등록했지만, 웹 애플리케이션이 중지될 때, 해당 JDBC 드라이버의 등록을 제거하지 못했습니다. 메모리 누수를 방지하기 위하여, 등록을 강제로 제거했습니다.
webappClassLoader.clearObjectStreamClassCachesFail=웹 애플리케이션 [{0}]을(를) 위해, ObjectStreamClass$Caches로부터 soft references를 폐기하지 못했습니다.
webappClassLoader.clearRmi=스텁 클래스 [{0}]와(과) 값 [{1}]을(를) 가진 RMI Target을 발견했습니다. 이 RMI Target은 메모리 누수를 방지하기 위하여 강제로 제거되었습니다.
webappClassLoader.clearRmiFail=sun.rmi.transport.Target으로부터 참조된 컨텍스트 클래스로더를, 웹 애플리케이션 [{0}]을(를) 위해, 폐기하지 못했습니다.
webappClassLoader.clearRmiInfo=웹 애플리케이션 [{0}]을(를) 위한 컨텍스트 클래스로더를 폐기하기 위한, 클래스 sun.rmi.transport.Target을 찾지 못했습니다. 이는 Sun JVM들이 아닌 환경에서 발생할 수 있습니다.
webappClassLoader.getThreadGroupError=ThreadGroup [{0}]의 부모 ThreadGroup을 얻을 수 없습니다. 잠재적인 메모리 누수 문제를 찾기 위해 모든 쓰레드들을 점검하는 것이 불가능합니다.
webappClassLoader.jarsAdded=하나 이상의 JAR들이 웹 애플리케이션 [{0}]에 추가됐습니다.
webappClassLoader.jarsModified=웹 애플리케이션 [{0}]에서 하나 이상의 JAR 파일(들)이 변경되었습니다.
webappClassLoader.jarsRemoved=하나 이상의 JAR들이 웹 애플리케이션 [{0}](으)로부터 제거되었습니다.
webappClassLoader.javaseClassLoaderNull=j2seClassLoader 속성이 널이어서는 안됩니다.
webappClassLoader.jdbcRemoveFailed=웹 애플리케이션 [{0}]을(를) 위한 JDBC 드라이버의 등록을 제거하지 못했습니다.
webappClassLoader.loadedByThisOrChildFail=컨텍스트 [{1}]에서 잠재적 메모리 누수를 방지하기 위해, [{0}] 클래스의 인스턴스 내에 있는 엔트리들을 모두 점검하려는 시도가 실패했습니다.
webappClassLoader.readError=리소스 읽기 오류 : [{0}]을(를) 로드할 수 없었습니다.
webappClassLoader.removeTransformer=웹 애플리케이션 [{1}](으)로부터 클래스 파일 Transformer [{0}]을(를) 제거했습니다.
webappClassLoader.resourceModified=리소스 [{0}]이(가) 변경된 적이 있습니다. 최종 변경 시간이 [{1}]이었는데, 이제 [{2}](으)로 바뀌었습니다.
webappClassLoader.restrictedPackage=보안 위반 행위: 제한된 클래스 [{0}]을(를) 사용하려 시도했습니다.
webappClassLoader.securityException=findClassInternal에서, 클래스 [{0}]을(를) 찾으려 시도 중 보안 예외 발생: [{1}]
webappClassLoader.stackTrace=웹 애플리케이션 [{0}]이(가) [{1}](이)라는 이름의 쓰레드를 시작시킨 것으로 보이지만, 해당 쓰레드를 중지시키지 못했습니다. 이는 메모리 누수를 유발할 가능성이 큽니다. 해당 쓰레드의 스택 트레이스:{2}
webappClassLoader.stackTraceRequestThread=웹 애플리케이션 [{0}]이(가) 여전히 완료되지 않은 요청을 처리하고 있습니다. 이는 메모리 누수를 유발할 가능성이 높습니다. 표준 컨텍스트 구현의 unloadDelay 속성을 이용하여, 요청 완료 허용 시간을 통제할 수 있습니다. 요청 처리 쓰레드의 스택 트레이스:[{2}]
webappClassLoader.stopThreadFail=웹 애플리케이션 [{1}]을 위한, [{0}](이)라는 이름의 쓰레드를 종료시키지 못했습니다.
webappClassLoader.stopTimerThreadFail=웹 애플리케이션 [{1}]을(를) 위한, [{0}](이)라는 이름의 TimerThread를 종료시키지 못했습니다.
webappClassLoader.stopped=불허되는 접근: 이 웹 애플리케이션 인스턴스는 이미 중지되었습니다. [{0}]을(를) 로드할 수 없습니다. 디버그 목적 및 불허되는 접근을 발생시킨 해당 쓰레드를 종료시키기 위한 시도로서, 다음 스택 트레이스가 생성됩니다.
webappClassLoader.superCloseFail=부모 클래스의 close() 호출 시 실패 발생
webappClassLoader.transformError=Instrumentation 오류: 클래스 파일 포맷이 규약을 따르지 않기 때문에, 클래스 [{0}]을(를) 변환시킬 수 없었습니다.
webappClassLoader.warnTimerThread=웹 애플리케이션 [{0}]이(가) java.util.Timer API를 통해 [{1}](이)라는 이름을 가진 TimerThread를 시작한 것으로 보이지만, 그 쓰레드를 중지시키지 못했습니다. 메모리 누수를 방지하기 위해, 타이머가 (연관된 쓰레드와 함께) 강제로 취소되었습니다.
webappClassLoader.wrongVersion=(클래스 [{0}]을(를) 로드할 수 없습니다)

webappClassLoaderParallel.registrationFailed=org.apache.catalina.loader.ParallelWebappClassLoader를 병렬 클래스 로딩이 가능하게 등록하려 했지만 실패했습니다.

webappLoader.deploy=작업 디렉토리 [{0}]에 클래스 레파지토리들을 배치합니다.
webappLoader.reloadable=reloadable 프로퍼티를 [{0}](으)로 설정할 수 없습니다.
webappLoader.setContext.ise=웹 애플리케이션 로더가 시작되고 있는 동안, 컨텍스트를 설정하는 것은 허용되지 않습니다.
webappLoader.starting=이 로더를 시작합니다.
webappLoader.stopping=이 로더를 중지시키는 중
