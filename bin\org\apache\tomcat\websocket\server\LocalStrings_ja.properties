# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

serverContainer.addNotAllowed=以前に登録したことのあるエンドポイントには登録できません。
serverContainer.configuratorFail=POJO クラス [{1}] のインスタンスを構成するクラス [{0}] のインスタンスを作成できません。
serverContainer.duplicatePaths=複数のエンドポイントを同じパス[{0}]に配備することはできません：既存のエンドポイントは[{1}]で、新しいエンドポイントは[{2}]です。
serverContainer.encoderFail=タイプ[{0}]のエンコーダを作成できません
serverContainer.failedDeployment=以前の配備が失敗したため、ホスト[{1}]内のパス[{0}]を持つWebアプリケーションへのWebSocketエンドポイントの配備は許可されていません。
serverContainer.missingAnnotation=POJOクラス[{0}]は@ServerEndpointでアノテーション付けされていないため、デプロイ出来ません。
serverContainer.servletContextMissing=ServletContextが指定されていません

upgradeUtil.incompatibleRsv=互換性のないRSVビットの使用法を持つ拡張が指定されました

uriTemplate.duplicateParameter=パス中にパラメーター [{0}] を複数回登場させることはできません。
uriTemplate.emptySegment=パス [{0}] に一つ以上の空セグメントを含めることはできません。
uriTemplate.invalidPath=[{0}] は不正なパスです。
uriTemplate.invalidSegment=パス [{1}] に存在しないセグメント [{0}] が指定されました。

wsFrameServer.bytesRead=入力バッファーに読み込んだ [{0}] バイトのデータは処理可能です。
wsFrameServer.illegalReadState=予期しない読み取り状態[{0}]
wsFrameServer.onDataAvailable=メソッドエントリ

wsHttpUpgradeHandler.closeOnError=エラーが発生したため WebSocket コネクションを切断します。
wsHttpUpgradeHandler.destroyFailed=WebSocket HttpUpgradeHandlerを破棄している間にWebConnectionを閉じることができませんでした。
wsHttpUpgradeHandler.noPreInit=コンテナがinit()を呼び出す前に、preInit()メソッドを呼び出すようにWebSocket HttpUpgradeHandlerを設定する必要があります。 通常、これはWsHttpUpgradeHandlerインスタンスを作成したサーブレットがpreInit()を呼び出す必要があることを意味します。
wsHttpUpgradeHandler.serverStop=サーバ停止中

wsRemoteEndpointServer.closeFailed=ServletOutputStreamコネクションを正常に閉じることができませんでした
