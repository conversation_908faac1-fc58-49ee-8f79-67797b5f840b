# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

combinedRealm.authFail=Kann <PERSON> [{0}] mit Realm [{1}] nicht authentisieren
combinedRealm.authSuccess=Authentifizierter Benutzer [{0}] mit Realm [{1}]

dataSourceRealm.authenticateFailure=Benutzername [{0}] konnte NICHT authentifiziert werden
dataSourceRealm.authenticateSuccess=Benutzername [{0}] konnte erfolgreich authentifiziert werden
dataSourceRealm.getPassword.exception=Ausnahme beim Holen des Passwortes für [{0}]
dataSourceRealm.getRoles.exception=Ausnahme beim Holen der Rollen für [{0}]

jaasRealm.accountExpired=Benutzername [{0}] konnte auf Grund eines abgelaufenen Kontos NICHT authentifiziert werden
jaasRealm.authenticateFailure=Benutzername [{0}] konnte NICHT authentifiziert werden
jaasRealm.credentialExpired=Benutzername [{0}] konnte auf Grund abgelaufener Zugangsdaten NICHT authentifiziert werden
jaasRealm.failedLogin=Benutzername [{0}] konnte auf Grund einer fehlerhaften Anmeldung NICHT authentifiziert werden
jaasRealm.loginContextCreated=JAAS LoginContext für Benutzername [{0}] erzeugt

jdbcRealm.authenticateFailure=Benutzername [{0}] konnte NICHT authentifiziert werden
jdbcRealm.authenticateSuccess=Benutzername [{0}] konnte erfolgreich authentifiziert werden

jndiRealm.authenticateFailure=Benutzername [{0}] konnte NICHT authentifiziert werden
jndiRealm.authenticateSuccess=Benutzername [{0}] konnte erfolgreich authentifiziert werden

lockOutRealm.authLockedUser=Es wurde versucht den gesperrten Benutzer [{0}] zu authentifizieren

memoryRealm.authenticateFailure=Benutzername [{0}] konnte NICHT authentifiziert werden
memoryRealm.authenticateSuccess=Benutzername [{0}] konnte erfolgreich authentifiziert werden.\n
memoryRealm.loadExist=Datei [{0}] für Memory Database kann nicht gelesen werden

realmBase.authenticateFailure=Benutzername [{0}] konnte NICHT authentifiziert werden
realmBase.authenticateSuccess=Benutzername [{0}] konnte erfolgreich authentifiziert werden
realmBase.createUsernameRetriever.ClassCastException=Klasse [{0}] ist keine X509UsernameRetriever Klasse.
realmBase.digest=Fehler beim Anwenden des Digest auf die Benutzer-Credentials
realmBase.forbidden=Zugriff auf die gewünschte Resource wurde verweigert.
realmBase.gotX509Username=Benutzername aus dem X.509 Zertifikate extrahiert: [{0}]
realmBase.hasRoleFailure=Benutzername [{0}] hat NICHT die Rolle [{1}]
realmBase.hasRoleSuccess=Benutzername [{0}] hat die Rolle [{1}]

userDatabaseRealm.noDatabase=Keine UserDatabase Komponente unter dem Schlüssel [{0}] gefunden
