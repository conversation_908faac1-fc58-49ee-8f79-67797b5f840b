# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

farmWarDeployer.alreadyDeployed=webapp[{0}]已部署。
farmWarDeployer.deleteFail=无法删除 [{0}]
farmWarDeployer.deployEnd=[{0}]中的部署已完成。
farmWarDeployer.fileCopyFail=无法从[{0}]复制到[{1}]
farmWarDeployer.hostOnly=FarmWarDeployer 只有做为 host cluster 的子元素是才生效
farmWarDeployer.hostParentEngine=FarmWarDeployer只能在[{0}]的父级是引擎时工作！
farmWarDeployer.mbeanNameFail=无法为引擎[{0}]和主机[{1}]构造MBean对象名
farmWarDeployer.modInstall=从 [{1}] 安装 webapp [{0}]
farmWarDeployer.modInstallFail=无法安装 WAR 文件
farmWarDeployer.msgIoe=无法读取服务器场部署文件消息。
farmWarDeployer.msgRxDeploy=接收集群部署路径[{0}]，war[{1}]
farmWarDeployer.msgRxUndeploy=从路径[{0}]接收未部署群集
farmWarDeployer.removeFailLocal=从[{0}]本地移除失败
farmWarDeployer.removeFailRemote=本地从[{0}]删除失败，其他经理有app在服务！
farmWarDeployer.removeLocal=正在删除webapp[{0}]
farmWarDeployer.removeLocalFail=无法移除WAR文件
farmWarDeployer.removeStart=群集范围内删除web应用程序[{0}]
farmWarDeployer.removeTxMsg=从[{0}]发送群集范围的取消部署
farmWarDeployer.renameFail=将 [{0}] 重命名为 [{1}] 失败
farmWarDeployer.sendEnd=发送群集war部署路径[{0}]，war[{1}]已完成。
farmWarDeployer.sendFragment=将群集war片段路径[{0}]，war[{1}]发送到[{2}]
farmWarDeployer.sendStart=发送集群war部署路径[{0}]，war[{1}]已启动
farmWarDeployer.servicingDeploy=应用程序[{0}]正在服务。再次触摸WAR文件[{1}]！
farmWarDeployer.servicingUndeploy=正在处理应用程序[{0}]，无法从备份群集节点中删除它
farmWarDeployer.started=群集FarmWarDeployer已启动。
farmWarDeployer.stopped=群集FarmWarDeployer已停止。
farmWarDeployer.undeployEnd=从[{0}]取消部署完成。
farmWarDeployer.undeployLocal=不能部署本地上下文[{0}]
farmWarDeployer.watchDir=群集部署正在监视[{0}]的更改

fileMessageFactory.cannotRead=无法读取消息，此工厂正在写入
fileMessageFactory.cannotWrite=无法写入消息，此工厂正在读取
fileMessageFactory.closed=工厂已经关闭
fileMessageFactory.deleteFail=无法删除 [{0}]
fileMessageFactory.duplicateMessage=收到重复消息。发件人超时是否太低？上下文：[{0}]文件名：[{1}]数据：[{2}]数据长度：[{3}]

fileNewFail=无法创建[{0}]

warWatcher.cantListWatchDir=无法列出WatchDir文件夹 [{0}] 中的文件：检查该路径是否为目录且应用具有读取权限。
warWatcher.checkWarResult=WarInfo.check() 为[{1}]返回[{0}]
warWatcher.checkingWar=检查 WAR 文件 [{0}]
warWatcher.checkingWars=正在检查[{0}]中的wars
warWatcher.listedFileDoesNotExist=[{1}]中检测到[{0}]，但不存在。在[{1}]上检查目录权限？
