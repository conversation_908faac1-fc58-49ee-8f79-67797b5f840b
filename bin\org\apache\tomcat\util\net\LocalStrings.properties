# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

channel.nio.interrupted=The current thread was interrupted
channel.nio.ssl.appInputNotEmpty=Application input buffer still contains data. Data would have been lost.
channel.nio.ssl.appOutputNotEmpty=Application output buffer still contains data. Data would have been lost.
channel.nio.ssl.closeSilentError=As expected, there was an exception trying to close the connection cleanly.
channel.nio.ssl.closing=Channel is in closing state.
channel.nio.ssl.eofDuringHandshake=EOF during handshake.
channel.nio.ssl.expandNetInBuffer=Expanding network input buffer to [{0}] bytes
channel.nio.ssl.expandNetOutBuffer=Expanding network output buffer to [{0}] bytes
channel.nio.ssl.foundHttp=Found an plain text HTTP request on what should be an encrypted TLS connection
channel.nio.ssl.handshakeError=Handshake error
channel.nio.ssl.incompleteHandshake=Handshake incomplete, you must complete handshake before reading data.
channel.nio.ssl.invalidCloseState=Invalid close state, will not send network data.
channel.nio.ssl.invalidStatus=Unexpected status [{0}].
channel.nio.ssl.netInputNotEmpty=Network input buffer still contains data. Handshake will fail.
channel.nio.ssl.netOutputNotEmpty=Network output buffer still contains data. Handshake will fail.
channel.nio.ssl.notHandshaking=NOT_HANDSHAKING during handshake
channel.nio.ssl.pendingWriteDuringClose=Pending write, so remaining data in the network buffer, can't send SSL close message, socket closed anyway
channel.nio.ssl.remainingDataDuringClose=Remaining data in the network buffer, can't send SSL close message, socket closes anyway
channel.nio.ssl.sniDefault=Unable to buffer enough data to determine requested SNI host name. Using default
channel.nio.ssl.sniHostName=The SNI host name extracted for connection [{0}] was [{1}]
channel.nio.ssl.timeoutDuringHandshake=Timeout during handshake.
channel.nio.ssl.unexpectedStatusDuringUnwrap=Unexpected status [{0}] during handshake UNWRAP.
channel.nio.ssl.unexpectedStatusDuringWrap=Unexpected status [{0}] during handshake WRAP.
channel.nio.ssl.unwrapFail=Unable to unwrap data, invalid status [{0}]
channel.nio.ssl.unwrapFailResize=Unable to unwrap data because buffer is too small, invalid status [{0}]
channel.nio.ssl.wrapException=Handshake failed during wrap
channel.nio.ssl.wrapFail=Unable to wrap data, invalid status [{0}]

endpoint.accept.fail=Socket accept failed
endpoint.alpn.fail=Failed to configure endpoint for ALPN using [{0}]
endpoint.alpn.negotiated=Negotiated [{0}] protocol using ALPN
endpoint.apr.applyConf=Applying OpenSSLConfCmd to SSL context
endpoint.apr.checkConf=Checking OpenSSLConf
endpoint.apr.errApplyConf=Could not apply OpenSSLConf to SSL context
endpoint.apr.errCheckConf=Error during OpenSSLConf check
endpoint.apr.errMakeConf=Could not create OpenSSLConf context
endpoint.apr.failSslContextMake=Unable to create SSLContext. Check that SSLEngine is enabled in the AprLifecycleListener, the AprLifecycleListener has initialised correctly and that a valid SSLProtocol has been specified
endpoint.apr.invalidSslProtocol=An invalid value [{0}] was provided for the SSLProtocol attribute
endpoint.apr.maxConnections.running=The APR endpoint does not support the setting of maxConnections while it is running. The existing value of [{0}] will continue to be used.
endpoint.apr.maxConnections.unlimited=The APR endpoint does not support unlimited connections. The existing value of [{0}] will continue to be used.
endpoint.apr.noSendfileWithSSL=Sendfile is not supported for the APR/native connector when SSL is enabled
endpoint.apr.pollAddInvalid=Invalid attempt to add a socket [{0}] to the poller
endpoint.apr.pollError=Poller failed with error [{0}] : [{1}]
endpoint.apr.pollMergeEvents=Merge poller event [{1}] for socket [{0}] to create merged event [{2}]
endpoint.apr.pollUnknownEvent=A socket was returned from the poller with an unrecognized event [{0}]
endpoint.apr.remoteport=APR socket [{0}] opened with remote port [{1}]
endpoint.apr.tooManyCertFiles=More certificate files were configured than the AprEndpoint can handle
endpoint.debug.channelCloseFail=Failed to close channel
endpoint.debug.destroySocket=Destroying socket [{0}]
endpoint.debug.pollerAdd=Add to addList socket [{0}], timeout [{1}], flags [{2}]
endpoint.debug.pollerAddDo=Add to poller socket [{0}]
endpoint.debug.pollerProcess=Processing socket [{0}] for event(s) [{1}]
endpoint.debug.pollerRemove=Attempting to remove [{0}] from poller
endpoint.debug.pollerRemoved=Removed [{0}] from poller
endpoint.debug.registerRead=Registered read interest for [{0}]
endpoint.debug.registerWrite=Registered write interest for [{0}]
endpoint.debug.socket=socket [{0}]
endpoint.debug.socketCloseFail=Failed to close socket
endpoint.debug.socketTimeout=Timing out [{0}]
endpoint.debug.unlock.fail=Caught exception trying to unlock accept on port [{0}]
endpoint.debug.unlock.localFail=Unable to determine local address for [{0}]
endpoint.debug.unlock.localNone=Failed to unlock acceptor for [{0}] because the local address was not available
endpoint.duplicateSslHostName=Multiple SSLHostConfig elements were provided for the host name [{0}]. Host names must be unique.
endpoint.err.close=Caught exception trying to close socket
endpoint.err.handshake=Handshake failed
endpoint.err.unexpected=Unexpected error processing socket
endpoint.executor.fail=Executor rejected socket [{0}] for processing
endpoint.getAttribute=[{0}] is [{1}]
endpoint.init.bind=Socket bind failed: [{0}] [{1}]
endpoint.init.bind.inherited=No inherited channel while the connector was configured to use one
endpoint.init.listen=Socket listen failed: [{0}] [{1}]
endpoint.init.notavail=APR not available
endpoint.invalidJmxNameSslHost=Unable to generate a valid JMX object name for the SSLHostConfig associated with host [{0}]
endpoint.invalidJmxNameSslHostCert=Unable to generate a valid JMX object name for the SSLHostConfigCertificate associated with host [{0}] and certificate type [{1}]
endpoint.jmxRegistrationFailed=Failed to register the JMX object with name [{0}]
endpoint.jsse.cannotHonorServerCipherOrder=The Java Runtime does not support "useServerCipherSuitesOrder" with JSSE. You must use OpenSSL or Java 8 onwards to use this feature.
endpoint.jsse.noSslContext=No SSLContext could be found for the host name [{0}]
endpoint.launch.fail=Failed to launch new runnable
endpoint.nio.registerFail=Failed to register socket with selector from poller
endpoint.nio.selectorCloseFail=Failed to close selector when closing the poller
endpoint.nio.stopLatchAwaitFail=The pollers did not stop within the expected time
endpoint.nio.stopLatchAwaitInterrupted=This thread was interrupted while waiting for the pollers to stop
endpoint.nio.timeoutCme=Exception during processing of timeouts. The code has been checked repeatedly and no concurrent modification has been found. If you are able to repeat this error please open a Tomcat bug and provide the steps to reproduce.
endpoint.nio2.exclusiveExecutor=The NIO2 connector requires an exclusive executor to operate properly on shutdown
endpoint.noSslHostConfig=No SSLHostConfig element was found with the hostName [{0}] to match the defaultSSLHostConfigName for the connector [{1}]
endpoint.noSslHostName=No host name was provided for the SSL host configuration
endpoint.poll.error=Unexpected poller error
endpoint.poll.fail=Critical poller failure (restarting poller): [{0}] [{1}]
endpoint.poll.initfail=Poller creation failed
endpoint.poll.limitedpollsize=Failed to create poller with specified size of [{0}]
endpoint.process.fail=Error allocating socket processor
endpoint.processing.fail=Error running socket processor
endpoint.removeDefaultSslHostConfig=The default SSLHostConfig (named [{0}]) may not be removed
endpoint.sendfile.addfail=Sendfile failure: [{0}] [{1}]
endpoint.sendfile.error=Unexpected sendfile error
endpoint.serverSocket.closeFailed=Failed to close server socket for [{0}]
endpoint.setAttribute=Set [{0}] to [{1}]
endpoint.timeout.err=Error processing socket timeout
endpoint.unknownSslHostName=The SSL host name [{0}] is not recognised for this endpoint
endpoint.warn.executorShutdown=The executor associated with thread pool [{0}] has not fully shutdown. Some application threads may still be running.
endpoint.warn.incorrectConnectionCount=Incorrect connection count, multiple calls to socket.close for the same socket.
endpoint.warn.noLocalAddr=Unable to determine local address for socket [{0}]
endpoint.warn.noLocalName=Unable to determine local host name for socket [{0}]
endpoint.warn.noLocalPort=Unable to determine local port for socket [{0}]
endpoint.warn.noRemoteAddr=Unable to determine remote address for socket [{0}]
endpoint.warn.noRemoteHost=Unable to determine remote host name for socket [{0}]
endpoint.warn.noRemotePort=Unable to determine remote port for socket [{0}]
endpoint.warn.unlockAcceptorFailed=Acceptor thread [{0}] failed to unlock. Forcing hard socket shutdown.

sniExtractor.clientHelloInvalid=The ClientHello message was not correctly formatted
sniExtractor.clientHelloTooBig=The ClientHello was not presented in a single TLS record so no SNI information could be extracted
sniExtractor.tooEarly=It is illegal to call this method before the client hello has been parsed

socket.apr.clientAbort=The client aborted the connection.
socket.apr.closed=The socket [{0}] associated with this connection has been closed.
socket.apr.read.error=Unexpected error [{0}] reading data from the APR/native socket [{1}] with wrapper [{2}].
socket.apr.write.error=Unexpected error [{0}] writing data to the APR/native socket [{1}] with wrapper [{2}].
socket.closed=The socket associated with this connection has been closed.
socket.sslreneg=Exception re-negotiating SSL connection

socketWrapper.readTimeout=Read timeout
socketWrapper.writeTimeout=Write timeout

sslHostConfig.certificate.notype=Multiple certificates were specified and at least one is missing the required attribute type
sslHostConfig.certificateVerificationInvalid=The certificate verification value [{0}] is not recognised
sslHostConfig.fileNotFound=Configured file [{0}] does not exist
sslHostConfig.invalid_truststore_password=The provided trust store password could not be used to unlock and/or validate the trust store. Retrying to access the trust store with a null password which will skip validation.
sslHostConfig.mismatch=The property [{0}] was set on the SSLHostConfig named [{1}] and is for the [{2}] configuration syntax but the SSLHostConfig is being used with the [{3}] configuration syntax
sslHostConfig.opensslconf.alreadyset=Attempt to set another OpenSSLConf ignored
sslHostConfig.opensslconf.null=Attempt to set null OpenSSLConf ignored
sslHostConfig.prefix_missing=The protocol [{0}] was added to the list of protocols on the SSLHostConfig named [{1}]. Check if a +/- prefix is missing.

sslHostConfigCertificate.mismatch=The property [{0}] was set on the SSLHostConfigCertificate named [{1}] and is for certificate storage type [{2}] but the certificate is being used with a storage of type [{3}]

sslImplementation.cnfe=Unable to create SSLImplementation for class [{0}]

sslUtilBase.active=The [{0}] that are active are : [{1}]
sslUtilBase.aliasIgnored=FIPS enabled so alias name [{0}] will be ignored. If there is more than one key in the key store, the key used will depend on the key store implementation
sslUtilBase.alias_no_key_entry=Alias name [{0}] does not identify a key entry
sslUtilBase.invalidTrustManagerClassName=The trustManagerClassName provided [{0}] does not implement javax.net.ssl.TrustManager
sslUtilBase.keystore_load_failed=Failed to load keystore type [{0}] with path [{1}] due to [{2}]
sslUtilBase.noCertFile=SSLHostConfig attribute certificateFile must be defined when using an SSL connector
sslUtilBase.noCrlSupport=The truststoreProvider [{0}] does not support the certificateRevocationFile configuration option
sslUtilBase.noKeys=No aliases for private keys found in key store
sslUtilBase.noVerificationDepth=The truststoreProvider [{0}] does not support the certificateVerificationDepth configuration option
sslUtilBase.noneSupported=None of the [{0}] specified are supported by the SSL engine : [{1}]
sslUtilBase.skipped=Some of the specified [{0}] are not supported by the SSL engine and have been skipped: [{1}]
sslUtilBase.ssl3=SSLv3 has been explicitly enabled. This protocol is known to be insecure.
sslUtilBase.tls13.auth=The JSSE TLS 1.3 implementation does not support authentication after the initial handshake and is therefore incompatible with optional client authentication
sslUtilBase.trustedCertNotChecked=The validity dates of the trusted certificate with alias [{0}] were not checked as the certificate was of an unknown type
sslUtilBase.trustedCertNotValid=The trusted certificate with alias [{0}] and DN [{1}] is not valid due to [{2}]. Certificates signed by this trusted certificate WILL be accepted
