<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE mbeans-descriptors PUBLIC
   "-//Apache Software Foundation//DTD Model MBeans Configuration File"
   "http://jakarta.apache.org/commons/dtds/mbeans-descriptors.dtd">
<mbeans-descriptors>

  <mbean name="ContextEnvironment"
         className="org.apache.catalina.mbeans.ContextEnvironmentMBean"
         description="Representation of an application environment entry"
         domain="Catalina"
         group="Resources"
         type="org.apache.tomcat.util.descriptor.web.ContextEnvironment">

    <attribute name="className"
               description="Fully qualified class name of the managed object"
               type="java.lang.String"
               writeable="false"/>

    <attribute name="description"
               description="The description of this environment entry"
               type="java.lang.String"/>

    <attribute name="name"
               description="The name of this environment entry"
               type="java.lang.String"/>

    <attribute name="override"
               description="Does this environment entry allow overrides by the
               application deployment descriptor"
               type="boolean"/>

    <attribute name="type"
               description="The type of this environment entry"
               type="java.lang.String"/>

    <attribute name="value"
               description="The value of this environment entry"
               type="java.lang.String"/>
  </mbean>


  <mbean         name="ContextResource"
         className="org.apache.catalina.mbeans.ContextResourceMBean"
         description="Representation of a resource reference for a web application"
         domain="Catalina"
         group="Resources"
         type="org.apache.tomcat.util.descriptor.web.ContextResource">

    <attribute   name="auth"
               description="The authorization requirement for this resource"
               type="java.lang.String"/>

    <attribute   name="description"
               description="The description of this resource"
               type="java.lang.String"/>

    <attribute   name="name"
               description="The name of this resource"
               type="java.lang.String"/>

    <attribute   name="scope"
               description="The sharing scope of this resource factory"
               type="java.lang.String"/>

    <attribute   name="type"
               description="The type of this environment entry"
               type="java.lang.String"/>
  </mbean>


   <mbean         name="ContextResourceLink"
          className="org.apache.catalina.mbeans.ContextResourceLinkMBean"
          description="Representation of a resource link for a web application"
          domain="Catalina"
          group="Resources"
          type="org.apache.tomcat.util.descriptor.web.ContextResourceLink">

    <attribute   name="global"
               description="The global name of this resource"
               type="java.lang.String"/>

    <attribute   name="name"
               description="The name of this resource"
               type="java.lang.String"/>

    <attribute   name="description"
               description="The description of this resource"
               type="java.lang.String"/>

    <attribute   name="type"
               description="The type of this resource"
               type="java.lang.String"/>

  </mbean>


</mbeans-descriptors>
