# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

domainFilterInterceptor.member.refused=成员被拒绝加入集群 cluster[{0}]
domainFilterInterceptor.message.refused=从集群[{0}]中接收的消息被拒绝

encryptInterceptor.algorithm.required=加密算法是必需的,充分说明,例如AES / CBC / PKCS5Padding
encryptInterceptor.algorithm.unsupported-mode=EncryptInterceptor不支持分组密码模式[{0}]
encryptInterceptor.decrypt.error.short-message=解密消息失败: 结尾消息提前结束
encryptInterceptor.decrypt.failed=无法解密信息
encryptInterceptor.encrypt.failed=无法加密信息
encryptInterceptor.init.failed=初始化EncryptInterceptor失败
encryptInterceptor.key.required=需要加密密钥
encryptInterceptor.tcpFailureDetector.ordering=加密拦截器必须位于TCP故障检测器的上游。请重新订购加密拦截器，将其列在通道拦截器管道中的TCP故障检测器之前。

fragmentationInterceptor.fragments.missing=碎片丢失。
fragmentationInterceptor.heartbeat.failed=无法在frag拦截器中执行心跳清除

gzipInterceptor.compress.failed=无法压缩字节内容
gzipInterceptor.decompress.failed=无法解压缩字节内容

messageDispatchInterceptor.AsyncMessage.failed=处理异步消息时出错。
messageDispatchInterceptor.completeMessage.failed=无法报告已完成的邮件。
messageDispatchInterceptor.errorMessage.failed=无法回传错误信息
messageDispatchInterceptor.queue.full=异步队列已满，达到 [{0}] 字节的限制，当前：[{1}] 字节
messageDispatchInterceptor.unableAdd.queue=无法将消息添加到异步队列，队列 bug？
messageDispatchInterceptor.warning.optionflag=警告！你正在覆盖异步选项标志，这将禁用其它程序可能用到的 Channel.SEND_OPTIONS_ASYNCHRONOUS。

nonBlockingCoordinator.electionMessage.sendfailed=无法将选择消息发送到：[{0}]
nonBlockingCoordinator.heartbeat.failed=无法执行心跳
nonBlockingCoordinator.heartbeat.inconsistency=心跳发现不一致，重新启动选举
nonBlockingCoordinator.memberAdded.failed=添加成员后无法开始选举。
nonBlockingCoordinator.memberAlive.failed=无法执行成员活动检查，猜测成员下线。
nonBlockingCoordinator.memberDisappeared.failed=当成员被移除后无法启动选举
nonBlockingCoordinator.processCoordinationMessage.failed=处理协调消息时出错。 可能是致命的。

orderInterceptor.messageAdded.sameCounter=添加的消息具有相同的计数器，同步错误。禁用订单拦截程序

staticMembershipInterceptor.no.failureDetector=没有TcpFailureDetector。 自动检测静态成员无法正常工作。 通过在TcpFailureDetector下定义StaticMembershipInterceptor，可以自动检测静态成员。
staticMembershipInterceptor.no.pingInterceptor=在没有TcpPingInterceptor的情况下，静态成员的健康检查不会正常工作。只有定义了TcpPingInterceptor，才能使健康检查正常进行。
staticMembershipInterceptor.sendLocalMember.failed=本地成员通知失败
staticMembershipInterceptor.sendShutdown.failed=关闭通知失败。

tcpFailureDetector.already.disappeared=验证完成。成员已消失[{0}]。
tcpFailureDetector.failureDetection.failed=无法进行失败监测，假定成员宕机。[{0}]
tcpFailureDetector.heartbeat.failed=TCP心跳检测器无法执行心跳
tcpFailureDetector.member.disappeared=认证完成。成员消失[{0}]
tcpFailureDetector.memberDisappeared.verify=(:收到的membermissed[{0}]消息。将验证。
tcpFailureDetector.performBasicCheck.memberAdded=成员已添加，即使我们未收到通知：[{0}]。
tcpFailureDetector.still.alive=验证完成。成员 [{0}] 仍然存活
tcpFailureDetector.suspectMember.alive=验证可疑成员服务器还活着。[{0}]
tcpFailureDetector.suspectMember.dead=可疑对象，确认已死。[{0}]

tcpPingInterceptor.ping.failed=无法发送 TCP ping
tcpPingInterceptor.pingFailed.pingThread=不能从ping 线程发送ping

throughputInterceptor.report=吞吐量拦截器报告[\n\
\    传输消息: {0}消息数\n\
\    发送：{1}MB(总共)\n\
\    发送：{2}MB (应用)\n\
\    耗时：{3}秒\n\
\    传输速率：{4}MB/秒（总共）\n\
\    传输速率：{5}MB/秒（应用）\n\
\    错误消息：{6}\n\
\    接收消息：{7} 消息数\n\
\    接收速率：{8} MB/秒（从第一个消息开始）\n\
\    收到：{9}MB]\n\
\n

twoPhaseCommitInterceptor.heartbeat.failed=无法在两阶段提交拦截器上执行心跳。
twoPhaseCommitInterceptor.originalMessage.missing=收到确认，但原始邮件丢失。Id:[{0}]
