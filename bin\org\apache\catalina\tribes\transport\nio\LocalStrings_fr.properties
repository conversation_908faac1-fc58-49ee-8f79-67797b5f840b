# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

nioReceiver.alreadyStarted=ServerSocketChannel est déjà démarré
nioReceiver.cleanup.fail=Impossible de nettoyer lors de la fermeture du sélecteur
nioReceiver.clientDisconnect=Le client de réplication est déconnecté, erreur lors du "polling" de clé. Le client est ignoré.
nioReceiver.requestError=Impossible de traiter la requête dans NioReceiver
nioReceiver.run.fail=Impossible d'exécuter l'écouteur de réplication
nioReceiver.start.fail=Incapable de démarrer le récepteur de cluster
nioReceiver.stop.fail=Incapable de femer le sélecteur de récepteur de cluster ("cluster receiver selector")
nioReceiver.stop.threadRunning=Le thread NioReceiver ne s'est pas arrêté suffisamment rapidement, des erreurs peuvent se produire lorsque le sélecteur sera fermé
nioReceiver.threadpool.fail=Le ThreadPool n'a pas pu être initialisé. Le Listener n'a pas démarré.
nioReceiver.threadsExhausted=La clé du canal est enregistrée mais n''a pas reçue d''opérations qui l''intéressaient depuis [{0}] ms (annulé : [{1}]) : [{2}] dernier accès : [{3}] cause possible : tous les threads sont utilisés, effectuez un dump des threads

nioReplicationTask.error.register.key=Erreur lors de l''enregistrement de la clé en lecture : [{0}]
nioReplicationTask.exception.drainChannel=Erreur rencontrée dans TcpReplicationThread.drainChannel
nioReplicationTask.process.clusterMsg.failed=Le traitement du message du cluster a échoué
nioReplicationTask.unable.ack=Impossible d''envoyer un ACK réponse par le canal, le canal peut avoir été déconnecté : [{0}]
nioReplicationTask.unable.drainChannel.ioe=IOException pendant le traitement de la réplication, impossible de drainer le canal, cause probable : le socket gardé actif a été fermé [{0}]

nioSender.already.connected=NioSender est déjà dans l'état connecté
nioSender.datagram.already.established=Le canal de datagramme a déjà été établi, une connection peut être déjà en cours
nioSender.key.inValid=La clé est invalide, elle doit avoir été annulée
nioSender.not.connected=NioSender n'est pas connecté, cela ne devrait jamais arriver
nioSender.receive.failedAck=Réception d'un échec de confirmation : org.apache.catalina.tribes.transport.Constants.FAIL_ACK_DATA
nioSender.sender.disconnected=L'envoyeur s'est déconnecté, impossible d'envoyer la clé de sélection
nioSender.socketChannel.already.established=Le canal du socket a déjà été établi, la connection est peut-être déjà en cours
nioSender.unable.disconnect=Impossible de déconnecter le NioSender, msg=[{0}]
nioSender.unable.receive.ack=Impossible de recevoir un message de confirmation, le canal a rencontré l'EOF
nioSender.unknown.state=Les données sont dans un état inconnu. readyOps=[{0}]

parallelNioSender.error.keepalive=Erreur lors du test de temps d''attente pour l''expéditeur [{0}]
parallelNioSender.operation.timedout=L''opération a dépassé le temps imparti ([{0}] ms)
parallelNioSender.send.fail=L''envoi d''un membre a échoué pour [{0}], le membre est considéré suspect
parallelNioSender.send.fail.retrying=L''envoi au membre[{0}] ne fonctionne pas ; Marqué comme suspect et nouvel essai.
parallelNioSender.send.failed=L'envoi NIO en parallèle a échoué
parallelNioSender.sendFailed.attempt=Echec de l''envoi, tentative : [{0}] maximum : [{1}]
parallelNioSender.sender.disconnected.notRetry=Pas de réessai d''envoi de [{0}], l''expéditeur s''est déconnecté
parallelNioSender.sender.disconnected.sendFailed=L'envoi a échoué et l'envoyeur est déconnecté, pas de nouvel essai
parallelNioSender.unable.setup.NioSender=Impossible d'installer un NioSender

pooledParallelSender.sender.disconnected=Emetteur non connecté
pooledParallelSender.unable.open=Impossible d'ouvrir le sélecteur NIO
pooledParallelSender.unable.retrieveSender=Impossible d'obtenir un envoyeur depuis le pool
pooledParallelSender.unable.retrieveSender.timeout=Impossible d''obtenir un expéditeur de données, temps d''attente dépassé ([{0}] ms)
