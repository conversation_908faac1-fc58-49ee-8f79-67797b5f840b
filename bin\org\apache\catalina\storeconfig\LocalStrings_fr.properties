# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

config.storeContextError=Erreur d''enregistrement du contexte [{0}]
config.storeServerError=Erreur d'enregistrement de la configuration du serveur

factory.storeNoDescriptor=Le descripteur pour l''élément de la classe [{0}] n''est pas configuré
factory.storeTag=enregistrement du tag [{0}] (objet : [{1}])

storeConfigListener.notServer=L'écouteur a été ajouté à un composant autre que le Server et sera donc ignoré

storeFileMover.directoryCreationError=Impossible de créer le répertoire [{0}]
storeFileMover.renameError=Impossible de renommer [{0}] en [{1}]
