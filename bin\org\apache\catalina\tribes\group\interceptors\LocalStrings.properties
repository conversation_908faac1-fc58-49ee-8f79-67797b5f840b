# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

domainFilterInterceptor.member.refused=Member [{0}] was refused to join cluster
domainFilterInterceptor.message.refused=Received message from cluster[{0}] was refused.

encryptInterceptor.algorithm.required=Encryption algorithm is required, fully-specified e.g. AES/CBC/PKCS5Padding
encryptInterceptor.algorithm.unsupported-mode=EncryptInterceptor does not support block cipher mode [{0}]
encryptInterceptor.decrypt.error.short-message=Failed to decrypt message: premature end-of-message
encryptInterceptor.decrypt.failed=Failed to decrypt message
encryptInterceptor.encrypt.failed=Failed to encrypt message
encryptInterceptor.init.failed=Failed to initialize EncryptInterceptor
encryptInterceptor.key.required=Encryption key is required
encryptInterceptor.tcpFailureDetector.ordering=EncryptInterceptor must be upstream of TcpFailureDetector. Please re-order EncryptInterceptor to be listed before TcpFailureDetector in your channel interceptor pipeline.

fragmentationInterceptor.fragments.missing=Fragments are missing.
fragmentationInterceptor.heartbeat.failed=Unable to perform heartbeat clean up in the frag interceptor

gzipInterceptor.compress.failed=Unable to compress byte contents
gzipInterceptor.decompress.failed=Unable to decompress byte contents

messageDispatchInterceptor.AsyncMessage.failed=Error while processing async message.
messageDispatchInterceptor.completeMessage.failed=Unable to report back completed message.
messageDispatchInterceptor.errorMessage.failed=Unable to report back error message.
messageDispatchInterceptor.queue.full=Asynchronous queue is full, reached its limit of [{0}] bytes, current:[{1}] bytes.
messageDispatchInterceptor.unableAdd.queue=Unable to add the message to the async queue, queue bug?
messageDispatchInterceptor.warning.optionflag=Warning, you are overriding the asynchronous option flag, this will disable the Channel.SEND_OPTIONS_ASYNCHRONOUS that other apps might use.

nonBlockingCoordinator.electionMessage.sendfailed=Unable to send election message to:[{0}]
nonBlockingCoordinator.heartbeat.failed=Unable to perform heartbeat.
nonBlockingCoordinator.heartbeat.inconsistency=Heartbeat found inconsistency, restart election
nonBlockingCoordinator.memberAdded.failed=Unable to start election when member was added.
nonBlockingCoordinator.memberAlive.failed=Unable to perform member alive check, assuming member down.
nonBlockingCoordinator.memberDisappeared.failed=Unable to start election when member was removed.
nonBlockingCoordinator.processCoordinationMessage.failed=Error processing coordination message. Could be fatal.

orderInterceptor.messageAdded.sameCounter=Message added has the same counter, synchronization bug. Disable the order interceptor

staticMembershipInterceptor.no.failureDetector=There is no TcpFailureDetector. Automatic detection of static members does not work properly. By defining the StaticMembershipInterceptor under the TcpFailureDetector, automatic detection of the static members will work.
staticMembershipInterceptor.no.pingInterceptor=There is no TcpPingInterceptor. The health check of static members does not work properly. By defining the TcpPingInterceptor, the health check of static members will work.
staticMembershipInterceptor.sendLocalMember.failed=Local member notification failed.
staticMembershipInterceptor.sendShutdown.failed=Shutdown notification failed.

tcpFailureDetector.already.disappeared=Verification complete. Member already disappeared[{0}]
tcpFailureDetector.failureDetection.failed=Unable to perform failure detection check, assuming member down.[{0}]
tcpFailureDetector.heartbeat.failed=Unable to perform heartbeat on the TcpFailureDetector.
tcpFailureDetector.member.disappeared=Verification complete. Member disappeared[{0}]
tcpFailureDetector.memberDisappeared.verify=Received memberDisappeared[{0}] message. Will verify.
tcpFailureDetector.performBasicCheck.memberAdded=Member added, even though we weren''t notified:[{0}]
tcpFailureDetector.still.alive=Verification complete. Member still alive[{0}]
tcpFailureDetector.suspectMember.alive=Suspect member, confirmed alive.[{0}]
tcpFailureDetector.suspectMember.dead=Suspect member, confirmed dead.[{0}]

tcpPingInterceptor.ping.failed=Unable to send TCP ping.
tcpPingInterceptor.pingFailed.pingThread=Unable to send ping from TCP ping thread.

throughputInterceptor.report=ThroughputInterceptor Report[\n\
\tTx Msg:{0} messages\n\
\tSent:{1} MB (total)\n\
\tSent:{2} MB (application)\n\
\tTime:{3} seconds\n\
\tTx Speed:{4} MB/sec (total)\n\
\tTx Speed:{5} MB/sec (application)\n\
\tError Msg:{6}\n\
\tRx Msg:{7} messages\n\
\tRx Speed:{8} MB/sec (since 1st msg)\n\
\tReceived:{9} MB]\n

twoPhaseCommitInterceptor.heartbeat.failed=Unable to perform heartbeat on the TwoPhaseCommit interceptor.
twoPhaseCommitInterceptor.originalMessage.missing=Received a confirmation, but original message is missing. Id:[{0}]
