# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

ajpMessage.invalidPos=Une lecture d''octets à la position [{0}] a été demandée ce qui est au-delà de la fin du message AJP

ajpmessage.invalid=Message invalide reçu avec la signature [{0}]
ajpmessage.invalidLength=Message invalide reçu avec une longueur [{0}]
ajpmessage.null=Impossible d'ajouter une valeur nulle.
ajpmessage.overflow=Débordement du tampon en ajoutant [{0}] octets à la position [{1}]

ajpprocessor.certs.fail=La conversion du certificat a échouée
ajpprocessor.header.error=Erreur de traitement du message d'en-tête
ajpprocessor.header.tooLong=Un en-tête de message de taille [{0}] a été reçu mais la packtSize est de seulement [{1}]
ajpprocessor.readtimeout=Timeout lors de la lecture de données sur le socket
ajpprocessor.request.prepare=Erreur lors de la préparation de la requête
ajpprocessor.request.process=Erreur de traitement de la requête
ajpprocessor.unknownAttribute=La requête est rejetée à cause de l''attribut de requête [{0}] inconnu reçu du reverse proxy

ajpprotocol.noSSL=SSL n''est pas supporté par AJP, la configuration de l''hôte SSL pour [{0}] a été ignorée
ajpprotocol.noSecret=Le connecteur AJP est configuré avec secretRequired="true" mais l'attribut secret est soit null soit "", cette combinaison n'est pas valide
ajpprotocol.noUpgrade=L''upgrade n''est pas supporté par AJP.  La configuration UpgradeProtocol pour [{0}] a été ignorée
ajpprotocol.noUpgradeHandler=AJP ne supporte pas la mise à niveau (upgrade) de HTTP/1.1, le HttpUpgradeHandler [{0}] ne peut pas être utilisé
