# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cgiServlet.emptyEnvVarName=초기화 파라미터 [environment-variable-] 내에, 빈 문자열의 환경 변수 이름입니다.
cgiServlet.expandCloseFail=경로 [{0}]에 위치한 스크립트를 위한, 입력 스트림을 닫지 못했습니다.
cgiServlet.expandCreateDirFail=스크립트를 압축해제 하기 위한 대상 디렉토리 [{0}]을(를) 생성하지 못했습니다.
cgiServlet.expandDeleteFail=압축해제 중 IOException이 발생한 후, [{0}]에 위치한 해당 파일을 삭제하지 못했습니다.
cgiServlet.expandFail=경로 [{0}]의 스크립트를 [{1}](으)로 압축해제 하지 못했습니다.
cgiServlet.expandNotFound=[{0}]을(를) 찾을 수 없어서 압축해제 할 수 없습니다.
cgiServlet.expandOk=[{0}] 경로에 있는 스트립트가 [{1}](으)로 압축 해제되었습니다.
cgiServlet.find.found=CGI 발견: 이름 [{0}], 경로 [{1}], 스크립트 이름 [{2}], CGI 이름 [{3}]
cgiServlet.find.location=[{0}]에 위치한 파일을 찾는 중
cgiServlet.find.path=CGI 위치 [{1}]에 대해 상대적 경로 [{0}]에 위치한, CGI 스크립트가 요청되었습니다.
cgiServlet.invalidArgumentDecoded=디코드된 명령 행 아규먼트 [{0}]이(가), 설정된 cmdLineArgumentsDecoded 패턴 [{1}]과(와) 부합되지 않습니다.
cgiServlet.invalidArgumentEncoded=인코드된 명령 행 아규먼트 [{0}]이(가), 설정된 cmdLineArgumentsEncoded 패턴 [{1}]과(와) 부합되지 않습니다.
cgiServlet.runBadHeader=잘못된 헤더 행: [{0}]
cgiServlet.runFail=CGI 처리 중 I/O 문제 발생
cgiServlet.runHeaderReaderFail=헤더를 읽기 위한 reader를 닫는 중 I/O 문제 발생
cgiServlet.runInvalidStatus=유효하지 않은 HTTP 상태: [{0}]
cgiServlet.runOutputStreamFail=출력 스트림을 닫는 중 I/O 문제 발생
cgiServlet.runReaderInterrupt=stderr에 대한 읽기 쓰레드를 기다리는 중 중단됨
cgiServlet.runStdErr=stderr 행: [{0}]
cgiServlet.runStdErrCount=stderr에서 [{0}] 행들을 받았습니다.
cgiServlet.runStdErrFail=stderr에서 I/O 문제 발생

defaultServlet.blockExternalEntity=PublicId가 [{0}](이)고 systemId가 [{0}]인 외부 엔티티에 대한 접근을 차단했습니다.
defaultServlet.blockExternalEntity2=이름이 [{0}], publicId가 [{1}], baseURI가 [{2}]이며 systemId가 [{3}]인, 외부 엔티티에 대한 접근을 차단했습니다.
defaultServlet.blockExternalSubset=이름이 [{0}](이)고 baseURI가 [{1}]인 외부 하위 집합에 대한 접근이 차단되었습니다.
defaultServlet.missingResource=요청된 리소스 [{0}]은(는) 가용하지 않습니다.
defaultServlet.noResources=정적 리소스들을 찾을 수 없었습니다.
defaultServlet.readerCloseFailed=Reader를 닫지 못했습니다.
defaultServlet.skipfail=단지 [{0}] 바이트들만이 가용하기 때문에, 읽기가 실패했습니다. 요청된 범위의 시작 위치에 도달하기 위하여, [{1}] 바이트들을 건너뛰어야 했습니다.
defaultServlet.xslError=XSL Transformer 오류

directory.filename=파일명
directory.lastModified=최종변경시간
directory.parent=상위로: [{0}]
directory.size=크기
directory.title=[{0}]을(를) 위한 디렉토리 목록 표시

webdavservlet.externalEntityIgnored=요청이, PublicID가 [{0}]이고 SystemID가 [{1}]인 외부 엔티티에 대한 참조를 포함했는데, 이는 무시되었습니다.
webdavservlet.inputstreamclosefail=[{0}]의 입력 스트림을 닫지 못했습니다.
webdavservlet.jaxpfailed=JAXP의 초기화가 실패했습니다.
