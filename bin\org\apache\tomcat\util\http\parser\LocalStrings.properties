# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

authorization.unknownType=Unknown Type [{0}]

cookie.fallToDebug=\n\
\ Note: further occurrences of this error will be logged at DEBUG level.
cookie.invalidCookieValue=A cookie header was received [{0}] that contained an invalid cookie. That cookie will be ignored.
cookie.invalidCookieVersion=A cookie header was received using an unrecognised cookie version of [{0}]. The header and the cookies it contains will be ignored.
cookie.valueNotPresent=<not present>

http.closingBracket=A closing bracket ']' was found in a non-IPv6 host name.
http.illegalAfterIpv6=The character [{0}] is not permitted to follow an IPv6 address in a host name
http.illegalCharacterDomain=The character [{0}] is never valid in a domain name.
http.illegalCharacterIpv4=The character [{0}] is never valid in an IPv4 address.
http.illegalCharacterIpv6=The character [{0}] is never valid in an IPv6 address.
http.invalidCharacterDomain.afterColon=The character [{0}] is not valid after a colon in a domain name.
http.invalidCharacterDomain.afterHyphen=The character [{0}] is not valid after a hyphen in a domain name.
http.invalidCharacterDomain.afterLetter=The character [{0}] is not valid after a letter in a domain name.
http.invalidCharacterDomain.afterNumber=The character [{0}] is not valid after a number in a domain name.
http.invalidCharacterDomain.afterPeriod=The character [{0}] is not valid after a period in a domain name.
http.invalidCharacterDomain.atEnd=The character [{0}] is not valid at the end of a domain name.
http.invalidCharacterDomain.atStart=The character [{0}] is not valid at the start of a domain name.
http.invalidHextet=Invalid hextet. A hextet must consist of 4 or less hex characters.
http.invalidIpv4Location=The IPv6 address contains an embedded IPv4 address at an invalid location.
http.invalidLeadingZero=A non-zero IPv4 octet may not contain a leading zero.
http.invalidOctet=Invalid octet [{0}]. The valid range for IPv4 octets is 0 to 255.
http.invalidRequestTargetCharacter=Character [{0}] is not allowed and will continue to be rejected.
http.invalidSegmentEndState=The state [{0}] is not valid for the end of a segment.
http.noClosingBracket=The IPv6 address is missing a closing bracket.
http.noOpeningBracket=The IPv6 address is missing an opening bracket.
http.singleColonEnd=An IPv6 address may not end with a single ':'.
http.singleColonStart=An IPv6 address may not start with a single ':'.
http.tooFewHextets=An IPv6 address must consist of 8 hextets but this address contains [{0}] hextets and no ''::'' sequence to represent one or more zero hextets.
http.tooManyColons=An IPv6 address may not contain more than 2 sequential colon characters.
http.tooManyDoubleColons=An IPv6 address may only contain a single '::' sequence.
http.tooManyHextets=The IPv6 address contains [{0}] hextets but a valid IPv6 address may not have more than 8.
