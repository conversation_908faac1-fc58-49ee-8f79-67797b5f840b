# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

hostManagerServlet.listed=OK - Hosts aufgelistet
hostManagerServlet.managerXml=FAIL - Konnte manager.xml nicht installieren
hostManagerServlet.start=start: Starte Host mit Name [{0}]
hostManagerServlet.stopFailed=FEHLER - der Host [{0}] konnte nicht gestoppt werden

htmlHostManagerServlet.addAutoDeploy=AutoDeploy
htmlHostManagerServlet.addDeployOnStartup=DeployOnStartup
htmlHostManagerServlet.addHost=Host
htmlHostManagerServlet.addManager=Manager-Anwendung
htmlHostManagerServlet.addTitle=Füge virtuellen Host hinzu
htmlHostManagerServlet.helpHtmlManager=HTML Host-Manager-Hilfe
htmlHostManagerServlet.helpHtmlManagerFile=../docs/html-host-manager-howto.html
htmlHostManagerServlet.helpManager=Host-Manager-Hilfe
htmlHostManagerServlet.hostName=Hostname
htmlHostManagerServlet.hostTasks=Kommandos
htmlHostManagerServlet.hostsRemove=Entferne
htmlHostManagerServlet.hostsStart=Start
htmlHostManagerServlet.hostsStop=Stopp
htmlHostManagerServlet.manager=Host-Manager
htmlHostManagerServlet.messageLabel=Nachricht:
htmlHostManagerServlet.serverOSArch=Betriebssystemarchitektur
