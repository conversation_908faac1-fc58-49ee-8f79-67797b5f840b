# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

dataSourceLinkFactory.badWrapper=타입 [{0}]을(를) 위한 wrapper가 아닙니다.

factoryBase.factoryClassError=리소스 팩토리 클래스를 로드하지 못했습니다.
factoryBase.factoryCreationError=리소스 팩토리 인스턴스를 생성하지 못했습니다.
factoryBase.instanceCreationError=리소스 인스턴스를 생성하지 못했습니다.

lookupFactory.circularReference=[{0}]을(를) 수반한 순환 참조를 발견했습니다.
lookupFactory.createFailed=JNDI lookup 팩토리 클래스의 인스턴스를 생성할 수 없었습니다.
lookupFactory.loadFailed=JNDI lookup 팩토리 클래스를 로드할 수 없었습니다.
lookupFactory.typeMismatch=JNDI 참조 [{0}]은(는) 타입이 [{1}]이어야 하지만, lookup으로 찾아진 객체 [{2}]은(는) 타입이 [{3}]입니다.

resourceFactory.factoryCreationError=리소스 팩토리 인스턴스를 생성할 수 없었습니다.

resourceLinkFactory.invalidGlobalContext=호출자가, 유효하지 않은 글로벌 컨텍스트를 제공했습니다.
resourceLinkFactory.nullType=글로벌 리소스 [{1}]을(를) 참조하는 해당 로컬 리소스 링크 [{0}]이(가) 필수적인 속성 타입을 지정하지 않았습니다.
resourceLinkFactory.unknownType=글로벌 리소스 [{1}]을(를) 참조하는 해당 로컬 리소스 링크 [{0}]이(가), 알 수 없는 타입 [{2}]으로 지정되어 있습니다.
resourceLinkFactory.wrongType=글로벌 리소스 [{1}]을(를) 참조하는 해당 로컬 리소스 링크 [{0}]은(는), 타입 [{2}]의 인스턴스틀 반환할 것으로 기대되었지만, 정작 타입 [{3}]의 인스턴스를 반환했습니다.
