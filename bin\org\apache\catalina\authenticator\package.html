<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<body>

<p>This package contains <code>Authenticator</code> implementations for the
various supported authentication methods (BASIC, DIGEST, and FORM).  In
addition, there is a convenience base class,
<code>AuthenticatorBase</code>, for customized <code>Authenticator</code>
implementations.</p>

<p>If you are using the standard context configuration class
(<code>org.apache.catalina.startup.ContextConfig</code>) to configure the
Authenticator associated with a particular context, you can register the Java
class to be used for each possible authentication method by modifying the
following Properties file:</p>
<pre>
    src/share/org/apache/catalina/startup/Authenticators.properties
</pre>

<p>Each of the standard implementations extends a common base class
(<code>AuthenticatorBase</code>), which is configured by setting the
following JavaBeans properties (with default values in square brackets):</p>
<ul>
<li><b>cache</b> - Should we cache authenticated Principals (thus avoiding
    per-request lookups in our underlying <code>Realm</code>) if this request
    is part of an HTTP session?  [true]</li>
<li><b>debug</b> - Debugging detail level for this component.  [0]</li>
</ul>

<p>The standard authentication methods that are currently provided include:</p>
<ul>
<li><b>BasicAuthenticator</b> - Implements HTTP BASIC authentication, as
    described in RFC 2617.</li>
<li><b>DigestAuthenticator</b> - Implements HTTP DIGEST authentication, as
    described in RFC 2617.</li>
<li><b>FormAuthenticator</b> - Implements FORM-BASED authentication, as
    described in the Servlet API Specification.</li>
</ul>

</body>
