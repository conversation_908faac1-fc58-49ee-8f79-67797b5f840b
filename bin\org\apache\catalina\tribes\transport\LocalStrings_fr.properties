# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

PooledSender.senderDisconnectFail=Impossible de se déconnecter de l'envoyeur

pooledSender.closed.queue=La queue est fermée

receiverBase.bind.failed=Échec d''attachement en écoute de la réplication à l''adresse [{0}]
receiverBase.socket.bind=Socket de réception du serveur attaché à : [{0}]
receiverBase.udp.bind=Le socket serveur receveur est associé avec [{0}]
receiverBase.unable.bind=Impossible de lier la socket serveur à : [{0}], cela renvoie une erreur.
receiverBase.unable.bind.udp=Impossible d''associer le socket UDP à [{0}], propagation de l''erreur
