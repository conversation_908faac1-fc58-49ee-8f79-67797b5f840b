# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

combinedRealm.addRealm=Realm [{0}]을(를) 추가하여, 전체 Realm 개수는 [{1}]이(가) 됩니다.
combinedRealm.authFail=사용자 [{0}]을(를) realm [{1}]을(를) 사용하여 인증하지 못했습니다.
combinedRealm.authStart=사용자 [{0}]을(를), realm [{1}]을(를) 사용하여 인증 시도 중
combinedRealm.authSuccess=사용자 [{0}]을(를) realm [{1}]을(를) 사용하여 인증했습니다.
combinedRealm.getPassword=getPassword() 메소드는 절대 호출되서는 안됩니다.
combinedRealm.getPrincipal=getPrincipal() 메소드는 절대로 호출되서는 안됩니다.
combinedRealm.realmStartFail=[{0}] realm을 시작하지 못했습니다.
combinedRealm.setCredentialHandler=CredentialHandler가 해당 CombinedRealm (혹은 CombinedRealm의 서브클래스) 객체에 설정 되었습니다만, CombinedRealm 클래스는 CredentialHandler를 사용하지 않습니다. 설정 오류인가요?
combinedRealm.unexpectedMethod=CombinedRealm의 메소드에 예기치 않은 호출이었습니다.

credentialHandler.invalidStoredCredential=사용자가 제공한 credentials과 부합하는지 검사하기 위하여, 유효하지 않은 저장된 credentials 문자열 [{0}]이(가), Realm에 의해 제공되었습니다.

dataSourceRealm.authenticateFailure=사용자명 [{0}]은(는) 성공적으로 인증되지 않았습니다.
dataSourceRealm.authenticateSuccess=사용자명 [{0}]이(가) 성공적으로 인증되었습니다.
dataSourceRealm.close=데이터베이스 연결을 닫는 중 예외 발생
dataSourceRealm.exception=인증 처리 수행 중 예외 발생
dataSourceRealm.getPassword.exception=[{0}]을(를) 위한 비밀번호를 조회하던 중 예외 발생
dataSourceRealm.getRoles.exception=사용자 [{0}]을(를) 위한 역할들을 조회하는 중 예외 발생

jaasCallback.username=반환된 사용자명 [{0}]

jaasRealm.accountExpired=사용자명 [{0}]은(는) 만료된 계정이라서 인증되지 않았습니다.
jaasRealm.authenticateFailure=사용자명 [{0}]은(는) 성공적으로 인증되지 못했습니다.
jaasRealm.authenticateSuccess=사용자명 [{0}]이(가) 성공적으로 Principal [{1}](으)로서 인증되었습니다 -- Subject 또한 생성되었습니다.
jaasRealm.beginLogin=애플리케이션 [{1}]을(를) 위한 LoginContext를 사용하여, 사용자명 [{0}]을(를) 위한 JAASRealm 로그인이 요청되었습니다.
jaasRealm.checkPrincipal=Principal [{0}] [{1}]을(를) 점검합니다.
jaasRealm.credentialExpired=만료된 credentials로 인하여, 사용자명 [{0}]이(가) 인증되지 않았습니다.
jaasRealm.failedLogin=사용자명 [{0}]은(는) 로그인 실패로 인하여 인증되지 않았습니다.
jaasRealm.loginContextCreated=사용자명 [{0}]을(를) 위해 생성된 JAAS LoginContext
jaasRealm.loginException=사용자 이름 [{0}]을(를) 인증하는 중 로그인 예외 발생
jaasRealm.rolePrincipalAdd=이 사용자 Principal의 역할들에, 역할 Principal [{0}]을(를) 추가합니다.
jaasRealm.rolePrincipalFailure=유효한 역할 Principal들을 찾을 수 없습니다.
jaasRealm.unexpectedError=예기치 않은 오류
jaasRealm.userPrincipalFailure=유효한 사용자 Principal을 찾을 수 없습니다.
jaasRealm.userPrincipalSuccess=Principal [{0}]은(는) 유효한 사용자 클래스입니다. 이를 사용자 Principal로 사용하겠습니다.

jdbcRealm.authenticateFailure=사용자명 [{0}]이(가) 성공적으로 인증되지 못했습니다.
jdbcRealm.authenticateSuccess=사용자명 [{0}]이(가) 성공적으로 인증되었습니다.
jdbcRealm.close=데이터베이스 연결을 닫는 중 예외 발생
jdbcRealm.exception=인증 처리 수행 중 예외 발생
jdbcRealm.open=데이터베이스 연결을 여는 중 예외 발생
jdbcRealm.open.invalidurl=드라이버 [{0}]은(는) URL [{1}]을(를) 지원하지 않습니다.

jndiRealm.authenticateFailure=사용자명 [{0}]이(가) 성공적으로 인증되지 못했습니다.
jndiRealm.authenticateSuccess=사용자명 [{0}]이(가) 성공적으로 인증되었습니다.
jndiRealm.cipherSuites=이 tls 연결을 위한 cipher suite들로서, [{0}]을(를) 사용 가능하게 합니다.
jndiRealm.close=디렉토리 서버 연결을 닫는 중 예외 발생
jndiRealm.emptyCipherSuites=주어진 cipher suite들에 빈 문자열이 설정되었습니다. 기본 cipher suite들을 사용합니다.
jndiRealm.exception=인증 처리 수행 중 예외 발생
jndiRealm.exception.retry=인증 처리 수행 중 예외 발생. 재시도합니다...
jndiRealm.invalidHostnameVerifier=[{0}]은(는), HostnameVerifier를 위한 클래스 이름으로서, 유효하지 않습니다.
jndiRealm.invalidSslProtocol=주어진 프로토콜 [{0}]은(는) 유효하지 않습니다. 반드시 [{1}] 중의 하나여야 합니다.
jndiRealm.invalidSslSocketFactory=[{0}]은(는) SSLSocketFactory 객체를 위해 유효한 클래스 이름이 아닙니다.
jndiRealm.multipleEntries=[{0}] 사용자 이름은 여러 개의 엔트리들을 가지고 있습니다
jndiRealm.negotiatedTls=프로토콜 [{0}]을(를) 사용하여 TLS 연결을 negotiate 했습니다.
jndiRealm.open=디렉토리 서버 연결을 여는 중 예외 발생
jndiRealm.tlsClose=tls 응답을 닫는 중 예외 발생

lockOutRealm.authLockedUser=잠금 상태인 사용자 [{0}]을(를) 인증하려는 시도가 이루어졌습니다.
lockOutRealm.removeWarning=캐시 크기를 한계값 내에서 유지하기 위하여, 사용자 [{0}]을(를), [{1}]초 후에 실패 사용자 캐시로부터 제거했습니다.

mdCredentialHandler.unknownEncoding=인코딩 [{0}]이(가) 지원되지 않아서, 현 설정 [{1}]이(가) 계속 사용될 것입니다.

memoryRealm.authenticateFailure=사용자명 [{0}]이(가) 성공적으로 인증되지 못했습니다.
memoryRealm.authenticateSuccess=사용자명 [{0}]이(가) 성공적으로 인증되었습니다.
memoryRealm.loadExist=메모리 데이터베이스 파일 [{0}]을(를) 읽을 수 없습니다.
memoryRealm.loadPath=메모리 데이터베이스 파일 [{0}](으)로부터 사용자들을 로드합니다.
memoryRealm.readXml=메모리 데이터베이스 파일을 읽는 중 예외 발생
memoryRealm.xmlFeatureEncoding=XML 파일들에서 자바 인코딩 이름들을 허용하기 위해 digester를 설정하는 중 예외 발생. 오직 IANA 인코딩 이름들만 지원될 것입니다.

pbeCredentialHandler.invalidKeySpec=비밀번호 기반의 키를 생성할 수 없습니다.

realmBase.algorithm=유효하지 않은 메시지 Digest 알고리즘 [{0}]이(가) 지정되었습니다.
realmBase.authenticateFailure=사용자명 [{0}]이(가) 성공적으로 인증되지 못했습니다.
realmBase.authenticateSuccess=사용자명 [{0}]이(가) 성공적으로 인증되었습니다.
realmBase.cannotGetRoles=Principal [{0}](으)로부터 역할들을 얻을 수 없습니다.
realmBase.createUsernameRetriever.ClassCastException=클래스 [{0}]이(가) X509UsernameRetriever 타입이 아닙니다.
realmBase.createUsernameRetriever.newInstance=타입이 [{0}]인 객체를 생성할 수 없습니다.
realmBase.credentialNotDelegated=인증서 저장 옵션이 요청되었지만, 사용자 [{0}]을(를) 위한 인증서 대리 처리가 사용 가능하지 않습니다.
realmBase.delegatedCredentialFail=사용자 [{0}]을(를) 위한 대리 인증서를 얻을 수 없습니다.
realmBase.digest=사용자의 credentials를 digest하는 중 오류 발생
realmBase.forbidden=요청된 리소스에 대한 접근이 거부되었습니다.
realmBase.gotX509Username=X509 인증서로부터 사용자 이름을 구했습니다: [{0}]
realmBase.gssContextNotEstablished=Authenticator 구현 오류: 전달된 보안 컨텍스트가 완전히 확립되지 않았습니다.
realmBase.gssNameFail=확립된 GSSContext로부터, 이름을 추출하지 못했습니다.
realmBase.hasRoleFailure=사용자명 [{0}]은(는) 역할 [{1}]을(를) 가지고 있지 않습니다.
realmBase.hasRoleSuccess=사용자명 [{0}]이(가) 역할 [{1}]을(를) 가지고 있습니다.

userDatabaseRealm.lookup=키 [{0}]을(를) 사용하여 사용자 데이터베이스를 찾는 중 예외 발생
userDatabaseRealm.noDatabase=키 [{0}]을(를) 사용하여 UserDatabase 구성요소를 찾을 수 없습니다.
