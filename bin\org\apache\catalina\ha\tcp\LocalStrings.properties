# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

ReplicationValve.crossContext.add=add Cross Context session replication container to replicationValve threadlocal
ReplicationValve.crossContext.registerSession=register Cross context session id=[{0}] from context [{1}]
ReplicationValve.crossContext.remove=remove Cross Context session replication container from replicationValve threadlocal
ReplicationValve.crossContext.sendDelta=send Cross Context session delta from context [{0}].
ReplicationValve.filter.failure=Unable to compile filter=[{0}]
ReplicationValve.filter.loading=Loading request filter=[{0}]
ReplicationValve.invoke.uri=Invoking replication request on [{0}]
ReplicationValve.nocluster=No cluster configured for this request.
ReplicationValve.resetDeltaRequest=Cluster is standalone: reset Session Request Delta at context [{0}]
ReplicationValve.send.failure=Unable to perform replication request.
ReplicationValve.send.invalid.failure=Unable to send session [id={0}] invalid message over cluster.
ReplicationValve.session.found=Context [{0}]: Found session [{1}] but it isn''t a ClusterSession.
ReplicationValve.session.indicator=Context [{0}]: Primarity of session [{1}] in request attribute [{2}] is [{3}].
ReplicationValve.session.invalid=Context [{0}]: Requested session [{1}] is invalid, removed or not replicated at this node.
ReplicationValve.stats=Average request time=[{0}] ms with cluster overhead time=[{1}] ms for [{2}] requests, [{3}] send requests, [{4}] cross context requests, and [{5}] filter requests (Total request=[{6}] ms, total cluster request=[{7}] ms).

simpleTcpCluster.clustermanager.cloneFailed=Unable to clone cluster manager, defaulting to org.apache.catalina.ha.session.DeltaManager
simpleTcpCluster.clustermanager.notImplement=Manager [{0}] does not implement ClusterManager, addition to cluster has been aborted.
simpleTcpCluster.member.addFailed=Unable to connect to replication system.
simpleTcpCluster.member.added=Replication member added:[{0}]
simpleTcpCluster.member.disappeared=Received member disappeared:[{0}]
simpleTcpCluster.member.removeFailed=Unable remove cluster node from replication system.
simpleTcpCluster.sendFailed=Unable to send message through cluster sender.
simpleTcpCluster.start=Cluster is about to start
simpleTcpCluster.startUnable=Unable to start cluster.
simpleTcpCluster.stopUnable=Unable to stop cluster.
simpleTcpCluster.unableSend.localMember=Unable to send message to local member [{0}]
