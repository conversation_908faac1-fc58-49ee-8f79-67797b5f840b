# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

bioReceiver.already.started=Le ServerSocket a déjà démarré
bioReceiver.run.fail=Impossible d'exécuter l’écouteur de réplication
bioReceiver.socket.closeFailed=Echec de fermeture de la connection
bioReceiver.start.fail=Impossible de démarrer le receveur du cluster
bioReceiver.threadpool.fail=Le ThreadPool n'a pas pu être initialisé, l'écouteur n'a pas démarré
bioReceiver.threads.busy=Tous les fils d'exécution du serveur de réplication sont occupés, impossible de traiter plus de requêtes avant qu'un ne se libère

bioReplicationTask.messageDataReceived.error=Erreur lors de messageDataReceived
bioReplicationTask.reader.closeFailed=Echec de fermeture du lecteur
bioReplicationTask.socket.closeFailed=Ecech de la fermeture du socket
bioReplicationTask.unable.sendAck=Impossible de renvoyer une confirmation par le canal, il peut être déconnecté : [{0}]
bioReplicationTask.unable.service=Incapable de traiter un socket BIO

bioSender.ack.eof=EOF recontré sur le port local [{0}:{1,number,integer}]
bioSender.ack.missing=Incapable de lire l'accusé de réception de [{0}:{1,number,integer}] en {2,number,integer] ms. Déconnexion de la socket et nouvel tentative.
bioSender.ack.wrong=Il manque un ACK correct après la lecture de 10 octets sur le port local [{0}:{1,number,integer}]
bioSender.closeSocket=Sender fermeture du socket vers [{0}:{1,number,integer}] (nombre de fermetures {2,number,integer})
bioSender.disconnect=L'envoyeur s'est déconnecté de [{0}:{1,number,integer}] (nombre de déconnections {2,number,integer})
bioSender.fail.AckReceived=Reçu une confirmation en échec : org.apache.catalina.tribes.transport.Constants.FAIL_ACK_DATA
bioSender.openSocket=L''expéditeur ouvre une socket vers [{0}:{1,number,integer}] ({2,number,integer} sockets ouvertes})
bioSender.openSocket.failure=Echec d'ouverture du socket d'envoi [{0}:{1,number,integer} (nombre d'écecs d'ouverture {2,number,integer})
bioSender.send.again=Envoyer les données à nouveau à [{0}:{1,number,integer}]

pooledMultiSender.retrieve.fail=Impossible d'obtenir un envoyeur à partir du pool
pooledMultiSender.unable.retrieve.sender=Impossible de récupéré un expéditeur de données. délai d''attente ([{0}] ms) dépassé
