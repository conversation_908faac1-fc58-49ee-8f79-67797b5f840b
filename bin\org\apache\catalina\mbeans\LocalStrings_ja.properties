# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jmxRemoteLifecycleListener.createRegistryFailed=ポート[{1}]を使用して[{0}]サーバーのRMIレジストリを作成できません
jmxRemoteLifecycleListener.createServerFailed=[{0}]サーバーのJMXコネクタサーバーを作成できなかったか、または開始できませんでした
jmxRemoteLifecycleListener.destroyServerFailed=[{0}]サーバーのJMXコネクタサーバーを停止できませんでした。
jmxRemoteLifecycleListener.invalidRmiBindAddress=無効なRMIバインドアドレス[{0}]
jmxRemoteLifecycleListener.invalidURL=[{0}] サーバーに不正な JMX サービスリクエスト [{1}] が要求されました。
jmxRemoteLifecycleListener.start=サーバー [{2}] の JMX リモートリスナーをレジストリーポート番号 [{0}] サーバーポート番号 [{1}] で構成しました。

mBeanFactory.managerContext=ManagerコンポーネントはContextにのみ追加できます。
