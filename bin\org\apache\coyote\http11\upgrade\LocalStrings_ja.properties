# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

upgrade.sis.errorCloseFail=直前のエラーの後に完全にInputStreamを閉じることができませんでした。
upgrade.sis.isFinished.ise=ノンブロッキングモードではない ServletInputStream の isFinished() を呼び出すことはできません。(すなわち最初に setReadListener() を呼び出さなければなりません。)
upgrade.sis.isReady.ise=ServletInputStreamが非ブロッキングモードでない場合（つまり、最初にsetReadListener()を呼び出さなければならない場合）isReady()を呼び出すことはできません。
upgrade.sis.onErrorFail=onErrorは登録されたReadListenerが引き起こしたさらなるエラーを飲み込んで処理しています
upgrade.sis.read.closed=InputStream はすでに切断されています。
upgrade.sis.read.ise=isReady()を呼び出すことによって利用可能なデータがあることを最初にチェックすることなく、非ブロックモードでread()メソッドを呼び出すことは不正です。
upgrade.sis.readListener.null=setReadListener() には null を指定できません。
upgrade.sis.readListener.set=アップグレードしたコネクションに何度も setReadListener() を呼び出すのは不正な操作です。
upgrade.sos.canWrite.ise=ServletOutputStreamが非ブロッキングモードでない場合（つまり、setWriteListener()を最初に呼び出さなければならない場合）、canWrite()を呼び出すことはできません。
upgrade.sos.errorCloseFail=以前のエラーの後にOutputStreamをきれいに閉じることができませんでした。
upgrade.sos.onErrorFail=onErrorは登録されたWriteListenerが引き起こしたさらなるエラーを飲み込んで処理しています
upgrade.sos.write.closed=OutputStreamはクローズされました
upgrade.sos.write.ise=ノンブロッキングモードでは初めに isReady() を呼び出して利用可能な領域があることを確かめなければ、あらゆる write() メソッドの呼び出しは不正になります。
upgrade.sos.writeListener.null=setWriteListener() に null を渡すのは不正な操作です。
upgrade.sos.writeListener.set=同じアップグレードされたコネクションに対してsetWriteListener()を複数回呼び出すことはできません。

upgradeProcessor.isCloseFail=アップグレードされたコネクションに関連付けられた入力ストリームを閉じることができませんでした。
upgradeProcessor.osCloseFail=アップグレードされた接続に関連付けられた出力ストリームを閉じることができませんでした
upgradeProcessor.requiredClose=ストリームがcloseRequired状態になったため、アップグレードされた接続をクローズしています：入力[{0}]、出力[{1}]
upgradeProcessor.stop=着信ソケットの状態がSTOPだったのでアップグレードされたコネクションを閉じています。
upgradeProcessor.unexpectedState=ソケットの状態は [{0}] でしたがアップグレードしたコネクションは予期せぬ理由で切断しました。
