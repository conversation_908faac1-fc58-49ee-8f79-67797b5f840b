# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractReplicatedMap.broadcast.noReplies=广播收到0回复，可能是超时了。
abstractReplicatedMap.heartbeat.failed=无法发送AbstractReplicatedMap.ping消息
abstractReplicatedMap.init.completed=AbstractReplicatedMap[{0}]初始化在[{1}]ms内完成。
abstractReplicatedMap.init.start=正在初始化上下文名称为：[{0}]的AbstractReplicatedMap
abstractReplicatedMap.leftOver.ignored=消息[{0}]被忽略
abstractReplicatedMap.leftOver.pingMsg=PING消息已超过超时时间。映射成员[{0}]可能已从映射成员身份中删除。
abstractReplicatedMap.mapMember.unavailable=Member[{0}]还不可用
abstractReplicatedMap.mapMemberAdded.added=已添加映射成员：[{0}]
abstractReplicatedMap.mapMemberAdded.nullMember=通知的成员未注册：[{0}]
abstractReplicatedMap.member.disappeared=成员[{0}]已消失。相关的映射项将重新定位到新节点。
abstractReplicatedMap.ping.stateTransferredMember=成员[{0}]已状态转移，但尚不可用。
abstractReplicatedMap.ping.timeout=映射[{1}]中的成员[{0}]在ping处理中超时。
abstractReplicatedMap.relocate.complete=已在[{0}]ms内完成映射项的重新定位。
abstractReplicatedMap.transferState.noReplies=传输状态，0响应，也许是超时。
abstractReplicatedMap.unable.deserialize.MapMessage=无法反序列化映射消息。
abstractReplicatedMap.unable.diffObject=无法区分对象。将复制整个对象。
abstractReplicatedMap.unable.get=无法复制AbstractReplicatedMap.get操作的数据
abstractReplicatedMap.unable.put=无法复制AbstractReplicatedMap.Put操作的数据
abstractReplicatedMap.unable.relocate=无法将[{0}]重新定位到新的备份节点
abstractReplicatedMap.unable.remove=无法为AbstractReplicatedMap.remove操作复制数据
abstractReplicatedMap.unable.replicate=无法复制数据。
abstractReplicatedMap.unable.retrieve=无法获取远程对象，主键：[{0}]
abstractReplicatedMap.unable.transferState=无法传输AbstractReplicatedMap状态
abstractReplicatedMap.unableApply.diff=无法将diff应用于键：[{0}]
abstractReplicatedMap.unableSelect.backup=无法选择备用节点
abstractReplicatedMap.unableSend.startMessage=无法发送map启动消息。
abstractReplicatedMap.unableStart=无法启动复制Map

lazyReplicatedMap.unableReplicate.backup=无法将备份密钥：[{0}]复制到备份：[{1}]。原因：[{2}]
lazyReplicatedMap.unableReplicate.proxy=不能复制proxy key:[{0}]到备份:[{1}]. 原因是:[{2}]

mapMessage.deserialize.error.key=反序列化MapMessage主键失败
mapMessage.deserialize.error.value=MapMessage.value的反序列化误差

replicatedMap.member.disappeared=成员[{0}]消失，关联的键值实体会重新关联到一个新的节点。
replicatedMap.relocate.complete=map 条目的重定位在 [{0}] ms内完成。
replicatedMap.unable.relocate=不能为一个新的备份节点重启定位[{0}]
replicatedMap.unableReplicate.completely=无法复制备份主键：[{0}]。成功节点：[{1}]。失败节点：[{2}]
