# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jre9Compat.invalidModuleUri=モジュール URI [{0}] を JarScanner で処理する URL に変換できませんでした。
jre9Compat.javaPre9=クラスが見つからないため Java 9 以前の JVM 上でコードが実行されていると仮定します
jre9Compat.unexpected=Java9 クラスやメソッドへの参照の生成に失敗しました

jreCompat.noApplicationProtocol=Java 実行環境が SSLEngine.getApplicationProtocol() に対応していません。Java 9 以降で実行する必要があります。
jreCompat.noApplicationProtocols=Java 実行環境はSSLParameters.setApplicationProtocols()をサポートしていません。 この機能を使用するには、Java 9以降を使用する必要があります。
