# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

bufferPool.created=Created a buffer pool with max size:[{0}] bytes of type: [{1}]

objectReader.retrieveFailed.socketReceiverBufferSize=Unable to retrieve the socket receiver buffer size, setting to default 43800 bytes.

replicationStream.conflict=conflicting non-public interface class loaders

xByteBuffer.discarded.invalidHeader=Discarded the package, invalid header
xByteBuffer.no.package=No package exists in XByteBuffer
xByteBuffer.size.larger.buffer=Size is larger than existing buffer.
xByteBuffer.unableCreate=Unable to create data package, buffer is too small.
xByteBuffer.unableTrim=Cannot trim more bytes than are available. length:[{0}] trim:[{1}]
xByteBuffer.wrong.class=Message has the wrong class. It should implement Serializable, instead it is:[{0}]
