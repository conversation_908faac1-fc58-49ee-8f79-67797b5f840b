# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

htmlManagerServlet.appsAvailable=gestartet
htmlManagerServlet.appsExpire=Lösche Sitzungen
htmlManagerServlet.appsName=Anzeigename
htmlManagerServlet.appsPath=Kontext Pfad
htmlManagerServlet.appsReload=Neu laden
htmlManagerServlet.appsSessions=Sitzungen
htmlManagerServlet.appsStart=Start
htmlManagerServlet.appsStop=Stop
htmlManagerServlet.appsTasks=Kommandos
htmlManagerServlet.appsTitle=Anwendungen
htmlManagerServlet.appsUndeploy=Entfernen
htmlManagerServlet.connectorStateProcessingTime=Verarbeitungszeit:
htmlManagerServlet.connectorStateTableTitleRequest=Anfrage
htmlManagerServlet.connectorStateTableTitleVHost=VHost
htmlManagerServlet.deployButton=Installieren
htmlManagerServlet.deployConfig=XML Konfigurationsdatei URL:
htmlManagerServlet.deployPath=Kontext Pfad (optional):
htmlManagerServlet.deployServer=Verzeichnis oder WAR Datei auf Server installieren
htmlManagerServlet.deployTitle=Installieren
htmlManagerServlet.deployUpload=Lokale WAR Datei zur Installation hochladen
htmlManagerServlet.deployUploadFail=FEHLER - Hochladen zur Installation fehlgeschlagen, Ausnahme: [{0}]
htmlManagerServlet.deployUploadFile=WAR Datei auswählen
htmlManagerServlet.deployUploadNoFile=FEHLER - Hochladen fehlgeschlagen, keine Datei vorhanden
htmlManagerServlet.deployUploadNotWar=FEHLER - Hochgeladene Datei [{0}] muss ein .war sein
htmlManagerServlet.deployUploadWarExists=FEHLER - WAR Datei [{0}] existiert bereits auf Server
htmlManagerServlet.deployWar=WAR oder Verzeichnis URL:
htmlManagerServlet.diagnosticsSslConnectorCertsButton=Zertifikate
htmlManagerServlet.diagnosticsSslConnectorCertsText=Liste die konfigurierten TLS Virtual Hosts und deren Zertifikats-Ketten.
htmlManagerServlet.diagnosticsSslConnectorTrustedCertsButton=Vertrauenswürdige Zertifikate
htmlManagerServlet.expire.explain=mit Inaktivität &ge;
htmlManagerServlet.expire.unit=Minuten
htmlManagerServlet.helpHtmlManager=Hilfeseite HTML Manager (englisch)
htmlManagerServlet.helpHtmlManagerFile=../docs/html-manager-howto.html
htmlManagerServlet.helpManager=Hilfeseite Manager (englisch)
htmlManagerServlet.helpManagerFile=../docs/manager-howto.html
htmlManagerServlet.jvmFreeMemory=Freier Speicher:
htmlManagerServlet.jvmTableTitleMaximum=Maximum
htmlManagerServlet.jvmTableTitleType=Typ
htmlManagerServlet.list=Anwendungen auflisten
htmlManagerServlet.manager=Manager
htmlManagerServlet.messageLabel=Nachricht:
htmlManagerServlet.noManager=-
htmlManagerServlet.serverIPAddress=IP-Adresse
htmlManagerServlet.serverJVMVendor=JVM Hersteller
htmlManagerServlet.serverJVMVersion=JVM Version
htmlManagerServlet.serverOSArch=OS Architektur
htmlManagerServlet.serverOSName=OS Name
htmlManagerServlet.serverOSVersion=OS Version
htmlManagerServlet.serverTitle=Server Informationen
htmlManagerServlet.serverVersion=Tomcat Version
htmlManagerServlet.title=Tomcat Webanwendungs-Manager

managerServlet.alreadyContext=FEHLER - Anwendung existiert bereits für Kontext Pfad [{0}]
managerServlet.deployed=OK - Anwendung mit Kontext Pfad [{0}] installiert
managerServlet.exception=FEHLER - Ausnahme aufgetreten [{0}]
managerServlet.invalidPath=FEHLER - Ungültiger Kontext Pfad [{0}] angegeben
managerServlet.listed=OK - Auflistung der Webanwendungen für virtuellen Server [{0}]
managerServlet.mkdirFail=Fehler - Das Verzeichnis [{0}] konnte nicht erstellt werden.
managerServlet.noCommand=FEHLER - Es wurde kein Kommando angegeben
managerServlet.noContext=FEHLER - Es existiert kein Kontext für Pfad [{0}]
managerServlet.noGlobal=FEHLER - Keine globalen JNDI Ressourcen verfügbar
managerServlet.noSelf=FEHLER - Manager-Kommandos können nicht auf die Manager-Anwendung selbst angewendet werden
managerServlet.noWrapper=Container hat setWrapper() für dieses Servlet nicht aufgerufen
managerServlet.reloaded=OK - Anwendung mit Kontext Pfad [{0}] neu geladen
managerServlet.resourcesAll=OK - Auflistung globaler Ressourcen (alle Typen)
managerServlet.resourcesType=OK - Auflistung globaler Ressourcen von Typ [{0}]
managerServlet.saveFail=FEHLER - Speichern der Konfiguration fehlgeschlagen: [{0}]
managerServlet.sessiondefaultmax=Voreingestellter Sitzungsablauf nach maximal [{0}] Minuten Inaktivität
managerServlet.sessions=OK - Sitzungs-Informationen für Anwendung mit Kontext Pfad [{0}]
managerServlet.sessiontimeout=Inaktiv für [{0}] Minuten: [{1}] Sitzungen
managerServlet.sessiontimeout.expired=Inaktiv für [{0}] Minuten: [{1}] Sitzungen sind abgelaufen
managerServlet.sessiontimeout.unlimited=unlimited Minuten: [{0}] Sitzungen
managerServlet.startFailed=FEHLER - Anwendung mit Kontext Pfad [{0}] konnte nicht gestartet werden
managerServlet.started=OK - Anwendung mit Kontext Pfad [{0}] gestartet
managerServlet.stopped=OK - Anwendung mit Kontext Pfad [{0}] gestoppt
managerServlet.trustedCertsNotConfigured=Für diesen virtuellen Host wurden keine vertrauenswürdige Zertifikate konfiguriert
managerServlet.undeployed=OK - Anwendung mit Kontext Pfad [{0}] entfernt
managerServlet.unknownCommand=FEHLER - Unbekanntes Kommando [{0}]

statusServlet.complete=Ausführlicher Server Status
statusServlet.title=Server Status
