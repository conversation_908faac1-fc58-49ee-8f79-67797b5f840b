# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

bioReceiver.already.started=ServerSocket already started
bioReceiver.run.fail=Unable to run replication listener.
bioReceiver.socket.closeFailed=Failed to close socket
bioReceiver.start.fail=Unable to start cluster receiver
bioReceiver.threadpool.fail=ThreadPool cannot be initialized. Listener not started
bioReceiver.threads.busy=All BIO server replication threads are busy, unable to handle more requests until a thread is freed up.

bioReplicationTask.messageDataReceived.error=Error thrown from messageDataReceived.
bioReplicationTask.reader.closeFailed=Failed to close reader
bioReplicationTask.socket.closeFailed=Failed to close socket
bioReplicationTask.unable.sendAck=Unable to send ACK back through channel, channel disconnected?: [{0}]
bioReplicationTask.unable.service=Unable to service bio socket

bioSender.ack.eof=EOF reached at local port [{0}:{1,number,integer}]
bioSender.ack.missing=Unable to read acknowledgement from [{0}:{1,number,integer}] in {2,number,integer} ms. Disconnecting socket, and trying again.
bioSender.ack.wrong=Missing correct ACK after 10 bytes read at local port [{0}:{1,number,integer}]
bioSender.closeSocket=Sender close socket to [{0}:{1,number,integer}] (close count {2,number,integer})
bioSender.disconnect=Sender disconnect from [{0}:{1,number,integer}] (disconnect count {2,number,integer})
bioSender.fail.AckReceived=Received a failed ack:org.apache.catalina.tribes.transport.Constants.FAIL_ACK_DATA
bioSender.openSocket=Sender open socket to [{0}:{1,number,integer}] (open count {2,number,integer})
bioSender.openSocket.failure=Open sender socket [{0}:{1,number,integer}] failure! (open failure count {2,number,integer})
bioSender.send.again=Send data again to [{0}:{1,number,integer}]

pooledMultiSender.retrieve.fail=Unable to retrieve a sender from the sender pool
pooledMultiSender.unable.retrieve.sender=Unable to retrieve a data sender, time out([{0}] ms) error.
