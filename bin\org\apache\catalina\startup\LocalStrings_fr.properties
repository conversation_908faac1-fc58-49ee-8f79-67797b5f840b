# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

catalina.configFail=Impossible de charger la configuration du serveur depuis [{0}]
catalina.noCluster=le RuleSet du cluster n''a pas été trouvé à cause de [{0}], la configuration du cluster est désactivée
catalina.noNaming=L'environnement de noms JNDI est désactivé
catalina.serverStartFail=Le composant Server requis n'a pas démarré, en conséquence Tomcat ne peut démarrer.
catalina.shutdownHookFail=Le crochet d'arrêt a rencontré une erreur en tentant d'arrêter le serveur
catalina.stopServer=Pas de port d'arrêt configuré, l'arrêt du serveur se fera via un signal du système d'exploitation ; le serveur est en cours d'exécution

connector.noSetExecutor=Le connecteur [{0}] ne supporte pas les exécuteurs externes, la méthode setExecutor(java.util.concurrent.Executor) n''a pas été trouvée
connector.noSetSSLImplementationName=Le connecteur [{0}] ne supporte pas le changement de l''implémentation SSL, car la méthode setSslImplementationName(String) n''a pas été trouvée

contextConfig.altDDNotFound=fichier alt-dd [{0}] pas trouvé
contextConfig.annotationsStackOverflow=Impossible de finir l''analyse des annotations de l''application web [{0}] à cause d''une StackOverflowError, les causes possibles sont une valeur trop petite pour -Xss et des dépendances d''héritage cycliques ; la hiérarchie de classe qui était traitée était [{1}]
contextConfig.applicationMissing=Le fichier web.xml de l'application est absent, utilisation des paramètres par défaut
contextConfig.applicationParse=Erreur d''évaluation (parse) dans le fichier web.xml de l''application à [{0}]
contextConfig.applicationPosition=S''est produite à la ligne [{0}] colonne [{1}]
contextConfig.applicationStart=Traitement du fichier web.xml de l''application à [{0}]
contextConfig.applicationUrl=Impossible de déterminer l'URL pour le fichier d'application web.xml
contextConfig.authenticatorConfigured=Configuration d''un authentificateur (authenticator) pour la méthode [{0}]
contextConfig.authenticatorInstantiate=Impossible d''instancier un authentificateur (authenticator) pour la classe [{0}]
contextConfig.authenticatorMissing=Impossible de configurer un authentificateur (authenticator) pour la méthode [{0}]
contextConfig.authenticatorResources=Impossible de charger la liste de correspondance des authentificateurs (authenticators)
contextConfig.badUrl=Impossible de traiter le descripteur de contexte [{0}]
contextConfig.cce=L''objet donnée évènement cycle de vie (Lifecycle event data object) [{0}] n''est pas un Contexte
contextConfig.contextClose=Erreur lors de la fermeture de context.xml
contextConfig.contextMissing=context.xml manquant : [{0}]
contextConfig.contextParse=Erreur de traitement de context.xml pour [{0}]
contextConfig.defaultError=Erreur de traitement du web.xml par défaut appelé [{0}] à [{1}]
contextConfig.defaultMissing=Fichier web.xml global non trouvé
contextConfig.defaultPosition=S''est produite à la ligne [{0}] colonne [{1}]
contextConfig.destroy=ContextConfig : Destruction
contextConfig.fileUrl=Impossible de créer un objet fichier à partir de l''URL [{0}]
contextConfig.fixDocBase=Exception durant la fixation du "docBase" pour le contexte [{0}]
contextConfig.init=ContextConfig : Initialisation
contextConfig.inputStreamFile=Impossible de traiter les annotations du fichier [{0}]
contextConfig.inputStreamJar=Impossible de traiter l''entrée [{0}] du JAR [{1}] pour les annotations
contextConfig.inputStreamWebResource=Incapable de traiter les annotations de la ressource web [{0}]
contextConfig.invalidSciHandlesTypes=Impossible de charger la classe [{0}] pour la vérifier avec l''annotation @HandlesTypes d''un ou plusieurs ServletContainerInitializer
contextConfig.jarFile=Impossible de traiter les annotations du JAR [{0}]
contextConfig.jspFile.error=Le fichier JSP [{0}] doit commencer par un ''/''
contextConfig.jspFile.warning=WARNING : Le fichier JSP [{0}] doit commencer par un  ''/'' dans l''API Servlet 2.4
contextConfig.missingRealm=Aucun royaume (realm) n'a été configuré pour réaliser l'authentification
contextConfig.noAntiLocking=La valeur [{0}] configurée pour java.io.tmpdir ne correspond pas à un répertoire valide, le paramètre antiResourceLocking configuré pour l''application [{1}] sera ignoré
contextConfig.processAnnotationsDir.debug=Balayage du répertoire pour trouver des fichiers de classe avec annotations [{0}]
contextConfig.processAnnotationsJar.debug=Analyse du fichier jars pour des classes annotées avec [{0}]
contextConfig.processAnnotationsWebDir.debug=Balayage du répertoire d''applications web, pour fichiers de classe avec annotations [{0}]
contextConfig.resourceJarFail=Echec du traitement du JAR trouvé à l''URL [{0}] pour les ressources statiques qui devront être incluses dans le contexte avec le nom [{1}]
contextConfig.role.auth=Le nom de rôle de sécurité [{0}] est utilisé dans un <auth-constraint> sans avoir été défini dans <security-role>
contextConfig.role.link=Le nom de rôle de sécurité [{0}] est utilisé dans un <role-link> sans avoir été défini dans <security-role>
contextConfig.role.runas=Le nom de rôle de sécurité [{0}] est utilisé dans un <run-as> sans avoir été défini dans <security-role>
contextConfig.sci.debug=Impossible de traiter le ServletContainerInitializaer pour [{0}], c''est probablement dû au fait que la classe dans l''annotation @HandlesTypes est manquante
contextConfig.sci.info=Impossible de traiter le ServletContainerInitializaer pour [{0}], c''est probablement dû au fait que la classe dans l''annotation @HandlesTypes est manquante (activer le niveau de log DEBUG pour voir la trace complète)
contextConfig.servletContainerInitializerFail=Impossible de détecter les ServletContainerInitializers pour le contexte nommé [{0}]
contextConfig.start=ContextConfig : Traitement du "START"
contextConfig.stop="ContextConfig" : Traitement du "STOP"
contextConfig.unavailable=Cette application est marquée comme non disponible suite aux erreurs précédentes
contextConfig.unknownUrlProtocol=Le protocole de l''URL [{0}] n''a pas été reconnu pendant le traitement des annotations, l''URL [{1}] a été ignorée
contextConfig.urlPatternValue=A la fois les attributs urlPatterns et la valeur ont été fixés pour l''annotation [{0}] de la classe [{1}]
contextConfig.xmlSettings=Le contexte [{0}] va traiter les fichiers web.xml et le web-fragment.xml avec la validation [{1}] et namespaceAware [{2}]

engineConfig.cce=L''objet donnée évènement cycle de vie (Lifecycle event data object) [{0}] n''est pas un moteur (engine)
engineConfig.start="EngineConfig" : Traitement du "START"
engineConfig.stop="EngineConfig" : Traitement du "STOP"

expandWar.copy=Erreur lors de la copie de [{0}] vers [{1}]
expandWar.createFailed=Impossible de créer le répertoire [{0}]
expandWar.createFileFailed=Impossible de créer le fichier [{0}]
expandWar.deleteFailed=[{0}] n''a pas pu être complètement effacé.  La présence des fichiers résiduels peut causer des problèmes ultérieurs.
expandWar.deleteOld=Un répertoire décompressé [{0}] a été trouvé avec une date de dernière modification qui ne correspond pas au WAR associé, il sera effacé
expandWar.illegalPath=L''archive [{0}] est invalide est sera ignorée : une entrée contient un chemin invalide [{1}] qui n''a pas été extrait vers [{2}] puisque c''est en dehors du docBase [{3}] qui a été défini
expandWar.lastModifiedFailed=Impossible de fixer la date de dernière modification pour [{0}]
expandWar.missingJarEntry=Impossible d''obtenir un flux d''entrée pour le JarEntry [{0}], le WAR peut être invalide

failedContext.start=Impossible de traiter le context.xml soit global, par hôte ou spécifique au contexte, donc le contexte [{0}] ne peut pas être démarré

hostConfig.appBase=La base d''application [{1}] pour le hôte [{0}] n''existe pas ou n''est pas un répertoire. deployOnStartUp et autoDeploy ont été remis à "false" pour éviter des erreurs de déployement. D''autres erreurs peuvent suivre.
hostConfig.canonicalizing=Impossible de déterminer le chemin canonique pour [{0}] pendant qu''on essaie de retirer [{1}]
hostConfig.cce=L''objet donnée évènement cycle de vie (Lifecycle event data object) [{0}] n''est pas un hôte
hostConfig.context.remove=Erreur en enlevant le contexte [{0}]
hostConfig.context.restart=Erreur pendant le redémarrage du contexte [{0}]
hostConfig.createDirs=Impossible de créer un répertoire pour le déploiement : [{0}]
hostConfig.deploy.error=Exception lors du déploiement du répertoire [{0}] de l''application web
hostConfig.deployDescriptor=Déploiement du descripteur de configuration [{0}]
hostConfig.deployDescriptor.blocked=L''application web dont le chemin est [{0}] n''a pas été déployée car elle contenait un descripteur de déploiement [{1}] qui pourrait inclure de la configuration nécessaire pour le déploiement sécurisé de l''application mais ce traitement est empêché par le paramètre deployXML pour cet hôte, un descripteur approprié devrait être crée à [{2}] pour déployer cette application
hostConfig.deployDescriptor.error=Erreur lors du déploiement du descripteur de configuration [{0}]
hostConfig.deployDescriptor.finished=Le traitement du descripteur de déploiement [{0}] a pris [{1}] ms
hostConfig.deployDescriptor.localDocBaseSpecified=Un docBase [{0}] dans l''appBase de l''hôte a été spécifié et sera ignoré
hostConfig.deployDescriptor.threaded.error=Erreur en attendant la fin du déploiement de descripteurs en parallèle
hostConfig.deployDir=Déploiement du répertoire d''application web [{0}]
hostConfig.deployDir.error=Erreur lors du déploiement du répertoire [{0}] de l''application web
hostConfig.deployDir.finished=Le déploiement du répertoire [{0}] de l''application web s''est terminé en [{1}] ms
hostConfig.deployDir.threaded.error=Erreur en attendant la fin du déploiement de répertoires en parallèle
hostConfig.deployWar=Déploiement de l''archive [{0}] de l''application web
hostConfig.deployWar.error=Erreur lors du déploiement de l''archive [{0}] de l''application web
hostConfig.deployWar.finished=Le déploiement de l''archive de l''application web [{0}] s''est terminé en [{1}] ms
hostConfig.deployWar.hiddenDir=Le répertoire [{0}] sera ignoré parce que le WAR [{1}] a la priorité et que unpackWARs est faux
hostConfig.deployWar.threaded.error=Erreur en attendant la fin du déploiement de fichiers WAR en parallèle
hostConfig.deploying=Déploiement des applications web trouvées
hostConfig.docBaseUrlInvalid=La "docBase" fournie ne peut pas être exprimée comme URL
hostConfig.expand=Décompression de l''archive [{0}] de l''application web
hostConfig.expand.error=Exception lors de la décompression de l''archive d''application web [{0}]
hostConfig.ignorePath=Le chemin [{0}] est ignoré pour le déploiement automatique dans appBase
hostConfig.illegalWarName=Le nom du war [{0}] est invalide, l''archive sera ignorée
hostConfig.jmx.register=Echec d''enregistrement du contexte [{0}]
hostConfig.jmx.unregister=Le désenregistrement du contexte [{0}] a échoué
hostConfig.reload=Rechargement du contexte [{0}]
hostConfig.resourceNotAbsolute=Impossible d''enlever la ressource du contexte [{0}] car [{1}] n''est pas absolu
hostConfig.start="HostConfig" : Traitement du "START"
hostConfig.stop="HostConfig" : Traitement du "STOP"
hostConfig.undeploy=Retrait de l''application web ayant pour chemin de contexte [{0}]
hostConfig.undeployVersion=Retrait de l''ancienne version du contexte [{0}] car elle n''a pas de session active

passwdUserDatabase.readFail=Echec d'obtention de la liste complète des utilisateurs depuis /etc/passwd

tomcat.addWebapp.conflictChild=Impossible de déployer le WAR à [{0}] sur le chemin de contexte [{1}] à cause du contexte existant [{2}]
tomcat.addWebapp.conflictFile=Impossible de déployer le WAR à [{0}] sur le chemin de contexte [{1}] à cause du fichier existant [{2}]
tomcat.baseDirMakeFail=Impossible de créer le répertoire [{0}] pour utiliser comme répertoire de base
tomcat.baseDirNotDir=L''emplacement [{0}] spécifié comme répertoire de base n''est pas un répertoire
tomcat.defaultMimeTypeMappingsFail=Impossible de charger les types MIME par défaut
tomcat.homeDirMakeFail=Impossible de créer le répertoire [{0}] pour l''utiliser comme répertoire d''origine

userConfig.database=Exception lors du chargement de la base de données utilisateur
userConfig.deploy=Déploiement de l''application web pour l''utilisateur [{0}]
userConfig.deploy.threaded.error=Erreur en attendant la fin du déploiement de répertoires utilisateur en parallèle
userConfig.deploying=Déploiement des applications web utilisateur
userConfig.error=Erreur lors du déploiement de l''application web pour l''utilisateur [{0}]
userConfig.start="UserConfig" : Traitement du "START"
userConfig.stop="UserConfig" : Traitement du "STOP"

versionLoggerListener.arg=Argument de la ligne de commande : {0}
versionLoggerListener.catalina.base=CATALINA_BASE :                    {0}
versionLoggerListener.catalina.home=CATALINA_HOME :                    {0}
versionLoggerListener.env=Variable d''environnement :         {0} = {1}
versionLoggerListener.java.home=Java Home :                        {0}
versionLoggerListener.os.arch=Architecture :                     {0}
versionLoggerListener.os.name=Nom de l''OS :                      {0}
versionLoggerListener.os.version=Version OS :                       {0}
versionLoggerListener.prop=Propriété système :                {0} = {1}
versionLoggerListener.serverInfo.server.built=Serveur compilé :                  {0}
versionLoggerListener.serverInfo.server.number=Version du serveur :               {0}
versionLoggerListener.serverInfo.server.version=Nom version serveur :              {0}
versionLoggerListener.vm.vendor=Fournisseur de la JVM :            {0}
versionLoggerListener.vm.version=Version JVM :                      {0}

webAnnotationSet.invalidInjection=L'annotation d'injection de ressource de la méthode est invalide
