# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

taskQueue.notRunning=Executor가 실행 중이지 않습니다. 명령을 강제로 큐에 넣을 수 없습니다.

threadPoolExecutor.queueFull=큐의 용량이 꽉 찼습니다.
threadPoolExecutor.threadStoppedToAvoidPotentialLeak=컨텍스트가 중지된 후에 발생할 수 있는 잠재적 메모리 누수를 피하기 위하여, 쓰레드 [{0}]을(를) 중지시킵니다.
