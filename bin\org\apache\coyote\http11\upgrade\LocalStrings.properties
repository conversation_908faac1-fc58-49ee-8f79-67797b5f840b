# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

upgrade.sis.errorCloseFail=Failed to close InputStream cleanly after a previous error
upgrade.sis.isFinished.ise=It is illegal to call isFinished() when the ServletInputStream is not in non-blocking mode (i.e. setReadListener() must be called first)
upgrade.sis.isReady.ise=It is illegal to call isReady() when the ServletInputStream is not in non-blocking mode (i.e. setReadListener() must be called first)
upgrade.sis.onErrorFail=onError processing for the registered ReadListener triggered this further error which was swallowed
upgrade.sis.read.closed=The InputStream has been closed
upgrade.sis.read.ise=It is illegal to call any of the read() methods in non-blocking mode without first checking that there is data available by calling isReady()
upgrade.sis.readListener.null=It is illegal to pass null to setReadListener()
upgrade.sis.readListener.set=It is illegal to call setReadListener() more than once for the same upgraded connection
upgrade.sos.canWrite.ise=It is illegal to call canWrite() when the ServletOutputStream is not in non-blocking mode (i.e. setWriteListener() must be called first)
upgrade.sos.errorCloseFail=Failed to close OutputStream cleanly after a previous error
upgrade.sos.onErrorFail=onError processing for the registered WriteListener triggered this further error which was swallowed
upgrade.sos.write.closed=The OutputStream has been closed
upgrade.sos.write.ise=It is illegal to call any of the write() methods in non-blocking mode without first checking that there is space available by calling isReady()
upgrade.sos.writeListener.null=It is illegal to pass null to setWriteListener()
upgrade.sos.writeListener.set=It is illegal to call setWriteListener() more than once for the same upgraded connection

upgradeProcessor.isCloseFail=Failed to close input stream associated with upgraded connection
upgradeProcessor.osCloseFail=Failed to close output stream associated with upgraded connection
upgradeProcessor.requiredClose=Closing upgraded connection due to closeRequired state of streams: Input [{0}], Output [{1}]
upgradeProcessor.stop=Closing upgraded connection as incoming socket status was STOP
upgradeProcessor.unexpectedState=Closing upgraded connection unexpectedly as incoming socket status was [{0}]
