# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

hostManagerServlet.add=추가: 호스트 [{0}]을(를) 추가합니다.
hostManagerServlet.addFailed=실패 - 호스트 [{0}]을(를) 추가하지 못했습니다.
hostManagerServlet.addSuccess=OK - 호스트 [{0}]이(가) 추가되었습니다.
hostManagerServlet.alreadyHost=실패 - 이름이 [{0}]인 호스트가 이미 존재합니다.
hostManagerServlet.alreadyStarted=실패 - 호스트 [{0}]은(는) 이미 시작되었습니다.
hostManagerServlet.alreadyStopped=실패 - 호스트 [{0}]은(는) 이미 중지되었습니다.
hostManagerServlet.appBaseCreateFail=실패 - 호스트 [{1}]을(를) 위한 appBase [{0}]을(를) 생성하지 못했습니다.
hostManagerServlet.cannotRemoveOwnHost=실패 - 자신의 호스트 [{0}]을(를) 제거할 수는 없습니다.
hostManagerServlet.cannotStartOwnHost=실패 - 자기 자신의 호스트 [{0}]을(를) 시작할 수 없습니다.
hostManagerServlet.cannotStopOwnHost=실패 - 자신의 호스트 [{0}]을(를) 중지시킬 수 없습니다.
hostManagerServlet.configBaseCreateFail=실패 - 호스트 [{0}]을(를) 위한 configBase를 식별하지 못했습니다.
hostManagerServlet.exception=실패 - 예외 발생 [{0}]
hostManagerServlet.invalidHostName=실패 - 유효하지 않은 호스트 이름 [{0}]이(가) 지정되었습니다.
hostManagerServlet.list=목록: 엔진 [{0}]을(를) 위한 호스트들의 목록을 표시합니다.
hostManagerServlet.listed=OK - 호스트 목록
hostManagerServlet.managerXml=실패 - manager.xml을 설치할 수 없었습니다.
hostManagerServlet.noCommand=실패 - 명령이 지정되지 않았습니다.
hostManagerServlet.noHost=실패 - 호스트 이름 [{0}]은(는) 존재하지 않습니다.
hostManagerServlet.noWrapper=이 서블릿을 위해, 컨테이너가 setWrapper()를 호출한 적이 없습니다.
hostManagerServlet.persist=저장: 현재 설정을 저장합니다.
hostManagerServlet.persistFailed=실패 - 설정을 저장하지 못했습니다.
hostManagerServlet.persisted=OK - 설정이 저장되었습니다.
hostManagerServlet.postCommand=실패 - GET 요청을 통해 명령 [{0}]을(를) 사용하려 시도했지만, POST 메소드가 필수적입니다.
hostManagerServlet.remove=제거: 호스트 [{0}]을(를) 제거합니다.
hostManagerServlet.removeFailed=실패 - 호스트 [{0}]을(를) 제거하지 못했습니다.
hostManagerServlet.removeSuccess=OK - 호스트 [{0}]을(를) 제거했습니다.
hostManagerServlet.start=시작: 이름이 [{0}]인 호스트를 시작합니다.
hostManagerServlet.startFailed=실패 - 호스트 [{0}]을(를) 시작하지 못했습니다.
hostManagerServlet.started=OK - 호스트 [{0}](이)가 시작되었습니다.
hostManagerServlet.stop=중지: [{0}](이)라는 이름의 호스트를 중지합니다.
hostManagerServlet.stopFailed=실패 - 호스트 [{0}]을(를) 중지시키지 못했습니다.
hostManagerServlet.stopped=OK - 호스트 [{0}]이(가) 중지되었습니다.
hostManagerServlet.unknownCommand=실패 - 알 수 없는 명령 [{0}]

htmlHostManagerServlet.addAliases=별칭들:
htmlHostManagerServlet.addAppBase=앱 base:
htmlHostManagerServlet.addAutoDeploy=자동배치
htmlHostManagerServlet.addButton=추가
htmlHostManagerServlet.addCopyXML=XML복사
htmlHostManagerServlet.addDeployOnStartup=시작 시 배치하기
htmlHostManagerServlet.addDeployXML=XML배치
htmlHostManagerServlet.addHost=호스트
htmlHostManagerServlet.addManager=매니저 앱
htmlHostManagerServlet.addName=이름:
htmlHostManagerServlet.addTitle=가상 호스트 추가
htmlHostManagerServlet.addUnpackWARs=WAR들의 압축 풀기
htmlHostManagerServlet.helpHtmlManager=HTML 호스트 매니저 도움말
htmlHostManagerServlet.helpHtmlManagerFile=../docs/html-host-manager-howto.html
htmlHostManagerServlet.helpManager=호스트 매니저 도움말
htmlHostManagerServlet.helpManagerFile=../docs/host-manager-howto.html
htmlHostManagerServlet.hostAliases=호스트의 별칭들
htmlHostManagerServlet.hostName=호스트 이름
htmlHostManagerServlet.hostTasks=명령들
htmlHostManagerServlet.hostThis=호스트 매니저가 설치되었습니다 - 명령들은 사용 불능 상태입니다.
htmlHostManagerServlet.hostsRemove=제거
htmlHostManagerServlet.hostsStart=시작
htmlHostManagerServlet.hostsStop=중지
htmlHostManagerServlet.list=가상 호스트들의 목록을 표시
htmlHostManagerServlet.manager=호스트 관리자
htmlHostManagerServlet.messageLabel=메시지:
htmlHostManagerServlet.persistAll=현재 설정을 (가상 호스트들 포함) server.xml과 각 웹 애플리케이션의 context.xml 파일들에 저장합니다.
htmlHostManagerServlet.persistAllButton=전부
htmlHostManagerServlet.persistTitle=저장 환경 설정
htmlHostManagerServlet.serverJVMVendor=JVM 벤더
htmlHostManagerServlet.serverJVMVersion=JVM 버전
htmlHostManagerServlet.serverOSArch=운영체제 아키첵처
htmlHostManagerServlet.serverOSName=운영체제 이름
htmlHostManagerServlet.serverOSVersion=운영체제 버전
htmlHostManagerServlet.serverTitle=서버 정보
htmlHostManagerServlet.serverVersion=Tomcat 버전
htmlHostManagerServlet.title=Tomcat 가상 호스트 매니저

statusServlet.complete=서버 상태 전부
statusServlet.title=서버 상태
