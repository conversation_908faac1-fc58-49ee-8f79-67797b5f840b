# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

JDBCStore.SQLException=SQL错误[{0}]
JDBCStore.checkConnectionClassNotFoundException=找不到 JDBC 驱动程序类 [{0}]
JDBCStore.checkConnectionDBClosed=数据库连接为空或已关闭。正在尝试重新连接。
JDBCStore.checkConnectionDBReOpenFail=重新打开数据库失败，数据库可能已经宕机。
JDBCStore.checkConnectionSQLException=发生 SQL 异常 [{0}]
JDBCStore.close=关闭数据库连接[{0}]时发生异常
JDBCStore.commitSQLException=关闭前提交连接的SQLException
JDBCStore.loading=正在从数据库[{1}]加载会话[{0}]
JDBCStore.missingDataSourceName=没有给出有效的 JNDI 名称。
JDBCStore.removing=正在删除数据库{1}上的会话{0}]
JDBCStore.saving=保存Session [{0}] 到数据库 [{1}]
JDBCStore.wrongDataSource=无法打开 JNDI 数据源 [{0}]

fileStore.createFailed=无法创建用于存储会话数据的目录[{0}]。
fileStore.deleteFailed=无法删除阻止创建会话存储位置的文件 [{0}]
fileStore.deleteSessionFailed=无法删除不再需要的文件[{0}]
fileStore.invalid=无效的持久化文件[{0}]，会话ID为[{1}]
fileStore.loading=正在从文件[{1}]加载会话[{0}]
fileStore.removing=正在删除文件{1}处的会话{0}]
fileStore.saving=保存会话[{0}]到文件[{1}]

managerBase.container.noop=添加到上下文以外的容器的管理器将永远不会被使用
managerBase.contextNull=使用 Manager 之前，必须将 Context 设置为非 null 值
managerBase.createSession.ise=createSession：活跃session过多
managerBase.sessionAttributeNameFilter=已跳过名为[{0}]的会话属性，因为它与名称筛选器[{1}]不匹配。
managerBase.sessionAttributeValueClassNameFilter=已跳过名为[{0}]的会话属性，因为值类型[{1}]与筛选器[{2}]不匹配
managerBase.sessionNotFound=找不到会话 [{0}]
managerBase.sessionTimeout=无效的会话超时设置[{0}]
managerBase.setContextNotNew=如果Manager未处于NEW状态，则调用setContext（）以更改与Manager关联的Context是非法的

persistentManager.backupMaxIdle=正在将会话[{0}]备份到存储区，空闲时间为[{1}]秒。
persistentManager.deserializeError=错误反序列化会话[{0}]: [{1}]
persistentManager.isLoadedError=检查内存中是否加载了会话{0}时出错
persistentManager.loading=正在加载[{0}]持久化会话
persistentManager.removeError=从存储中删除会话[{0}]时出错
persistentManager.serializeError=错误的序列化会话 [{0}]:[{1}]
persistentManager.storeClearError=清除存储区中的所有会话时出错
persistentManager.storeKeysException=不能从 session存储中获取session ID 的列表，假设存储为空
persistentManager.storeLoadError=从存储区交换会话时出错
persistentManager.storeLoadKeysError=从存储加载会话密钥时出错
persistentManager.storeSizeException=无法确定 session 存储区的会话数，假定存储区为空
persistentManager.swapIn=在表单存储中,交换会话[{0}]
persistentManager.swapInException=交换期间存储区中出现异常：[{0}]
persistentManager.swapInInvalid=交换会话[{0}]无效。
persistentManager.swapMaxIdle=交换会话[{0}]以存储，空闲为[{1}]秒
persistentManager.swapTooManyActive=太多活跃会话,替换闲置 [{1}] 秒的会话 [{0}]
persistentManager.tooManyActive=活跃会话太多，[{0}]，寻找闲置的会话来交换
persistentManager.unloading=正在保存[{0}]持续会话

standardManager.deletePersistedFileFail=读取持久会话后无法删除[{0}]。 此文件的持续存在可能导致将来尝试持续会话失败。
standardManager.loading=正在从[{0}]加载持久化会话
standardManager.loading.exception=加载持久化会话时发生异常
standardManager.managerLoad=从持久化存储加载会话发生异常
standardManager.managerUnload=卸载会话到持久存储的异常
standardManager.unloading=保存持久化会话到[{0}]
standardManager.unloading.debug=卸载持续会话
standardManager.unloading.nosessions=没有要卸载的持久会话

standardSession.attributeEvent=会话属性事件侦听器引发异常
standardSession.bindingEvent=会话绑定事件侦听器引发异常
standardSession.getAttribute.ise=getAttribute: 会话已失效
standardSession.getAttributeNames.ise=getAttributeNames：会话已失效
standardSession.getCreationTime.ise=getCreataionTime：会话已经无效
standardSession.getIdleTime.ise=getIdleTime: 已失效的会话
standardSession.getLastAccessedTime.ise=getLastAccessedTime: 会话已失效
standardSession.getThisAccessedTime.ise=getThisAccessedTime:会话已经失效
standardSession.getValueNames.ise=getValueNames：会话已经失效
standardSession.invalidate.ise=无效：会话已无效。
standardSession.isNew.ise=isNew:会话已失效
standardSession.logoutfail=当回话将过期登出用户异常
standardSession.notDeserializable=无法反序列化会话 [{1}] 的属性 [{0}]
standardSession.notSerializable=不能序列化会话[{1}]的属性[{0}]
standardSession.principalNotDeserializable=无法为会话[{0}]反序列化Principal对象
standardSession.principalNotSerializable=无法为会话[{0}]序列化Principal对象
standardSession.removeAttribute.ise=删除属性：会话已失效
standardSession.sessionEvent=会话时间监听抛出异常
standardSession.setAttribute.iae=setAttribute:不可序列化的属性[{0}]。
standardSession.setAttribute.ise=setAttribute:会话[{0}]已无效
standardSession.setAttribute.namenull=setAttribute:name参数不能为空
