# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

beanNameELResolver.beanReadOnly=Le nom de bean [{0}] est en lecture seule

elProcessor.defineFunctionInvalidClass=La classe [{0}] n''est pas publique
elProcessor.defineFunctionInvalidMethod=La méthode [{0}] sur la classe [{1}] n''est pas une méthode statique publique
elProcessor.defineFunctionInvalidParameterList=La liste de paramètres [{0}] pour la méthode [{1}] de la classe [{2}] n''est pas valide
elProcessor.defineFunctionInvalidParameterTypeName=Le type [{0}] du paramètre de la méthode [{1}] sur la classe [{2}] n''est pas valide
elProcessor.defineFunctionNoMethod=Une méthode statique et publique [{0}] n''a pas pu être trouvée sur la classe [{1}]
elProcessor.defineFunctionNullParams=On ou plusieurs paramètres d'entrée sont null

expressionFactory.cannotCreate=Impossible de créer une ExpressionFactory de type [{0}]
expressionFactory.cannotFind=Impossible de trouver une ExpressionFactory de type [{0}]
expressionFactory.readFailed=Impossible de lire [{0}]

importHandler.ambiguousImport=La classe [{0}] n''a pas pu être importée car elle entre en conflit avec [{1}] qui a déjà été importée
importHandler.ambiguousStaticImport=L''import statique [{0}] ne peut pas être traité parce qu''il est en conflit avec [{1}] qui a déjà été importé
importHandler.classNotFound=La classe [{0}] n''a pu être importée, vu qu''on ne l''a pas trouvée
importHandler.invalidClass=La classe [{0}] doit être publique, non abstraite, ne pas être une interface et (pour Java 9+) être dans un package exporté
importHandler.invalidClassName=Le nom de la classe à importer [{0}] doit comprendre un paquet
importHandler.invalidClassNameForStatic=La classe [{0}] spécifiée pour l''import statique [{1}] n''est pas valide
importHandler.invalidStaticName=Le nom de la méthode statique ou champ à importer [{0}] doit inclure une calsse
importHandler.staticNotFound=L''importation statique [{0}] n''a pas été trouvée dans la classe [{1}] pour [{2}]

lambdaExpression.tooFewArgs=Seuls [{0}] arguments ont été fournis pour une expression lambda qui en demande au moins [{1}]

objectNotAssignable=Impossible d''ajouter un objet du type [{0}] à un tableau d''objets de type [{1}]
propertyNotFound=La propriété [{1}] n''a pas été trouvée sur le type [{0}]
propertyNotReadable=La propriété [{1}] n''est pas lisible sur le type [{0}]
propertyNotWritable=La propriété [{1}] ne peut pas être écrite pour le type [{0}]
propertyReadError=Erreur lors de la lecture de [{1}] sur le type [{0}]
propertyWriteError=Erreur d''écriture [{1}] sur le type [{0}]

staticFieldELResolver.methodNotFound=Aucune méthode publique et statique nommée [{0}] n''a été trouvée dans la classe [{1}]
staticFieldELResolver.notFound=Aucun champ public statique nommé [{0}] n''a été trouvé dans la classe [{1}] (exportée pour Java 9+)
staticFieldELResolver.notWriteable=L''écriture dans les champs statiques (champ [{0}] dans la classe [{1}] dans le cas présent) est interdite

util.method.ambiguous=Impossible de trouver une méthode non ambiguë : {0}.{1}({2})
util.method.notfound=Méthode non trouvée : {0}.{1}({2})
