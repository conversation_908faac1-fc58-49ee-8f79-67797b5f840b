# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

channel.nio.interrupted=Le thread en cours a été interrompu
channel.nio.ssl.appInputNotEmpty=Le tampon d'entrée de l'application contient toujours des données, des données ont été perdues
channel.nio.ssl.appOutputNotEmpty=Le tampon de sortie de l'application contient toujours des données, des données ont été perdues
channel.nio.ssl.closeSilentError=Il y a eu une exception en essayant de fermer proprement la connection, comme prévu
channel.nio.ssl.closing=Le canal est en état de fermeture
channel.nio.ssl.eofDuringHandshake=EOF pendant la négociation
channel.nio.ssl.expandNetInBuffer=Augmentation de la taille du tampon d''entrée réseau à [{0}] octets
channel.nio.ssl.expandNetOutBuffer=Augmentation de la taille du tampon de sortie réseau à [{0}] octets
channel.nio.ssl.foundHttp=Une requête HTTP non cryptée a été trouvée sur la connection qui aurait dû être cryptée par TLS
channel.nio.ssl.handshakeError=Erreur lors de la négociation
channel.nio.ssl.incompleteHandshake=La négociation est incomplète, elle doit être terminée pour pouvoir lire des données
channel.nio.ssl.invalidCloseState=Etat de fermeture invalide, aucune donnée ne sera envoyée sur le réseau
channel.nio.ssl.invalidStatus=Etat inattendu [{0}]
channel.nio.ssl.netInputNotEmpty=Le tampon d'entrée du réseau contient toujours des données, la négociation va échouer
channel.nio.ssl.netOutputNotEmpty=Le tampon de sortie du réseau contient toujours des données, la négociation va échouer
channel.nio.ssl.notHandshaking=NOT_HANDSHAKING pendant la négociation SSL
channel.nio.ssl.pendingWriteDuringClose=Une écriture est en cours donc des données sont toujours présentes dans le tampon réseau, impossible d'envoyer le message de fermeture de SSL mais le socket sera fermé de toutes manières
channel.nio.ssl.remainingDataDuringClose=Des données sont toujours présentes dans le tampon réseau, impossible d'envoyer le message de fermeture de SSL mais le socket sera fermé de toutes manières
channel.nio.ssl.sniDefault=Incapacité d'accumuler assez d'information pour déterminer le nom du hôte SNI demandé.  Valeur par défaut utilisée.
channel.nio.ssl.sniHostName=Le nom d''hôte SNI extrait pour la connexion [{0}] est [{1}]
channel.nio.ssl.timeoutDuringHandshake=Timeout pendant la négociation
channel.nio.ssl.unexpectedStatusDuringUnwrap=Statut inattendu [{0}] lors de l''UNWRAP de la négociation
channel.nio.ssl.unexpectedStatusDuringWrap=Statut inattendu [{0}] lors du WRAP de la négociation
channel.nio.ssl.unwrapFail=Incapable de désenrober les données ("unwrap data"), statut invalide [{0}]
channel.nio.ssl.unwrapFailResize=Impossible de faire l''unwrap des données parce que le tampon est trop petit, statut invalide [{0}]
channel.nio.ssl.wrapException=La négociation a échouée pendant le wrap
channel.nio.ssl.wrapFail=Impossible d''enrober (wrap) les données, le status est invalide [{0}]

endpoint.accept.fail=Aucun socket n'a pu être accepté
endpoint.alpn.fail=Erreur de configuration de la terminaison pour ALPN en utilisant [{0}]
endpoint.alpn.negotiated=Le protocole [{0}] a été négocié en utilisant ALPN
endpoint.apr.applyConf=Application de OpenSSLConfCmd au contexte SSL
endpoint.apr.checkConf=Vérification de OpenSSLConf en cours
endpoint.apr.errApplyConf=Impossible d'appliquer OpenSSLConf au contexte SSL
endpoint.apr.errCheckConf=Erreur pendant la vérification de OpenSSLConf
endpoint.apr.errMakeConf=Impossible de créer le contexte de OpenSSLConf
endpoint.apr.failSslContextMake=Incapable de créer un SSLContext. Vérifier que SSLEngine est activé dans l'AprLifecycleListener, que l'AprLifecycleListener a été correctement initialisé et qu'un protocole SSL valide a été spécifié.
endpoint.apr.invalidSslProtocol=Un valeur invalide [{0}] a été donnée pour l''attribut SSLProtocol
endpoint.apr.maxConnections.running=La terminaison APR ne permet pas de fixer maxConnections pendant son exécution, la valeur existante [{0}] continuera à être utilisée
endpoint.apr.maxConnections.unlimited=La terminaison APR ne supporte pas un nombre illimité de connections, la valeur existante [{0}] va continuer à être utilisée
endpoint.apr.noSendfileWithSSL=Sendfile n'est pas supporté avec le connecteur APR lorsque SSL est active
endpoint.apr.pollAddInvalid=Tentative invalide d''ajout d''une socket [{0}] au scrutateur ("poller")
endpoint.apr.pollError=Le scrutateur ("poller") a échoué avec l''erreur [{0}] : [{1}]
endpoint.apr.pollMergeEvents=Fusion des évènements [{1}] du poller pour le socket [{0}] pour créer l''évènement fusionné [{2}]
endpoint.apr.pollUnknownEvent=Un socket a été retourné par le poller avec un évènement inconnu [{0}]
endpoint.apr.remoteport=Le socket APR [{0}] a été ouvert avec le port distant [{1}]
endpoint.apr.tooManyCertFiles=Plus de fichiers de certificats ont été configurés que ce que l'AprEndpoint peut gérer
endpoint.debug.channelCloseFail=Echec de la fermeture du canal (channel)
endpoint.debug.destroySocket=Destruction du socket [{0}]
endpoint.debug.pollerAdd=Ajout à la addList socket [{0}], inactivité maximale [{1}], drapeaux [{2}]
endpoint.debug.pollerAddDo=Ajout du socket [{0}] au poller
endpoint.debug.pollerProcess=Traitement de(s) évènement(s) [{1}] pour la socket [{0}]
endpoint.debug.pollerRemove=Essai d''enlever [{0}] du poller
endpoint.debug.pollerRemoved=Enlevé [{0}] du poller
endpoint.debug.registerRead=Enregistrement de l’intérêt en lecture pour [{0}]
endpoint.debug.registerWrite=Enregistrement de l’intérêt en écriture pour [{0}]
endpoint.debug.socket=socket [{0}]
endpoint.debug.socketCloseFail=Echec de fermeture du socket
endpoint.debug.socketTimeout=Expiration [{0}]
endpoint.debug.unlock.fail=Reçu une exception en essayant de déverrouiller l''accepteur sur le port [{0}]
endpoint.debug.unlock.localFail=Impossible de déterminer l''adresse locales pour [{0}]
endpoint.debug.unlock.localNone=Impossible de débloquer l''accepteur pour [{0}] car l''adresse locale n''était pas disponible
endpoint.duplicateSslHostName=Plusieurs éléments SSLHostConfig ont été fournis pour le nom d''hôte [{0}], les noms d''hôte doivent être uniques
endpoint.err.close=Une exception s'est produite en essayant de fermer le socket
endpoint.err.handshake=Echec de négociation
endpoint.err.unexpected=Erreur inattendue lors du traitement du socket
endpoint.executor.fail=L''exécuteur a rejeté le traitement du socket [{0}]
endpoint.getAttribute=[{0}] est [{1}]
endpoint.init.bind=L''association du socket a échoué : [{0}] [{1}]
endpoint.init.bind.inherited=Pas de canal hérité alors que le connecteur était configuré pour en utiliser un
endpoint.init.listen=L''écoute sur le socket a échoué : [{0}] [{1}]
endpoint.init.notavail=APR n'est pas disponible
endpoint.invalidJmxNameSslHost=Impossible de générer un nom d''objet JMX valide pour le SSLHostConfig associé à l''hôte [{0}]
endpoint.invalidJmxNameSslHostCert=Impossible de générer un nom d''objet JMX valide pour le SSLHostConfigCertificate associé à l''hôte [{0}] et au type de certificat [{1}]
endpoint.jmxRegistrationFailed=Echec de l''enregistrement JMX de l''objet avec le nom [{0}]
endpoint.jsse.noSslContext=Aucun SSLContext n''a été trouvé pour le nom d''hôte [{0}]
endpoint.launch.fail=Impossible de démarrer le nouvel exécutable
endpoint.nio.registerFail=Echec d'enregistrement du socket avec le sélecteur du poller
endpoint.nio.selectorCloseFail=Impossible de fermer le sélecteur lors de la fermeture du poller
endpoint.nio.stopLatchAwaitFail=Les pollers ne se sont pas arrêtés dans le temps imparti
endpoint.nio.stopLatchAwaitInterrupted=Ce thread a été interrompu pendant qu'il attendait l'arrêt des scrutateurs ("pollers")
endpoint.nio.timeoutCme=Exception pendant le traitement du délai d'attente maximum ; le code a été vérifié de manière répétée et aucune modification concurrence n'a pu être trouvée, si vous obtenez cette erreur de manière reproductible merci d'ouvrir un rapport d'erreur sur Tomcat en fournissant les informations pour la reproduire
endpoint.nio2.exclusiveExecutor=Le connecteur NIO2 a besoin d'un accès exclusif à un exécuteur pour pouvoir avoir un comportement prévisible lors de son arrêt
endpoint.noSslHostConfig=Pas d''élément SSLHostConfig trouvé avec hostName [{0}] correspondant au defaultSSLHostConfigName du connecteur [{1}]
endpoint.noSslHostName=Aucun nom d'hôte n'a été fourni pour la configuration de l'hôte SSL
endpoint.poll.error=Erreur inattendue du poller
endpoint.poll.fail=Echec critique du poller, redémarrage : [{0}] [{1}]
endpoint.poll.initfail=Echec de création du poller
endpoint.poll.limitedpollsize=Echec de création d''un poller avec la taille spécifiée [{0}]
endpoint.process.fail=Erreur lors de l'allocation d'un processeur de socket
endpoint.processing.fail=Erreur lors de l’exécution du processeur du socket
endpoint.removeDefaultSslHostConfig=Le SSLHostConfig par défaut (de nom [{0}]) ne peut pas être retiré
endpoint.sendfile.addfail=Echec de Sendfile :  [{0}] [{1}]
endpoint.sendfile.error=Erreur lors de sendfile
endpoint.serverSocket.closeFailed=Le socket serveur [{0}] n''a pas pu être fermé
endpoint.setAttribute=Met [{0}] à [{1}]
endpoint.timeout.err=Erreur en traitant le dépassement de temps d'attente du socket
endpoint.unknownSslHostName=Le nom d''hôte SSL [{0}] n''est pas reconnu pour cette terminaison
endpoint.warn.executorShutdown=L''exécuteur associé au pool de threads [{0}] n''est pas complètement arrêté, certains threads d''application peuvent toujours être en cours d''exécution
endpoint.warn.incorrectConnectionCount=Le décompte du nombre de connections est incorrect, la méthode de fermeture d'un même socket a été appelée plusieurs fois
endpoint.warn.noLocalAddr=Impossible de déterminer l''addresse locale pour le socket [{0}]
endpoint.warn.noLocalName=Incapable de déterminer l''hôte local ("local host") pour la socket [{0}]
endpoint.warn.noLocalPort=Impossible de déterminer le port local pour le socket [{0}]
endpoint.warn.noRemoteAddr=Impossible de déterminer l''adresse distante pour le socket [{0}]
endpoint.warn.noRemoteHost=Impossible de déterminer le nom d''hôte distant pour le socket [{0}]
endpoint.warn.noRemotePort=Impossible de déterminer le port distant pour le socket [{0}]
endpoint.warn.unlockAcceptorFailed=Le thread qui accepte les sockets [{0}] n''a pu être débloqué, arrêt forcé su socket serveur

sniExtractor.clientHelloInvalid=Le message ClientHello n'était pas formaté correctement
sniExtractor.clientHelloTooBig=Le ClientHello n'a pas été présenté dans un seul enregistrement TLS donc l'information SNI n'a pu être extraite
sniExtractor.tooEarly=Il est illégal d'appeler cette méthode avant que le hello du client ait été traité

socket.apr.clientAbort=Le client a avorté la connection
socket.apr.closed=Le socket [{0}] associé avec cete connection a été fermé.
socket.apr.read.error=Erreur inattendue [{0}] lors de la lecture de données depuis le socket APR [{1}] avec l''enrobeur [{2}]
socket.apr.write.error=Erreur inattendue [{0}] lors de l''écriture de données vers le socket APR [{1}] avec l''enrobeur [{2}]
socket.closed=Le socket associé à cette connection a été fermé
socket.sslreneg=Exception lors de la renégociation de la connection SSL

socketWrapper.readTimeout=Timeout en lecture
socketWrapper.writeTimeout=Timeout en écriture

sslHostConfig.certificate.notype=Plusieurs certificats ont été spécifiés et au moins un n'a pas d'attribut type
sslHostConfig.certificateVerificationInvalid=La valeur de vérification de certificat [{0}] n''est pas reconnue
sslHostConfig.fileNotFound=Le fichier [{0}] configuré n''existe pas.
sslHostConfig.invalid_truststore_password=Le mot de passe de la base de confiance n'a pas pu être utilisé pour déverrouiller et ou valider celle ci, nouvel essai en utilisant un mot de passe null pour passer la validation
sslHostConfig.mismatch=La propriété [{0}] a été fixée sur le SSLHostConfig nommé [{1}] et est pour la syntaxe de configuration [{2}] mais le SSLHostConfig est utilisé avec la syntaxe de configuration [{3}]
sslHostConfig.opensslconf.alreadyset=Un tentative de fixer une autre OpenSSLConf a été ignorée
sslHostConfig.opensslconf.null=L'OpenSSLConf nul a été ignoré
sslHostConfig.prefix_missing=Le protocole [{0}] a été ajouté à la liste des protocoles du SSLHostConfig nommé [{1}], vérifier qu''un préfixe +/- ne manque pas

sslHostConfigCertificate.mismatch=La propriété [{0}] a été définie sur le SSLHostConfigCertificate nommé [{1}] et est pour un certificat de stockage de type [{2}] mais le certificat est utilisé avec un stockage de type [{3}]

sslImplementation.cnfe=Impossible de créer une SSLImplementation avec la class [{0}]

sslUtilBase.active=Les [{0}] qui sont actifs sont : [{1}]
sslUtilBase.aliasIgnored=Le mode FIPS est activé donc le nom d''alias [{0}] sera ignoré, s''il il y a plus d''une clé dans la keystore, la clé utilisée dépendra de son implémentation
sslUtilBase.alias_no_key_entry=Le nom alias [{0}] n''identifie pas une entrée de clé
sslUtilBase.invalidTrustManagerClassName=Le trustManagerClassName fourni [{0}] n''implémente pas javax.net.ssl.TrustManager
sslUtilBase.keystore_load_failed=Impossible de changer la base de clés de type [{0}] avec le chemin [{1}] à cause de [{2}]
sslUtilBase.noCertFile=L'attribut certificateFile de SSLHostConfig doit être défini lorsqu'un connecteur SSL est utilisé
sslUtilBase.noCrlSupport=Le truststoreProvider [{0}] ne supporte pas d''option de configuration certificateRevocationFile
sslUtilBase.noKeys=Aucun alias pour les clés privées n'a été trouvé dans la base de clés
sslUtilBase.noVerificationDepth=Le truststoreProvider [{0}] ne supporte pas l''option de configuration certificateVerificationDepth
sslUtilBase.noneSupported=Aucun des [{0}] spécifiés n''est supporté par le moteur SSL : [{1}]
sslUtilBase.skipped=Quelques [{0}] spécifiés ne sont pas supportés par le moteur SSL et ont été ignorés : [{1}]
sslUtilBase.ssl3=SSLv3 a été explicitement activé.  Ce protocole est connu comme non-sécurisé.
sslUtilBase.tls13.auth=L’implémentation JSSE de TLS 1.3 ne supporte pas l'authentification après la négociation initiale, elle est donc incompatible avec l’authentification optionnelle du client
sslUtilBase.trustedCertNotChecked=Les dates de validité du certificat de confiance dont l''alias est [{0}] n''ont pas été vérifiées car sont type est inconnu
sslUtilBase.trustedCertNotValid=Le certificat de confiance avec l''alias [{0}] et le DN [{1}] n''est pas valide à cause de [{2}], les certificats signés par ce certificat de confiance SERONT acceptés
