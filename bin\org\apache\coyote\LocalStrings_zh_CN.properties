# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractConnectionHandler.connectionsGet=为套接字[{1}]找到一个处理器[{0}]
abstractConnectionHandler.error=读取请求时出错，忽略
abstractConnectionHandler.ioexception.debug=正常的 IOException，忽略
abstractConnectionHandler.negotiatedProcessor.fail=无法为协商协议[{0}]创建处理器
abstractConnectionHandler.oome=无法完成请求的处理
abstractConnectionHandler.process=正在处理状态为[{1}]的套接字[{0}]
abstractConnectionHandler.processorCreate=创建新处理器[{0}]
abstractConnectionHandler.processorPop=从缓存中弹出的处理器[{0}]
abstractConnectionHandler.protocolexception.debug=ProtocolExceptions是正常的，被忽略
abstractConnectionHandler.socketexception.debug=(:SocketException是正常的，忽略
abstractConnectionHandler.upgradeCreate=为套接字包装程序[{1}]创建了升级处理器[{0}]

abstractProcessor.fallToDebug=\n\
\ 注意：更多的请求解析错误将以DEBUG级别日志进行记录。
abstractProcessor.hostInvalid=[{0}] 是无效主机
abstractProcessor.httpupgrade.notsupported=此协议不支持HTTP升级（upgrade）。
abstractProcessor.pushrequest.notsupported=此协议不支持服务器推送请求
abstractProcessor.setErrorState=正在处理请求时出现错误状态[{0}]
abstractProcessor.socket.ssl=获取SSL属性异常

abstractProtocol.mbeanDeregistrationFailed=无法从MBean服务器[{1}]中注销名为[{0}]的MBean
abstractProtocol.processorRegisterError=注册请求处理器错误
abstractProtocol.processorUnregisterError=注销请求处理器错误
abstractProtocol.waitingProcessor.add=添加处理器[{0}]到等待队列
abstractProtocol.waitingProcessor.remove=从等待的处理器中移除处理器[{0}]

abstractProtocolHandler.destroy=正在摧毁协议处理器 [{0}]
abstractProtocolHandler.init=初始化协议处理器 [{0}]
abstractProtocolHandler.pause=暂停ProtocolHandler[{0}]
abstractProtocolHandler.resume=正在恢复ProtocolHandler[{0}]
abstractProtocolHandler.setAttribute=使用值[{1}]设置属性[{0}]
abstractProtocolHandler.start=开始协议处理句柄[{0}]
abstractProtocolHandler.stop=正在停止ProtocolHandler [{0}]

asyncStateMachine.invalidAsyncState=调用[{0}]对于具有异步状态[{1}]的请求无效

compressionConfig.ContentEncodingParseFail=检查压缩是否已经在使用时，解析Content-Encoding头失败

continueResponseTiming.invalid=对于continueResponseTiming，值[{0}]不是有效的配置项

request.notAsync=只有在异步处理或HTTP升级处理中切换到非阻塞IO才有效
request.nullReadListener=传递给setReadListener（）的侦听器不能为空
request.readListenerSet=已设置非阻塞读取侦听器

response.encoding.invalid=JRE无法识别编码[{0}]
response.notAsync=只有在异步处理或HTTP升级处理中切换到非阻塞IO才有效
response.notNonBlocking=当响应尚未进入非阻塞模式时，调用 isReady() 无效
response.nullWriteListener=传递给setWriteListener（）的侦听器不能为空
response.writeListenerSet=非阻塞的写入监听器已经被设置.
