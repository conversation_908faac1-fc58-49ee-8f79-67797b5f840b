# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

channelCoordinator.alreadyStarted=通道已经启动，级别：[{0}]
channelCoordinator.invalid.startLevel=启动级别无效，有效级别为：SND_RX_SEQ，SND_TX_SEQ，MBR_TX_SEQ，MBR_RX_SEQ

groupChannel.listener.alreadyExist=侦听器已存在：[{0}][{1}]
groupChannel.noDestination=没有指定目的地
groupChannel.nullMessage=无法发送空消息
groupChannel.optionFlag.conflict=拦截器选项标志冲突：[{0}]
groupChannel.receiving.error=接收消息时出错：
groupChannel.sendFail.noRpcChannelReply=找不到rpc通道，无法发送NoRpcChannelReply。
groupChannel.unable.deserialize=无法反序列化消息：[{0}]
groupChannel.unable.sendHeartbeat=无法通过Tribes拦截器堆栈发送心跳。 会再试一次。

rpcChannel.replyFailed=无法在RpcChannel中发送回复
