# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cgiServlet.emptyEnvVarName=la nom de variable d'environnement est vide dans le paramètre d'initialisation [environment-variable-]
cgiServlet.expandCloseFail=Impossible de fermer le flux d''entrée du script avec le chemin [{0}]
cgiServlet.expandCreateDirFail=Echec de la création du répertoire de destination [{0}] pour la décompression du script
cgiServlet.expandDeleteFail=Impossible d''effacer le fichier [{0}] suite à une IOException pendant la décompression
cgiServlet.expandFail=Impossible de faire l''expansion du script au chemin [{0}] vers [{1}]
cgiServlet.expandNotFound=Impossible de décompresser [{0}] car il n''a pas été trouvé
cgiServlet.expandOk=Extrait le script du chemin [{0}] vers [{1}]
cgiServlet.find.found=Trouvé le CGI : nom [{0}], chemin [{1}], nom de script [{2}] et nom du CGI [{3}]
cgiServlet.find.location=Recherche d''un fichier en [{0}]
cgiServlet.find.path=Script CGI demandé au chemin [{0}] relatif au CGI à [{1}]
cgiServlet.invalidArgumentDecoded=Les paramètres de ligne de commande décodés [{0}] ne correspondent pas au modèle cmdLineArgumentsDecoded configuré [{1}]
cgiServlet.invalidArgumentEncoded=Les paramètres de ligne de commande encodés [{0}] ne correspondent pas au modèle cmdLineArgumentsEncoded configuré [{1}]
cgiServlet.runBadHeader=Mauvaise ligne d''en-tête [{0}]
cgiServlet.runFail=Problèmes d'IO lors de l'exécution du CGI
cgiServlet.runHeaderReaderFail=Problème d'E/S lors de la fermeture du lecteur de headers
cgiServlet.runInvalidStatus=Statut invalide [{0}]
cgiServlet.runOutputStreamFail=Problème d'E/S à la fermeture du flux de sortie
cgiServlet.runReaderInterrupt=Interrompu pendant l'attente du thread de lecture de la sortie d'erreur (stderr reader thread)
cgiServlet.runStdErr=ligne stderr : [{0}]
cgiServlet.runStdErrCount=Reçues [{0}] lignes sur le stderr
cgiServlet.runStdErrFail=Problème d'entrée sortie pour le stderr

defaultServlet.blockExternalEntity=L''accès aux entités externes avec le publicId [{0}] et le systemId [{1}] est bloqué
defaultServlet.blockExternalEntity2=L''accès à l''entité externe nommée [{0}], publicId [{1}], baseURI [{2}], systemId [{3}] a été bloqué
defaultServlet.blockExternalSubset=L''accès au sous-ensemble externe de nom [{0}] et de baseURI [{1}] a été bloqué
defaultServlet.missingResource=La ressource demandée [{0}] n''est pas disponible
defaultServlet.noResources=Pas de ressources statiques
defaultServlet.readerCloseFailed=Impossible de fermer le lecteur
defaultServlet.skipfail=La lecture a échouée parce que seuls [{0}] octets étaient disponibles alors qu''il était nécessaire d''en sauter [{1}] pour atteindre le début de la plage demandée
defaultServlet.xslError=Erreur de transformation XSL

directory.filename=Nom de fichier
directory.lastModified=Dernière modification
directory.parent=Jusqu''à [{0}]
directory.size=Taille
directory.title=Liste du répertoire pour [{0}]

webdavservlet.externalEntityIgnored=La requête a inclus une référence à une entité externe avec publicId [{0}] et systemId [{1}] qui a été ignorée
webdavservlet.inputstreamclosefail=Impossible de fermer le flux d''entrée pour [{0}]
webdavservlet.jaxpfailed=Erreur d'initialisation de JAXP
