# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

contextBindings.noContextBoundToCL=Naming 컨텍스트가 이 클래스로더에 바인딩되지 않았습니다.
contextBindings.noContextBoundToThread=이 쓰레드에 Naming 컨텍스트가 바인딩되지 않았습니다.
contextBindings.unknownContext=알 수 없는 컨텍스트 이름: [{0}]

namingContext.alreadyBound=Name [{0}]이(가) 이미 이 컨텍스트에 바인딩 되어 있습니다.
namingContext.contextExpected=Name이 컨텍스트에 바인딩 되지 않았습니다.
namingContext.failResolvingReference=참조를 결정하는 중 예기치 않은 예외 발생
namingContext.invalidName=Name이 유효하지 않습니다.
namingContext.nameNotBound=Name [{0}]은(는) 이 컨텍스트에 바인딩되지 않았습니다. [{1}]을(를) 찾을 수 없습니다.
namingContext.noAbsoluteName=이 네임스페이스를 위한 절대 이름을 생성할 수 없습니다.
namingContext.readOnly=컨텍스트가 읽기 전용입니다.

selectorContext.methodUsingName=Name [{1}]을(를) 사용하여 메소드 [{0}]을(를) 호출합니다.
selectorContext.methodUsingString=문자열 [{1}]을(를) 사용하여 메소드 [{0}]을(를) 호출합니다.
selectorContext.noJavaUrl=이 컨텍스트는 반드시 java: URL을 통해 접근되어야 합니다.
