package com.yq.cc.vxml;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * VXML 转 IVR JSON 工具测试类
 */
public class VxmlToIvrUtilTest {
    
    public static void main(String[] args) {
        try {
            System.out.println("=== VXML 转 IVR JSON 测试 ===");
            
            // 测试转换功能
            testConversion();
            
            // 验证生成的 JSON 结构
            validateGeneratedJson();
            
            System.out.println("\n=== 所有测试通过 ===");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试转换功能
     */
    private static void testConversion() throws Exception {
        System.out.println("\n1. 测试 VXML 转 IVR JSON 转换...");
        
        String vxmlFile = "src/com/yq/cc/vxml/generated.vxml";
        String outputFile = "src/com/yq/cc/vxml/test_output.json";
        
        // 执行转换
        String jsonResult = VxmlToIvrUtil.convertVxmlToIvrJson(vxmlFile);
        
        // 保存结果
        Files.write(Paths.get(outputFile), jsonResult.getBytes("UTF-8"));
        
        // 验证文件是否生成
        File file = new File(outputFile);
        if (file.exists() && file.length() > 0) {
            System.out.println("✓ 转换成功，输出文件: " + outputFile);
            System.out.println("✓ 文件大小: " + file.length() + " 字节");
        } else {
            throw new Exception("转换失败，输出文件不存在或为空");
        }
    }
    
    /**
     * 验证生成的 JSON 结构
     */
    private static void validateGeneratedJson() throws Exception {
        System.out.println("\n2. 验证生成的 JSON 结构...");
        
        String jsonFile = "src/com/yq/cc/vxml/test_output.json";
        String jsonContent = new String(Files.readAllBytes(Paths.get(jsonFile)), "UTF-8");
        
        // 解析 JSON
        JSONObject ivrJson = JSON.parseObject(jsonContent);
        
        // 验证基本结构
        validateBasicStructure(ivrJson);
        
        // 验证节点列表
        validateNodeList(ivrJson);
        
        // 验证连线列表
        validateLineList(ivrJson);
        
        System.out.println("✓ JSON 结构验证通过");
    }
    
    /**
     * 验证基本结构
     */
    private static void validateBasicStructure(JSONObject ivrJson) throws Exception {
        String[] requiredFields = {"ivrCode", "ivrName", "ivrType", "nodeList", "lineList"};
        
        for (String field : requiredFields) {
            if (!ivrJson.containsKey(field)) {
                throw new Exception("缺少必需字段: " + field);
            }
        }
        
        System.out.println("✓ 基本结构字段完整");
    }
    
    /**
     * 验证节点列表
     */
    private static void validateNodeList(JSONObject ivrJson) throws Exception {
        JSONArray nodeList = ivrJson.getJSONArray("nodeList");
        
        if (nodeList == null || nodeList.size() == 0) {
            throw new Exception("节点列表为空");
        }
        
        // 统计节点类型
        int startNodes = 0, endNodes = 0, userTasks = 0, serviceTasks = 0;
        
        for (int i = 0; i < nodeList.size(); i++) {
            JSONObject node = nodeList.getJSONObject(i);
            String type = node.getString("type");
            
            switch (type) {
                case "start": startNodes++; break;
                case "end": endNodes++; break;
                case "userTask": userTasks++; break;
                case "serviceTask": serviceTasks++; break;
            }
            
            // 验证节点必需字段
            String[] nodeFields = {"id", "name", "type", "color", "ico"};
            for (String field : nodeFields) {
                if (!node.containsKey(field) || node.getString(field).isEmpty()) {
                    throw new Exception("节点 " + node.getString("id") + " 缺少字段: " + field);
                }
            }
        }
        
        System.out.println("✓ 节点统计: 开始(" + startNodes + "), 结束(" + endNodes + 
                          "), 用户任务(" + userTasks + "), 服务任务(" + serviceTasks + ")");
        
        if (startNodes != 1) {
            throw new Exception("应该有且仅有一个开始节点，实际: " + startNodes);
        }
        
        if (endNodes != 1) {
            throw new Exception("应该有且仅有一个结束节点，实际: " + endNodes);
        }
    }
    
    /**
     * 验证连线列表
     */
    private static void validateLineList(JSONObject ivrJson) throws Exception {
        JSONArray lineList = ivrJson.getJSONArray("lineList");
        
        if (lineList == null || lineList.size() == 0) {
            throw new Exception("连线列表为空");
        }
        
        // 统计连线类型
        int conditionalLines = 0, normalLines = 0;
        
        for (int i = 0; i < lineList.size(); i++) {
            JSONObject line = lineList.getJSONObject(i);
            
            // 验证连线必需字段
            String[] lineFields = {"id", "from", "to"};
            for (String field : lineFields) {
                if (!line.containsKey(field) || line.getString(field).isEmpty()) {
                    throw new Exception("连线缺少字段: " + field);
                }
            }
            
            // 判断连线类型
            if (line.containsKey("lx_tj") && line.containsKey("expression")) {
                conditionalLines++;
                
                // 验证条件连线的表达式
                JSONObject expression = line.getJSONObject("expression");
                if (!expression.containsKey("param") || !expression.containsKey("conditions") || 
                    !expression.containsKey("val1")) {
                    throw new Exception("条件连线表达式不完整");
                }
            } else {
                normalLines++;
            }
        }
        
        System.out.println("✓ 连线统计: 普通连线(" + normalLines + "), 条件连线(" + conditionalLines + ")");
    }
}
