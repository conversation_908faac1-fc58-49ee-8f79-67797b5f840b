# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cgiServlet.emptyEnvVarName=初期化パラメータの空の環境変数名[環境変数]
cgiServlet.expandCloseFail=パス[{0}]のスクリプトの入力ストリームを閉じることができませんでした。
cgiServlet.expandCreateDirFail=スクリプトの展開先ディレクトリ[{0}]の作成に失敗しました。
cgiServlet.expandDeleteFail=拡張中にIOExceptionの後に[{0}]でファイルを削除できませんでした
cgiServlet.expandFail=パス[{0}]のスクリプトを[{1}]に展開できませんでした
cgiServlet.expandNotFound=見つけることが出来なかったので[{0}]を展開できませんでした。
cgiServlet.expandOk=パス[{0}]の[{1}]に展開されたスクリプト
cgiServlet.find.found=見つかったCGI：名前[{0}]、パス[{1}]、スクリプト名[{2}]、CGI名[{3}]
cgiServlet.find.location=ファイル [{0}] を探しています。
cgiServlet.find.path=パス[{0}]でCGIロケーション[{1}]に対して相対的に要求されたCGIスクリプト
cgiServlet.invalidArgumentDecoded=デコードされたコマンドライン引数 [{0}] は、構成されたcmdLineArgumentsDecoded パターン [{1}] にマッチしません
cgiServlet.invalidArgumentEncoded=エンコードされたコマンドライン引数 [{0}] は、構成されたcmdLineArgumentsEncoded パターン [{1}] にマッチしません
cgiServlet.runBadHeader=悪いヘッダライン [{0}]
cgiServlet.runFail=CGI処理中のIO問題
cgiServlet.runHeaderReaderFail=ヘッダーリーダーを閉じる時のI / O問題
cgiServlet.runInvalidStatus=不正なステータス [{0}]
cgiServlet.runOutputStreamFail=出力ストリームを切断するとき入出力エラーが発生しました。
cgiServlet.runReaderInterrupt=標準エラー出力の読み取りスレッドは割り込みを待機しています。
cgiServlet.runStdErr=標準エラー 行：[{0}]
cgiServlet.runStdErrCount=stderrの[{0}]行を受信しました。
cgiServlet.runStdErrFail=stderrでI/O問題

defaultServlet.blockExternalEntity=publicId [{0}]およびsystemId [{0}]を持つ外部エンティティへのアクセスがブロックされました
defaultServlet.blockExternalEntity2=外部エンティティへのアクセスを抑止しました。エンティティ名 [{0}]、publicId [{1}]、ベースURI [{2}]、systemId  [{3}]
defaultServlet.blockExternalSubset=名前[{0}]およびベースURI [{1}]を持つ外部サブセットへのアクセスがブロックされました
defaultServlet.missingResource=要求されたリソース [{0}] は利用できません。
defaultServlet.noResources=静的リソースが見つかりません。
defaultServlet.readerCloseFailed=readerのクローズに失敗しました
defaultServlet.skipfail=[{0}]バイトしか利用できなかったため、[{1}]バイトをスキップして要求された範囲の先頭に到達する必要があったため、読み取りに失敗しました。
defaultServlet.xslError=XSL変換エラー

directory.filename=ファイル名
directory.lastModified=最終更新
directory.parent=[{0}] に移動
directory.size=サイズ
directory.title=[{0}] のディレクトリの一覧

webdavservlet.externalEntityIgnored=PublicID [{0}]およびSystemID [{1}]を持つ外部エンティティへの参照を含むリクエストが無視されました
webdavservlet.inputstreamclosefail=入力ストリーム [{0}] を切断できません。
webdavservlet.jaxpfailed=JAXPの初期化に失敗しました
