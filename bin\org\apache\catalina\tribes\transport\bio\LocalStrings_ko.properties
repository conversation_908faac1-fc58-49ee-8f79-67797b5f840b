# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

bioReceiver.already.started=ServerSocket이 이미 시작되었습니다.
bioReceiver.run.fail=복제 리스너를 실행할 수 없습니다.
bioReceiver.socket.closeFailed=소켓을 닫지 못했습니다.
bioReceiver.start.fail=클러스터 Receiver를 시작할 수 없습니다.
bioReceiver.threadpool.fail=쓰레드풀이 초기화될 수 없습니다. 리스너가 시작되지 않았습니다.
bioReceiver.threads.busy=모든 BIO 서버 복제 쓰레드들이 작업 중입니다. 유휴 쓰레드가 하나라도 생기기 전에는, 더 이상 요청을 처리할 수 없습니다.

bioReplicationTask.messageDataReceived.error=messageDataReceived로부터 오류 발생
bioReplicationTask.reader.closeFailed=Reader를 닫지 못했습니다.
bioReplicationTask.socket.closeFailed=소켓을 닫지 못했습니다.
bioReplicationTask.unable.sendAck=채널을 통해 ACK을 되돌려 보낼 수 없습니다. 채널의 연결이 끊겼나요?: [{0}]
bioReplicationTask.unable.service=BIO 소켓을 서비스할 수 없습니다.

bioSender.ack.eof=로컬 포트 [{0}:{1,number,integer}]에서 EOF에 도달했습니다.
bioSender.ack.missing=[{0}:{1,number,integer}](으)로부터 ACK을 {2,number,integer} 밀리초 내에 읽을 수 없습니다. 소켓 연결을 끊고, 다시 시도합니다.
bioSender.ack.wrong=10 바이트들을 로컬 포트 [{0}]에서 읽고 난 후 올바른 ACK를 받지 못했습니다: {1,number,integer}]
bioSender.closeSocket=Sender가 소켓 닫기 메시지를 [{0}:{1,number,integer}]에 전송합니다. (전송 회수:  {2,number,integer})
bioSender.disconnect=Sender가 [{0}:{1,number,integer}](으)로부터 연결을 끊습니다. (연결 끊기 회수: {2,number,integer})
bioSender.fail.AckReceived=실패한 ACK을 받았습니다: org.apache.catalina.tribes.transport.Constants.FAIL_ACK_DATA
bioSender.openSocket=Sender가 [{0}:{1,number,integer}]을(를) 향해 소켓을 엽니다. (연 소켓 개수: {2,number,integer})
bioSender.openSocket.failure=Sender 소켓 [{0}:{1,number,integer}]을(를) 열지 못했습니다! (열기 실패 회수: {2,number,integer})
bioSender.send.again=데이터를 [{0}:{1,number,integer}](으)로 다시 전송합니다.

pooledMultiSender.retrieve.fail=Sender 풀로부터 sender를 검색할 수 없습니다.
pooledMultiSender.unable.retrieve.sender=데이터 sender를 조회할 수 없습니다. 제한 시간 초과 ([{0}] 밀리초) 오류 발생.
