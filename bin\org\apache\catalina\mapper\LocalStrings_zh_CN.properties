# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

mapper.addHost.sameHost=同一主机[{0}]的重复注册。忽略。
mapper.addHost.success=注册主机[{0}]
mapper.addHostAlias.sameHost=为同一主机[{1}]重复注册别名[{0}]。忽略。
mapper.addHostAlias.success=为虚拟主机 [{1}] 注册了别名 [{0}]
mapper.duplicateHost=重复的主机[{0}]。主机[{1}已使用该名称。此主机将被忽略。
mapper.duplicateHostAlias=主机[{1}]中的主机别名[{0}]重复。主机[{2}已使用该名称。将忽略此别名。
mapper.removeWrapper=正在从路径为[{1}]的上下文[{0}]中删除包装。

mapperListener.pauseContext=根据服务需要，注册内容[{0}]已经重新加载
mapperListener.registerContext=为服务[{1}]注册上下文[{0}]
mapperListener.registerHost=这域名[{1}]注册主机[{0}]，服务：[{2}]
mapperListener.registerWrapper=为服务Service[{2}]在上下文Context[{1}]注册Wrapper[{0}]
mapperListener.unknownDefaultHost=服务{1}的默认主机{0}未知。Tomcat将无法处理未指定主机名的HTTP/1.0请求。
mapperListener.unregisterContext=注销服务[{1}]的上下文[{0}]
mapperListener.unregisterHost=在域[{1}]中.,不能注册主机[{0}]为服务[{2}]
mapperListener.unregisterWrapper=在上下文[{1}]中注销服务[{2}]的包装程序[{0}]
