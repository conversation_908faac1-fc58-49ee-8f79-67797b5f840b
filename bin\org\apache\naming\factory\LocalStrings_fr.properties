# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

dataSourceLinkFactory.badWrapper=Pas un enrobeur pour le type [{0}]

factoryBase.factoryClassError=Impossible de charger la classe de la fabrique de ressources
factoryBase.factoryCreationError=Impossible de créer l'instance de la fabrique de ressources
factoryBase.instanceCreationError=Impossible de créer l'instance de la ressource

lookupFactory.circularReference=Trouvé une référence circulaire avec [{0}]
lookupFactory.createFailed=Echec de création de l'instance de la classe de fabrique de recherche JNDI
lookupFactory.loadFailed=Echec de chargement de la classe de fabrique de recherche JNDI
lookupFactory.typeMismatch=La référence JNDI [{0}] devrait être de type [{1}] mais la recherche [{2}] retourne un objet de type [{3}]

resourceFactory.factoryCreationError=Impossible de créer une instance de la fabrique de ressources

resourceLinkFactory.invalidGlobalContext=L'appelant a fourni un contexte global invalide
resourceLinkFactory.nullType=Le lien local de resource [{0}] qui se réfère à la resource globale [{1}] ne spécifie pas le type d''attribut requis
resourceLinkFactory.unknownType=Le lien local de resource [{0}] qui se réfère à la resource globale [{1}] a spécifié le type inconnu [{2}]
resourceLinkFactory.wrongType=Le lien de ressource local [{0}] qui se réfère à la ressource globale [{1}] devait renvoyer une instance de [{2}] mais à renvoyé une instance de [{3}]
