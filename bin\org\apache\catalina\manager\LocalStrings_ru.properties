# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

htmlManagerServlet.appsAvailable=Запущено
htmlManagerServlet.appsExpire=Завершить сеансы
htmlManagerServlet.appsName=Название
htmlManagerServlet.appsPath=Путь
htmlManagerServlet.appsReload=Перезагрузить
htmlManagerServlet.appsSessions=Сеансы
htmlManagerServlet.appsStart=Старт
htmlManagerServlet.appsStop=Стоп
htmlManagerServlet.appsTasks=Команды
htmlManagerServlet.appsTitle=Приложения
htmlManagerServlet.appsUndeploy=Удалить
htmlManagerServlet.appsVersion=Версия
htmlManagerServlet.configReloadButton=Перечитать
htmlManagerServlet.configSslHostName=Имя TLS хоста (не обязательно)
htmlManagerServlet.configSslReloadTitle=Перечитать конфигурационные файлы TLS
htmlManagerServlet.configTitle=Конфигурация
htmlManagerServlet.connectorStateAliveSocketCount=Количество всё ещё живых сокетов:
htmlManagerServlet.connectorStateBytesReceived=Байтов получено:
htmlManagerServlet.connectorStateBytesSent=Байтов отправлено:
htmlManagerServlet.connectorStateErrorCount=Количество ошибок:
htmlManagerServlet.connectorStateHint=P: Разбирается и готовится к обработке, S: Сервис, F: Завершение, R: Готов, K: Продолжает существовать
htmlManagerServlet.connectorStateMaxProcessingTime=Максимальное время обработки запроса:
htmlManagerServlet.connectorStateMaxThreads=Максимум потоков:
htmlManagerServlet.connectorStateProcessingTime=Время обработки:
htmlManagerServlet.connectorStateRequestCount=Количество запросов:
htmlManagerServlet.connectorStateTableTitleBRecv=Б Получено
htmlManagerServlet.connectorStateTableTitleBSent=Б Отправлено
htmlManagerServlet.connectorStateTableTitleClientAct=Клиент (Настоящий)
htmlManagerServlet.connectorStateTableTitleClientForw=Клиент (Переданный)
htmlManagerServlet.connectorStateTableTitleRequest=Запрос
htmlManagerServlet.connectorStateTableTitleStage=Этап
htmlManagerServlet.connectorStateTableTitleTime=Время
htmlManagerServlet.connectorStateTableTitleVHost=ВХост
htmlManagerServlet.connectorStateThreadBusy=Нынешнее число занятых потоков:
htmlManagerServlet.connectorStateThreadCount=Нынешнее число потоков:
htmlManagerServlet.deployButton=Развернуть
htmlManagerServlet.deployConfig=Путь XML файла конфигурации контекста:
htmlManagerServlet.deployPath=Путь:
htmlManagerServlet.deployServer=Развернуть серверный WAR файл
htmlManagerServlet.deployTitle=Развернуть
htmlManagerServlet.deployUpload=WAR файл для развёртывания
htmlManagerServlet.deployUploadFail=ОШИБКА - Ошибка при развёртывании: [{0}]
htmlManagerServlet.deployUploadFile=Выберите WAR файл для загрузки
htmlManagerServlet.deployUploadInServerXml=ОШИБКА - War файл [{0}] не может быть загружен, eсли он уже задан в файле server.xml
htmlManagerServlet.deployUploadNoFile=ОШИБКА - Ошибка при загрузке файла. Файла нет
htmlManagerServlet.deployUploadNotWar=ОШИБКА - Загружаемый файл должен быть с расширением .war
htmlManagerServlet.deployUploadWarExists=ОШИБКА - War файл [{0}] уже существует на сервере
htmlManagerServlet.deployWar=WAR или путь до директории:
htmlManagerServlet.diagnosticsLeak=Проверяет, произошла ли утечка памяти после остановки, перезагрузки или удаления веб-приложений
htmlManagerServlet.diagnosticsLeakButton=Найти утечки памяти
htmlManagerServlet.diagnosticsLeakWarning=Данная диагностика запускает сборку мусора. Будьте осторожны при использовании её на продуктивных системах.
htmlManagerServlet.diagnosticsSsl=Диагностика конфигурации TLS для коннекторов
htmlManagerServlet.diagnosticsSslConnectorCertsButton=Сертификаты безопасности
htmlManagerServlet.diagnosticsSslConnectorCertsText=Список виртуальных TLS хостов и их сертификатов безопасности
htmlManagerServlet.diagnosticsSslConnectorCipherButton=Шифры
htmlManagerServlet.diagnosticsSslConnectorCipherText=Список виртуальных TLS хостов и их шифры
htmlManagerServlet.diagnosticsSslConnectorTrustedCertsButton=Доверенные сертификаты
htmlManagerServlet.diagnosticsSslConnectorTrustedCertsText=Список виртуальных TLS хостов и их доверенных сертификатов безопасности
htmlManagerServlet.diagnosticsTitle=Диагностика
htmlManagerServlet.expire.explain=с неактивностью &ge;
htmlManagerServlet.expire.unit=минут
htmlManagerServlet.findleaksList=Следующие веб-приложения были остановлены (перезагружены, удалены),\n\
но их классы с предыдущих запусков до сих пор присутствуют в памяти\n\
(используйте программу-профайлер, чтобы подтвердить наличие проблемы):\n
htmlManagerServlet.findleaksNone=Кажется, что веб-приложения не вызвали утечку памяти при остановке, перезагрузке или удалении.
htmlManagerServlet.helpHtmlManager=Справка для пользователей приложения
htmlManagerServlet.helpHtmlManagerFile=../docs/html-manager-howto.html
htmlManagerServlet.helpManager=Справка по API приложения
htmlManagerServlet.helpManagerFile=../docs/manager-howto.html
htmlManagerServlet.jvmFreeMemory=Свободная память:
htmlManagerServlet.jvmMaxMemory=Максимум памяти:
htmlManagerServlet.jvmTableTitleInitial=Изначально
htmlManagerServlet.jvmTableTitleMaximum=Максимум
htmlManagerServlet.jvmTableTitleMemoryPool=Области памяти
htmlManagerServlet.jvmTableTitleTotal=Всего
htmlManagerServlet.jvmTableTitleType=Тип
htmlManagerServlet.jvmTableTitleUsed=Используется
htmlManagerServlet.jvmTotalMemory=Вся память:
htmlManagerServlet.list=Список приложений
htmlManagerServlet.manager=Менеджер
htmlManagerServlet.messageLabel=Сообщение:
htmlManagerServlet.noManager=-
htmlManagerServlet.noVersion=Не указано
htmlManagerServlet.osAvailableMemory=Доступная память:
htmlManagerServlet.osFreePageFile=Свободные файловые страницы:
htmlManagerServlet.osKernelTime=Время обработки процесса ядром:
htmlManagerServlet.osMemoryLoad=Памяти загружено:
htmlManagerServlet.osPhysicalMemory=Физическая память:
htmlManagerServlet.osTotalPageFile=Всего файловых страниц:
htmlManagerServlet.osUserTime=Время обработки пользователя:
htmlManagerServlet.serverHostname=Имя хоста
htmlManagerServlet.serverIPAddress=IP Адрес
htmlManagerServlet.serverJVMVendor=Поставщик JVM
htmlManagerServlet.serverJVMVersion=Версия JVM
htmlManagerServlet.serverOSArch=Архитектура ОС
htmlManagerServlet.serverOSName=ОС
htmlManagerServlet.serverOSVersion=Версия ОС
htmlManagerServlet.serverTitle=Информация о сервере
htmlManagerServlet.serverVersion=Версия Tomcat
htmlManagerServlet.title=Управление веб-приложениями Tomcat

managerServlet.alreadyContext=ОШИБКА - Приложение уже существует на пути [{0}]
managerServlet.certsNotAvailable=Информация о сертификате не может быть получена во время работы
managerServlet.deleteFail=ОШИБКА - Не удалось удалить [{0}].
managerServlet.deployFailed=ОШИБКА - Не удалось развернуть приложение на контекстном пути [{0}]
managerServlet.deployed=OK - Приложение успешно развёрнуто в контекстном пути [{0}]
managerServlet.deployedButNotStarted=ОШИБКА - Приложение было развёрнуто в контекстном пути [{0}], но не стартовало
managerServlet.exception=ОШИБКА - Встретилось исключение [{0}]
managerServlet.findleaksFail=ОШИБКА - Не удалось найти утечки памяти: Host not instance of StandardHost
managerServlet.findleaksList=OK - Найдены потенциальные утечки памяти в следующих приложениях:
managerServlet.findleaksNone=OK - Не найдено утечек памяти
managerServlet.inService=ОШИБКА -  Приложение [{0}] уже обслуживается
managerServlet.invalidCommand=ОШИБКА - Недопустимые параметры, предоставленные для команды [{0}]
managerServlet.invalidPath=ОШИБКА - Указан недопустимый контекстный путь [{0}]
managerServlet.listed=OK - Список приложений для виртуального хоста [{0}]
managerServlet.mkdirFail=ОШИБКА - Не удалось создать директорию [{0}]
managerServlet.noCommand=ОШИБКА - Команда не указана.
managerServlet.noContext=ОШИБКА - Контекст не существует [{0}]
managerServlet.noGlobal=ОШИБКА - Глобальные ресурсы JNDI недоступны
managerServlet.noManager=ОШИБКА - Отсутствует менеджер для пути [{0}]
managerServlet.noSelf=ОШИБКА - Менеджер не может перезагрузить, развернуть, остановить или удалить себя
managerServlet.noWrapper=Контейнер не вызвал setWrapper() для этого сервлета
managerServlet.notDeployed=ОШИБКА - [{0}] определен в файле server.xml и не может быть развёрнут
managerServlet.notSslConnector=Протокол SSL/TLS для этого коннектора не включен
managerServlet.objectNameFail=ОШИБКА - Не удалось зарегистрировать имя объекта [{0}] для Manager Servlet
managerServlet.postCommand=ОШИБКА - Попытка использовать команду [{0}] через запрос GET но требуется POST
managerServlet.reloaded=OK - Приложение по пути контекста [{0}] было перезагружено
managerServlet.renameFail=ОШИБКА - Невозможно переименовать [{0}] в [{1}]. Это может вызвать проблемы для будущих развертываний.
managerServlet.resourcesAll=OK - Перечислены глобальные ресурсы всех видов
managerServlet.resourcesType=OK - Перечислены глобальные ресурсы вида [{0}]
managerServlet.saveFail=ОШИБКА - Не удалось сохранить настройки: [{0}]
managerServlet.saved=OK - Конфигурация сервера сохранена
managerServlet.savedContext=OK - Контекстные настройки для [{0}] сохранены
managerServlet.sessiondefaultmax=Стандартный максимальный период неактивного сеанса: [{0}] минут
managerServlet.sessions=OK - Информация о сеансах приложения по пути контекста [{0}]
managerServlet.sessiontimeout=Неактивные [{0}] минут: [{1}] сеанс(ов)
managerServlet.sessiontimeout.expired=Неактивные [{0}] минут: [{1}] сеанс(ов) были завершены
managerServlet.sessiontimeout.unlimited=Неограниченное время: [{0}] сеансов
managerServlet.sslConnectorCerts=OK - Информация о цепочке сертификатов
managerServlet.sslConnectorCiphers=OK - Информация о шифровании SSL
managerServlet.sslConnectorTrustedCerts=OK - Информация о доверенном сертификате безопасности
managerServlet.sslReload=OK - Перезагрузка конфигурации TLS для [{0}]
managerServlet.sslReloadAll=OK - Перезагрузка конфигурации TLS для всех виртуальных хостов TLS
managerServlet.sslReloadFail=ОШИБКА - Не удалось перезагрузить конфигурацию TLS
managerServlet.startFailed=ОШИБКА - Приложение по контекстному пути [{0}] не запустилось
managerServlet.started=OK - Запущено приложение по пути контекста [{0}]
managerServlet.stopped=OK - Остановлено приложение по пути контекста [{0}]
managerServlet.storeConfig.noMBean=ОШИБКА - Нет StoreConfig MBean, зарегистрированный на [{0}]
managerServlet.threaddump=OK - JVM thread dump
managerServlet.trustedCertsNotConfigured=Для этого виртуального хоста не настроены доверенные сертификаты безопасности
managerServlet.undeployed=OK - Удалено приложение по пути контекста [{0}]
managerServlet.unknownCommand=ОШИБКА - Неизвестная команда [{0}]
managerServlet.vminfo=OK - VM инфо

statusServlet.complete=Подробный отчёт о состоянии
statusServlet.title=Состояние сервера
