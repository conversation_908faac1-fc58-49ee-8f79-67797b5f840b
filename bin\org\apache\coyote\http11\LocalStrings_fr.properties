# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractHttp11Protocol.alpnConfigured=Le connecteur [{0}] a été configuré pour supporter la négociation de [{1}] avec ALPN
abstractHttp11Protocol.alpnWithNoAlpn=Le gestionnaire de mise à niveau [{0}] pour [{1}] ne supporte qu''une mise à niveau via ALPN mais il a été configuré pour le connecteur [{2}] qui ne supporte pas ALPN
abstractHttp11Protocol.httpUpgradeConfigured=Le connecteur [{0}] a été configuré pour supporter la mise à niveau de HTTP vers [{1}]
abstractHttp11Protocol.upgradeJmxNameFail=Impossible de créer l'ObjectName pour l'enregistrement JMX du protocole d'upgrade
abstractHttp11Protocol.upgradeJmxRegistrationFail=Impossible d'enregistrer le protocole d'upgrade dans JMX

http11processor.fallToDebug=\n\
\ Note : les occurrences suivantes d'erreurs d'analyse de requête HTTP seront enregistrées au niveau DEBUG.
http11processor.header.parse=Erreur lors de l'analyse d'un en-tête de requête HTTP
http11processor.request.finish=Erreur en terminant la requête
http11processor.request.inconsistentHosts=L'hôte spécifié dans la ligne de requête ne correspond pas à celui de l'en-tête hôte
http11processor.request.invalidScheme=La requête HTTP contenait une URi absolue avec un schéma invalide
http11processor.request.invalidTransferEncoding=La requête HTTP contenait un en-tête Trasfer-Encoding invalide
http11processor.request.invalidUri=La requête HTTP contenait un URI non valide
http11processor.request.invalidUserInfo=La requête HTTP contenait un URI absolu avec un composant "userinfo" invalide
http11processor.request.multipleContentLength=La requête contenait plusieurs en-têtes content-length
http11processor.request.multipleHosts=La requête contenait plusieurs en-têtes hôtes
http11processor.request.noHostHeader=La requ6ete HTTP/1.1 ne contient pas d'en-tête host
http11processor.request.nonNumericContentLength=La requête contenait un en-tête content-length avec une valeur non numérique
http11processor.request.prepare=Echec de préparation de la requête
http11processor.request.process=Erreur de traitement de la requête
http11processor.response.finish=Erreur en finissant la réponse
http11processor.sendfile.error=Erreur d'envoi des données avec sendfile, cela peut être causé par des attributs de démarrage ou de fin incorrects dans la requête
http11processor.socket.info=Exception pendant la requête d'information sur le socket.

iib.available.readFail=Une lecture non-bloquante a échoué lors de la détermination préalable de données disponibles
iib.eof.error=Fin de flux (EOF) inattendue à la lecture sur la socket
iib.failedread.apr=Echec de lecteur avec le code d''erreur APR [{0}]
iib.filter.npe=Impossible d'ajouter un filtre null
iib.invalidHttpProtocol=Un caractère invalide a été trouvé dans le protocole HTTP
iib.invalidPhase=Etape invalide de traitement [{0}] de la ligne de requête
iib.invalidRequestTarget=Un caractère invalide a été trouvé dans la cible de la requête, les caractères valides sont définis dans RFC 7230 et RFC 3986
iib.invalidheader=La ligne d''en-être HTTP [{0}] n''est pas conforme à la RFC 7230 et a été ignorée
iib.invalidmethod=Caractère invalide trouvé dans le nom de méthode.  Les noms HTTP doivent être des "token".
iib.parseheaders.ise.error=Etat inattendu, les en-êtres ont déjà été traités, il est possible que le buffer n'ait pas été recyclé
iib.readtimeout=Délai d'attente dépassé en lisant des données du socket
iib.requestheadertoolarge.error=L'entête de requête est trop important

iob.failedwrite=Echec d'écriture
iob.failedwrite.ack=Echec d'envoi de la réponse HTTP 100 Continue
iob.responseheadertoolarge.error=Essai d'écriture de plus de données dans les en-t^tes de réponse, qu'il n'y a de place disponible dans le tampon.  Augmentez maxHttpHeaderSize pour le connecteur, ou écrivez moins de données dans les en-têtes de réponse.
