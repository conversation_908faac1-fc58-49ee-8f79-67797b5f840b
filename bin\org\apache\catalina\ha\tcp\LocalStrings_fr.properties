# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

ReplicationValve.crossContext.add=Ajout du conteneur de réplication de la session multi contexte au ThreadLocal de replicationValve
ReplicationValve.crossContext.registerSession=enregistrement de la session multi contexte id=[{0}] du contexte [{1}]
ReplicationValve.crossContext.remove=Retrait du conteneur de réplication de la session multi contexte au ThreadLocal de replicationValve
ReplicationValve.crossContext.sendDelta=Envoi du delta de la session multi contexte du contexte [{0}]
ReplicationValve.filter.failure=Incapacité de compiler le filtre=[{0}]
ReplicationValve.filter.loading=Chargement du filtre de requête [{0}]
ReplicationValve.invoke.uri=Invocation de la requête de réplication sur [{0}]
ReplicationValve.nocluster=Aucun cluster de configuré pour cette requête
ReplicationValve.resetDeltaRequest=Le cluster se suffit à lui-même : réinitialisation du delta de la requête de session [{0}]
ReplicationValve.send.failure=Impossible d'effectuer la requête de réplication
ReplicationValve.send.invalid.failure=Incapable d''envoyer le message invalide de la session [id={0}] sur le cluster
ReplicationValve.session.found=Le Contexte [{0}] a touvé la session [{1}] mais ce n''est pas une ClusterSession.
ReplicationValve.session.indicator=Contexte [{0}] : la primarité de la session [{1}] dans l''attribut de requête [{2}] est [{3}].
ReplicationValve.session.invalid=Contexte [{0}] : la session demandée [{1}] est invalide, non répliquée, ou enlevée sur ce nœud
ReplicationValve.stats=Temps de requête moyen= [{0}] ms pour le Cluster le temps ajouté est de=[{1}] ms pour [{2}] requêtes [{3}] requêtes d''envoi [{4}] requêtes multi contextes et [{5}] requêtes fitrées (Total requêtes=[{6}] ms total requêtes du cluster=[{7}] ms)

simpleTcpCluster.clustermanager.cloneFailed=Impossible de cloner le gestionnaire du cluster, le org.apache.catalina.ha.session.DeltaManager par défaut sera utilisé
simpleTcpCluster.clustermanager.notImplement=Le gestionnaire ("Manager") [{0}] n''implémente pas ClusterManager. Son ajout au cluster a été abandonné.
simpleTcpCluster.member.addFailed=Impossible de se connecter au système de réplication
simpleTcpCluster.member.added=Membre de réplication ajouté : [{0}]
simpleTcpCluster.member.disappeared=Le membre recu a disparu : [{0}]
simpleTcpCluster.member.removeFailed=Impossible d'enlever un nœud du cluster du système de réplication
simpleTcpCluster.sendFailed=Impossible d'envoyer un message à travers l'expéditeur du cluster
simpleTcpCluster.start=Le cluster va démarrer
simpleTcpCluster.startUnable=Impossible de démarre le cluster
simpleTcpCluster.stopUnable=Incapable d'arrêter le cluster
simpleTcpCluster.unableSend.localMember=Impossible d''envoyer un message au membre local [{0}]
