# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jmxRegistry.no.domain=JMX ドメインが未指定です。
jmxRegistry.objectName.failed=不正なオブジェクト名 [{0}] を要求されました。
jmxRegistry.registerJmx.failed=オブジェクト [{0}] を名前 [{1}] で登録できませんでした。
jmxRegistry.registerJmx.notCompliant=要求されたオブジェクト[{0}]はJMX仕様に準拠していません。
jmxRegistry.unregisterJmx.failed=名前[{0}]のMBeanの登録を解除できませんでした。
jmxRegistry.unregisterJmx.notFound=要求されたObjectName [{0}]はMBeanServerに登録されていません。
