<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE mbeans-descriptors PUBLIC
   "-//Apache Software Foundation//DTD Model MBeans Configuration File"
   "http://jakarta.apache.org/commons/dtds/mbeans-descriptors.dtd">
<mbeans-descriptors>

    <mbean name="RequestGroupInfo"
           description="Runtime information of a group of requests"
           domain="Catalina"
           group="Connector"
           type="org.apache.coyote.RequestGroupInfo">

        <attribute name="maxTime"
                   description="Maximum time to process a request"
                   type="long"
                   writeable="false"/>

        <attribute name="processingTime"
                   description="Total time to process the requests"
                   type="long"
                   writeable="false"/>

        <attribute name="requestCount"
                   description="Number of requests processed"
                   type="int"
                   writeable="false"/>

        <attribute name="errorCount"
                   description="Number of errors"
                   type="int"
                   writeable="false"/>

        <attribute name="bytesReceived"
                   description="Amount of data received, in bytes"
                   type="long"
                   writeable="false"/>

        <attribute name="bytesSent"
                   description="Amount of data sent, in bytes"
                   type="long"
                   writeable="false"/>

        <operation name="resetCounters" description="Reset counters" impact="ACTION" returnType="void"/>

    </mbean>

    <mbean name="UpgradeGroupInfo"
           description="Runtime information of a group of connections upgraded via the HTTP upgrade process"
           domain="Catalina"
           group="Connector"
           type="org.apache.coyote.http11.upgrade.UpgradeGroupInfo">

        <attribute name="bytesReceived"
                   description="Amount of data received, in bytes"
                   type="long"
                   writeable="false"/>

        <attribute name="bytesSent"
                   description="Amount of data sent, in bytes"
                   type="long"
                   writeable="false"/>

        <attribute name="msgsReceived"
                   description="Number of messages received where applicable for the given protocol"
                   type="long"
                   writeable="false"/>

        <attribute name="msgsSent"
                   description="Number of messages sent where applicable for the given protocol"
                   type="long"
                   writeable="false"/>

        <operation name="resetCounters" description="Reset counters" impact="ACTION" returnType="void"/>

    </mbean>
</mbeans-descriptors>