# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

SecurityListener.checkUmaskFail=尝试使用umask设置[{0}]启动。生命周期侦听器org.apache.catalina.security.security listener（通常在catalina-BASE/conf/server.xml中配置）已阻止在没有umask的情况下运行Tomcat，其限制至少与[{1}]相同
SecurityListener.checkUmaskNone=在系统属性[{0}]中找不到umask设置。但是，看来Tomcat在支持umask的平台上运行。系统属性通常在CATALINA_HOME / bin / catalina.sh中设置。生命周期侦听器org.apache.catalina.security.SecurityListener（通常在CATALINA_BASE / conf / server.xml中配置）期望umask的限制至少与[{1}]相同
SecurityListener.checkUmaskParseFail=无法将值[{0}]分析为有效的umask。
SecurityListener.checkUmaskSkip=无法确定权限。这表示 Tomcat 正在 Windows 上运行，所以跳过权限检查。
SecurityListener.checkUserWarning=以用户[{0}]身份运行时尝试启动。作为此用户运行Tomcat已被生命周期侦听器org.apache.catalina.security.security listener（通常在catalina_BASE/conf/server.xml中配置）阻止

SecurityUtil.doAsPrivilege=运行privilegedexception块时发生异常。
