# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

coyoteAdapter.accesslogFail=尝试向访问日志添加条目时发生异常。
coyoteAdapter.asyncDispatch=处理异步请求时发生异常
coyoteAdapter.authenticate=连接器提供的经过身份验证的用户[{0}]。
coyoteAdapter.authorize=(:使用Tomcat的领域授权用户[{0}]
coyoteAdapter.checkRecycled.request=遇到未回收的请求并强制回收。
coyoteAdapter.checkRecycled.response=遇到非回收的相应并强行回收。
coyoteAdapter.debug=变量[{0}]的值为[{1}]。
coyoteAdapter.nullRequest=异步分派只能在现有请求上发生

coyoteConnector.invalidEncoding=编码 [{0}] 不能被 JRE 识别，Connector 将继续使用 [{1}]
coyoteConnector.invalidPort=连接器不能启动，因为指定的端口 [{0}]无效
coyoteConnector.notAsciiSuperset=编码[{0}]不是RFC 7230要求的ASCII超集。连接器将继续使用[{1}]
coyoteConnector.parseBodyMethodNoTrace=方法TRACE禁止包含实体（详情查看RFC 2616 章节 9.6）
coyoteConnector.protocolHandlerDestroyFailed=协议处理程序销毁失败
coyoteConnector.protocolHandlerInitializationFailed=协议处理程序初始化失败
coyoteConnector.protocolHandlerInstantiationFailed=协议处理程序实例化失败
coyoteConnector.protocolHandlerNoAprLibrary=配置的协议[{0}]需要不可用的APR/本机库
coyoteConnector.protocolHandlerNoAprListener=配置的协议[{0}]需要不可用的aprlifecycleListener
coyoteConnector.protocolHandlerPauseFailed=协议处理程序暂停失败
coyoteConnector.protocolHandlerResumeFailed=协议处理程序恢复失败
coyoteConnector.protocolHandlerStartFailed=协议处理器启动失败
coyoteConnector.protocolHandlerStopFailed=协议处理程序.停止失败

coyoteInputStream.nbNotready=在非阻塞模式下，只有之前的读数据完成，并且isReady()方法返回true，你才可以使用 ServletInputStream 读取数据

coyoteOutputStream.nbNotready=在非阻塞模式下，在上一次写入完成且isReady（）返回true之前，您不能写入ServletOutputStream

coyoteRequest.alreadyAuthenticated=此请求已通过身份验证
coyoteRequest.attributeEvent=属性事件侦听器引发的异常
coyoteRequest.authenticate.ise=):提交响应后无法调用authenticate()
coyoteRequest.changeSessionId=无法更改 session ID。 没有与此请求关联的 session。
coyoteRequest.chunkedPostTooLarge=由于请求参数数据太大，导致参数不能解析。因为当前请求是块状请求，后续也不会处理。如果应用程序需要接收大的POST请求，可以使用连接器的maxPostSize解决它。
coyoteRequest.filterAsyncSupportUnknown=无法确定是否有任何过滤器不支持异步处理
coyoteRequest.getContextPath.ise=找不到规范上下文路径[{0}]与用户代理[{1}]提供的URI之间的匹配项。
coyoteRequest.getInputStream.ise=已为此请求调用getReader（）
coyoteRequest.getReader.ise=当前请求已经调用过方法getInputStream()
coyoteRequest.gssLifetimeFail=为用户主体 [{0}] 获取剩余生命期失败
coyoteRequest.maxPostSizeExceeded=):大多部分请求包含的参数数据（不包括上载的文件）超过了关联连接器上设置的maxPostSize 的限制
coyoteRequest.noAsync=无法启动async，因为处理链中的下列类不支持async[{0}]
coyoteRequest.noMultipartConfig=由于没有提供multi-part配置，无法处理parts
coyoteRequest.parseParameters=处理发布的参数时引发异常
coyoteRequest.postTooLarge=未分析参数，因为发布的数据太大。如果应用程序应接受大型post，请使用连接器的maxPostSize属性来解决此问题。
coyoteRequest.sendfileNotCanonical=无法确定指定用于sendfile的文件[{0}]的规范名称
coyoteRequest.sessionCreateCommitted=提交响应后无法创建会话
coyoteRequest.sessionEndAccessFail=在回收请求时，异常触发了对会话的结束访问。
coyoteRequest.setAttribute.namenull=不能在一个空的名字上调用setAttribute
coyoteRequest.uploadCreate=根据servlet[{1}]的要求创建临时上载位置[{0}]
coyoteRequest.uploadCreateFail=无法创建上载位置[{0}]
coyoteRequest.uploadLocationInvalid=临时上传路径[{0}]无效

coyoteResponse.encoding.invalid=JRE无法识别编码[{0}]
coyoteResponse.getOutputStream.ise=已为此响应调用getWriter（）
coyoteResponse.getWriter.ise=当前响应已经调用了方法getOutputStream()
coyoteResponse.reset.ise=已经提交响应后无法调用reset()
coyoteResponse.resetBuffer.ise=提交响应后无法重置缓冲区
coyoteResponse.sendError.ise=响应提交后无法调用sendError()
coyoteResponse.sendRedirect.ise=提交响应后无法调用sendRedirect（）。
coyoteResponse.sendRedirect.note=<html><body><p>重定向到<a href="{0}">{0}</a></p></body></html>
coyoteResponse.setBufferSize.ise=写入数据后无法更改缓冲区大小

inputBuffer.requiresNonBlocking=非阻塞模式下不可用
inputBuffer.streamClosed=关闭的流。

outputBuffer.writeNull=要写入的字符串参数（String，int，int）不能为空

request.asyncNotSupported=当前链的筛选器或servlet不支持异步操作。
request.fragmentInDispatchPath=调度路径[{0}]中的片段已被删除
request.illegalWrap=请求包装器必须包装从getRequest（）获得的请求
request.notAsync=如果当前请求不在异步模式下，则调用此方法是非法的（即isAsyncStarted（）返回false）
request.session.failed=由于[{1}]，加载会话[{0}]失败

requestFacade.nullRequest=请求对象已被回收，不再与此facade关联

response.illegalWrap=响应包装器必须包装从getResponse（）获得的响应
response.sendRedirectFail=重定向到[{0}]失败

responseFacade.nullResponse=响应对象已被回收，不再与此外观关联
