# IVR 到 VXML 转换工具 - 使用说明

## 项目概述

本项目提供了一个完整的 IVR（交互式语音应答）呼叫流程到 VXML（Voice XML）格式的转换工具。该工具能够将 JSON 格式的 IVR 配置自动转换为符合 VXML 2.1 标准的语音应用程序。

## 文件结构

```
src/com/yq/cc/vxml/
├── IvrToVxmlUtil.java          # 主要转换工具类
├── IvrToVxmlExample.java       # 使用示例类
├── IvrToVxmlUtilTest.java      # 单元测试类
├── ivr.json                    # 示例 IVR 配置文件
├── ivr.vxml                    # 参考 VXML 格式文件
├── generated.vxml              # 工具生成的 VXML 文件
├── example1_output.vxml        # 示例1输出文件
├── example2_output.vxml        # 示例2输出文件
├── example3_batch_1.vxml       # 批量处理输出文件1
├── example3_batch_2.vxml       # 批量处理输出文件2
├── README.md                   # 详细技术文档
└── 工具使用说明.md             # 本文件
```

## 快速开始

### 1. 编译项目

```bash
# 编译主工具类
javac -encoding UTF-8 -cp "lib/json-20160810.jar;E:/work/server/mars/lib/fastjson-1.2.62.jar" -d bin src/com/yq/cc/vxml/IvrToVxmlUtil.java

# 编译示例类
javac -encoding UTF-8 -cp "bin;lib/json-20160810.jar;E:/work/server/mars/lib/fastjson-1.2.62.jar" -d bin src/com/yq/cc/vxml/IvrToVxmlExample.java

# 编译测试类
javac -encoding UTF-8 -cp "bin;lib/json-20160810.jar;E:/work/server/mars/lib/fastjson-1.2.62.jar" -d bin src/com/yq/cc/vxml/IvrToVxmlUtilTest.java
```

### 2. 运行示例

```bash
# 运行主工具（转换 ivr.json）
java -cp "bin;lib/json-20160810.jar;E:/work/server/mars/lib/fastjson-1.2.62.jar" com.yq.cc.vxml.IvrToVxmlUtil

# 运行使用示例
java -cp "bin;lib/json-20160810.jar;E:/work/server/mars/lib/fastjson-1.2.62.jar" com.yq.cc.vxml.IvrToVxmlExample

# 运行单元测试
java -cp "bin;lib/json-20160810.jar;E:/work/server/mars/lib/fastjson-1.2.62.jar" com.yq.cc.vxml.IvrToVxmlUtilTest
```

## 核心功能

### 1. 基本转换功能
- 从 JSON 文件读取 IVR 配置
- 解析节点和连线关系
- 生成标准 VXML 格式输出
- 支持文件保存

### 2. 节点类型支持
- **开始节点 (start)**: 流程入口点
- **用户任务节点 (userTask)**: 语音播放
- **服务任务节点 (serviceTask)**: 用户输入处理
- **结束节点 (end)**: 流程结束点

### 3. 高级特性
- 条件分支逻辑处理
- XML 特殊字符自动转义
- 语法规则自动生成
- 错误处理机制
- 循环引用检测

## 使用方法

### 方法1：直接调用工具类

```java
// 从文件转换
String vxml = IvrToVxmlUtil.convertFromFile("path/to/ivr.json");

// 从 JSON 字符串转换
String jsonContent = "{ ... }";
String vxml = IvrToVxmlUtil.convertJsonToVxml(jsonContent);
```

### 方法2：使用示例类

```java
// 运行所有示例
IvrToVxmlExample.main(new String[]{});

// 或单独运行某个示例
IvrToVxmlExample.example1_ConvertExistingFile();
```

## 配置格式说明

### JSON 输入格式

```json
{
  "ivrCode": "流程代码",
  "ivrName": "流程名称",
  "nodeList": [
    {
      "id": "节点ID",
      "name": "节点名称", 
      "type": "节点类型",
      "voicePlayContent": "语音内容"
    }
  ],
  "lineList": [
    {
      "id": "连线ID",
      "from": "起始节点ID",
      "to": "目标节点ID",
      "expression": {
        "param": "参数名",
        "conditions": "条件类型",
        "val1": "比较值"
      }
    }
  ]
}
```

### VXML 输出格式

生成的 VXML 包含：
- XML 声明和命名空间
- 主表单结构
- 语音播放块
- 用户输入字段
- 条件分支逻辑
- 错误处理机制
- 结束块

## 测试验证

工具包含完整的单元测试，覆盖以下场景：
- ✅ 基本转换功能
- ✅ 条件分支处理
- ✅ XML 转义处理
- ✅ 空配置处理
- ✅ 错误处理机制

运行测试命令：
```bash
java -cp "bin;lib/json-20160810.jar;E:/work/server/mars/lib/fastjson-1.2.62.jar" com.yq.cc.vxml.IvrToVxmlUtilTest
```

## 实际应用示例

### 示例1：客服热线 IVR
- 播放欢迎语
- 等待用户按键选择
- 根据选择播放不同响应
- 最终挂断或转人工

### 示例2：银行服务 IVR
- 主菜单选择
- 余额查询功能
- 转账服务功能
- 多级菜单支持

### 示例3：批量处理
- 支持多个 IVR 配置同时转换
- 自动生成不同的输出文件
- 适合大规模部署场景

## 技术特点

1. **高度可配置**: 支持复杂的 IVR 流程配置
2. **标准兼容**: 生成符合 VXML 2.1 标准的代码
3. **错误处理**: 完善的异常处理和错误提示
4. **扩展性强**: 易于添加新的节点类型和功能
5. **测试完备**: 包含全面的单元测试

## 注意事项

1. **编码问题**: 确保 JSON 文件使用 UTF-8 编码
2. **依赖库**: 需要 FastJSON 或 org.json 库支持
3. **路径配置**: 注意文件路径的正确配置
4. **VXML 兼容性**: 生成的 VXML 需要在支持的语音平台上测试

## 扩展建议

1. **添加新节点类型**: 如转接节点、录音节点等
2. **支持更多条件**: 如时间条件、用户属性条件等
3. **增强错误处理**: 更详细的错误信息和恢复机制
4. **性能优化**: 对大型 IVR 配置的处理优化
5. **可视化支持**: 添加流程图生成功能

## 技术支持

如有问题或建议，请参考：
- `README.md` - 详细技术文档
- `IvrToVxmlUtilTest.java` - 测试用例参考
- `IvrToVxmlExample.java` - 使用示例参考

---

**版本**: 1.0  
**更新日期**: 2025-07-03  
**兼容性**: Java 8+, VXML 2.1
