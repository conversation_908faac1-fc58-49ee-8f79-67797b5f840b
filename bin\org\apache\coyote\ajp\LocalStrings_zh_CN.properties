# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

ajpMessage.invalidPos=请求读取的字节位于位置[{0}]，该位置超出了AJP消息的结尾

ajpmessage.invalid=收到无效的带有签名[{0}]的消息
ajpmessage.invalidLength=接收到长度为[{0}]的无效消息。
ajpmessage.null=不能赋空值
ajpmessage.overflow=在缓冲区[{1}]位置添加[{0}]字节时发生溢出错误

ajpprocessor.certs.fail=):证书转换失败
ajpprocessor.header.error=头部信息解析失败
ajpprocessor.header.tooLong=已收到长度为[{0}]的头消息，但packetSize仅为[{1}]
ajpprocessor.readtimeout=从Socket读取数据超时
ajpprocessor.request.prepare=准备请求错误
ajpprocessor.request.process=处理请求错误
ajpprocessor.unknownAttribute=由于请求属性[{0}]接收自反向代理，请求被拒绝

ajpprotocol.noSSL=AJP不支持SSL。[{0}]的SSL主机配置被忽略
ajpprotocol.noSecret=AJP连接器配置secretRequired="true",但是属性secret确实空或者空字符串，这样的组合是无效的。
ajpprotocol.noUpgrade=AJP 不支持升级。[{0}] 的升级协议配置被忽略。
ajpprotocol.noUpgradeHandler=AJP不支持升级。 HttpUpgradeHandler [{0}]无法处理
