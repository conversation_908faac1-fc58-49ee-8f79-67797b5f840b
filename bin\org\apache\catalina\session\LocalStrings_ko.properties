# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

JDBCStore.SQLException=SQL 오류 [{0}]
JDBCStore.checkConnectionClassNotFoundException=JDBC 드라이버 클래스를 찾을 수 없습니다: [{0}]
JDBCStore.checkConnectionDBClosed=데이터베이스 연결이 널이거나 닫힌 상태입니다. 다시 열려고 시도합니다.
JDBCStore.checkConnectionDBReOpenFail=데이터베이스에 대해 다시 연결을 맺지 못했습니다. 데이터베이스가 다운되었을 수 있습니다.
JDBCStore.checkConnectionSQLException=SQL 예외 발생 [{0}]
JDBCStore.close=데이터베이스 연결 [{0}]을(를) 닫는 동안 예외 발생
JDBCStore.commitSQLException=데이터베이스 연결을 닫기 전, 커밋을 시도하는 중 SQLException 발생
JDBCStore.connectError=데이터베이스 [{0}]에 연결할 수 없습니다.
JDBCStore.loading=데이터베이스 [{1}](으)로부터 세션 [{0}]을(를) 로드합니다.
JDBCStore.missingDataSourceName=유효한 JNDI 이름이 주어지지 않았습니다.
JDBCStore.removing=데이터베이스 [{1}]에서 세션 [{0}]을(를) 제거합니다.
JDBCStore.saving=세션 [{0}]을(를) 데이터베이스 [{1}]에 저장합니다.
JDBCStore.wrongDataSource=JNDI DataSource [{0}]을(를) 열 수 없습니다.

fileStore.createFailed=세션 데이터 저장소를 위한 디렉토리[{0}]을(를) 생성할 수 없습니다.
fileStore.deleteFailed=파일 [{0}]을(를) 삭제할 수 없습니다. 이는 세션 저장소 위치의 생성을 방해하고 있습니다.
fileStore.deleteSessionFailed=더 이상 필요하지 않은 파일 [{0}]을(를) 삭제할 수 없습니다.
fileStore.invalid=세션 ID [{1}]을(를) 위한 세션 저장소 파일 [{0}]이(가) 유효하지 않습니다.
fileStore.loading=파일 [{1}](으)로부터 세션 [{0}]을(를) 로드합니다.
fileStore.removing=파일 [{1}]에 저장된 세션 [{0}]을(를) 제거합니다.
fileStore.saving=세션 [{0}]을(를) 파일 [{1}]에 저장합니다.

managerBase.container.noop=컨텍스트들이 아닌, 컨테이너들에 추가된 매니저들은 전혀 사용되지 않을 것입니다.
managerBase.contextNull=매니저가 사용되기 전에, 컨텍스트가 반드시 널이 아닌 값으로 설정되어야 합니다.
managerBase.createSession.ise=createSession: 활성화된 세션이 너무 많습니다.
managerBase.sessionAttributeNameFilter=이름 필터 [{1}]와(과) 부합되지 않기 때문에, [{0}](이)라는 이름의 세션 속성을 건너뛰었습니다.
managerBase.sessionAttributeValueClassNameFilter=값의 타입 [{1}]이(가) 필터 [{2}]와(과) 부합하지 않기 때문에, [{0}](이)라는 이름의 세션 속성을 건너뛰었습니다.
managerBase.sessionNotFound=세션 [{0}]을(를) 찾을 수 없었습니다.
managerBase.sessionTimeout=유효하지 않은, 세션 제한 시간 초과 설정입니다: [{0}]
managerBase.setContextNotNew=만일 매니저가 NEW 상태에 있지 않다면, 매니저와 연관된 컨텍스트를 변경하기 위해 setContext()를 호출하는 것은 불허됩니다.

persistentManager.backupMaxIdle=세션 [{0}]을(를) 세션 저장소에 백업합니다. [{1}]초 동안 유휴 상태였습니다.
persistentManager.deserializeError=세션 [{0}]을(를) 역직렬화하는 중 오류 발생
persistentManager.isLoadedError=세션 [{0}]이(가) 메모리에 로드되었는지 점검 중 오류 발생
persistentManager.loading=[{0}]개의 저장된 세션들을 로드합니다.
persistentManager.removeError=세션 [{0}]을(를) 저장소로부터 제거하는 중 오류 발생
persistentManager.serializeError=세션을 직렬화하는 중 오류 발생 [{0}]: [{1}]
persistentManager.storeClearError=저장소로부터 모든 세션들을 해제하는 중 오류 발생
persistentManager.storeKeysException=세션 저장소에 있는 세션들의 ID 목록을 결정할 수 없습니다. 아마도 세션 저장소가 비어 있는 것 같습니다.
persistentManager.storeLoadError=저장소로부터 세션들을 메모리로 로드하는 중 오류 발생
persistentManager.storeLoadKeysError=저장소로부터 세션 키들을 로드하는 중 오류 발생
persistentManager.storeSizeException=세션 저장소에 얼마나 많은 세션이 존재하는지 알아낼 수 없습니다. 아마도 세션 저장소가 비어 있는 것 같습니다.
persistentManager.swapIn=저장소로부터 세션 [{0}]을(를) 다시 로드하여 활성화시킵니다.
persistentManager.swapInException=저장소에 저장된 세션을 메모리로 로드하는 중, 저장소에서 예외 발생: [{0}]
persistentManager.swapInInvalid=세션 저장소로부터 로드된 세션 [{0}]은(는) 유효하지 않습니다.
persistentManager.swapMaxIdle=[{1}]초 동안 유휴 상태에 있던, 세션 [{0}]을(를) 저장소로 옮깁니다.
persistentManager.swapTooManyActive=[{1}]초 동안 비활성화 상태에 있던 세션 [{0}]을(를) 매니저로부터 저장소로 이동합니다. 너무 많은 세션들이 활성화되어 있습니다.
persistentManager.tooManyActive=활성화된 세션들이 너무 많습니다: [{0}]. 세션 저장소로 내보낼 만한 유휴 세션들을 찾습니다.
persistentManager.unloading=[{0}]개의 세션들을 저장합니다.

standardManager.deletePersistedFileFail=저장된 세션들을 읽은 후 [{0}]을(를) 삭제할 수 없습니다. 이 파일이 계속 존재한다면, 이후 세션을 저장하려는 시도들이 이로 인해 실패할 수 있습니다.
standardManager.loading=[{0}](으)로부터 저장된 세션들을 로드합니다.
standardManager.loading.exception=저장된 세션들을 로드하는 중 예외 발생
standardManager.managerLoad=세션 저장소로부터 세션들을 로드하는 중 예외 발생
standardManager.managerUnload=세션들을 저장소로 언로드하는 중 예외 발생
standardManager.unloading=세션들을 [{0}]에 저장합니다.
standardManager.unloading.debug=저장된 세션들을 언로드합니다.
standardManager.unloading.nosessions=언로드할 수 있는 저장된 세션들이 없습니다.

standardSession.attributeEvent=세션 속성 이벤트 리스너가 예외를 발생시켰습니다.
standardSession.bindingEvent=세션 바인딩 이벤트 리스너가 예외를 발생시켰습니다.
standardSession.getAttribute.ise=getAttribute: 세션이 이미 무효화되었습니다.
standardSession.getAttributeNames.ise=getAttributeNames: 세션이 이미 무효화되었습니다.
standardSession.getCreationTime.ise=getCreationTime: 세션이 이미 무효화되었습니다.
standardSession.getIdleTime.ise=getIdleTime: 세션이 이미 무효화 되었습니다.
standardSession.getLastAccessedTime.ise=getLastAccessedTime: 세션이 이미 무효화 되었습니다.
standardSession.getThisAccessedTime.ise=getThisAccessedTime: 세션이 이미 만료되었습니다.
standardSession.getValueNames.ise=getValueNames: 세션이 이미 무효화 되었습니다.
standardSession.invalidate.ise=invalidate: 세션이 이미 무효화되었습니다.
standardSession.isNew.ise=isNew: 세션이 이미 무효화 되었습니다.
standardSession.logoutfail=세션을 만료시킬 때, 사용자를 로그아웃 하는 중 예외 발생
standardSession.notDeserializable=세션 [{1}]을(를) 위한 세션 속성 [{0}]을(를) 역직렬화할 수 없습니다.
standardSession.notSerializable=세션 [{1}]을(를) 위한 세션 속성 [{0}]을(를) 직렬화할 수 없습니다.
standardSession.principalNotDeserializable=세션 [{0}]을(를) 위한 Principal 객체를 역직렬화할 수 없습니다.
standardSession.principalNotSerializable=세션 [{0}]을(를) 위한 Principal 객체를 직렬화할 수 없습니다.
standardSession.removeAttribute.ise=removeAttribute: 세션이 이미 무효화되었습니다.
standardSession.sessionEvent=세션 이벤트 리스너가 예외를 발생시켰습니다.
standardSession.setAttribute.iae=setAttribute: 직렬화할 수 없는 속성 [{0}]
standardSession.setAttribute.ise=setAttribute: 세션 [{0}]이(가) 이미 무효화되었습니다.
standardSession.setAttribute.namenull=setAttribute: name 파라미터는 널일 수 없습니다.
