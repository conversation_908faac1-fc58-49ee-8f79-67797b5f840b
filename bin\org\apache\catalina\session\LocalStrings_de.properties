# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

JDBCStore.missingDataSourceName=Ke<PERSON> gültiger JNDI Namen wurde übergeben.
JDBCStore.saving=Speichere Session [{0}] in Datenbank [{1}]

fileStore.deleteFailed=Kann Datei [{0}] nicht löschen. Das verhindert das Erzeugen des Ablageortes für die Session-Ablage

managerBase.contextNull=Der Context muss auf einen nicht-null Wert gesetzt sein, bevor der Manager benutzt werden kann

persistentManager.storeKeysException=Kann die Liste der Session IDs für die Sessions in der Session-Ablage nicht ermitteln, nehme an, dass die Ablage leer ist.
persistentManager.storeSizeException=Konnte die Anzahl der Sessions im Session-Store nicht ermitteln, gehe davon aus, dass der Store leer ist.
persistentManager.swapTooManyActive=Lagere Session [{0}] aus, Untätig seit [{1}] Sekunden und zu viele Sessions aktiv

standardManager.loading.exception=Ausnahme beim Laden der persistierten Sitzungen
standardManager.managerLoad=Ausnahme beim Laden der Sitzungen aus dem Persistenten Speicher
standardManager.managerUnload=Fehler beim Entladen der Session zu persistenten Speicher

standardSession.notSerializable=Kann Session Attribut [{0}] für Sitzung [{1}] nicht serialisieren
