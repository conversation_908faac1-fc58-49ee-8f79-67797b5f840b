# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

htmlManagerServlet.appsAvailable=Ejecutándose
htmlManagerServlet.appsExpire=Expirar sesiones
htmlManagerServlet.appsName=Nombre a Mostrar
htmlManagerServlet.appsPath=Ruta
htmlManagerServlet.appsReload=Recargar
htmlManagerServlet.appsSessions=Sesiones
htmlManagerServlet.appsStart=Arrancar
htmlManagerServlet.appsStop=Parar
htmlManagerServlet.appsTasks=Comandos
htmlManagerServlet.appsTitle=Aplicaciones
htmlManagerServlet.appsUndeploy=Replegar
htmlManagerServlet.appsVersion=Versión
htmlManagerServlet.connectorStateProcessingTime=Tiempo de procesamiento:
htmlManagerServlet.connectorStateTableTitleRequest=Solicitud
htmlManagerServlet.connectorStateTableTitleVHost=VHost
htmlManagerServlet.deployButton=Desplegar
htmlManagerServlet.deployConfig=URL de archivo de Configuración XML:
htmlManagerServlet.deployPath=Trayectoria de Contexto (opcional):
htmlManagerServlet.deployServer=Desplegar directorio o archivo WAR localizado en servidor
htmlManagerServlet.deployTitle=Desplegar
htmlManagerServlet.deployUpload=Archivo WAR a desplegar
htmlManagerServlet.deployUploadFail=FALLO - Falló Carga de Despliegue, Excepción: [{0}]
htmlManagerServlet.deployUploadFile=Seleccione archivo WAR a cargar
htmlManagerServlet.deployUploadInServerXml=FALLO - El fichero war [{0}] no se puede cargar si se define el contexto en server.xml
htmlManagerServlet.deployUploadNoFile=FALLO - Falló la carga del fichero, no hay fichero
htmlManagerServlet.deployUploadNotWar=FALLO - El fichero cargado [{0}] debe de ser un .war
htmlManagerServlet.deployUploadWarExists=FALLO - El fichero war [{0}] ya existe en el servidor
htmlManagerServlet.deployWar=URL de WAR o Directorio:
htmlManagerServlet.diagnosticsLeak=Revisa a ver si una aplicación web ha causado fallos de memoria al parar, recargar o replegarse.
htmlManagerServlet.diagnosticsLeakButton=Halla fallos de memoria
htmlManagerServlet.diagnosticsLeakWarning=Este chequeo de diagnóstico disparará una colección completa de basura. Utilízalo con extremo cuidado en sistemas en producción.
htmlManagerServlet.diagnosticsSslConnectorCertsText=Lista los virtual hosts configurados con TLS y la cadena de ceritifaco para cada uno de ellos.\n
htmlManagerServlet.diagnosticsSslConnectorCipherButton=Cifrados
htmlManagerServlet.diagnosticsTitle=Diagnósticos
htmlManagerServlet.expire.explain=sin trabajar &ge;
htmlManagerServlet.expire.unit=minutos
htmlManagerServlet.findleaksList=Las siguientes aplicaciones web fueron paradas (recargadas, replegadas), pero sus clases de las ejecuciones previas aún se encuentran en memoria, causando así un fallo de memoria (usa un perfilador para confirmarlo):
htmlManagerServlet.findleaksNone=No parece haber aplicaciones web que hayan disparado un fallo de memoria al ser paradas, recargadas o replegadas.
htmlManagerServlet.helpHtmlManager=Ayuda HTML de Gestor
htmlManagerServlet.helpHtmlManagerFile=../docs/html-manager-howto.html
htmlManagerServlet.helpManager=Ayuda de Gestor
htmlManagerServlet.helpManagerFile=../docs/manager-howto.html
htmlManagerServlet.jvmFreeMemory=Memoria disponible:
htmlManagerServlet.jvmTableTitleMemoryPool=Pool de Memoria
htmlManagerServlet.list=Listar Aplicaciones
htmlManagerServlet.manager=Gestor
htmlManagerServlet.messageLabel=Mensaje:
htmlManagerServlet.noManager=-
htmlManagerServlet.noVersion=Ninguno especificado
htmlManagerServlet.osFreePageFile=Archivo de página gratis:
htmlManagerServlet.osTotalPageFile=Tamaño total de archivo de página:
htmlManagerServlet.serverHostname=NombreDeMáquina
htmlManagerServlet.serverIPAddress=Dirección IP
htmlManagerServlet.serverJVMVendor=Vendedor JVM
htmlManagerServlet.serverJVMVersion=Versión JVM
htmlManagerServlet.serverOSArch=Arquitectura de SO
htmlManagerServlet.serverOSName=Nombre del SO
htmlManagerServlet.serverOSVersion=Versión de SO
htmlManagerServlet.serverTitle=Información de Servidor
htmlManagerServlet.serverVersion=Versión de Tomcat
htmlManagerServlet.title=Gestor de Aplicaciones Web de Tomcat

managerServlet.alreadyContext=FALLO - Ya existe la aplicación en la trayectoria [{0}]
managerServlet.deleteFail=FALLO - No pude borrar [{0}]. La presencia continua de este fichero puede causar problemas.
managerServlet.deployFailed=FALLO - No pude desplegar la aplicación en ruta de contexto [{0}]
managerServlet.deployed=OK - Desplegada aplicación en trayectoria de contexto [{0}]
managerServlet.deployedButNotStarted=FALLO - Apliación desplegada en la ruta de contexto [{0}], pero el contexto no pudo arrancar
managerServlet.exception=FALLO - Encontrada excepción [{0}]
managerServlet.findleaksFail=FALLO - Ha fallado la búsqueda de fallos: La Máquina no es una instancia de StandardHost
managerServlet.findleaksList=OK - Hallados fallos potenciales de memoria en las siguientes aplicaciones:
managerServlet.findleaksNone=OK - No se han hallado fallos de memoria
managerServlet.invalidCommand=Fallo - Se proveiron parámetros inválidos para el comando [{0}]
managerServlet.invalidPath=FALLO - Se ha especificado una trayectoria inválida de contexto [{0}]
managerServlet.listed=OK - Aplicaciones listadas para máquina virtual [{0}]
managerServlet.mkdirFail=FALLO - No pude crear directorio [{0}]
managerServlet.noCommand=FALLO - No se ha especificado comando
managerServlet.noContext=FALLO - No existe contexto para trayectoria [{0}]
managerServlet.noGlobal=FALLO - No hay disponibles recursos globales JNDI
managerServlet.noManager=FALLO - No existe gestor para ruta [{0}]
managerServlet.noSelf=FALLO - El gestor no puede recargarse, replegarse, pararse o replegarse a sí mismo
managerServlet.noWrapper=El Contenedor no ha llamado a setWrapper() para este servlet
managerServlet.notDeployed=FALLO - El contexto [{0}] está definido en server.xml y puede que no esté desplegado
managerServlet.objectNameFail=FALLO - No pude registrar objeto de nombre [{0}] para Gestor de Servlet
managerServlet.postCommand=FALLO - Intenté usar el comando [{0}] vía un requerimiento GET pero se necesita POST
managerServlet.reloaded=OK - Recargada aplicación en trayectoria de contexto [{0}]
managerServlet.resourcesAll=OK - Listados recursos globales de todos los tipos
managerServlet.resourcesType=OK - Listados recursos globales de tipo [{0}]
managerServlet.saveFail=FAIL - Fallo al guardar la configuración: [{0}]
managerServlet.saved=OK - Configuración de Servidor guardada
managerServlet.savedContext=OK - Configuración de Contexto [{0}] guardada
managerServlet.sessiondefaultmax=Intervalo máximo por defecto de sesión inactiva [{0}] minutos
managerServlet.sessions=OK - Información de sesión para aplicación en trayectoria de contexto [{0}]
managerServlet.sessiontimeout=[{0}] minutos: [{1}] sesiones
managerServlet.sessiontimeout.expired=[{0}] minutos: expired [{1}] sesiones
managerServlet.sessiontimeout.unlimited=unlimited minutos: [{0}] sesiones
managerServlet.sslConnectorCiphers=OK - Connector / Información cifrada con SSL
managerServlet.startFailed=FALLO - No se pudo arrancar la aplicación en trayectoria de contexto [{0}]
managerServlet.started=OK - Arrancada aplicación en trayectoria de contexto [{0}]
managerServlet.stopped=OK - Parada aplicación en trayectoria de contexto [{0}]
managerServlet.trustedCertsNotConfigured=No se configuraron certificados confiables para este virtual host
managerServlet.undeployed=OK - Replegada aplicación en trayectoria de contexto [{0}]
managerServlet.unknownCommand=FALLO - Comando desconocido [{0}]

statusServlet.complete=Estado Completo de Servidor
statusServlet.title=Estado de Servidor
