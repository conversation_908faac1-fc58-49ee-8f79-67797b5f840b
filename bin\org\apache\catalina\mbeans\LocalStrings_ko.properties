# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jmxRemoteLifecycleListener.createRegistryFailed=[{0}] 서버를 위한 RMI 레지스트리를, 포트 번호 [{1}]을(를) 사용하여 생성할 수 없습니다.
jmxRemoteLifecycleListener.createServerFailed=JMX connector 서버가 생성될 수 없었거나, [{0}] 서버를 위해 시작되지 못했습니다.
jmxRemoteLifecycleListener.destroyServerFailed=[{0}] 서버를 위해, JMX connector 서버가 중지될 수 없었습니다.
jmxRemoteLifecycleListener.invalidRmiBindAddress=유효하지 않은 RMI 바인딩 주소 [{0}]
jmxRemoteLifecycleListener.invalidURL=[{0}] 서버에 요청하는 JMX 서비스 URL [{1}]은(는) 유효하지 않습니다.
jmxRemoteLifecycleListener.start=[{2}] 서버를 위하여, JMX 원격 리스너가 포트 [{0}]에 레지스트리를 설정했으며, 포트 [{1}]에 서버를 설정했습니다.

mBeanFactory.managerContext=매니저 구성요소들은 컨텍스트들에만 추가될 수 있습니다.
