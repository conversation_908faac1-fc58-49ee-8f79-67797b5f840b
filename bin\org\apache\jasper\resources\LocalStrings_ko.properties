# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jasper.error.emptybodycontent.nonempty=TLD 파일에 따르면, [{0}] 태그는 반드시 비어 있어야 하는데, 그렇지 않습니다,

jsp.engine.info=Jasper JSP {0} 엔진
jsp.error.action.isnottagfile=[{0}] 액션은 오직 태그 파일들 내에서만 사용될 수 있습니다.
jsp.error.action.istagfile=[{0}] 액션은 태그 파일 내에서 사용될 수 없습니다.
jsp.error.attempt_to_clear_flushed_buffer=오류: 이미 배출되어 버린 버퍼를 폐기하려는 시도
jsp.error.attr.quoted=속성 값은 인용부호로 처리되어야 합니다.
jsp.error.attribute.custom.non_rt_with_expr=TLD 또는 태그 파일 내의 attribute 지시어에 의하면, 속성 [{0}]은(는) 어떤 표현식도 받아들이지 않습니다.
jsp.error.attribute.deferredmix=${} 와 #{} 모두를 동일한 속성 값 내에서 표현식들로 사용할 수 없습니다.
jsp.error.attribute.duplicate=엘리먼트 내에서 속성의 qualified 이름들은 반드시 유일해야 합니다.
jsp.error.attribute.invalidPrefix=속성 prefix [{0}]이(가), 임포트된 어떤 태그 라이브러리와도 대응되지 않습니다.
jsp.error.attribute.noequal=등호("=")가 요구됩니다.
jsp.error.attribute.noescape=속성 값 [{0}]이(가) [{1}]을(를) 사용하여 인용부 처리되어 있는데, 이는 값 내에서 사용될 때에는 반드시 escape되어야 하는 것입니다.
jsp.error.attribute.noquote=인용부호가 요구됩니다.
jsp.error.attribute.nowhitespace=JSP 스펙에 따르면, 속성 이름은 반드시 whitespace 다음에 나타나야 합니다.
jsp.error.attribute.null_name=속성 이름이 널임
jsp.error.attribute.standard.non_rt_with_expr=표준 액션 [{1}]의 속성 [{0}]은(는), 어떠한 표현식도 받아들이지 않습니다.
jsp.error.attribute.unterminated=[{0}]을(를) 위한 속성 값이 올바르게 종료되지 않았습니다.
jsp.error.bad.scratch.dir=귀하가 지정한 scratchDir [{0}]은(는) 사용할 수 없습니다.
jsp.error.badStandardAction=유효하지 않은 표준 액션
jsp.error.bad_attribute=TLD에 따르면, 속성 [{0}]은(는) 태그 [{1}]을(를) 위해 유효하지 않습니다.
jsp.error.bad_tag=Prefix [{1}]와(과) 함께 임포트된 태그 라이브러리 내에, 태그 [{0}]이(가) 정의되지 않았습니다.
jsp.error.beans.nomethod=타입 [{1}]인 bean의 프로퍼티 [{0}]을(를) 읽기 위한 메소드를 찾을 수 없습니다.
jsp.error.beans.nomethod.setproperty=타입이 [{2}]인 bean 내에서, 타입 [{1}]의 프로퍼티 [{0}]에 대하여, 쓰기 가능한 메소드를 찾을 수 없습니다.
jsp.error.beans.noproperty=타입이 [{1}]인 bean의 프로퍼티 [{0}]에 대한 정보를 찾을 수 없습니다.
jsp.error.beans.nullbean=널 객체에 대하여, bean 오퍼레이션이 시도되었습니다.
jsp.error.beans.property.conversion=속성 [{2}]에 설정된 문자열 [{0}]을(를), 클래스 [{1}](으)로 변환할 수 없습니다: [{3}]
jsp.error.beans.propertyeditor.notregistered=PropertyEditorManager에 PropertyEditor가 등록되어 있지 않습니다.
jsp.error.beans.setproperty.noindexset=인덱스화된 프로퍼티를 설정할 수 없습니다.
jsp.error.bug48498=JSP extract를 표시할 수 없습니다. XML 파서의 버그 때문일 수 있습니다 (상세 정보는 Tomcat 버그 48498을 참조).
jsp.error.classname=.class 파일로부터 클래스명을 결정할 수 없습니다.
jsp.error.coerce_to_type=속성 [{0}]을(를) 위한 값 [{2}]을(를), 타입 [{1}](으)로 강제 변환 시킬 수 없습니다.
jsp.error.compilation=파일 컴파일 중 오류 발생: [{0}] [{1}]
jsp.error.compiler=가용한 자바 컴파일러가 없습니다.
jsp.error.compiler.config=설정 옵션들인 compilerClassName: [{0}]와(과) compiler: [{1}]들을 지원하는, 가용한 자바 컴파일러가 없습니다.
jsp.error.config_pagedir_encoding_mismatch=jsp-property-group [{0}]에 지정된 페이지 인코딩이, 페이지 지시어 [{1}]에 지정된 것과 다릅니다.
jsp.error.corresponding.servlet=생성된 서블릿 오류:\n\
\n
jsp.error.could.not.add.taglibraries=하나 이상의 태그 라이브러리들을 추가할 수 없었습니다.
jsp.error.data.file.processing=파일 [{0}]을(를) 처리하는 중 오류 발생
jsp.error.data.file.read=파일 [{0}]을(를) 읽는 중 오류 발생
jsp.error.data.file.write=데이터 파일을 쓰는 중 오류 발생
jsp.error.deferredmethodandvalue='deferredValue'와 'deferredMethod', 둘 다 'true'일 수 없습니다.
jsp.error.deferredmethodsignaturewithoutdeferredmethod='deferredMethod'가 'true'가 아니면, 메소드 signature를 지정할 수 없습니다.
jsp.error.deferredvaluetypewithoutdeferredvalue=만일 'deferredValue' 값이 'true'가 아니라면, 값의 타입을 지정할 수 없습니다.
jsp.error.directive.isnottagfile=[{0}] 지시어는 오직 태그 파일 내에서만 사용될 수 있습니다.
jsp.error.directive.istagfile=[{0}] 지시어는 태그 파일 내에서 사용될 수 없습니다.
jsp.error.duplicate.name.jspattribute=표준 또는 커스텀 액션에 지정된 속성 [{0}]이(가), 내부에 포함된 jsp:attribute 내의 name 속성의 값으로도 지정된 것 같습니다.
jsp.error.duplicateqname=중복된 qualified 이름 [{0}]을(를) 갖는 속성이 발견되었습니다. 속성의 qualified 이름들은 반드시 엘리먼트 내에서 유일해야 합니다.
jsp.error.dynamic.attributes.not.implemented=[{0}] 태그가 동적 속성들을 받아들인다고 선언되어 있으나, 필수적인 인터페이스를 구현하지 않았습니다.
jsp.error.el.parse=[{0}] : [{1}]
jsp.error.el.template.deferred=#{...}은(는) 템플릿 텍스트에서 허용되지 않습니다.
jsp.error.el_interpreter_class.instantiation=ELInterpreter 클래스 [{0}]을(를) 로드하지 못했거나, 인스턴스를 생성하지 못했습니다.
jsp.error.fallback.invalidUse=jsp:fallback은 jsp:plugin의 직계 자식 엘리먼트여야 합니다.
jsp.error.file.already.registered=파일 [{0}]의 재귀적인 include입니다.
jsp.error.file.cannot.read=파일을 읽을 수 없습니다: [{0}]
jsp.error.file.not.found=파일 [{0}]을(를) 찾을 수 없습니다.
jsp.error.flush=데이터를 배출하는 중 예외가 발생했습니다.
jsp.error.fragmentwithtype=''fragment''와 ''type'' 속성, 둘 다를 지정할 수 없습니다. 만일 ''fragment''이 지정되면, ''type''은 ''{0}''으로 고정됩니다.
jsp.error.function.classnotfound=function [{1}]을(를) 위하여 TLD에 지정된, 클래스 [{0}]을(를) 찾을 수 없습니다: [{2}]
jsp.error.include.exception=[{0}]을(를) include할 수 없습니다.
jsp.error.include.tag=유효하지 않은 jsp:include 태그
jsp.error.internal.filenotfound=내부 오류: 파일 [{0}]을(를) 찾을 수 없습니다.
jsp.error.invalid.attribute=[{0}]은(는) 유효하지 않은 속성을 가지고 있습니다: [{1}]
jsp.error.invalid.bean=useBean의 class 속성을 위한 값 [{0}]은(는) 유효하지 않습니다.
jsp.error.invalid.directive=유효하지 않은 지시어
jsp.error.invalid.expression=[{0}]이(가) 유효하지 않은 표현식(들)을 포함하고 있습니다: [{1}]
jsp.error.invalid.implicit=[{0}]에 위치한 태그 파일을 위해, 유효하지 않은 묵시적 TLD입니다.
jsp.error.invalid.implicit.version=[{0}]에 위치한 태그 파일을 위한 묵시적인 TLD에, 유효하지 않은 JSP 버전이 정의되어 있습니다.
jsp.error.invalid.scope=''scope'' 속성으로 불허되는 값: [{0}] (반드시 "page", "request", "session", "application" 중의 하나여야 합니다.)
jsp.error.invalid.tagdir=태그 파일 디렉토리 [{0}]이(가) "/WEB-INF/tags"로 시작하지 않습니다.
jsp.error.invalid.version=[{0}]에 있는 tag 파일에 유효하지 않은 JSP 버전이 정의되어 있습니다.
jsp.error.ise_on_clear=버퍼 크기가 0일 때 clear()를 호출하는 것은 불허됩니다.
jsp.error.java.line.number=코드 생성된 자바 파일 [{1}]의 [{0}] 행에서 오류가 발생했습니다.
jsp.error.javac=Javac 예외 발생
jsp.error.javac.env=환경:
jsp.error.jspbody.emptybody.only=[{0}] 태그는 오직 jsp:attribute만을 body 안에 포함할 수 있습니다.
jsp.error.jspbody.invalidUse=jsp:body는 반드시 표준 또는 커스텀 액션의 하위 엘리먼트이어야 합니다.
jsp.error.jspbody.required=jsp:attribute가 사용되는 경우, [{0}]을(를) 위한 태그의 body를 지정하기 위해, jsp:body를 사용해야 합니다.
jsp.error.jspc.missingTarget=target이 없음: 반드시 -webapp 또는 -uriroot, 또는 하나 이상의 JSP page들을 지정해야 합니다.
jsp.error.jspc.no_uriroot=uriroot가 지정되지 않아서, 지정된 해당 JSP 파일(들)에 대한 위치를 알아낼 수 없습니다.
jsp.error.jspc.uriroot_not_dir=-uriroot 옵션은 반드시 이미 존재하는 디렉토리를 지정해야 합니다.
jsp.error.jspelement.missing.name=필수 항목인 XML 스타일의 'name' 속성이 존재하지 않습니다.
jsp.error.jspoutput.conflict=&lt;jsp:output&gt;: [{0}]이(가) 다른 값들로 여러 번 나타나는 것은 불허됩니다. (이전 값: [{1}], 신규 값: [{2}])
jsp.error.jspoutput.doctypenamesystem=&lt;jsp:output&gt;: 'doctype-root-element'와 'doctype-system' 속성들은 반드시 함께 나타나야 합니다.
jsp.error.jspoutput.doctypepublicsystem=&lt;jsp:output&gt;: 'doctype-public' 속성이 나타나는 경우에는, 'doctype-system' 속성이 반드시 존재해야 합니다.
jsp.error.jspoutput.invalidUse=&lt;jsp:output&gt;은 표준 문법에서 사용되서는 안됩니다.
jsp.error.jspoutput.nonemptybody=&lt;jsp:output&gt;은 body를 포함해서는 안됩니다.
jsp.error.jsproot.version.invalid=유효하지 않은 버전: [{0}]. 반드시 다음 중 하나여야 합니다: "1.2", "2.0", "2.1", "2.2", "2.3"
jsp.error.jsptext.badcontent='&lt;'가 &lt;jsp:text&gt;의 body 내에 존재할 때에는 반드시 CDATA 내에 포함되어야 합니다.
jsp.error.lastModified=파일 [{0}]을(를) 위한 최종 변경 시간을 결정할 수 없습니다.
jsp.error.library.invalid=라이브러리 [{0}]에 의하면, JSP 페이지가 유효하지 않습니다: [{1}]
jsp.error.literal_with_void=Void 반환 타입인 deferred 메소드로서 정의된 속성 [{0}]을(를) 위해, literal 값이 지정되었습니다. JSP.2.3.4는 이런 경우 literal 값들을 허용하지 않습니다.
jsp.error.loadclass.taghandler=태그 [{1}]을(를) 위한 태그 핸들러 클래스 [{0}]을(를) 로드할 수 없습니다.
jsp.error.location=행: [{0}], 열: [{1}]
jsp.error.mandatory.attribute=[{0}]: 필수 속성 [{1}]이(가) 없습니다.
jsp.error.missing.tagInfo=[{0}]을(를) 위한 TagInfo 객체가 TLD에 없습니다.
jsp.error.missing_attribute=해당 TLD 또는 태그 파일에 의하면, 속성 [{0}]은(는) 태그 [{1}]에 필수 사항입니다.
jsp.error.missing_var_or_varReader='var' 또는 'varReader' 속성이 없습니다.
jsp.error.namedAttribute.invalidUse=jsp:attribute는 반드시 표준 또는 커스텀 액션의 하위 엘리먼트이어야 합니다.
jsp.error.needAlternateJavaEncoding=기본 자바 인코딩 [{0}]은(는) 귀하의 자바 플랫폼에서 유효하지 않습니다. JspServlet의 ''javaEncoding'' 파라미터를 통해, 대안적 인코딩을 설정할 수 있습니다.
jsp.error.nested.jspattribute=jsp:attribute 표준 액션은 또 다른 jsp:attribute 표준 액션 내에 포함될 수 없습니다.
jsp.error.nested.jspbody=jsp:body 표준 액션은, 또 다른 jsp:body 또는 jsp:attribute 표준 액션 내에 포함될 수 없습니다.
jsp.error.nested_jsproot=Nested &lt;jsp:root&gt;
jsp.error.no.more.content=파싱이 더 요구되는 상황에서, 컨텐트의 끝에 도달했습니다: 태그 nesting 오류일까요?
jsp.error.no.scratch.dir=JSP 엔진에 scratch 디렉토리가 설정되지 않았습니다. 이 컨텍스트를 위해 "jsp.initparams=scratchdir=<dir-name>"을 servlets.properties 파일에 추가하십시오.
jsp.error.no.scriptlets=스크립팅 엘리먼트들은 ( &lt;%!, &lt;jsp:declaration, &lt;%=, &lt;jsp:expression, &lt;%, &lt;jsp:scriptlet ) 이곳에서 허용되지 않습니다.
jsp.error.noFunction=지정된 prefix를 사용하여 function [{0}]의 위치를 결정할 수 없습니다.
jsp.error.noFunctionMethod=클래스 [{2}] 내에서, function [{1}]을(를) 위한 메소드 [{0}]을(를) 찾을 수 없습니다.
jsp.error.non_null_tei_and_var_subelems=태그 [{0}]이(가) 하나 이상의 variable 하위 엘리먼트들과, 하나 이상의 VariableInfo들을 반환하는 TagExtraInfo 클래스를 포함하고 있습니다.
jsp.error.not.in.template=JSP 템플릿 텍스트의 body에서, [{0}]은(는) 허용되지 않습니다.
jsp.error.outputfolder=출력 폴더가 없음
jsp.error.overflow=오류: JSP 버퍼 오버플로우
jsp.error.page.conflict.autoflush=페이지 지시어: ''autoFlush''가 다른 값들로 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.page.conflict.buffer=페이지 지시어: 다른 값들을 가진 ''buffer''가 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.page.conflict.contenttype=페이지 지시어: 다른 값들을 가지는 ''contentType''이 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.page.conflict.deferredsyntaxallowedasliteral=페이지 지시어: 다른 값들을 가지는 ''deferredSyntaxAllowedAsLiteral''이 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.page.conflict.errorpage=페이지 지시어: 다른 값들을 가지는 ''errorPage''가 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.page.conflict.extends=페이지 지시어: 다른 값들을 가지는 ''extends''가 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.page.conflict.info=페이지 지시어: 다른 값들을 가지는 ''info''가 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.page.conflict.iselignored=페이지 지시어: 다른 값들을 가지는 ''isELIgnored''가 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.page.conflict.iserrorpage=페이지 지시어: 다른 값들을 가지는 ''isErrorPage''가 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.page.conflict.isthreadsafe=페이지 지시어: 다른 값들을 가지는 ''isThreadSafe''가 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.page.conflict.language=페이지 지시어: 다른 값들로 ''language''가 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.page.conflict.session=페이지 지시어: ''session''이 다른 값들로 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.page.conflict.trimdirectivewhitespaces=페이지 지시어: 다른 값들을 가지는 ''trimDirectiveWhitespaces''가 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.page.invalid.buffer=페이지 지시어: 유효하지 않은 버퍼 설정 값
jsp.error.page.invalid.deferredsyntaxallowedasliteral=페이지 지시어: deferredSyntaxAllowedAsLiteral에 유효하지 않은 값
jsp.error.page.invalid.import=페이지 지시어: 임포트를 위한 유효한 값이 아닙니다.
jsp.error.page.invalid.iselignored=페이지 지시어: isELIgnored에 유효하지 않은 값
jsp.error.page.invalid.iserrorpage=페이지 지시어: isErrorPage에 유효하지 않은 값
jsp.error.page.invalid.isthreadsafe=페이지 지시어: isThreadSafe에 유효하지 않은 값
jsp.error.page.invalid.session=페이지 지시어: 세션을 위해 유효하지 않은 값
jsp.error.page.invalid.trimdirectivewhitespaces=페이지 지시어: trimDirectiveWhitespaces에 유효하지 않은 값
jsp.error.page.language.nonjava=페이지 지시어: 유효하지 않은 language 속성입니다.
jsp.error.page.multi.pageencoding=페이지 지시어는 여러 개의 pageencoding을 가져서는 안됩니다.
jsp.error.page.noSession=어떤 세션에도 참여하지 않는 페이지에서 세션 scope에 접근할 수 없습니다.
jsp.error.param.invalidUse=jsp:param 액션은, jsp:include, jsp:forward, 또는 jsp:params 엘리먼트들 외부에서 사용되서는 안됩니다.
jsp.error.paramexpected="name"과 "value" 속성들을 포함한 "jsp:param" 표준 액션이 요구됩니다.
jsp.error.params.emptyBody=jsp:params는 반드시 적어도 하나 이상의 jsp:param을 포함해야 합니다.
jsp.error.params.invalidUse=jsp:params는 반드시 jsp:plugin의 직계 자식이어야 합니다.
jsp.error.parse.error.in.TLD=태그 라이브러리 descriptor 내에서 파싱 오류 발생: [{0}]
jsp.error.parse.xml=[{0}] 파일에서 XML 파싱 오류 발생
jsp.error.parse.xml.line=파일 [{0}]에서 XML 파싱 오류 발생: (행: [{1}], 열: [{2}])
jsp.error.parse.xml.scripting.invalid.body=[{0}] 엘리먼트의 body 내에서는, 반드시 어떠한 XML 엘리먼트들도 포함해서는 안됩니다.
jsp.error.plugin.badtype=jsp:plugin: 내의 'type' 속성을 위해 불허되는 값입니다. 반드시 'bean' 또는 'applet'이어야 합니다.
jsp.error.plugin.nocode=jsp:plugin에 code가 선언 안됨
jsp.error.plugin.notype=jsp:plugin에 type이 선언되지 않았습니다.
jsp.error.prefix.refined=현재 범위에서 이미 [{2}](으)로서 정의되어 있는데, prefix를 [{0}]에서 [{1}](으)로 재정의하려는 시도입니다.
jsp.error.prefix.use_before_dcl=이 태그 지시어에 지정된 prefix [{0}]은(는), 이전에 파일 [{1}] 내의 [{2}] 행에 있는 액션에 의해 사용된 적이 있습니다.
jsp.error.prolog_config_encoding_mismatch=XML 프롤로그 [{0}]에 지정된 페이지 인코딩이, jsp-property-group [{1}]에 지정된 것과 다릅니다.
jsp.error.prolog_pagedir_encoding_mismatch=XML 프롤로그 [{0}]에 지정된 페이지 인코딩이, 페이지 지시어 [{1}]에 지정된 것과 다릅니다.
jsp.error.quotes.unterminated=종료되지 않은 인용부들
jsp.error.scripting.variable.missing_name=속성 [{0}](으)로부터 스크립팅 변수 이름을 결정할 수 없습니다.
jsp.error.security=컨텍스트를 위한 보안 초기화 작업이 실패했습니다.
jsp.error.servlet.destroy.failed=JSP 페이지를 위한 Servlet.destroy() 호출 중 예외 발생
jsp.error.servlet.invalid.method=JSP들은 오직 GET, POST 또는 HEAD 메소드만을 허용합니다. Jasper는 OPTIONS 메소드 또한 허용합니다.
jsp.error.setLastModified=파일 [{0}]의 최종 변경 시간을 설정할 수 없습니다.
jsp.error.signature.classnotfound=TLD 내에 function [{1}]을 위해 지정된 메소드 signature에 포함된 클래스 [{0}]을(를) 찾을 수 없습니다. [{2}]
jsp.error.simpletag.badbodycontent=클래스 [{0}]을(를) 위한 TLD가 SimpleTag로서 유효하지 않은 body-content (JSP)를 지정하고 있습니다.
jsp.error.single.line.number=JSP 파일 [{1}]의 [{0}] 행에서 오류가 발생했습니다.
jsp.error.stream.close.failed=스트림 닫기 실패
jsp.error.stream.closed=스트림이 닫혔습니다.
jsp.error.string_interpreter_class.instantiation=StringInterpreter 클래스 [{0}]을(를) 찾지 못하거나 생성할 수 없습니다.
jsp.error.tag.conflict.attr=태그 지시어: 서로 다른 값들을 가지는 속성 [{0}]이(가) 여러 번 나타나는 것은 불허됩니다. (이전 값: [{1}], 신규 값: [{2}])
jsp.error.tag.conflict.deferredsyntaxallowedasliteral=태그 지시어: 다른 값들을 가지는 "deferredSyntaxAllowedAsLiteral"이 여러 번 나타나는 것은 불허됩니다 (이전 값: [{0}], 신규 값: [{1}])
jsp.error.tag.conflict.iselignored=태그 지시어: 다른 값들을 가지는 ''isELIgnored''가 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.tag.conflict.language=태그 지시어: 다른 값들을 가지는 ''language''가 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.tag.conflict.trimdirectivewhitespaces=태그 지시어: 다른 값들을 가지는 ''trimDirectiveWhitespaces''가 여러 번 나타나는 것은 불허됩니다. (이전 값: [{0}], 신규 값: [{1}])
jsp.error.tag.invalid.deferredsyntaxallowedasliteral=태그 지시어: deferredSyntaxAllowedAsLiteral을 위해 유효하지 않은 값
jsp.error.tag.invalid.iselignored=태그 지시어: isELIgnored을 위해 유효하지 않은 값
jsp.error.tag.invalid.trimdirectivewhitespaces=태그 지시어: trimDirectiveWhitespaces에 유효하지 않은 값
jsp.error.tag.language.nonjava=태그 지시어: 유효하지 않은 language 속성
jsp.error.tag.multi.pageencoding=태그 지시어에서 pageEncoding이 여러 번 나타나서는 안됩니다.
jsp.error.tagdirective.badbodycontent=태그 지시어 내에 유효하지 않은 body-content: [{0}]
jsp.error.tagfile.badSuffix=태그 파일 경로 [{0}]이(가) ".tag"로 끝나지 않습니다.
jsp.error.tagfile.illegalPath=불허되는 태그 파일 경로: [{0}]. 경로는 반드시 "/WEB-INF/tags" 또는 "/META-INF/tags"로 시작해야 합니다.
jsp.error.tagfile.missingPath=태그 파일에 대한 경로가 지정되지 않았습니다.
jsp.error.tagfile.nameFrom.badAttribute=attribute 지시어는 ([{1}] 행에 선언되고 그 name 속성이 [{0}]이며 그 값은 name-from-attribute의 값인) 반드시 java.lang.String 타입이어야 하고, "required"여야 하며, "rtexprvalue"가 되어서는 안됩니다.
jsp.error.tagfile.nameFrom.noAttribute=이 name-from-attribute 속성 값, [{0}]을(를) 가진 name 속성을 가진 attribute 지시어를 찾을 수 없습니다.
jsp.error.tagfile.nameNotUnique=[{2}] 행에 있는 [{0}] 값과 [{1}] 값이 동일합니다.
jsp.error.taglibDirective.absUriCannotBeResolved=절대 URI인 [{0}]을(를), web.xml 또는 이 애플리케이션과 함께 배치된 JAR 파일 내에서 찾을 수 없습니다.
jsp.error.taglibDirective.both_uri_and_tagdir='uri'와 'tagdir' 속성, 둘 다 지정되었습니다.
jsp.error.taglibDirective.missing.location='uri'나 'tagdir' 중 어느 것도 지정되지 않았습니다.
jsp.error.taglibDirective.uriInvalid=태그 라이브러리를 위해 제공된 URI [{0}]이(가) 유효한 URI가 아닙니다.
jsp.error.tei.invalid.attributes=[{0}]을(를) 위한 TagExtraInfo로부터 Validation 오류 메시지입니다.
jsp.error.teiclass.instantiation=TagExtraInfo 클래스 [{0}]을(를) 로드하거나 인스턴스를 생성하지 못했습니다.
jsp.error.text.has_subelement=&lt;jsp:text&gt;는 하위 엘리먼트들을 가져서는 안됩니다.
jsp.error.tld.fn.duplicate.name=태그 라이브러리 [{1}] 내에, 중복된 function 이름 [{0}]이(가) 존재합니다.
jsp.error.tld.fn.invalid.signature=TLD 내에 function signature로서 유효하지 않은 문법입니다. 태그 라이브러리: [{0}], Function: [{1}]
jsp.error.tld.invalid_tld_file=유효하지 않은 TLD 파일: [{0}]. 보다 상세한 정보는 JSP 스펙 7.3.1 장을 참조하십시오.
jsp.error.tld.mandatory.element.missing=TLD 파일 [{1}] 내에서, 필수 항목인 TLD 엘리먼트 [{0}]이(가) 존재하지 않거나 비어 있습니다.
jsp.error.tld.missing=URI [{1}]을(를) 위한 태그라이브러리 [{0}]을(를) 찾을 수 없습니다.
jsp.error.tld.missing_jar=TLD를 포함하는 JAR 리소스 [{0}]이(가) 없습니다.
jsp.error.tld.unable_to_get_jar=TLD [{1}]을(를) 포함한 JAR 리소스 [{0}]을(를) 구할 수 없습니다.
jsp.error.tlv.invalid.page=[{1}] 내의 [{0}]을(를) 위한 TagLibraryValidator로부터 Validation 오류 메시지들
jsp.error.tlvclass.instantiation=TagLibraryValidator 클래스 [{0}]을(를) 로드하거나 인스턴스를 생성하지 못했습니다.
jsp.error.unable.compile=JSP를 위한 클래스를 컴파일할 수 없습니다.
jsp.error.unable.deleteClassFile=클래스 파일을 삭제할 수 없습니다.
jsp.error.unable.load=JSP를 위한 클래스를 로드할 수 없습니다.
jsp.error.unable.renameClassFile=클래스 파일의 이름을 [{0}]에서 [{1}] (으)로 바꿀 수 없습니다.
jsp.error.unable.to_find_method=속성 [{0}]을(를) 위한 setter 메소드를 찾을 수 없습니다.
jsp.error.unavailable=JSP가 가용하지 않은 상태로 표시되어 있습니다.
jsp.error.unbalanced.endtag=종료 태그 "&lt;/{0}"이(가) 시작 태그와 맞지 않습니다.
jsp.error.undeclared_namespace=커스텀 태그가, 선언되지 않은 네임스페이스 [{0}]을(를) 포함했습니다.
jsp.error.unknown_attribute_type=속성 [{0}]을(를) 위한 속성 타입으로 알 수 없는 값 [{1}]이(가) 설정되었습니다.
jsp.error.unsupported.encoding=지원되지 않는 인코딩: [{0}]
jsp.error.unterminated=종료되지 않은 [{0}] 태그
jsp.error.usebean.duplicate=useBean: 중복된 bean 이름: [{0}]
jsp.error.usebean.noSession=JSP 페이지가 세션에 참여하지 않기로 (page 지시어를 통해) 선언되어 있을 때, useBean이 session scope을 사용하는 것은 불허됩니다.
jsp.error.var_and_varReader='var'또는 'varReader' 중 오직 하나만 지정할 수 있습니다.
jsp.error.variable.alias=variable 지시어 내에서, name-from-attribute과 alias 속성, 둘 다 지정하거나, 둘 다 지정하지 말아야 합니다.
jsp.error.variable.both.name=variable 지시어 내에서, name-given과 name-from-attribute 속성, 둘 다를 지정할 수 없습니다.
jsp.error.variable.either.name=name-given 또는 name-from-attribute 속성 둘 중 하나는, variable 지시어 내에서 반드시 지정되어야 합니다.
jsp.error.xml.badStandardAction=유효하지 않은 표준 액션: [{0}]
jsp.error.xml.bad_tag=URI [{1}]와(과) 연관된 태그 라이브러리 내에, 태그 [{0}]이(가) 정의되지 않았습니다.
jsp.exception=행 [{1}]에서 [{0}]을(를) 처리하는 중 예외 발생
jsp.info.ignoreSetting=SecurityManager가 사용 가능 상태로 설정되었기 때문에, [{1}]의 [{0}]을(를) 위한 설정은 무시됩니다.
jsp.message.dont.modify.servlets=중요사항: 코드 생성된 서블릿들을 변경하지 마시오.
jsp.message.jsp_added=컨텍스트 [{1}]의 큐에, 경로 [{0}]을(를) 위한 JSP를 추가합니다.
jsp.message.jsp_queue_created=컨텍스트 [{1}]을(를) 위해 길이가 [{0}]인 JSP 큐를 생성했습니다.
jsp.message.jsp_queue_update=컨텍스트 [{1}]의 큐에 존재하는 JSP 서블릿(경로: [{0}])을 변경합니다.
jsp.message.jsp_removed_excess=컨텍스트 [{1}]의 큐로부터, 과도하게 오래 수행되는 JSP (경로: [{0}]) 페이지를 제거합니다.
jsp.message.jsp_removed_idle=컨텍스트 [{1}] 내의 경로 [{0}]을(를) 위한 JSP가 [{2}] 밀리초 동안 유휴 상태에 있었으므로 제거합니다.
jsp.message.jsp_unload_check=컨텍스트 [{0}] 내에서 JSP들이 언로드되어야 하는지 점검합니다. JSP 개수: [{1}], 큐 길이: [{2}]
jsp.message.parent_class_loader_is=부모 클래스로더: [{0}]]
jsp.message.scratch.dir.is=JSP 엔진을 위한 Scratch 디렉토리: [{0}]
jsp.tldCache.noTldInDir=디렉토리 [{0}] 내에서 TLD 파일들이 발견되지 않았습니다.
jsp.tldCache.noTldInJar=[{0}]에서 TLD 파일들을 찾을 수 없습니다. CATALINA_BASE/conf/catalina.properties 파일 내의 tomcat.util.scan.StandardJarScanFilter.jarsToSkip 프로퍼티에, 해당 JAR를 추가하는 것을 고려하십시오.
jsp.tldCache.noTldInResourcePath=리소스 경로 [{0}]에서 TLD 파일들을 찾을 수 없습니다.
jsp.tldCache.noTldSummary=적어도 하나의 JAR가 TLD들을 찾기 위해 스캔되었으나 아무 것도 찾지 못했습니다. 스캔했으나 TLD가 없는 JAR들의 전체 목록을 보시려면, 로그 레벨을 디버그 레벨로 설정하십시오. 스캔 과정에서 불필요한 JAR들을 건너뛰면, 시스템 시작 시간과 JSP 컴파일 시간을 단축시킬 수 있습니다.
jsp.tldCache.tldInDir=TLD 파일들이 디렉토리 [{0}] 내에서 발견되었습니다.
jsp.tldCache.tldInJar=JAR [{0}] 내에서 TLD 파일들을 찾지 못했습니다.
jsp.tldCache.tldInResourcePath=리소스 경로 [{0}]에서 TLD 파일들이 발견되었습니다.
jsp.warning.bad.urlpattern.propertygroup=web.xml 내 url-pattern 하위 엘리먼트에 잘못된 값: [{0}]
jsp.warning.checkInterval=경고: initParam인 checkInterval에 유효하지 않은 값. 기본 값인 "300" 초를 사용할 것입니다.
jsp.warning.classDebugInfo=경고: initParam인 classdebuginfo에 유효하지 않은 값. 기본 값인 "false"를 사용할 것입니다.
jsp.warning.classpathUrl=클래스패스 내에 유효하지 않은 URL이 발견됨. 이 URL은 무시될 것입니다.
jsp.warning.compiler.classfile.delete.fail=생성된 클래스 파일 [{0}]을(를) 삭제하지 못했습니다.
jsp.warning.compiler.classfile.delete.fail.unknown=코드 생성된 클래스 파일(들)을 삭제하지 못했습니다.
jsp.warning.compiler.javafile.delete.fail=생성된 자바 파일 [{0}]을(를) 삭제하지 못했습니다.
jsp.warning.development=경고: initParam인 development에 유효하지 않은 값. 기본 값인 "true"를 사용할 것입니다.
jsp.warning.displaySourceFragment=경고: initParam인 displaySourceFragment에 유효하지 않은 값. 기본 값인 "true"를 사용할 것입니다.
jsp.warning.dumpSmap=경고: initParam인 dumpSmap에 유효하지 않은 값. 기본 값인 "false"를 사용할 것입니다.
jsp.warning.enablePooling=주의: initParam인 enablePooling에 유효하지 않은 값. 기본 값인 "true"를 사용할 것입니다.
jsp.warning.fork=경고: initParam인 fork에 유효하지 않은 값. 기본 값인 "true"를 사용할 것입니다.
jsp.warning.genchararray=경고: initParam인 genStringAsCharArray에 유효하지 않은 값. 기본 값인 "false"를 사용할 것입니다.
jsp.warning.jspIdleTimeout=경고: initParam인 jspIdleTimeout에 유효하지 않은 값. 기본값 "-1"을 사용할 것입니다.
jsp.warning.keepgen=경고: initParam인 keepgenerated에 유효하지 않은 값. 기본 값인 "false"를 사용할 것입니다.
jsp.warning.mappedFile=경고: initParam인 mappedFile에 유효하지 않은 값. 기본 값인 "false"를 사용할 것입니다.
jsp.warning.maxLoadedJsps=경고: initParam인 maxLoadedJsps에 유효하지 않은 값. 기본 값인 "-1"을 사용할 것입니다.
jsp.warning.modificationTestInterval=경고: initParam인 modificationTestInterval에 유효하지 않은 값. 기본 값인 "4"초를 사용할 것입니다.
jsp.warning.noJarScanner=경고: ServletContext에 org.apache.tomcat.JarScanner가 설정되지 않았습니다. 기본 JarScanner 구현 객체를 사용할 것입니다.
jsp.warning.quoteAttributeEL=경고: initParam인 quoteAttributeEL에 유효하지 않은 값. 기본 값인 "false"를 사용할 것입니다.
jsp.warning.recompileOnFail=경고: initParam인 recompileOnFail에 유효하지 않은 값. 기본 값인 false를 사용할 것입니다.
jsp.warning.strictQuoteEscaping=경고: initParam인 strictQuoteEscaping에 유효하지 않은 값. 기본 값인 "true"를 사용할 것입니다.
jsp.warning.suppressSmap=경고: initParam인 suppressSmap에 유효하지 않은 값. 기본값인 "false"를 사용할 것입니다.
jsp.warning.tagPreDestroy=[{0}]의 태그 인스턴스에 대하여, preDestroy를 처리 중 오류 발생
jsp.warning.tagRelease=[{0}]의 태그 인스턴스에 대해 release를 처리하는 중 오류 발생
jsp.warning.unknown.sourceVM=알 수 없는 Source VM [{0}]은(는) 무시됩니다.
jsp.warning.unknown.targetVM=알 수 없는 target VM [{0}]은(는) 무시됩니다.
jsp.warning.unsupported.sourceVM=요청된 소스 VM [{0}]은(는) 지원되지 않습니다. [{1}]을(를) 사용합니다.
jsp.warning.unsupported.targetVM=요청된 대상 VM [{0}]은(는) 지원되지 않습니다. [{1}]을(를) 사용합니다.
jsp.warning.xpoweredBy=경고: initParam인 xpoweredBy에 유효하지 않은 값. 기본 값인 "false"를 사용할 것입니다.

jspc.delete.fail=파일 [{0}]을(를) 삭제하지 못했습니다.
jspc.error.fileDoesNotExist=파일 아규먼트 [{0}]이(가) 존재하지 않습니다.
jspc.error.generalException=오류: 파일 [{0}]이(가) 다음의 일반적인 예외를 발생시켰습니다:
jspc.error.invalidFragment=웹 fragment들 내의 오류들로 인하여, 사전 컴파일을 중단합니다.
jspc.error.invalidWebXml=web.xml 내의 오류들로 인해, 사전 컴파일을 중단합니다.
jspc.generation.result=[{0}]개의 오류를 발생시키며, 코드 생성이 [{1}] 밀리초에 완료되었습니다.
jspc.implicit.uriRoot=uriRoot가 묵시적으로 [{0}](으)로 설정됨
jspc.webfrg.footer=\n\
</web-fragment>\n\
\n
jspc.webinc.footer=<!--\n\
End of content automatically created by Apache Tomcat JspC.\n\
-->\n\
\n\
\n
jspc.webinc.header=\n\
<!--\n\
Automatically created by Apache Tomcat JspC.\n\
-->\n\
\n\
\n
jspc.webinc.insertEnd=<!-- JSPC servlet mappings end -->
jspc.webinc.insertStart=<!-- JSPC servlet mappings start -->
jspc.webxml.footer=\n\
</web-app>\n\
\n

org.apache.jasper.compiler.ELParser.invalidQuotesForStringLiteral=문자열 literal [{0}]은(는) 유효하지 않습니다. 반드시 홑따옴표들 또는 쌍따옴표들로 둘러싸여야 합니다.
org.apache.jasper.compiler.ELParser.invalidQuoting=표현식 [{0}]은(는) 유효하지 않습니다. 인용되는 문자열 내에서는, 오직 [], [''] 그리고 ["] 만이 [] 을 사용하여 escape될 수 있습니다.
org.apache.jasper.compiler.TldCache.servletContextNull=제공된 ServletContext가 널이었습니다.
org.apache.jasper.servlet.JasperInitializer.onStartup=컨텍스트 [{0}]을(를) 위한 Jasper를 초기화합니다.
org.apache.jasper.servlet.TldScanner.webxmlAdd=리소스 경로 [{0}](으)로부터 URL [{1}]을(를) 위한 TLD를 로드합니다.
org.apache.jasper.servlet.TldScanner.webxmlFailPathDoesNotExist=경로가 [{0}]이고 URI가 [{1}]인 TLD를 처리하지 못했습니다. 지정된 경로가 존재하지 않습니다.
org.apache.jasper.servlet.TldScanner.webxmlSkip=이미 <jsp-config>에 정의되었기 때문에, URI [{1}]을(를) 위한 TLD를, 리소스 경로 [{0}](으)로부터 로드하는 것을 건너뜁니다.

xmlParser.skipBomFail=XML 입력 스트림을 파싱할 때 BOM을 건너뛰지 못했습니다.
