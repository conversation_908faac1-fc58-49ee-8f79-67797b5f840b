# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

base64.impossibleModulus=不可能的模[{0}]
base64.inputTooLarge=输入数组太大，输出数组将比指定的最大大小[{1}]大[{0}]
base64.lineSeparator=行分隔符不能包含base64个字符[{0}]
base64.nullEncodeParameter=不能用空参数编码整数
