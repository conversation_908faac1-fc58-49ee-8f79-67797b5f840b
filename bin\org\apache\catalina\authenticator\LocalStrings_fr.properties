# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

authenticator.certificates=Aucune chaîne de certificat client (client certificate chain) dans cette requête
authenticator.changeSessionId=L''id de session a changé suite à l''authntification de [{0}] en [{1}]
authenticator.check.authorize=Le nom d''utilisateur [{0}] obtenu à partir du connecteur est considéré comme de valide et de confiance, les rôles sont obtenus à partir du royaume
authenticator.check.authorizeFail=Le royaume ne reconnait pas l''utilisateur [{0}], un principal a été crée avec ce nom mais sans rôles
authenticator.check.found=Déjà authentifié [{0}]
authenticator.check.sso=Pas d''authentification mais un session ID SSO [{0}] a été trouvé, nouvelle tentative d''authentification
authenticator.formlogin=Référence directe au formulaire de connexion (form login page) invalide
authenticator.jaspicCleanSubjectFail=Échec du nettoyage du sujet de JASPIC
authenticator.jaspicSecureResponseFail=Echec de la sécurisation de la réponse lors du traitement de JASPIC
authenticator.jaspicServerAuthContextFail=Échec d'obtention d'une instance JASPIC ServerAuthContext
authenticator.loginFail=Échec de connexion ("Login failed")
authenticator.manager=Exception lors de l'initialisation des gestionnaires d'authentification (trust managers)
authenticator.noAuthHeader=Aucun en-tête d'autorisation envoyé par le client
authenticator.notContext=Erreur de configuration :  Doit être attaché à un contexte
authenticator.requestBodyTooBig=Le corps de la requête était trop grand pour être mis en cache pendant le processus d'authentification
authenticator.sessionExpired=Le temps alloué au processus de login est échu.  Si vous désirez continuer, veuillez soit retourner en arrière 2 fois et recliquer le lien demandé, soit fermer et ré-ouvrir votre navigateur
authenticator.tomcatPrincipalLogoutFail=La déconnection avec l'instance de TomcatPrincipal a échoué
authenticator.unauthorized=Impossible d'authentifier avec les crédits fournis (provided credentials)

basicAuthenticator.invalidCharset=Les seules valeurs permises sont null, la chaîne vide, ou des caractères UTF-8

digestAuthenticator.cacheRemove=Une entrée valide du cache de nonce des clients a été enlevée pour faire de la place pour de nouvelles entrées, ce qui rend possible une attaque par répétition ; pour éviter cela, il est possible de reduire nonceValidity ou d'augmenter nonceCacheSize ; les avertissements de ce type ne se reproduiront pas avant 5 minutes

formAuthenticator.forwardErrorFail=Erreur inattendue lors de la transmission à la page d'erreur
formAuthenticator.forwardLogin=Transmission de la requête pour [{0}] faite avec la méthode [{1}] à la page de connection [{2}] du contexte [{3}] en utilisant la méthode GET
formAuthenticator.forwardLoginFail=Erreur inattendue lors de la transmission à la page de connection
formAuthenticator.noErrorPage=Aucune page d''erreur n''a été définie pour la méthode d''authentification FORM dans le contexte [{0}]
formAuthenticator.noLoginPage=Aucune page de connection n''a été définie pour la méthode d''authentification FORM dans le contexte [{0}]

singleSignOn.debug.associate=Association de la session [{1}] de l''application avec la session SSO [{0}]
singleSignOn.debug.associateFail=Le SSO n''a pu associer la session [{0}] de l''application car la session SSO [{1}] n''existe pas
singleSignOn.debug.cookieCheck=Le SSO recherche un cookie SSO.
singleSignOn.debug.cookieNotFound=Le SSO n'a pas trouvé de cookie SSO
singleSignOn.debug.deregister=Le SSO expire la session [{0}] de l''application associée à la session SSO [{1}]
singleSignOn.debug.deregisterFail=Le SSO n''a pu déenregistrer la session SSO [{0}] parce qu''elle n''est pas dans le cache
singleSignOn.debug.deregisterNone=Le SSO a désenregistré la session SSO [{0}] mais n''a trouvé aucune session d''application associée
singleSignOn.debug.hasPrincipal=Le SSO a trouvé un principal [{0}] précédemment authentifié
singleSignOn.debug.invoke=Le SSO traite la requête pour [{0}]
singleSignOn.debug.principalCheck=Le SSO recherche le Principal en cache pour la session SSO [{0}]
singleSignOn.debug.principalFound=Le SSO a trouvé en cache le Principal [{0}] avec le type d''authentification [{1}]
singleSignOn.debug.principalNotFound=Le SSO n''a pas trouvé de principal en cache, le cookie SSO de la session [{0}] est effacé
singleSignOn.debug.register=Enregistrement de la session SSO [{0}] pour l''utilisateur [{1}] avec le type d''authentification [{2}]
singleSignOn.debug.removeSession=Le SSO retire la session applicative [{0}] de la session SSO [{1}]
singleSignOn.debug.sessionLogout=Le SSO effectue une déconnection pour la session SSO [{0}] et la session [{1}] de l''application
singleSignOn.debug.sessionTimeout=Le SSO traite un timeout pour la session SSO [{0}] et la session [{1}] de l''application
singleSignOn.debug.update=Le SSO met à jour la session SSO [{0}] avec le type d''authentification [{1}]
singleSignOn.sessionExpire.contextNotFound=Le SSO n''a pu faire expirer la session [{0}] parce que le contexte n''a pas été trouvé
singleSignOn.sessionExpire.engineNull=Le SSO n''a pu faire expirer la session [{0}] parce que le moteur est null
singleSignOn.sessionExpire.hostNotFound=SSO ne peut pas faire expirer le session [{0}] parce que l''hôte ("Host") n''a pas été trouvé
singleSignOn.sessionExpire.managerError=Impossible d''expirer la session [{0}] parce que le Manager a lancé une exception lors de la recherche de la session
singleSignOn.sessionExpire.managerNotFound=Le SSO n''a pu faire expirer la session [{0}] parce que le gestionnaire de sessions n''a pas été trouvé
singleSignOn.sessionExpire.sessionNotFound=Impossible d''expirer la session [{0}] parce que la session n''a pas été trouvée

spnegoAuthenticator.authHeaderNoToken=L'en-tête de négociation d’autorisation ("Negotiate authorization header") envoyé par le client n'incluait pas de jeton ("token")
spnegoAuthenticator.authHeaderNotNego=L'en-tête d'autorisation envoyé par le client ne commence pas par Negotiate
spnegoAuthenticator.serviceLoginFail=Impossible de se connecteur en tant que principal de service
spnegoAuthenticator.ticketValidateFail=Impossible de valider le ticket fourni par le client
