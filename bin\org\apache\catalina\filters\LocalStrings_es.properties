# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

addDefaultCharset.unsupportedCharset=El conjunto especificado de caracteres [{0}] no se encuentra soportado

corsFilter.invalidPreflightMaxAge=Incapáz de procesar preflightMaxAge
corsFilter.nullRequestType=Objeto CORSRequestType es nulo\n

csrfPrevention.invalidRandomClass=No se puede crear fuente aleatórea usando la clase [{0}]

expiresFilter.exceptionProcessingParameter=Excepción al procesar parámetro de configuración [{0}]:[{1}]
expiresFilter.expirationHeaderAlreadyDefined=Ya se ha definido cabecera de expiración para el requerimiento [{0}] con status de respuesta [{1}] y content-type [{2}]
expiresFilter.filterInitialized=Filtro inicializado con configuración [{0}]
expiresFilter.noExpirationConfigured=No se ha configurado expiración para el requerimiento [{0}] con status de respuesta [{1}] y content-type [{2}]
expiresFilter.noExpirationConfiguredForContentType=No se ha hallado configuración de Expiración para content-type [{0}]
expiresFilter.responseAlreadyCommitted=El requerimiento [{0}], no puede aplicar ExpiresFilter en respuesta ya acometida.
expiresFilter.setExpirationDate=El requerimiento [{0}] con status de respuesta [{1}] y content-type [{2}], pone fecha de expiración a [{3}]
expiresFilter.skippedStatusCode=Generación de cabecera de expiración saltada para el requerimiento [{0}] con status de respuesta [{1}] y content-type [{2}]
expiresFilter.startingPointInvalid=Punto de arranque inválido (access|now|modification|a<seconds>|m<seconds>) [{0}] en la directiva [{1}]
expiresFilter.startingPointNotFound=Punto de Arranque (access|now|modification|a<seconds>|m<seconds>) no hallado en la directiva [{0}]
expiresFilter.unknownParameterIgnored=¡Se ignora el parámetro desconocido [{0}] con valor [{1}] especificado!
expiresFilter.unsupportedStartingPoint=startingPoint [{0}] no soportado
expiresFilter.useDefaultConfiguration=El uso de [{0}] por defecto para content-type [{1}] devuelve [{2}]
expiresFilter.useMatchingConfiguration=El Uso de [{0}] coincidente con [{1}] para content-type [{2}] devuelve [{3}]

filterbase.noSuchProperty=La propiedad [{0}] no está definida para los filtros del tipo [{1}]

http.403=El acceso al recurso especificado [{0}] ha sido prohibido.

httpHeaderSecurityFilter.clickjack.invalid=An invalid value [{0}] was specified for the anti click-jacking header

requestFilter.deny=Solicitud [{0}] denegada debido a propiedad [{1}]
