# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

contextBindings.noContextBoundToCL=没有绑定到此类加载器的命名上下文
contextBindings.noContextBoundToThread=没有绑定到此线程的命名上下文
contextBindings.unknownContext=未知.上下文名:[{0}]

namingContext.alreadyBound=名称[{0}]已在此上下文中绑定
namingContext.contextExpected=上下文Context未绑定名称name
namingContext.failResolvingReference=解析引用时意外异常
namingContext.invalidName=名称无效
namingContext.nameNotBound=名称[{0}]未在此上下文中绑定。找不到[{1}]。
namingContext.noAbsoluteName=无法为此命名空间生成绝对名称
namingContext.readOnly=上下文是只读的

selectorContext.methodUsingName=用[{1}]的name属性调用方法[{0}]
selectorContext.methodUsingString=使用字符串[{1}]调用方法[{0}]
selectorContext.noJavaUrl=必须通过java:url访问此上下文
