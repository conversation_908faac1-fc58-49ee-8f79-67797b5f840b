# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

base64.impossibleModulus=計算できない剰余 [{0}] です。
base64.inputTooLarge=入力配列が大きすぎます。出力配列は[{1}]の指定された最大サイズよりも大きくなります[{0}]。
base64.lineSeparator=行区切り記号にはbase64文字を使用できません[{0}]
base64.nullEncodeParameter=null 値を整数に符号化できませんでした。
