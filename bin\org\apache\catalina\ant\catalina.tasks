# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Pure catalina tasks
list=org.apache.catalina.ant.ListTask
deploy=org.apache.catalina.ant.DeployTask
start=org.apache.catalina.ant.StartTask
reload=org.apache.catalina.ant.ReloadTask
stop=org.apache.catalina.ant.StopTask
undeploy=org.apache.catalina.ant.UndeployTask
resources=org.apache.catalina.ant.ResourcesTask
sessions=org.apache.catalina.ant.SessionsTask
validator=org.apache.catalina.ant.ValidatorTask
findleaks=org.apache.catalina.ant.FindLeaksTask
vminfo=org.apache.catalina.ant.VminfoTask
threaddump=org.apache.catalina.ant.ThreaddumpTask
sslConnectorCiphers=org.apache.catalina.ant.SslConnectorCiphersTask

#Jk Task
jkupdate=org.apache.catalina.ant.JKStatusUpdateTask

# Manager JMX
jmxManagerSet=org.apache.catalina.ant.JMXSetTask
jmxManagerGet=org.apache.catalina.ant.JMXGetTask
jmxManagerQuery=org.apache.catalina.ant.JMXQueryTask

# Jasper tasks
jasper=org.apache.jasper.JspC
