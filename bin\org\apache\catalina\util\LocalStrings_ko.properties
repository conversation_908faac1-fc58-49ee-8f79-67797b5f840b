# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

SecurityUtil.doAsPrivilege=PrivilegedExceptionAction 블록을 실행하는 동안 예외가 발생했습니다.

customObjectInputStream.logRequired=로그를 남기면서 클래스 이름을 필터링 하기 위해서는 유효한 Logger가 필요합니다.
customObjectInputStream.nomatch=클래스 [{0}]은(는), 역직렬화 되도록 허용된 클래스들의 정규식 패턴 [{1}]과(와) 부합되지 않습니다.

extensionValidator.extension-not-found-error=ExtensionValidator[{0}][{1}] : 필수 extension [{2}]을(를) 찾을 수 없습니다.
extensionValidator.extension-validation-error=ExtensionValidator[{0}]: 필수 extension(들)을 찾는 과정에서 [{1}]번의 실패 발생
extensionValidator.failload=Extension [{0}]을(를) 로드하는 중 실패
extensionValidator.web-application-manifest=웹 애플리케이션 Manifest

introspection.classLoadFailed=클래스 [{0}]을(를) 로드하지 못했습니다.

lifecycleBase.alreadyDestroyed=이미 destroy() 메소드가 호출되었던 구성요소 [{0}]에서, 다시 destroy()가 호출되었습니다. 두번째 호출은 무시될 것입니다.
lifecycleBase.alreadyStarted=start()가 이미 호출된 후에, 구성요소 [{0}]에 start() 메소드가 호출되었습니다. 두번째 호출은 무시될 것입니다.
lifecycleBase.alreadyStopped=stop()이 이미 호출된 후에, 구성요소 [{0}]에 대해 stop() 메소드가 호출되었습니다. 두번째 호출은 무시될 것입니다.
lifecycleBase.destroyFail=구성요소 [{0}]을(를) 소멸시키지 못했습니다.
lifecycleBase.destroyStopFail=실패한 구성요소 [{0}]에 대해, clean-up을 개시하기 위해 stop()을 호출했으나, 이 호출 또한 실패했습니다.
lifecycleBase.initFail=구성요소 [{0}]을(를) 초기화하지 못했습니다.
lifecycleBase.invalidTransition=상태 [{2}]에 있는 구성 요소 [{1}]에 대해, 유효하지 않은 Lifecycle 전환이 시도되었습니다 ([{0}]).
lifecycleBase.setState=[{0}]을(를) 위한 상태를 [{1}](으)로 설정합니다.
lifecycleBase.startFail=구성요소 [{0}]을(를) 시작하지 못했습니다.
lifecycleBase.stopFail=구성요소 [{0}]을(를) 중지시키지 못했습니다.

lifecycleMBeanBase.registerFail=구성요소 초기화 중, [{1}](이)라는 이름을 가진 객체 [{0}]을(를) 등록하지 못했습니다.

netmask.cidrNegative=CIDR [{0}]이(가) 음수입니다.
netmask.cidrNotNumeric=CIDR [{0}]이(가) 숫자가 아닙니다.
netmask.cidrTooBig=CIDR [{0}]이(가) 주소 길이 [{1}]보다 큽니다.
netmask.invalidAddress=주소 [{0}]은(는) 유효하지 않습니다.
netmask.invalidPort=패턴 [{0}] 내의 포트 부분이 유효하지 않습니다.

parameterMap.locked=잠금 상태인 ParameterMap에 대한 변경이 허용되지 않습니다.

resourceSet.locked=잠금 상태인 ResourceSet에 대한 변경은 허용되지 않습니다.

sessionIdGeneratorBase.createRandom=[{0}] 알고리즘을 사용하여, 세션 ID를 생성하기 위한 SecureRandom 객체를 생성하는데, [{1}] 밀리초가 소요됐습니다.
sessionIdGeneratorBase.random=클래스 [{0}]의 난수 발생기를 초기화하는 중 예외 발생. java.secure.SecureRandom으로 대체합니다.
sessionIdGeneratorBase.randomAlgorithm=알고리즘 [{0}]을(를) 사용하여 난수 발생기를 초기화하는 중 오류 발생
sessionIdGeneratorBase.randomProvider=Provider [{0}]을(를) 사용하여, 난수 발생기를 초기화하는 중 예외 발생
