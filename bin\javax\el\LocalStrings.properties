# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

beanNameELResolver.beanReadOnly=The bean name [{0}] is read-only

elProcessor.defineFunctionInvalidClass=The class [{0}] is not public
elProcessor.defineFunctionInvalidMethod=The method [{0}] on class [{1}] is not a public static method
elProcessor.defineFunctionInvalidParameterList=The parameter list [{0}] for method [{1}] on class [{2}] is not valid
elProcessor.defineFunctionInvalidParameterTypeName=The parameter type [{0}] for method [{1}] on class [{2}] is not valid
elProcessor.defineFunctionNoMethod=A public static method [{0}] on class [{1}] could not be found
elProcessor.defineFunctionNullParams=One or more of the input parameters was null

expressionFactory.cannotCreate=Unable to create ExpressionFactory of type [{0}]
expressionFactory.cannotFind=Unable to find ExpressionFactory of type [{0}]
expressionFactory.readFailed=Failed to read [{0}]

importHandler.ambiguousImport=The class [{0}] could not be imported as it conflicts with [{1}] which has already been imported
importHandler.ambiguousStaticImport=The static import [{0}] could not be processed as it conflicts with [{1}] which has already been imported
importHandler.classNotFound=The class [{0}] could not be imported as it could not be found
importHandler.invalidClass=The class [{0}] must be public, in an exported package (for Java 9+), non-abstract and not an interface
importHandler.invalidClassName=Name of class to import [{0}] must include a package
importHandler.invalidClassNameForStatic=The class [{0}] specified for static import [{1}] is not valid
importHandler.invalidStaticName=Name of static method or field to import [{0}] must include a class
importHandler.staticNotFound=The static import [{0}] could not be found in class [{1}] for import [{2}]

lambdaExpression.tooFewArgs=Only [{0}] arguments were provided for a lambda expression that requires at least [{1}]

objectNotAssignable=Unable to add an object of type [{0}] to an array of objects of type [{1}]
propertyNotFound=Property [{1}] not found on type [{0}]
propertyNotReadable=Property [{1}] not readable on type [{0}]
propertyNotWritable=Property [{1}] not writable on type [{0}]
propertyReadError=Error reading [{1}] on type [{0}]
propertyWriteError=Error writing [{1}] on type [{0}]

staticFieldELResolver.methodNotFound=No matching public static method named [{0}] found on class [{1}]
staticFieldELResolver.notFound=No public static field named [{0}] was found on (exported for Java 9+) class [{1}]
staticFieldELResolver.notWriteable=Writing to static fields (in this case field [{0}] on class [{1}]) is not permitted

util.method.ambiguous=Unable to find unambiguous method: {0}.{1}({2})
util.method.notfound=Method not found: {0}.{1}({2})
