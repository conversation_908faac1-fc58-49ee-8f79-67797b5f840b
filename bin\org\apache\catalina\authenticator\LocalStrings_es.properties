# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

authenticator.certificates=No hay cadena de certificados del cliente en esta petición
authenticator.formlogin=Referencia directa al formulario de conexión (página de formulario de login) inválida
authenticator.jaspicCleanSubjectFail=Fallo al limpiar el elemento JASPIC \n
authenticator.jaspicServerAuthContextFail=Fallo al intentar obtener una instancia JASPIC ServerAuthContext
authenticator.loginFail=No pude ingresar
authenticator.manager=Excepción inicializando administradores de confianza
authenticator.noAuthHeader=El cliente no ha enviado autorización de cabecera
authenticator.notContext=Error de Configuración: Debe de estar unido a un Contexto
authenticator.requestBodyTooBig=El cuerpo del requerimiento era demasiado grande para realizar caché durante el proceso de autenticación
authenticator.sessionExpired=El tiempo permitido para realizar login ha sido excedido. Si deseas continuar, debes hacer clik dos veces y volver a hacer clik otra vez o cerrar y reabrir tu navegador
authenticator.unauthorized=Imposible autenticar mediante las credenciales suministradas

digestAuthenticator.cacheRemove=Se ha quitado una entrada válida de la caché "nonce" del cliente para hacer espacio a nuevas entradas.. Ahora es posible un ataque de reinyección. Para prevenirlos, reduce "nonceValidity" o incrementa "nonceCacheSize". El resto de mensajes de este tipo serán suspendidos durante 5 minutos.

formAuthenticator.forwardErrorFail=Error inesperado de reenvío a página de error
formAuthenticator.forwardLoginFail=Error inesperado de reenvío a pagina de ingreso
formAuthenticator.noErrorPage=No se ha definido página de error para la autenticación FORM en el contexto [{0}]
formAuthenticator.noLoginPage=No se ha definido página de ingreso para la autenticación FORM en el contexto [{0}]

singleSignOn.debug.principalCheck=SSO esta buscando un Principal cacheado para las sesión SSO [{0}]\n
singleSignOn.debug.principalFound=SSO encontró el Principal cacheado [{0}] con autenticación tipo [{1}]\n
singleSignOn.debug.removeSession=SSO removiendo la sesión de la aplicación [{0}]  SSO con sesión [{1}]\n
singleSignOn.sessionExpire.hostNotFound=SSO es incapaz de expirar la session  [{0}] porque el Host no puede ser encontrado
singleSignOn.sessionExpire.managerError=SSO incapaz de expirar sesión [{0}] porque el Gerenciador lanzó una excepción mientras buscaba la sesión

spnegoAuthenticator.authHeaderNoToken=La cabecera de Negociación de autorización enviada por el cliente no incluía una ficha
spnegoAuthenticator.authHeaderNotNego=La cabecera de autorización enviada por el cliente no comenzaba con Negotiate
spnegoAuthenticator.serviceLoginFail=No puedo ingresar como director del servicio
spnegoAuthenticator.ticketValidateFail=No pude validar el billete suministrado por el cliente
