# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

lookupFactory.createFailed=No se pudo crear una instancia de la clase de fábrica JNDI lookup\n
lookupFactory.typeMismatch=La referencia JNDI [{0}] se esperaba que fuera de tipo [{1}] pero la búsqueda  [{2}] devolvió un objeto tipo [{3}]

resourceLinkFactory.nullType=El enlace del recurso local [{0}] que se refiere al recurso global [{1}] no especifica el atributo obligatorio "type"
resourceLinkFactory.unknownType=El enlace del recurso local [{0}]  que apunta al recurso global especificado [{1}] de tipo desconocido [{2}]\n
