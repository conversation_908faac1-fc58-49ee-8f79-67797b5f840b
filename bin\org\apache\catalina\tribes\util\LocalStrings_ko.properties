# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

arrays.keyoffset.outOfBounds=keyoffset이 범위를 초과합니다.
arrays.length.outOfBounds=키 내에 충분한 데이터 엘리먼트들이 존재하지 않습니다. 길이가 범위 밖에 있습니다.
arrays.malformed.arrays=바이트 배열은 반드시 '{1,3,4,5,6}'과 같이 표현되어야 합니다.
arrays.srcoffset.outOfBounds=srcoffset이 범위 밖입니다.

executorFactory.not.running=Executor가 실행 중이 아닙니다. 명령을 강제로 큐에 넣을 수 없습니다.
executorFactory.queue.full=큐의 용량이 꽉 찼습니다.

uuidGenerator.createRandom=[{0}]을(를) 사용하여, UUID 생성을 위한 SecureRandom 인스턴스를 생성하는 데에, [{1}] 밀리초가 소요됐습니다.
uuidGenerator.unable.fit=[{0}] 바이트를 해당 배열에 맞출 수 없습니다. 길이:[{1}], 요구되는 길이:[{2}]
