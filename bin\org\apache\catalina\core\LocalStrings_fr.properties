# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

applicationContext.addFilter.ise=Des filtres ne peuvent plus être ajoutés au contexte [{0}] car il a déjà été initialisé
applicationContext.addJspFile.iae=Le fichier JSP [{0}] est invalide
applicationContext.addListener.iae.cnfe=Incapable de créer une instance de type [{0}]
applicationContext.addListener.iae.init=Impossible d''ajouter une instance du type [{0}] comme écouteur
applicationContext.addListener.iae.sclNotAllowed=Il est impossible d'ajouter un ServletContextListener après qu'un ait été appelé
applicationContext.addListener.iae.wrongType=Le type spécifié [{0}] n''est pas un type attendu de "listener"
applicationContext.addListener.ise=Des Listener ne peuvent plus être ajoutés au contexte [{0}] car il a déjà été initialisé
applicationContext.addRole.ise=Vous ne pouvez pas ajouter de rôles au contexte [{0}], parce qu''il a déjà été initialisé
applicationContext.addServlet.ise=Des Servlets ne peuvent plus être ajoutés au contexte [{0}] car il a déjà été initialisé
applicationContext.attributeEvent=Exception lancée par l'écouteur (listener) d'évènement attributs
applicationContext.illegalDispatchPath=La tentative d''obtenir un dispatcher de requêtes avec le chemin illégal [{0}] a été refusée car il contenait une tentative de traversée de répertoire encodée
applicationContext.invalidFilterName=Impossible d''ajouter la définition du filtre à cause d''un nom de filtre invalide [{0}]
applicationContext.invalidServletName=Impossible d''ajouter la définition de la servlet à cause de son nom invalide [{0}]
applicationContext.lookup.error=Impossible de trouver la ressource [{0}] dans le contexte [{1}]
applicationContext.mapping.error=Erreur lors du mapping
applicationContext.requestDispatcher.iae=Le chemin [{0}] ne commence pas par le caractère "/"
applicationContext.resourcePaths.iae=Le chemin [{0}] ne démarre pas avec un caractère "/"
applicationContext.role.iae=Un rôle individuel à déclarer pour le contexte [{0}] ne peut être null ou une chaîne vide
applicationContext.roles.iae=Le tableaux de rôles à déclarer pour le contexte [{0}] ne peut pas être null
applicationContext.setAttribute.namenull=le nom ne peut être nul
applicationContext.setInitParam.ise=les paramètres d''initialisation ne peuvent pas être configurés sur le contexte [{0}] car il a déjà été initialisé
applicationContext.setRequestEncoding.ise=L''encodage de la requête ne peut pas être configuré sur le contexte [{0}] car il a déjà été initialisé
applicationContext.setResponseEncoding.ise=L''encodage de la réponse ne peut pas être configuré sur le contexte [{0}] car il a déjà été initialisé
applicationContext.setSessionTimeout.ise=Le timeout de session ne peut pas être changé pour le contexte [{0}] car il a déjà été initialisé
applicationContext.setSessionTracking.iae.invalid=Le mode de suivi de la session [{0}] demandé par le contexte [{1}] n''est pas supporté par ce contexte
applicationContext.setSessionTracking.iae.ssl=Les modes de suivi de session pour le contexte [{0}]  incluent SSL et au moins un autre mode.  SSL ne peut pas être configuré avec d''autres modes.
applicationContext.setSessionTracking.ise=Les modes de suivi de session ("session tracking") du contexte [{0}] ne peuvent être définis pendant que le contexte est en cours d''exécution

applicationDispatcher.allocateException=Exception d''allocation pour la servlet [{0}]
applicationDispatcher.deallocateException=Exception de désallocation pour la servlet [{0}]
applicationDispatcher.forward.ise=Impossible d'utiliser faire-suivre (forward) après que la réponse ait été envoyée
applicationDispatcher.isUnavailable=La servlet [{0}] est actuellement indisponible
applicationDispatcher.serviceException="Servlet.service()" pour la servlet [{0}] a lancé une exception
applicationDispatcher.specViolation.request=La ServletRequest d'origine ou la ServletRequest d'origine enrobée n'a pas été passée au RequestDispatcher en violation de SRV.8.2 et SRV.14.2.5.1
applicationDispatcher.specViolation.response=La ServletResponse originale ou la ServletResponse enveloppée (wrapped) n'a pas été transmise au RequestDispatcher, en violation de SRV.8.2 et SRV.14.2.5.1

applicationFilterConfig.jmxRegisterFail=L''enregistrement JMX a échoué pour le filtre de type [{0}] et nommé [{0}]
applicationFilterConfig.jmxUnregister=Le désenregistrement JMX est terminé pour le filtre de type [{0}] nommé [{1}]
applicationFilterConfig.jmxUnregisterFail=Le désenregistrement JMX du filtre de type [{0}] nommé [{1}] a échoué
applicationFilterConfig.preDestroy=Echec lors de l''appel de preDestroy pour le filtre nommé [{0}] de type [{1}]
applicationFilterConfig.release=Impossible de détruite le filtre nommé [{0}] de type [{1}]

applicationFilterRegistration.nullInitParam=Impossible de fixer le paramètre d''initialisation du filtre, à cause d''un nom ou d''une valeur nulle, nom [{0}], valeur [{1}]
applicationFilterRegistration.nullInitParams=Impossible de fixer les paramètres d''initialisation du filtre, à cause d''un nom ou d''une valeur nulle, nom [{0}], valeur [{1}]

applicationHttpRequest.fragmentInDispatchPath=Le fragment dans le chemin de dispatch [{0}] a été enlevé

applicationPushBuilder.methodInvalid=La méthode HTTP pour une requête push doit être à la fois être sans danger et pouvoir être mise en cache, mais [{0}] ne correspond pas
applicationPushBuilder.methodNotToken=Les méthodes HTTP doivent être des "token", mais [{0}] contient un caractère invalide dans un token.

applicationServletRegistration.setServletSecurity.iae=Contrainte nulle spécifiée pour le Servlet [{0}] déployé dans le contexte avec le nom [{1}]
applicationServletRegistration.setServletSecurity.ise=Les contraintes de sécurité ne peuvent pas être ajoutées au Servlet [{0}] déployé dans le contexte [{1}] car le contexte a déjà été initialisé

applicationSessionCookieConfig.ise=La propriété [{0}] ne peut pas être ajoutée au SessionCookieConfig pour le contexte [{1}] car le contexte a déjà été initialisé

aprListener.aprDestroy=Echec de l'arrêt de la librairie Apache Tomcat Native basée sur APR
aprListener.aprInit=La librairie Apache Tomcat Native basée sur APR qui permet des performances optimales dans les environnements de production n''a pas été trouvée sur le java.library.path : [{0}]
aprListener.aprInitDebug=La librairie Apache Tomcat Native basée sur APR n''a pas été trouvée en utilisant les noms [{0}] dans le java.library.path [{1}], les erreurs retournées sont [{2}]
aprListener.aprInitError=La librairie Apache Tomcat Native basée sur APR n''a pas pu être chargée, l''erreur retournée est [{0}]
aprListener.config=Configuration de APR/OpenSSL : useAprConnector [{0}], useOpenSSL [{1}]
aprListener.currentFIPSMode=Mode FIPS actuel : [{0}]
aprListener.enterAlreadyInFIPSMode=AprLifecycleListener est configuré pour forcer le mode FIPS mais la librairie est déjà en mode FIPS [{0}]
aprListener.flags=Fonctionnalités d''APR : IPv6 [{0}], sendfile [{1}], accept filters [{2}], random [{3}]
aprListener.initializeFIPSFailed=Echec d'entrée en mode FIPS
aprListener.initializeFIPSSuccess=Entrée avec succès en mode FIPS
aprListener.initializedOpenSSL=OpenSSL a été initialisé avec succès [{0}]
aprListener.initializingFIPS=Initialisation du mode FIPS...
aprListener.requireNotInFIPSMode=AprLifecycleListener est configuré pour demander que la librarie soit déjà en mode FIPS et elle ne l'était pas
aprListener.skipFIPSInitialization=Déjà en mode FIPS, l'initialisation de FIPS n'est pas effectuée
aprListener.sslInit=Impossible d'initialiser le SSLEngine
aprListener.tcnInvalid=Une version incompatible [{0}] de la librairie Apache Tomcat Native basée sur APR est installée, alors que Tomcat nécessite la version [{1}]
aprListener.tcnValid=Chargement de la librairie Apache Tomcat Native [{0}] en utilisant APR version [{1}]
aprListener.tcnVersion=Un version ancienne [{0}] de la bibliothèque Apache Tomcat Native basée sur APR est installée, alors que Tomcat recommande au minimum la version [{1}]
aprListener.tooLateForFIPSMode=Ne peut pas passer en mode FIPS, SSL a déjà été initialisé
aprListener.tooLateForSSLEngine=Impossible d'appeler setSSEEngine, SSL a déjà été initialisé
aprListener.tooLateForSSLRandomSeed=setSSLRandomSeed impossible : SSL a déjà été initialisé
aprListener.wrongFIPSMode=Valuer inattendue de l''option FIPSMode de AprLifecycleListener : [{0}]

asyncContextImpl.asyncDispatchError=Erreur lors d'un dispatch asynchrone
asyncContextImpl.asyncRunnableError=Erreur lors du traitement asynchrone du Runnable via AsyncContext.start()
asyncContextImpl.dispatchingStarted=Une opération de dispatch asynchrone a déjà été appelée, plusieurs dispatch au cours d'un même cycle asynchrone n'est pas autorisé
asyncContextImpl.fireOnComplete=Déclenchement de l'évènement onComplete() sur tous les AsyncListeners
asyncContextImpl.fireOnError=Déclenchement de l'évènement onError() sur tous les AsyncListeners
asyncContextImpl.fireOnStartAsync=Déclenchement de l'évènement onStartAsync() sur tous les AsyncListeners
asyncContextImpl.fireOnTimeout=Déclenchement de l'évènement onTimeout() sur tous les AsyncListeners
asyncContextImpl.noAsyncDispatcher=Le Servlet dispatcher retourné par le ServletContext ne supporte pas de dispatch asynchrone
asyncContextImpl.onCompleteError=L''appel à onComplete() a échoué pour l''écouteur de type [{0}]
asyncContextImpl.onErrorError=L''appel à onError() a échoué pour l''écouteur de type [{0}]
asyncContextImpl.onStartAsyncError=L''appel à onStartAsync() a échoué pour l''écouteur de type [{0}]
asyncContextImpl.onTimeoutError=L''appel à onTimeout() a échoué pour l''écouteur de type [{0}]
asyncContextImpl.request.ise=Il est illégal d'appeler getRequest() après que complete() ou une autre des méthodes dispatch() ait été appelé
asyncContextImpl.requestEnded=La requête associée avec l'AsyncContext est déjà terminée
asyncContextImpl.response.ise=Il est illégal d'appeler getResponse() après que complete() ou n'importe laquelle des méthodes de dispatch a été appelée

containerBase.backgroundProcess.cluster=Une exception s''est produite lors du traitement d''arrière plan du cluster [{0}]
containerBase.backgroundProcess.realm=Exception lors du traitement d''arrière plan du realm [{0}]
containerBase.backgroundProcess.unexpectedThreadDeath=Mort inattendue du fil d''exécution d''arrière-plan ("background thread") [{0}]
containerBase.backgroundProcess.valve=Exception lors du traitement d''arrière plan de la valve [{0}]
containerBase.nullName=Le nom d'un conteneur ne peut être null
containerBase.threadedStartFailed=Un conteneur fils a échoué pendant son démarrage
containerBase.threadedStopFailed=Erreur lors de l'arrêt d'un conteneur fils

defaultInstanceManager.invalidInjection=Annotation invalide pour l'injection d'une resource méthode
defaultInstanceManager.restrictedClass=L''accès à la classe à accès restreint [{0}] est interdit, une application web doit être marquée comme étant privilégiée pour pouvoir la charger
defaultInstanceManager.restrictedContainerServlet=L''accès à la classe [{0}] est interdit. C''est une classe restreinte qui (implémente l''interface ContainerServlet). Une application web doit être privilégiée pour pouvoir la charger.
defaultInstanceManager.restrictedFiltersResource=Le fichier de propriétés contenant la liste des filtres restreints n''a pas été trouvée [{0}]
defaultInstanceManager.restrictedListenersResource=Le fichier de propriétés concernant les écouteurs à accès restreint n''a pas été trouvé [{0}]
defaultInstanceManager.restrictedServletsResource=Le fichier de propriétés contenant la liste des Servlets restreints n''a pas été trouvée [{0}]
defaultInstanceManager.restrictedWrongValue=Mauvaise valeur dans le fichier de propriété [{0}] contenant la liste des classes à accès restreint pour le nom de classe [{1}], valeur attendue : [restricted], valeur donnée : [{2}]

filterChain.filter=L'exécution du filtre (Filter) a lancé une exception
filterChain.servlet=L'exécution de la servlet a lancé une exception

jreLeakListener.authPolicyFail=Erreur en essayant de prévenir une fuite de mémoire dans la classe javax.security.auth.Policy
jreLeakListener.classToInitializeFail=Echec du chargement de la classe [{0}] pendant le démarrage de Tomcat, effectué pour empêcher de possibles fuites de mémoire
jreLeakListener.gcDaemonFail=Echec de la création du thread GC Daemon durant le démarrage de Tomcat pour éviter une fuite de mémoire, cela est normal sur toutes les JVMs non Oracle
jreLeakListener.jarUrlConnCacheFail=Échec de la désactivation du cache par défaut des URL de connexion de JAR
jreLeakListener.ldapPoolManagerFail=Echec de la création de la classe com.sun.jndi.ldap.LdapPoolManager durant le démarrage de Tomcat pour éviter une fuite de mémoire, cela est normal sur toutes les JVMs non Oracle
jreLeakListener.xmlParseFail=Erreur en essayant de prévenir une fuite de mémoire lors du traitement de contenu XML

naming.addEnvEntry=Ajout de l''entrée d''environnement [{0}]
naming.addResourceEnvRef=Ajout de la référence de ressource d''environnement [{0}]
naming.bindFailed=Echec lors du liage à l''objet : [{0}]
naming.invalidEnvEntryType=L''entrée environnement [{0}] a un type invalide
naming.invalidEnvEntryValue=L''entrée environnement [{0}] a une valeur invalide
naming.jmxRegistrationFailed=Echec d''enregistrement dans JMX : [{0}]
naming.namingContextCreationFailed=La création du contexte de nommage (naming context) a échoué : [{0}]
naming.unbindFailed=Echec lors du déliage à l''objet : [{0}]
naming.wsdlFailed=fichier wsdl [{0}] non trouvé

noPluggabilityServletContext.notAllowed=La section 4.4 de la spécification Servlet 3.0 ne permet pas à cette méthode d'être appelée à partir d'un ServletContextListener qui n'a pas été déclaré dans web.xml, un web-fragment.xml, ou annoté avec @WebListener

pushBuilder.noPath=Il est interdit d'appeler push() avant de fixer un chemin

standardContext.applicationListener=Erreur lors de la configuration de la classe d''écoute de l''application (application listener) [{0}]
standardContext.applicationSkipped=L'installation des écouteurs (listeners) de l'application a été sautée suite aux erreurs précédentes
standardContext.backgroundProcess.instanceManager=Exception lors du traitement d''arrière plan du gestionnaire d''instances [{0}]
standardContext.backgroundProcess.loader=Exception lors du traitement d''arrière plan du loader [{0}]
standardContext.backgroundProcess.manager=Exception lors du traitement d''arrière plan du gestionnaire de sessions [{0}]
standardContext.backgroundProcess.resources=Exception lors du traitement d''arrière plan des ressources [{0}]
standardContext.cluster.noManager=Aucun gestionnaire de session trouvé, vérification de l''utilisation éventuelle du gestionnaire de session fourni par le cluster ; cluster configuré : [{0}], application distribuable : [{1}]
standardContext.configurationFail=Un ou plusieurs composants ont marqué ce contexte comme n'étant pas correctement configuré
standardContext.cookieProcessor.null=Il est interdit de mettre un CookieProcessor null pour un contexte
standardContext.duplicateListener=L''écouteur [{0}] est déjà configuré pour le contexte, la double déclaration a été ignorée
standardContext.errorPage.error=La position de la page d''erreur (ErrorPage) [{0}] doit commencer par un ''/''
standardContext.errorPage.required=La page d'erreur (ErrorPage) ne peut être nulle
standardContext.errorPage.warning=WARNING : La position de la page d''erreur (ErrorPage) [{0}] doit commencer par un ''/'' dans l''API Servlet 2.4
standardContext.extensionValidationError=Erreur lors de la validation des extensions requises par l'application
standardContext.filterFail=Un ou plusieurs filtres n'ont pas pu démarrer, les détails sont dans le fichier log du conteneur
standardContext.filterMap.either=L'association de filtre (filter mapping) doit indiquer soit une <url-pattern> soit une <servlet-name>
standardContext.filterMap.name=L''association de filtre (filter mapping) indique un nom de filtre inconnu [{0}]
standardContext.filterMap.pattern=<url-pattern> [{0}] invalide dans l''association de filtre (filter mapping)
standardContext.filterStart=Exception au démarrage du filtre [{0}]
standardContext.invalidWrapperClass=[{0}] n''est pas une sous-classe de StandardWrapper
standardContext.isUnavailable=Cette application n'est pas disponible actuellement
standardContext.listenerFail=Un ou plusieurs écouteurs n'ont pas pu démarrer, les détails sont dans le fichier de log du conteneur
standardContext.listenerStart=Exception lors de l''envoi de l''évènement contexte initialisé (context initialized) à l''instance de classe d''écoute (listener) [{0}]
standardContext.listenerStop=Exception lors de l''envoi de l''évènement contexte détruit (context destroyed) à l''instance de classe d''écoute [{0}]
standardContext.loadOnStartup.loadException=Le Servlet [{1}] dans l''application web [{0}] a retourné une exception lors de son chargement
standardContext.loginConfig.errorPage=La page d''erreur de Form [{0}] doit commencer par un ''/''
standardContext.loginConfig.errorWarning=La page d''erreur de Form [{0}] doit commencer par un ''/'' dans l''API Servlet 2.4
standardContext.loginConfig.loginPage=La page de connexion du formulaire [{0}] doit commencer par un ''/''
standardContext.loginConfig.loginWarning=WARNING : La page de connexion du formulaire [{0}] doit commencer par un ''/'' dans l''API Servlet 2.4
standardContext.loginConfig.required="LoginConfig" ne peut être nul
standardContext.manager=Configuré un gestionnaire de la classe [{0}]
standardContext.managerFail=Echec lors du démarrage du gestionnaire de sessions
standardContext.namingResource.destroy.fail=Echec de destruction des anciennes ressources JNDI
standardContext.namingResource.init.fail=Echec d'initialisation des nouvelles ressources JNDI
standardContext.notStarted=Le contexte [{0}] n''a pas encore été démarré
standardContext.notWrapper=Le fils du contexte (child of context) doit être un enrobeur (wrapper)
standardContext.parameter.duplicate=Paramètre d''initialisation de contexte dupliqué [{0}]
standardContext.parameter.required=Le nom de paramètre ainsi que la valeur du paramètre sont requis
standardContext.pathInvalid=Un chemin de contexte doit être soit une chaîne vide soit commencer par un ''/'' et ne pas finir par un ''/'', le chemin [{0}] ne répond pas à ces conditions et a été changé en [{1}]
standardContext.postconstruct.duplicate=La méthode post construct est définie en double dans la classe [{0}]
standardContext.postconstruct.required=A la fois le nom complet qualifié et le nom de la méthode sont requis
standardContext.predestroy.duplicate=Définition dupliquée de la méthode de destruction "pre" pour la classe [{0}]
standardContext.predestroy.required=Les noms qualifiés de la classe et de la méthode sont tous deux requis
standardContext.reloadingCompleted=Le rechargement de ce contexte est terminé
standardContext.reloadingStarted=Le rechargement du contexte [{0}] a démarré
standardContext.requestListener.requestInit=Une exception lors de l''envoi de requête a initié un évènement cycle de vie (lifecycle event) pour l''instance de classe à l''écoute (listener) [{0}]
standardContext.resourcesInit=Erreur d'initialisation des ressources statiques
standardContext.resourcesStart=Erreur lors du démarrage des ressources statiques
standardContext.resourcesStop=Erreur lors de l'arrêt des ressources statiques
standardContext.sciFail=Erreur lors du traitement de ServletContainerInitializer
standardContext.securityConstraint.mixHttpMethod=Il n'est pas permis de combiner <http-method> et <http-method-omission> dans la même collection de ressources web
standardContext.securityConstraint.pattern=<url-pattern> [{0}] invalide d''après les contraintes de sécurité (security constraint)
standardContext.servletFail=Un ou plusieurs Servlets n'ont pas pu démarrer, les détails sont dans le fichier log du conteneur
standardContext.servletMap.name=L''association de servlet (servlet mapping) indique un nom de servlet inconnu [{0}]
standardContext.servletMap.pattern=<url-pattern> [{0}] invalide dans l''association de servlet (servlet mapping)
standardContext.startFailed=Erreur de démarrage du contexte [{0}] suite aux erreurs précédentes
standardContext.startingContext=Exception lors du démarrage du contexte [{0}]
standardContext.stop.asyncWaitInterrupted=Une interruption a été reçue en attendant unloadDelay millisecondes pour permettre aux requêtes asynchrones en cours de se terminer, l'arrêt du contexte va se poursuivre sans délai supplémentaire
standardContext.stoppingContext=Exception à l''arrêt du Context [{0}]
standardContext.threadBindingListenerError=Une erreur s''est produite dans l''écouteur de l’''association de thread configuré pour le contexte [{0}]
standardContext.urlPattern.patternWarning=WARNING : Le modèle (pattern) d''URL [{0}] doit commencer par un ''/'' dans l''API Servlet 2.4
standardContext.webappClassLoader.missingProperty=Impossible de fixer la propriété [{0}] du chargeur de classes de l''application web à [{1}] car cette propriété n''existe pas
standardContext.workCreateException=Impossible de déterminer le chemin absolu pour le répertoire de travail à partir du répertoire [{0}] et de CATALINA_HOME [{1}] pour le contexte [{2}]
standardContext.workCreateFail=Impossible de créer le répertoire de travail [{0}] pour le contexte [{1}]
standardContext.workPath=Impossible d''obtenir le chemin de travail pour le contexte [{0}]

standardContextValve.acknowledgeException=Impossible de confirmer la requête avec une réponse 100 (continuer)

standardEngine.jvmRouteFail=Impossible de fixer la jvmRoute de l'Engine à partir d'une propriété système
standardEngine.notHost=Le fils d'un moteur (child of an Engine) doit être un hôte
standardEngine.notParent=Un moteur (engine) ne peut avoir de conteneur parent (container)

standardHost.clientAbort=Le client distant a abandonné la requête, IOException : [{0}]
standardHost.invalidErrorReportValveClass=Impossible de charger la classe valve de rapport d''erreur : [{0}]
standardHost.noContext=Aucun contexte n'est configuré pour traiter cette requête
standardHost.notContext=Le fils d'un hôte (child of a Host) doit être un contexte
standardHost.nullName=Le nom d'hôte est requis
standardHost.problematicAppBase=Utiliser une chaîne vide pour l''appBase de l''hôte [{0}] la fera correspondre à CATALINA_BASE, ce qui causera des problèmes

standardHostValue.customStatusFailed=La page d''erreur personnalisée [{0}] n''a pu être redirigée correctement

standardPipeline.basic.start=Erreur lors du démarrage de la nouvelle valve de base
standardPipeline.basic.stop=Erreur lors de l'arrêt de l'ancienne valve de base
standardPipeline.valve.destroy=Erreur lors de la destruction de la valve
standardPipeline.valve.start=Erreur lors du démarrage de la valve
standardPipeline.valve.stop=Erreur lors de l'arrêt de la valve

standardServer.accept.error=Une erreur d'IO s'est produite en essayant d'accepter sur le socket qui attend la commande d'arrêt
standardServer.accept.readError=Une erreur d'IO s'est produite lors de la lecture de la commande d'arrêt
standardServer.accept.security=Une erreur de sécurité s'est produite en essayant d'accepter sur le socket qui attend la commande d'arrêt
standardServer.accept.timeout=Le socket qui écoute en attendant la commande d''arrêt a rencontré un délai d''attente dépassé inattendu [{0}] millisecondes après l''appel à accept()
standardServer.invalidShutdownCommand=Une commande d''arrêt invalide [{0}] a été reçue
standardServer.shutdownViaPort=Une commande d'arrêt valide a été reçue sur le port d'arrêt, arrêt de l'instance du serveur
standardServer.storeConfig.contextError=Erreur lors de l''enregistrement de la configuration du contexte [{0}]
standardServer.storeConfig.error=Erreur lors de l'enregistrement de la configuration du serveur
standardServer.storeConfig.notAvailable=Aucune implémentation de StoreConfig n''a été enregistrée comme un MBean nommé [{0}], et aucune configuration n''a donc été enregistrée. Un MBean adéquat est normalement référencé via le StoreConfigLifecycleListener

standardService.engine.startFailed=Impossible de démarrer l'Engine associé
standardService.engine.stopFailed=Echec de l'arrêt du moteur associé
standardService.mapperListener.startFailed=Impossible de démarrer le MapperListener associé
standardService.mapperListener.stopFailed=Impossible d'arrêter le MapperListener associé
standardService.start.name=Démarrage du service [{0}]
standardService.stop.name=Arrêt du service [{0}]

standardWrapper.allocate=Erreur d'allocation à une instance de servlet
standardWrapper.allocateException=Exception lors de l''allocation pour la servlet [{0}]
standardWrapper.deallocateException=Exception à la désallocation pour la servlet [{0}]
standardWrapper.destroyException="Servlet.destroy()" de la servlet [{0}] a généré une exception
standardWrapper.destroyInstance=InstanceManager.destroy() pour le Servlet [{0}] a renvoyé une exception
standardWrapper.initException="Servlet.init()" pour la servlet [{0}] a généré une exception
standardWrapper.instantiate=Erreur à l''instantiation de la classe servlet [{0}]
standardWrapper.isUnavailable=La servlet [{0}] est actuellement indisponible
standardWrapper.notChild=L'enrobeur de conteneur (wrapper container) ne peut pas avoir de conteneurs fils
standardWrapper.notClass=Aucune classe servlet n''a été spécifiée pour la servlet [{0}]
standardWrapper.notContext=Le conteneur parent d'un enrobeur (wrapper) doit être un contexte
standardWrapper.notFound=Servlet [{0}] n''est pas disponible.
standardWrapper.notServlet=La classe [{0}] n''est pas une servlet
standardWrapper.serviceException="Servlet.service()" pour la servlet [{0}] a généré une exception
standardWrapper.serviceExceptionRoot=Servlet.service() du Servlet [{0}] dans le contexte au chemin [{1}] a retourné une exception [{2}] avec la cause
standardWrapper.unavailable=La servlet [{0}] est marqué comme indisponible
standardWrapper.unloadException=La servlet [{0}] a généré une exception "unload()"
standardWrapper.unloading=Impossible d''allouer la servlet [{0}] car elle a été déchargée
standardWrapper.waiting=Attente de la désallocation de [{0}] instance(s) du Servlet [{1}]

threadLocalLeakPreventionListener.containerEvent.error=Exception lors du traitement de l''évènement du conteneur [{0}]
threadLocalLeakPreventionListener.lifecycleEvent.error=Exception lors du traitement de l''évènement [{0}] du cycle de vie du composant
