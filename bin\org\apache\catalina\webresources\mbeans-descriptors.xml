<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE mbeans-descriptors PUBLIC
   "-//Apache Software Foundation//DTD Model MBeans Configuration File"
   "http://jakarta.apache.org/commons/dtds/mbeans-descriptors.dtd">
<mbeans-descriptors>

  <mbean         name="StandardRoot"
          description="Provides the resources implementation for a web application"
               domain="Catalina"
                group="WebResourceRoot"
                 type="org.apache.catalina.webresources.StandardRoot">

    <attribute   name="allowLinking"
          description="Does this resources implementation allow the use of symbolic links?"
                 type="boolean"
            writeable="true"/>

    <attribute   name="cachingAllowed"
          description="Is in memory caching of resource content and metadata enabled?"
                 type="boolean"
                   is="true"
            writeable="true"/>

    <attribute   name="stateName"
          description="The current Lifecycle state of this object"
                 type="java.lang.String"
            writeable="false"/>

    <attribute   name="trackLockedFiles"
          description="Does this resources implementation track requests that lock files?"
                 type="boolean"
            writeable="true"/>

    <attribute   name="trackedResources"
          description="List of resources currently being tracked for possible resource leaks"
                 type="java.util.List"
            writeable="false"/>

  </mbean>

  <mbean         name="Cache"
          description="Provides caching of resource metadata and content"
               domain="Catalina"
                group="WebResourceRoot"
                 type="org.apache.catalina.webresources.Cache">

    <attribute   name="hitCount"
          description="The number of requests for resources that were served from the cache"
                 type="long"
            writeable="false"/>

    <attribute   name="lookupCount"
          description="The number of requests for resources"
                 type="long"
            writeable="false"/>

    <attribute   name="maxSize"
          description="The maximum permitted size of the cache in kB"
                 type="long"
            writeable="true"/>

    <attribute   name="objectMaxSize"
          description="The maximum permitted size for a single object in the cache in kB"
                 type="int"
            writeable="true"/>

    <attribute   name="size"
          description="The current estimate of the cache size in kB"
                 type="long"
            writeable="false"/>

    <attribute   name="ttl"
          description="The time-to-live for cache entries in milliseconds"
                 type="long"
            writeable="true"/>

    <operation name="clear"
        description="Clears all cached content from the cache."
             impact="ACTION"
         returnType="void">
    </operation>

  </mbean>

</mbeans-descriptors>
