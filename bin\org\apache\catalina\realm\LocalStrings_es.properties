# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

combinedRealm.addRealm=Añadir reino [{0}], totalizando [{1}] reinos
combinedRealm.authFail=No pude autenticar al usuario [{0}] con el reino [{1}]
combinedRealm.authStart=Intentando autentica al usuario [{0}] con el reino [{1}]
combinedRealm.authSuccess=Usuario autenticado [{0}] con reino [{1}]
combinedRealm.getPassword=No se debería de llamar nunca al método getPassword()
combinedRealm.getPrincipal=No se debería de llamar nunca al método getPrincipal()
combinedRealm.realmStartFail=No pude arrancar reino [{0}]
combinedRealm.unexpectedMethod=Una llamada inesperada se realizó a un método del reino combinado

dataSourceRealm.authenticateFailure=Nombre de usuario [{0}] NO autenticado con éxito
dataSourceRealm.authenticateSuccess=Nombre de usuario [{0}] autenticado con éxito
dataSourceRealm.close=Excepción cerrando conexión a base de datos
dataSourceRealm.exception=Excepción realizando autenticación
dataSourceRealm.getPassword.exception=Excepción recuperando contraseña para [{0}]
dataSourceRealm.getRoles.exception=Excepción recuperando papeles para [{0}]

jaasCallback.username=Devuelto nombre de usuario [{0}]

jaasRealm.accountExpired=El usuario [{0}] NO ha sido autentificado porque ha expirado su cuenta
jaasRealm.authenticateFailure=Nombre de usuario [{0}] NO autenticado con éxito
jaasRealm.authenticateSuccess=Nombre de usuario [{0}] autenticado con éxito como Principal [{1}] -- También se ha creado el Asunto
jaasRealm.beginLogin=JAASRealm login requerido para nombre de usuario [{0}] usando LoginContext para aplicación [{1}]
jaasRealm.checkPrincipal=Revisando Principal [{0}] [{1}]
jaasRealm.credentialExpired=El usuario [{0}] NO ha sido autentificado porque ha expirado su credencial
jaasRealm.failedLogin=El usuario [{0}] NO ha sido autentificado porque ha fallado el login
jaasRealm.loginContextCreated=JAAS LoginContext creado para nombre de usuario [{0}]
jaasRealm.loginException=Excepción de Login autenticando nombre de usuario [{0}]
jaasRealm.rolePrincipalAdd=Añadiend papel Principal [{0}] a los papeles de este usuario Principal
jaasRealm.rolePrincipalFailure=No se han hallado papeles de Principal válidos.
jaasRealm.unexpectedError=Error inesperado
jaasRealm.userPrincipalFailure=No se ha hallado usuario Principal
jaasRealm.userPrincipalSuccess=El Principal [{0}] es una clase válida de usuario. La vamos a usar como usuario Principal.

jdbcRealm.authenticateFailure=El usuario [{0}] NO ha sido autentificado correctamente
jdbcRealm.authenticateSuccess=El usuario [{0}] ha sido autentificado correctamente
jdbcRealm.close=Excepción al cerrar la conexión a la base de datos
jdbcRealm.exception=Excepción al realizar la autentificación
jdbcRealm.open=Excepción abriendo la conexión a la base de datos
jdbcRealm.open.invalidurl=El conductor [{0}] no soporta la url [{1}]

jndiRealm.authenticateFailure=Autentificación fallida para el usuario [{0}]
jndiRealm.authenticateSuccess=Autentificación correcta para el usuario [{0}]
jndiRealm.close=Excepción al cerrar la conexión al servidor de directorio
jndiRealm.exception=Excepción al realizar la autentificación
jndiRealm.open=Excepción al abrir la conexión al servidor de directorio

lockOutRealm.authLockedUser=Se ha intentado autenticar al usuario bloqueado [{0}]
lockOutRealm.removeWarning=Se ha quitado al usuario [{0}] de la caché de usuarios fallidos tras [{1}] secgundos para mantener la medida de caché dentro de los límites

memoryRealm.authenticateFailure=Autentificación fallida para el usuario [{0}]
memoryRealm.authenticateSuccess=Autentificación correcta para el usuario [{0}]
memoryRealm.loadExist=El fichero de usuarios [{0}] no ha podido ser leído
memoryRealm.loadPath=Cargando los usuarios desde el fichero [{0}]
memoryRealm.readXml=Excepción mientras se leía el fichero de usuarios
memoryRealm.xmlFeatureEncoding=Excepción al configurar resumidor para permitir nombres con codificación java en ficheros XML. Sóllo se soportan nombres con codificación IANA.

realmBase.algorithm=El algoritmo resumen (digest) [{0}] es inválido
realmBase.authenticateFailure=Nombre de usuario [{0}] NO autenticado con éxito
realmBase.authenticateSuccess=Nombre de usuario [{0}] autenticado con éxito
realmBase.createUsernameRetriever.ClassCastException=La clase [{0}] no es una X509UsernameRetriever.
realmBase.delegatedCredentialFail=No pude obtener credenciales de delegado para el usuario [{0}]
realmBase.digest=Error procesando las credenciales del usuario
realmBase.forbidden=El acceso al recurso pedido ha sido denegado
realmBase.gssNameFail=No pude extraer el nombre desde el GSSContext establecido
realmBase.hasRoleFailure=El usuario [{0}] NO desempeña el papel de [{1}]
realmBase.hasRoleSuccess=El usuario [{0}] desempeña el papel de [{1}]

userDatabaseRealm.lookup=Excepción buscando en Base de datos de Usuario mediante la clave [{0}]
userDatabaseRealm.noDatabase=No se ha hallado componente de Base de datos de Usuario mediante la clave [{0}]
