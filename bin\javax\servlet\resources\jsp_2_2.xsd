<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns="http://www.w3.org/2001/XMLSchema"
            targetNamespace="http://java.sun.com/xml/ns/javaee"
            xmlns:javaee="http://java.sun.com/xml/ns/javaee"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            elementFormDefault="qualified"
            attributeFormDefault="unqualified"
            version="2.2">
  <xsd:annotation>
    <xsd:documentation>

      DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

      Copyright 2003-2009 Sun Microsystems, Inc. All rights reserved.

      The contents of this file are subject to the terms of either the
      GNU General Public License Version 2 only ("GPL") or the Common
      Development and Distribution License("CDDL") (collectively, the
      "License").  You may not use this file except in compliance with
      the License. You can obtain a copy of the License at
      https://glassfish.dev.java.net/public/CDDL+GPL.html or
      glassfish/bootstrap/legal/LICENSE.txt.  See the License for the
      specific language governing permissions and limitations under the
      License.

      When distributing the software, include this License Header
      Notice in each file and include the License file at
      glassfish/bootstrap/legal/LICENSE.txt.  Sun designates this
      particular file as subject to the "Classpath" exception as
      provided by Sun in the GPL Version 2 section of the License file
      that accompanied this code.  If applicable, add the following
      below the License Header, with the fields enclosed by brackets []
      replaced by your own identifying information:
      "Portions Copyrighted [year] [name of copyright owner]"

      Contributor(s):

      If you wish your version of this file to be governed by only the
      CDDL or only the GPL Version 2, indicate your decision by adding
      "[Contributor] elects to include this software in this
      distribution under the [CDDL or GPL Version 2] license."  If you
      don't indicate a single choice of license, a recipient has the
      option to distribute your version of this file under either the
      CDDL, the GPL Version 2 or to extend the choice of license to its
      licensees as provided above.  However, if you add GPL Version 2
      code and therefore, elected the GPL Version 2 license, then the
      option applies only if the new code is made subject to such
      option by the copyright holder.

    </xsd:documentation>
  </xsd:annotation>

  <xsd:annotation>
    <xsd:documentation>
      The Apache Software Foundation elects to include this software under the
      CDDL license.
    </xsd:documentation>
  </xsd:annotation>

  <xsd:annotation>
    <xsd:documentation>

      This is the XML Schema for the JSP 2.2 deployment descriptor
      types.  The JSP 2.2 schema contains all the special
      structures and datatypes that are necessary to use JSP files
      from a web application.

      The contents of this schema is used by the web-common_3_0.xsd
      file to define JSP specific content.

    </xsd:documentation>
  </xsd:annotation>

  <xsd:annotation>
    <xsd:documentation>

      The following conventions apply to all Java EE
      deployment descriptor elements unless indicated otherwise.

      - In elements that specify a pathname to a file within the
      same JAR file, relative filenames (i.e., those not
      starting with "/") are considered relative to the root of
      the JAR file's namespace.  Absolute filenames (i.e., those
      starting with "/") also specify names in the root of the
      JAR file's namespace.  In general, relative names are
      preferred.  The exception is .war files where absolute
      names are preferred for consistency with the Servlet API.

    </xsd:documentation>
  </xsd:annotation>

  <xsd:include schemaLocation="javaee_6.xsd"/>


<!-- **************************************************** -->

  <xsd:complexType name="jsp-configType">
    <xsd:annotation>
      <xsd:documentation>

        The jsp-configType is used to provide global configuration
        information for the JSP files in a web application. It has
        two subelements, taglib and jsp-property-group.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="taglib"
                   type="javaee:taglibType"
                   minOccurs="0"
                   maxOccurs="unbounded"/>
      <xsd:element name="jsp-property-group"
                   type="javaee:jsp-property-groupType"
                   minOccurs="0"
                   maxOccurs="unbounded"/>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="jsp-fileType">
    <xsd:annotation>
      <xsd:documentation>

        The jsp-file element contains the full path to a JSP file
        within the web application beginning with a `/'.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="javaee:pathType"/>
    </xsd:simpleContent>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="jsp-property-groupType">
    <xsd:annotation>
      <xsd:documentation>

        The jsp-property-groupType is used to group a number of
        files so they can be given global property information.
        All files so described are deemed to be JSP files.  The
        following additional properties can be described:

        - Control whether EL is ignored.
        - Control whether scripting elements are invalid.
        - Indicate pageEncoding information.
        - Indicate that a resource is a JSP document (XML).
        - Prelude and Coda automatic includes.
        - Control whether the character sequence #{ is allowed
        when used as a String literal.
        - Control whether template text containing only
        whitespaces must be removed from the response output.
        - Indicate the default contentType information.
        - Indicate the default buffering model for JspWriter
        - Control whether error should be raised for the use of
        undeclared namespaces in a JSP page.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:group ref="javaee:descriptionGroup"/>
      <xsd:element name="url-pattern"
                   type="javaee:url-patternType"
                   maxOccurs="unbounded"/>
      <xsd:element name="el-ignored"
                   type="javaee:true-falseType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            Can be used to easily set the isELIgnored
            property of a group of JSP pages.  By default, the
            EL evaluation is enabled for Web Applications using
            a Servlet 2.4 or greater web.xml, and disabled
            otherwise.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="page-encoding"
                   type="javaee:string"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            The valid values of page-encoding are those of the
            pageEncoding page directive.  It is a
            translation-time error to name different encodings
            in the pageEncoding attribute of the page directive
            of a JSP page and in a JSP configuration element
            matching the page.  It is also a translation-time
            error to name different encodings in the prolog
            or text declaration of a document in XML syntax and
            in a JSP configuration element matching the document.
            It is legal to name the same encoding through
            multiple mechanisms.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="scripting-invalid"
                   type="javaee:true-falseType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            Can be used to easily disable scripting in a
            group of JSP pages.  By default, scripting is
            enabled.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="is-xml"
                   type="javaee:true-falseType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            If true, denotes that the group of resources
            that match the URL pattern are JSP documents,
            and thus must be interpreted as XML documents.
            If false, the resources are assumed to not
            be JSP documents, unless there is another
            property group that indicates otherwise.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="include-prelude"
                   type="javaee:pathType"
                   minOccurs="0"
                   maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation>

            The include-prelude element is a context-relative
            path that must correspond to an element in the
            Web Application.  When the element is present,
            the given path will be automatically included (as
            in an include directive) at the beginning of each
            JSP page in this jsp-property-group.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="include-coda"
                   type="javaee:pathType"
                   minOccurs="0"
                   maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation>

            The include-coda element is a context-relative
            path that must correspond to an element in the
            Web Application.  When the element is present,
            the given path will be automatically included (as
            in an include directive) at the end of each
            JSP page in this jsp-property-group.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="deferred-syntax-allowed-as-literal"
                   type="javaee:true-falseType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            The character sequence #{ is reserved for EL expressions.
            Consequently, a translation error occurs if the #{
            character sequence is used as a String literal, unless
            this element is enabled (true). Disabled (false) by
            default.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="trim-directive-whitespaces"
                   type="javaee:true-falseType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            Indicates that template text containing only whitespaces
            must be removed from the response output. It has no
            effect on JSP documents (XML syntax). Disabled (false)
            by default.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="default-content-type"
                   type="javaee:string"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            The valid values of default-content-type are those of the
            contentType page directive.  It specifies the default
            response contentType if the page directive does not include
            a contentType attribute.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="buffer"
                   type="javaee:string"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            The valid values of buffer are those of the
            buffer page directive.  It specifies if buffering should be
            used for the output to response, and if so, the size of the
            buffer to use.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="error-on-undeclared-namespace"
                   type="javaee:true-falseType"
                   minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>

            The default behavior when a tag with unknown namespace is used
            in a JSP page (regular syntax) is to silently ignore it.  If
            set to true, then an error must be raised during the translation
            time when an undeclared tag is used in a JSP page.  Disabled
            (false) by default.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>


<!-- **************************************************** -->

  <xsd:complexType name="taglibType">
    <xsd:annotation>
      <xsd:documentation>

        The taglibType defines the syntax for declaring in
        the deployment descriptor that a tag library is
        available to the application.  This can be done
        to override implicit map entries from TLD files and
        from the container.

      </xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="taglib-uri"
                   type="javaee:string">
        <xsd:annotation>
          <xsd:documentation>

            A taglib-uri element describes a URI identifying a
            tag library used in the web application.  The body
            of the taglib-uri element may be either an
            absolute URI specification, or a relative URI.
            There should be no entries in web.xml with the
            same taglib-uri value.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="taglib-location"
                   type="javaee:pathType">
        <xsd:annotation>
          <xsd:documentation>

            the taglib-location element contains the location
            (as a resource relative to the root of the web
            application) where to find the Tag Library
            Description file for the tag library.

          </xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
    <xsd:attribute name="id"
                   type="xsd:ID"/>
  </xsd:complexType>

</xsd:schema>


