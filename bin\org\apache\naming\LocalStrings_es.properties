# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

contextBindings.noContextBoundToCL=No hay contexto de nombres asociado a este cargador de clase
contextBindings.noContextBoundToThread=No hay contexto de nombres asociado a este hilo
contextBindings.unknownContext=Contexto [{0}] desconocido

namingContext.alreadyBound=El nombre [{0}] este ya asociado en este Contexto
namingContext.contextExpected=El nombre no esta asociado a ningun Contexto
namingContext.failResolvingReference=Excepción inesperada resolviendo referencia
namingContext.invalidName=Nombre no valido
namingContext.nameNotBound=El nombre [{0}] no este asociado a este contexto
namingContext.noAbsoluteName=No se puede generar un nombre absoluto para este espacio de nombres
namingContext.readOnly=El contexto es de solo lectura

selectorContext.methodUsingName=Llamada al método [{0}] con un Nombre de [{1}]
selectorContext.methodUsingString=Llamada al método [{0}] con una Cadena de [{1}]
selectorContext.noJavaUrl=Este contexto debe de ser accedido a traves de una URL de tipo java:
