# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

hostManagerServlet.add=add: Добавление сервера [{0}]
hostManagerServlet.addFailed=Ошибка - Не удалось добавить сервер [{0}]
hostManagerServlet.addSuccess=OK - Сервер [{0}] добавлен
hostManagerServlet.alreadyHost=Ошибка - Уже есть сервер с таким именем [{0}]
hostManagerServlet.alreadyStarted=Ошибка - Сервер [{0}] уже запущен
hostManagerServlet.alreadyStopped=Ошибка - Сервер [{0}] уже остановлен
hostManagerServlet.appBaseCreateFail=Ошибка - Не удалось создать директорию для приложений (appBase) [{0}] для сервера [{1}]
hostManagerServlet.cannotRemoveOwnHost=Ошибка - Нельзя удалить свой собственный сервер [{0}]
hostManagerServlet.cannotStartOwnHost=Ошибка - Нельзя запустить свой собственный сервер [{0}]
hostManagerServlet.cannotStopOwnHost=Ошибка - Нельзя остановить свой собственный сервер {0}
hostManagerServlet.configBaseCreateFail=Ошибка - Не удалось определить директорию с файлами конфигурации (configBase) для сервера [{0}]
hostManagerServlet.exception=Ошибка - Необычная ситуация [{0}]
hostManagerServlet.invalidHostName=Ошибка - Указано недопустимое имя сервера [{0}]
hostManagerServlet.list=list: Список серверов для движка [{0}]
hostManagerServlet.listed=OK - Список серверов
hostManagerServlet.managerXml=Ошибка - Не удалось создать файл manager.xml
hostManagerServlet.noCommand=Ошибка - Команда не указана.
hostManagerServlet.noHost=Ошибка - Сервер с указанным именем [{0}] не существует
hostManagerServlet.noWrapper=Метод setWrapper() у данного сервлета ещё не был вызван контейнером
hostManagerServlet.persist=persist: Сохранение текущей конфигурации
hostManagerServlet.persistFailed=Ошибка - Не удалось сохранить конфигурацию
hostManagerServlet.persisted=OK - Конфигурация сохранена
hostManagerServlet.postCommand=Ошибка - Команда [{0}] была подана при помощи запроса GET, но требуется POST
hostManagerServlet.remove=remove: Удаление сервера [{0}]
hostManagerServlet.removeFailed=Ошибка - Не удалось удалить сервер [{0}]
hostManagerServlet.removeSuccess=OK - Сервер удалён [{0}]
hostManagerServlet.start=start: Запуск сервера с именем [{0}]
hostManagerServlet.startFailed=Ошибка - Не удалось запустить сервер [{0}]
hostManagerServlet.started=OK - Сервер [{0}] запущен
hostManagerServlet.stop=stop: Остановка сервера с именем [{0}]
hostManagerServlet.stopFailed=Ошибка - Не удалось остановить сервер [{0}]
hostManagerServlet.stopped=OK - Сервер [{0}] остановлен
hostManagerServlet.unknownCommand=Ошибка - Неизвестная команда [{0}]

htmlHostManagerServlet.addAliases=Псевдонимы:
htmlHostManagerServlet.addAppBase=Директория для приложений (appBase):
htmlHostManagerServlet.addAutoDeploy=Автоматическое развёртывание (AutoDeploy)
htmlHostManagerServlet.addButton=Добавить
htmlHostManagerServlet.addCopyXML=Включить опцию CopyXML
htmlHostManagerServlet.addDeployOnStartup=Развёртывание при старте сервера (DeployOnStartup)
htmlHostManagerServlet.addDeployXML=Включить опцию DeployXML
htmlHostManagerServlet.addHost=Сервер
htmlHostManagerServlet.addManager=Установить приложение Manager
htmlHostManagerServlet.addName=Имя:
htmlHostManagerServlet.addTitle=Добавить виртуальный сервер
htmlHostManagerServlet.addUnpackWARs=Включить опцию UnpackWARs
htmlHostManagerServlet.helpHtmlManager=Справка для пользователей приложения
htmlHostManagerServlet.helpHtmlManagerFile=../docs/html-host-manager-howto.html
htmlHostManagerServlet.helpManager=Справка по API приложения
htmlHostManagerServlet.helpManagerFile=../docs/host-manager-howto.html
htmlHostManagerServlet.hostAliases=Псевдонимы сервера
htmlHostManagerServlet.hostName=Имя сервера
htmlHostManagerServlet.hostTasks=Управление
htmlHostManagerServlet.hostThis=Приложение Host Manager установлено здесь - команды управления не поддерживаются
htmlHostManagerServlet.hostsRemove=Удалить
htmlHostManagerServlet.hostsStart=Старт
htmlHostManagerServlet.hostsStop=Стоп
htmlHostManagerServlet.list=Список виртуальных серверов
htmlHostManagerServlet.manager=Управление cервером
htmlHostManagerServlet.messageLabel=Сообщение:
htmlHostManagerServlet.persistAll=Сохраняет текущие настройки (включая виртуальные хосты) в файл server.xml и в файлы context.xml для веб-приложений
htmlHostManagerServlet.persistAllButton=Сохранить всё
htmlHostManagerServlet.persistTitle=Сохранить настройки
htmlHostManagerServlet.serverJVMVendor=Поставщик JVM
htmlHostManagerServlet.serverJVMVersion=Версия JVM
htmlHostManagerServlet.serverOSArch=Архитектура ОС
htmlHostManagerServlet.serverOSName=ОС
htmlHostManagerServlet.serverOSVersion=Версия ОС
htmlHostManagerServlet.serverTitle=Информация о сервере
htmlHostManagerServlet.serverVersion=Версия Tomcat
htmlHostManagerServlet.title=Управление виртуальными серверами Tomcat

statusServlet.complete=Подробный отчёт о состоянии
statusServlet.title=Состояние сервера
