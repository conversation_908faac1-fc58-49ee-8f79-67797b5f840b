# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jarScan.classloaderFail=Failed to scan [{0}] from classloader hierarchy
jarScan.classloaderJarNoScan=Not performing JAR scanning on file [{0}] from classpath
jarScan.classloaderJarScan=Scanning JAR [{0}] from classpath
jarScan.classloaderStart=Scanning for JARs in classloader hierarchy
jarScan.jarUrlStart=Scanning JAR at URL [{0}]
jarScan.webinfclassesFail=Failed to scan /WEB-INF/classes
jarScan.webinflibFail=Failed to scan JAR [{0}] from /WEB-INF/lib
jarScan.webinflibJarNoScan=Not performing JAR scanning on file [{0}] from /WEB-INF/lib
jarScan.webinflibJarScan=Scanning JAR [{0}] from /WEB-INF/lib
jarScan.webinflibStart=Scanning /WEB-INF/lib for JARs
