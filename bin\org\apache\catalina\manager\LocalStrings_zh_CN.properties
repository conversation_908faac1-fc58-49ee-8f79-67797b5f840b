# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

htmlManagerServlet.appsAvailable=运行中
htmlManagerServlet.appsExpire=过期会话
htmlManagerServlet.appsName=显示.名称
htmlManagerServlet.appsPath=路径
htmlManagerServlet.appsReload=重新加载
htmlManagerServlet.appsSessions=会话
htmlManagerServlet.appsStart=启动
htmlManagerServlet.appsStop=停止
htmlManagerServlet.appsTasks=命令
htmlManagerServlet.appsTitle=应用程序
htmlManagerServlet.appsUndeploy=卸载
htmlManagerServlet.appsVersion=版本号
htmlManagerServlet.configReloadButton=重复读
htmlManagerServlet.configSslHostName=TLS 主机名字（可选)
htmlManagerServlet.configSslReloadTitle=重新读取 TLS 配置文件
htmlManagerServlet.configTitle=配置
htmlManagerServlet.connectorStateAliveSocketCount=存活套接字总数：
htmlManagerServlet.connectorStateBytesReceived=收到字节：
htmlManagerServlet.connectorStateBytesSent=发送.字节:
htmlManagerServlet.connectorStateErrorCount=错误数：
htmlManagerServlet.connectorStateHint=P：解析和准备request S：服务 F：结束 R：就绪 K：存活
htmlManagerServlet.connectorStateMaxProcessingTime=最大处理时间：
htmlManagerServlet.connectorStateMaxThreads=最大线程：
htmlManagerServlet.connectorStateProcessingTime=处理时间：
htmlManagerServlet.connectorStateRequestCount=请求总数：
htmlManagerServlet.connectorStateTableTitleBRecv=接收字节
htmlManagerServlet.connectorStateTableTitleBSent=发送字节：
htmlManagerServlet.connectorStateTableTitleClientAct=客户端（实际）
htmlManagerServlet.connectorStateTableTitleClientForw=客户端（转发）
htmlManagerServlet.connectorStateTableTitleRequest=请求
htmlManagerServlet.connectorStateTableTitleStage=阶段
htmlManagerServlet.connectorStateTableTitleTime=时间
htmlManagerServlet.connectorStateTableTitleVHost=虚拟主机
htmlManagerServlet.connectorStateThreadBusy=当前线程繁忙：
htmlManagerServlet.connectorStateThreadCount=当前线程数：
htmlManagerServlet.deployButton=部署
htmlManagerServlet.deployConfig=XML配置文件路径：
htmlManagerServlet.deployServer=服务器上.部署的目录或WAR文件
htmlManagerServlet.deployTitle=部署
htmlManagerServlet.deployUpload=要部署的WAR文件
htmlManagerServlet.deployUploadFail=失败 - 部署上传失败，异常信息：[{0}]
htmlManagerServlet.deployUploadFile=选择要上传的WAR文件
htmlManagerServlet.deployUploadInServerXml=失败 - 如果context是定义在server.xml中，War文件[{0}]无法上传
htmlManagerServlet.deployUploadNoFile=失败 - 文件上传失败，没有文件
htmlManagerServlet.deployUploadNotWar=失败 - 上传的文件[{0}]必须是一个.war文件
htmlManagerServlet.deployUploadWarExists=失败 - War文件 [{0}] 已存在于服务器上
htmlManagerServlet.deployWar=WAR文件或文件夹路径：
htmlManagerServlet.diagnosticsLeak=检查Web应用程序是否在停止、重新加载或取消部署时导致内存泄漏
htmlManagerServlet.diagnosticsLeakButton=发现泄漏
htmlManagerServlet.diagnosticsLeakWarning=诊断检查将触发完整的垃圾收集，在生产系统中使用时要格外小心。
htmlManagerServlet.diagnosticsSsl=TLS连接器配置诊断
htmlManagerServlet.diagnosticsSslConnectorCertsButton=证书
htmlManagerServlet.diagnosticsSslConnectorCertsText=列出已配置的TLS虚拟主机以及各自的证书链
htmlManagerServlet.diagnosticsSslConnectorCipherButton=密.码
htmlManagerServlet.diagnosticsSslConnectorCipherText=列出每个配置好的TLS虚拟主机和密码。
htmlManagerServlet.diagnosticsSslConnectorTrustedCertsButton=认证证书
htmlManagerServlet.diagnosticsSslConnectorTrustedCertsText=列出每个配置好的TLS虚拟主机和认证证书。
htmlManagerServlet.diagnosticsTitle=诊断
htmlManagerServlet.expire.explain=闲置 &ge;
htmlManagerServlet.expire.unit=分钟
htmlManagerServlet.findleaksList=以下web应用程序已停止（重新加载、卸载），但是它们的类仍然加载在内存中，因此导致了内存泄漏（使用分析器确认）：
htmlManagerServlet.findleaksNone=似乎没有任何Web应用程序在停止、重新加载或取消部署时触发内存泄漏。
htmlManagerServlet.helpHtmlManager=HTML管理器帮助
htmlManagerServlet.helpHtmlManagerFile=../docs/html-manager-howto.html
htmlManagerServlet.helpManager=管理者帮助
htmlManagerServlet.helpManagerFile=../docs/manager-howto.html
htmlManagerServlet.jvmFreeMemory=剩余内存：
htmlManagerServlet.jvmMaxMemory=最大内存
htmlManagerServlet.jvmTableTitleInitial=初始化
htmlManagerServlet.jvmTableTitleMaximum=最大.值
htmlManagerServlet.jvmTableTitleMemoryPool=内存.池
htmlManagerServlet.jvmTableTitleTotal=总共
htmlManagerServlet.jvmTableTitleType=类型
htmlManagerServlet.jvmTableTitleUsed=已用
htmlManagerServlet.jvmTotalMemory=总内存：
htmlManagerServlet.list=应用程序列表
htmlManagerServlet.manager=管理器
htmlManagerServlet.messageLabel=消息：
htmlManagerServlet.noManager=-
htmlManagerServlet.noVersion=未指定
htmlManagerServlet.osAvailableMemory=可用内存：
htmlManagerServlet.osFreePageFile=可用页文件：
htmlManagerServlet.osKernelTime=内核处理时间：
htmlManagerServlet.osMemoryLoad=加载内存：
htmlManagerServlet.osPhysicalMemory=物理内存
htmlManagerServlet.osTotalPageFile=最大页文件：
htmlManagerServlet.osUserTime=用户态处理实际：
htmlManagerServlet.serverHostname=主机名
htmlManagerServlet.serverIPAddress=IP地址
htmlManagerServlet.serverJVMVendor=JVM提供商
htmlManagerServlet.serverJVMVersion=JVM.版本
htmlManagerServlet.serverOSArch=操作系统架构
htmlManagerServlet.serverOSName=OS.名称
htmlManagerServlet.serverOSVersion=操作系统版本
htmlManagerServlet.serverTitle=服务器信息
htmlManagerServlet.serverVersion=Tomcat.版本
htmlManagerServlet.title=Tomcat Web应用程序管理者

jmxProxyServlet.noBeanFound=无法找到名为[{0}]的MBean对象
jmxProxyServlet.noOperationOnBean=在对象名为{2}的操作{0}中找不到参数为{1}]的操作{0}，该操作是{3}

managerServlet.alreadyContext=失败 - 应用程序已存在于路径 [{0}]
managerServlet.certsNotAvailable=在运行期，无法从连接器中获取认证信息
managerServlet.deleteFail=失败 - 不能删除[{0}]。这个文件一直存在会出现问题。
managerServlet.deployFailed=FAIL - 在上下文路径[{0}]下部署应用失败
managerServlet.deployed=OK - 以应用path [{0}] 部署应用
managerServlet.deployedButNotStarted=失败 - 在上下文[{0}]下部署应用程序，但是上下文启动失败。
managerServlet.exception=失败 - 发生异常[{0}]
managerServlet.findleaksFail=失败 - 查找泄漏失败：主机非StandardHost示例
managerServlet.findleaksList=OK - 在以下应用程序中发现潜在的内存泄漏
managerServlet.findleaksNone=OK - 没有发现内存泄漏
managerServlet.inService=失败 - 应用程序[{0}]已服务
managerServlet.invalidCommand=失败 - 无效参数，命令[{0}]
managerServlet.invalidPath=失败 - 指定了无效到上下文路径[{0}]
managerServlet.listed=OK - 虚拟主机[{0}]下的应用程序列表
managerServlet.mkdirFail=失败 - 无法创建目录 [{0}]
managerServlet.noCommand=失败 - 未指定命令
managerServlet.noContext=失败 - 上下文环境[{0}]不存在
managerServlet.noGlobal=失败 - 没有可用的全局 JNDI 资源
managerServlet.noManager=失败 - 路径[{0}]下无管理器存在
managerServlet.noSelf=失败 - 管理器不能重新加载、卸载、停止或者卸载它自身
managerServlet.noWrapper=容器未为当前servlet调用setWrapper()
managerServlet.notDeployed=失败 - 上下文[{0}]定义在server.xml中且可能未卸载
managerServlet.notSslConnector=不允许SSL连接
managerServlet.objectNameFail=FAIL - 不能将为Manager Servlet 注册 object name  [{0}]
managerServlet.postCommand=失败 - 尝试通过GET请求使用命令[{0}]，但需要POST
managerServlet.reloaded=OK - 上下文路径[{0}]重新加载应用程序
managerServlet.renameFail=失败 - 无法重命名[{0}]为[{1}]，这可能这未来部署时出现问题。
managerServlet.resourcesAll=OK - 列出所有类型的全部资源
managerServlet.resourcesType=OK - [{0}]类型全局资源列入清单
managerServlet.saveFail=失败 - 配置保存失败：[{0}]
managerServlet.saved=OK - 服务器配置已保存
managerServlet.savedContext=OK - 上下文[{0}]配置已保存
managerServlet.savedContextFail=失败 - 上下文[{0}]配置保存失败
managerServlet.serverInfo=OK - 服务器信息\n\
Tomcat版本: [{0}]\n\
操作系统名称: [{1}]\n\
操作系统版本: [{2}]\n\
操作系统架构: [{3}]\n\
JVM版本: [{4}]\n\
JVM提供商: [{5}]
managerServlet.sessiondefaultmax=最大会话时间失活间隔是[{0}]分钟
managerServlet.sessions=OK - 上下文路径[{0}]的会话信息
managerServlet.sessiontimeout=失活[{0}]分钟：[{1}]会话
managerServlet.sessiontimeout.expired=失活[{0}]分钟：[{1}]会话过期
managerServlet.sessiontimeout.unlimited=无限制到时间：[{0}]会话
managerServlet.sslConnectorCerts=OK - 连接器/认证链信息
managerServlet.sslConnectorCiphers=OK - Connector/SSL 密码.信息
managerServlet.sslConnectorTrustedCerts=OK - 连接器/认证证书信息
managerServlet.sslReload=OK - 为[{0}]重新加载TLS配置
managerServlet.sslReloadAll=OK - 未所有TLS虚拟主机重新加载TLS配置
managerServlet.sslReloadFail=FAIL - 重新加载TLS配制失败
managerServlet.startFailed=失败 - 上下文路径[{0}]下，应用程序无法启动
managerServlet.started=OK - 在上下文路径[{0}]下启动应用程序
managerServlet.stopped=OK - 上下文路径[{0}]下停止应用程序
managerServlet.storeConfig.noMBean=失败 - [{0}]未发现注册的StoreConfig MBean，注册一般是由StoreConfigLifecycleListener执行的。
managerServlet.threaddump=OK - JVM线程转储
managerServlet.trustedCertsNotConfigured=没有为此虚拟主机配置受信任的证书
managerServlet.undeployed=OK - 未部署的应用位于上下文路径[{0}]
managerServlet.unknownCommand=FAIL - 未知命令 [{0}]
managerServlet.vminfo=OK - VM信息

statusServlet.complete=完整的服务器状态
statusServlet.title=服务器状态
