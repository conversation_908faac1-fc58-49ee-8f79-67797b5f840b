# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jasper.error.emptybodycontent.nonempty=Согласно TLD, тег [{0}] должен быть пустым, но он не пустой

jsp.error.attribute.deferredmix=Невозможно использовать одновременно ${} и #{} EL выражения в значении атрибута
jsp.error.attribute.noequal=Необходим символ равенства
jsp.error.data.file.processing=Ошибка при обработке файла [{0}]
jsp.error.el.template.deferred=#{...}  не допускается в тексте шаблона
jsp.error.fallback.invalidUse=jsp:fallback должен быть прямым потомком jsp:plugin
jsp.error.invalid.tagdir=Директория файла тегов [{0}]  не начинается с "/WEB-INF/tags"
jsp.error.jspbody.required=Для указания тела тега для [{0}] должно быть использовано jsp: body если используется jsp: attribute.
jsp.error.jspelement.missing.name=Обязательный атрибут 'name' в XML-стиле отсутствует
jsp.error.outputfolder=Не указана выходная папка
jsp.error.parse.xml=XML файл [{0}] содержит ошибки
jsp.error.tld.mandatory.element.missing=Обязательный  TLD элемент [{0}] отсутствует или пуст в TLD [{1}]
jsp.error.unable.renameClassFile=не возможно переименовать файл класса с [{0}]  в [{1}]
jsp.error.unknown_attribute_type=Неизвестный тип [{1}] для атрибута [{0}]
jsp.exception=Произошла ошибка при обработке [{0}] в строке [{1}]

jspc.webxml.footer=</web-app>
