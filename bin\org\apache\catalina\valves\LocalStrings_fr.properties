# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

accessLogValve.alreadyExists=Échec de renommage du journal d''accès de [{0}] en [{1}], le fichier existe déjà.
accessLogValve.closeFail=Échec de fermeture du fichier de journal d'accès
accessLogValve.deleteFail=Impossible d''effacer l''ancien journal d''accès [{0}]
accessLogValve.invalidLocale=Impossible de définir les paramètres régionaux sur [{0}]
accessLogValve.invalidPortType=Type de port [{0}] invalide, utilisation du port (local) de serveur
accessLogValve.invalidRemoteAddressType=Le type [{0}] d''adresse distante est invalide, l''adresse distante classique sera utilisée et non celle du pair
accessLogValve.openDirFail=Echec de création du répertoire [{0}] pour les journaux d''accès
accessLogValve.openFail=Echec à l''ouverture du journal d''accès [{0}]
accessLogValve.renameFail=Échec de renommage du journal d''accès de [{0}] en [{1}]
accessLogValve.rotateFail=Échec de rotation des journaux d'accès
accessLogValve.unsupportedEncoding=Impossible de changer l''encodage en [{0}], le jeu de caractères par défaut du système sera utilisé
accessLogValve.writeFail=Impossible d''écrire le message de log [{0}]

errorReportValve.description=description
errorReportValve.exception=exception
errorReportValve.exceptionReport=Rapport d'exception
errorReportValve.message=message
errorReportValve.noDescription=Pas de description disponible
errorReportValve.note=note
errorReportValve.rootCause=cause mère
errorReportValve.rootCauseInLogs=La trace complète de la cause mère de cette erreur est disponible dans les fichiers journaux de ce serveur.
errorReportValve.statusHeader=État HTTP {0} – {1}
errorReportValve.statusReport=Rapport d'état
errorReportValve.type=Type
errorReportValve.unknownReason=Raison inconnue.

extendedAccessLogValve.badXParam=Le format du paramètre étendu est invalide, il doit être de la forme  'x-#(...)'
extendedAccessLogValve.badXParamValue=La valeur du paramètre étendu est invalide pour la requête de Servlet [{0}]
extendedAccessLogValve.decodeError=Impossible de décoder les caractères restants à partir de [{0}]
extendedAccessLogValve.emptyPattern=Le modèle est vide
extendedAccessLogValve.noClosing=Une parenthèse de fermeture n'a pas été trouvée lors du décodage
extendedAccessLogValve.patternParseError=Erreur lors de l''analyse du modèle [{0}]

http.400.desc=La requête envoyée par le client était syntaxiquement incorrecte.
http.400.reason=Requête invalide
http.401.desc=La requête nécessite une authentification HTTP.
http.401.reason=Non authorisé
http.402.desc=Un paiement est demandé pour accéder à cette ressource.
http.402.reason=Paiement requis
http.403.desc=L'accès à la ressource demandée a été interdit.
http.403.reason=Interdit
http.404.desc=La ressource demandée n'est pas disponible.
http.404.reason=Non trouvé
http.405.desc=La méthode HTTP spécifiée n'est pas autorisée pour la ressource demandée.
http.405.reason=Méthode non autorisée
http.406.desc=La ressource identifiée par cette requête n'est capable de générer des réponses qu'avec des caractéristiques incompatible avec la directive "accept" présente dans l'entête de requête.
http.406.reason=Inacceptable
http.407.desc=Le client doit d'abord s'authentifier auprès du relais.
http.407.reason=Authentification Proxy est requise
http.408.desc=Le client n'a pas produit de requête pendant le temps d'attente du serveur.
http.408.reason=Timeout de la requête
http.409.desc=La requête ne peut être finalisée suite à un conflit lié à l'état de la ressource.
http.409.reason=Conflit
http.410.desc=La ressource demandée n'est pas disponible, et aucune adresse de rebond (forwarding) n'est connue.
http.410.reason=Disparu
http.411.desc=La requête ne peut être traitée sans définition d'une taille de contenu (content length).
http.411.reason=Une longueur est requise
http.412.desc=Une condition préalable demandée n'est pas satisfaite pour cette requête.
http.412.reason=Erreur dans la pré-condition
http.413.desc=L'entité de requête est plus importante que ce que le serveur veut ou peut traiter.
http.413.reason=Les données sont trop grandes
http.414.desc=Le serveur a refusé cette requête car l'URI de requête est trop longue.
http.414.reason=L'URI est trop longue
http.415.desc=Le serveur a refusé cette requête car l'entité de requête est dans un format non supporté par la ressource demandée avec la méthode spécifiée.
http.415.reason=Type de média non supporté
http.416.desc=La plage d'octets demandée (byte range) ne peut être satisfaite.
http.416.reason=Plage non réalisable
http.417.desc=L'attente indiquée dans la directive "Expect" de l'entête de requête ne peut être satisfaite.
http.417.reason=L'expectation a échouée
http.421.desc=La requête a été dirigée vers un serveur qui est incapable de produire une réponse.
http.421.reason=Requête mal dirigée
http.422.desc=Le serveur a compris le type de contenu (content type) ainsi que la syntaxe de la requête mais a été incapable de traiter les instructions contenues.
http.422.reason=Impossible de traiter cette entité
http.423.desc=La ressource source ou destination de la méthode est verrouillée.
http.423.reason=Verrouillé
http.424.desc=La méthode n'a pas pu être exécutée sur la ressource parce qu'elle dépendait d'une autre action qui a échouée
http.424.reason=Echec de dépendence
http.426.desc=Le serveur a refusé de traiter cette requête en utilisant le protocole actuel mais pourrait le faire si le client en utilise un autre
http.426.reason=Mise à jour du protocole requise
http.428.desc=Le serveur d'origine exige que la requête soit conditionnelle
http.428.reason=Précondition requise
http.429.desc=L'utilisateur a effectué une nombre de requêtes trop élevé dans un laps de temps trop court (limitation de fréquence)
http.429.reason=Trop de requêtes
http.431.desc=Le serveur refuse de traiter la requête parce que ses champs d'en-tête sont trop gros
http.431.reason=Les champs d'en-tête de la requête sont trop gros
http.451.desc=Le serveur a refusé cette requête pour des raisons légales
http.451.reason=Indisponible pour des raisons légales
http.500.desc=Le serveur a rencontré une erreur interne qui l'a empêché de satisfaire la requête.
http.500.reason=Erreur interne du serveur
http.501.desc=Le serveur ne supporte pas la fonctionnalité demandée pour satisfaire cette requête.
http.501.reason=Non implémentée
http.502.desc=Le serveur a reçu une réponse invalide d'un serveur qu'il consultait en tant que relais ou passerelle.
http.502.reason=Mauvaise passerelle
http.503.desc=Le service demandé n'est pas disponible actuellement.
http.503.reason=Service indisponible
http.504.desc=Le serveur a reçu un dépassement de délai (timeout) d'un serveur amont qu'il consultait en tant que relais ou passerelle.
http.504.reason=Timeout de la passerelle
http.505.desc=Le serveur ne supporte pas la version demandée du protocole HTTP.
http.505.reason=Version HTTP non supportée
http.506.desc=Le serveur a rencontré une erreur de configuration interne : la variante choisie de la ressource est configurée pour mener elle-même la négociation de contenu de manière transparente, et n'est donc pas le bon endroit pour la négociation elle-même
http.506.reason=506 Variant Also Negotiates (RFC 2295) (référence circulaire)
http.507.desc=L'espace disponible est insuffisant pour enregistrer l'état de la ressource après exécution de cette méthode.
http.507.reason=Stockage insuffisant
http.508.desc=Le serveur a mis fin à une opération car il a rencontré une boucle infinie en traitant une requête avec "Depth : infinity"
http.508.reason=Boucle détectée
http.510.desc=La requête ne correspond pas à la politique d'accès pour cette ressource
http.510.reason=Non étendu
http.511.desc=Le client doit s'authentifier pour accéder au réseau.
http.511.reason=L’authentification du réseau est nécessaire

jdbcAccessLogValve.close=Echec de fermeture de la base de donnée
jdbcAccessLogValve.exception=Exception en insérant l'entrée de l'accès

persistentValve.filter.failure=Impossible de compiler le filtre=[{0}]

remoteCidrValve.invalid=La configuration fournie pour [{0}] est invalide, voir les précédents messages pour plus de détails
remoteCidrValve.noPort=La requête est rejetée car elle ne contient pas un port du serveur valide
remoteCidrValve.noRemoteIp=Le client n'a pas d'adresse IP, requête interdite
remoteCidrValve.unexpectedPort=La requête est rejetée car elle contient un port du serveur, alors que la configuration du connecteur addConnectorPort est false

remoteIpValve.invalidHostHeader=La valeur invalide [{0}] a été trouvée pour le Host dans l''en-tête HTTP [{1}]
remoteIpValve.invalidHostWithPort=La valeur de Host [{0}] dans l''en-tête HTTP [{1}] contenait un numéro de port qui sera ingnoré
remoteIpValve.invalidPortHeader=La valeur de port [{0}] trouvée dans l''en-tête HTTP [{1}] est invalide
remoteIpValve.invalidRemoteAddress=Impossible de déterminer l''hôte distant car l''adresse distante [{0}] est invalide

requestFilterValve.configInvalid=Un ou plusieurs paramètres de configuration spécifiés pour ce Remote[Addr|Host]Valve ont empêché la Valve et le conteneur parent de démarrer
requestFilterValve.deny=Refus de la requête pour [{0}] basé sur la propriété [{1}]

sslValve.certError=Impossible de traiter le certificat [{0}] pour créer un objet java.security.cert.X509Certificate
sslValve.invalidProvider=Le fournisseur SSL spécifié pour le connecteur associé avec cette requête de [{0}] est invalide, le certificat n''a pas pu être traité

stuckThreadDetectionValve.notifyStuckThreadCompleted=Le Thread [{0}] (id=[{3}]) qui a été préalablement rapporté comme étant bloqué s''est terminé, il a été actif pendant approximativement [{1}] millisecondes, il y a [{2}] thread(s) au total qui sont surveillés par cette valve et qui pourraient être bloqués
stuckThreadDetectionValve.notifyStuckThreadDetected=Le Thread [{0}] (id=[{6}]) a été actif depuis [{1}] millisecondes (depuis [{2}]) pour traiter la même requête pour [{4}] et pourrait être bloqué (le seuil configurable est de [{5}] secondes pour cette StuckThreadDetectionValve), il y a [{3}] thread(s) au total qui sont surveillés par cette valve et qui pourraient être bloqués
stuckThreadDetectionValve.notifyStuckThreadInterrupted=Le Thread [{0}] (id=[{5}]) a été interrompu car il a été actif depuis [{1}] millisecondes (depuis [{2}]) pour traiter la même requête pour [{3}] et était probablement bloqué (le seuil configurable est de [{4}] secondes pour cette StuckThreadDetectionValve)
