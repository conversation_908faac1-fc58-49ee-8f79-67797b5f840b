# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

authConfigFactoryImpl.load=[{0}](으)로부터 persistent provider 등록 사항들을 로드합니다.
authConfigFactoryImpl.registerClass=레이어 [{1}]와(과) 애플리케이션 컨텍스트 [{2}]을(를) 위한 클래스 [{0}]을(를) 등록합니다.
authConfigFactoryImpl.registerInstance=레이어 [{1}]와(과) 애플리케이션 컨텍스트 [{2}]을(를) 위한 타입 [{0}]의 인스턴스를 등록합니다.
authConfigFactoryImpl.zeroLengthAppContext=애플리케이션 컨텍스트 이름의 길이가 0으로, 이는 유효하지 않습니다.
authConfigFactoryImpl.zeroLengthMessageLayer=길이가 0인 메시지 레이어 이름은 유효하지 않습니다.

callbackHandlerImpl.containerMissing=타입이 [{0}]인 JASPIC 콜백을 위한 컨테이너가 존재하지 않아 무시됩니다.
callbackHandlerImpl.jaspicCallbackMissing=타입이 [{0}]인 지원되지 않는 JASPIC 콜백을 받았는데, 이는 무시됩니다.
callbackHandlerImpl.realmMissing=컨테이너 [{1}]에 타입이 [{0}]인 JASPIC 콜백을 위한 Realm이 존재하지 않아 무시됩니다.

jaspicAuthenticator.authenticate=[{0}]을(를) 위한 요청을 JASPIC를 통하여 인증합니다.

persistentProviderRegistrations.deleteFail=임시 파일 [{0}]을(를) 삭제할 수 없습니다.
persistentProviderRegistrations.existsDeleteFail=임시 파일 [{0}]이(가) 이미 존재하며 삭제될 수 없습니다.
persistentProviderRegistrations.moveFail=[{0}]을(를) [{1}](으)로 이동시키지 못했습니다.

simpleServerAuthConfig.noModules="ServerAuthModule이 설정되지 않음"
