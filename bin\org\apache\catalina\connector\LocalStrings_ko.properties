# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

coyoteAdapter.accesslogFail=접근 로그에 엔트리를 추가하기 위한 시도 중 예외 발생
coyoteAdapter.asyncDispatch=비동기 요청을 처리하는 중 예외 발생
coyoteAdapter.authenticate=Connector에 의해 제공된 사용자 [{0}]을(를) 인증했습니다.
coyoteAdapter.authorize=Tomcat의 Realm을 사용하여 사용자 [{0}]을(를) 승인 중
coyoteAdapter.checkRecycled.request=참조 해제 되지않은 요청을 발견하여 강제로 참조 해제했습니다.
coyoteAdapter.checkRecycled.response=참조 해제되지 않은 응답이 발견되어 강제로 참조 해제합니다.
coyoteAdapter.debug=변수 [{0}]이(가) 값 [{1}]을(를) 가지고 있습니다.
coyoteAdapter.nullRequest=비동기 디스패치는, 기존 요청에 대해 오직 한번만 일어나야 합니다.

coyoteConnector.invalidEncoding=인코딩 [{0}]은(는) JRE에 의해 인식되지 않습니다. Connector는 [{1}]을(를) 계속 사용할 것입니다.
coyoteConnector.invalidPort=지정된 포트 번호, [{0}]이(가) 유효하지 않기 때문에, Connector가 시작될 수 없습니다.
coyoteConnector.parseBodyMethodNoTrace=TRACE 메소드는 엔티티를 포함해서는 안됩니다. (RFC 2616 Section 9.6 참조)
coyoteConnector.protocolHandlerDestroyFailed=프로토콜 핸들러 소멸 중 실패
coyoteConnector.protocolHandlerInitializationFailed=프로토콜 핸들러 초기화가 실패했습니다.
coyoteConnector.protocolHandlerInstantiationFailed=프로토콜 핸들러 인스턴스 생성에 실패했습니다.
coyoteConnector.protocolHandlerNoAprLibrary=설정된 프로토콜 [{0}]이(가), 가용하지 않은 APR/native 라이브러리를 요구합니다.
coyoteConnector.protocolHandlerNoAprListener=설정된 프로토콜 [{0}]이(가), 가용하지 않은 AprLifecycleListener를 요구합니다.
coyoteConnector.protocolHandlerPauseFailed=프로토콜 핸들러에 대한 일시 정지가 실패했습니다.
coyoteConnector.protocolHandlerResumeFailed=프로토콜 핸들러를 재개하지 못했습니다.
coyoteConnector.protocolHandlerStartFailed=프로토콜 핸들러 시작 실패
coyoteConnector.protocolHandlerStopFailed=프로토콜 핸들러를 중지시키지 못했습니다.

coyoteInputStream.nbNotready=Non-blocking 모드에서는, 이전의 데이터 읽기가 완료되고 isReady()가 true를 반환하기 전까지는, ServletInputStream으로부터 데이터를 읽을 수 없습니다.

coyoteOutputStream.nbNotready=Non-blocking 모드에서는, 이전의 쓰기가 완료되고 isReady()가 true를 반환할 때까지는, ServletOutputStream에 쓸 수 없습니다.

coyoteRequest.alreadyAuthenticated=해당 요청은 이미 인증되었습니다.
coyoteRequest.attributeEvent=속성 이벤트 리스너에 의해 예외 발생
coyoteRequest.authenticate.ise=응답이 커밋된 후에는 authenticate()를 호출할 수 없습니다.
coyoteRequest.changeSessionId=세션 ID를 변경할 수 없습니다. 이 요청과 연관된 세션이 없습니다.
coyoteRequest.chunkedPostTooLarge=포스트된 데이터의 크기가 너무 커서 파라미터들이 파싱되지 않았습니다. 이 요청은 chunked request였기 때문에 더 이상 처리될 수 없었습니다. 만일 애플리케이션이 매우 큰 포스트 데이터를 받아들여야 한다면, Connector의 maxPostSize 속성을 사용하여 이 문제를 해결하십시오.
coyoteRequest.filterAsyncSupportUnknown=어떤 필터들이 비동기 처리를 지원하지 않는지 여부를 결정할 수 없습니다.
coyoteRequest.getContextPath.ise=Canonical 컨텍스트 경로 [{0}]이(가), User Agent [{1}]에 의해 표시된 URI와 부합되지 않습니다.
coyoteRequest.getInputStream.ise=이 요청에 대해 getReader()가 이미 호출되었습니다.
coyoteRequest.getReader.ise=getInputStream()이 이미 이 요청을 위해 호출되었습니다.
coyoteRequest.gssLifetimeFail=사용자 principal [{0}]의 남아있는 lifetime을 구하지 못했습니다.
coyoteRequest.maxPostSizeExceeded=Multi-part 요청이, 연관된 connector에 설정된 maxPostSize의 한계치를 초과하는 파라미터 데이터(업로드된 파일들은 제외)를 포함했습니다.
coyoteRequest.noAsync=요청 처리 체인 내의 다음 클래스들이 비동기 모드를 지원하지 않기 때문에, 비동기 모드를 시작할 수 없습니다: [{0}]
coyoteRequest.noMultipartConfig=어떤 multi-part 설정도 제공되지 않았기 때문에, part들을 처리할 수 없습니다.
coyoteRequest.parseParameters=포스트된 파라미터들을 처리하는 중 예외 발생
coyoteRequest.postTooLarge=포스트된 데이터의 크기가 너무 커서, 파라미터들이 파싱되지 않았습니다. 만일 애플리케이션이 대량의 포스트 데이터를 받아들여야 하는 경우, Connector의 maxPostSize 속성을 설정하여 문제를 해결하십시오.
coyoteRequest.sendfileNotCanonical=sendfile과 사용되도록 지정된 파일 [{0}]의 canonical 이름을 결정할 수 없습니다.
coyoteRequest.sessionCreateCommitted=응답이 이미 커밋된 후에는, 세션을 생성할 수 없습니다.
coyoteRequest.sessionEndAccessFail=요청을 참조 해제하는 과정에서, 세션에 대한 접근을 종료시키려 개시하는 중 예외 발생
coyoteRequest.setAttribute.namenull=널인 이름을 사용하여 setAttribute를 호출할 수 없습니다.
coyoteRequest.uploadCreate=서블릿 [{1}]에 의해 요구되는, 임시 업로드 폴더를 [{0}] 위치에 생성합니다.
coyoteRequest.uploadCreateFail=[{0}]에 업로드 폴더를 생성하지 못했습니다.
coyoteRequest.uploadLocationInvalid=임시 파일 업로드 위치 [{0}]은(는) 유효하지 않습니다.

coyoteResponse.encoding.invalid=인코딩 [{0}]은(는) JRE에 의해 인식되지 않는 것입니다.
coyoteResponse.getOutputStream.ise=이 응답에 대해 getWriter()가 이미 호출되었습니다.
coyoteResponse.getWriter.ise=이 응답을 위해 getOutputStream()이 이미 호출되었습니다.
coyoteResponse.reset.ise=응답이 이미 커밋된 후에는, reset()을 호출할 수 없습니다.
coyoteResponse.resetBuffer.ise=응답이 이미 커밋된 후에는, 버퍼를 재설정(reset)할 수 없습니다.
coyoteResponse.sendError.ise=응답이 이미 커밋된 후에는 sendError()를 호출할 수 없습니다.
coyoteResponse.sendRedirect.ise=응답이 이미 커밋된 후에는, sendRedirect()를 호출할 수 없습니다.
coyoteResponse.sendRedirect.note=<html><body><p>Redirecting to <a href="{0}">{0}</a></p></body></html>
coyoteResponse.setBufferSize.ise=데이터가 이미 쓰여진 후에는, 버퍼 크기를 변경할 수 없습니다.

inputBuffer.requiresNonBlocking=Non blocking 모드에서는 가용하지 않습니다.
inputBuffer.streamClosed=스트림이 닫혔습니다.

outputBuffer.writeNull=write(String,int,int) 메소드에 전달되는 String 아규먼트는 널일 수 없습니다.

request.asyncNotSupported=현재 체인의 필터 또는 서블릿이, 비동기 오퍼레이션들을 지원하지 않습니다.
request.fragmentInDispatchPath=디스패치 경로 [{0}](으)로부터 URI fragment를 제거했습니다.
request.illegalWrap=요청 wrapper는 반드시 getRequest()로부터 얻어진 요청을 wrap해야 합니다.
request.notAsync=만일 현재의 쓰레드가 비동기 모드에 있지 않다면, 이 메소드를 호출하는 것은 불허됩니다. (즉, isAsyncStarted()가 false를 반환하는 경우)
request.session.failed=다음 오류로 인해 세션 [{0}]을(를) 로드하지 못했습니다: [{1}]

requestFacade.nullRequest=요청 객체가 이미 참조 해제 되었고, 더 이상 이 facade와 연관되지 않습니다.

response.illegalWrap=응답 wrapper는, 반드시 getResponse()로부터 얻어진 응답 객체를 wrap한 것이어야 합니다.
response.sendRedirectFail=[{0}](으)로 redirect하지 못했습니다.

responseFacade.nullResponse=해당 응답 객체가 이미 참조 해제되었으며, 더 이상 이 ResponseFacade 객체와 연관이 없습니다.
