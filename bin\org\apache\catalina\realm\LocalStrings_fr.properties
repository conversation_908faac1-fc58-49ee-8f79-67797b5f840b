# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

combinedRealm.addRealm=Ajout du royaume [{0}], [{1}] royaumes au total
combinedRealm.authFail=Echec d''authentification de l''utilisateur [{0}] avec le domaine [{1}]
combinedRealm.authStart=Tentative d''authentifier l''utilisateur [{0}] avec le royaume [{1}]
combinedRealm.authSuccess=Authentifié l''utilisateur [{0}] avec le domaine [{1}]
combinedRealm.getPassword=La méthode getPassword() ne doit jamais être appelée
combinedRealm.getPrincipal=La méthode getPrincipal() ne devrait jamais être appelée
combinedRealm.realmStartFail=Impossible de démarrer le royaume [{0}]
combinedRealm.setCredentialHandler=Un CredentialHandler a été défini sur une instance de CombinedRealm (ou une sous classe de CombinedRealm), et CombinedRealm ne supporte pas de CredentialHandler
combinedRealm.unexpectedMethod=Un appel de méthode inattendu à été effectué sur le royaumes combiné

credentialHandler.invalidStoredCredential=Le royaume a fourni des identifiants [{0}] invalides à comparer avec ceux fournis par le client

dataSourceRealm.authenticateFailure=Le nom d''utilisateur [{0}] n''a pas été authentifié
dataSourceRealm.authenticateSuccess=Le nom d''utilisateur [{0}] a été authentifié avec succès
dataSourceRealm.close=Exception lors de la fermeture de la connection vers la base de données
dataSourceRealm.exception=Exception lors de l'anthentification
dataSourceRealm.getPassword.exception=Exception lors de la récupération du mot de passe pour [{0}]
dataSourceRealm.getRoles.exception=Exception lors de la récupération des rôles de [{0}]

jaasCallback.username=Nom d''utilisateur renvoyé [{0}]

jaasRealm.accountExpired=le nom d''utilisateur [{0}] N''A PAS été authentifié car le compte a expiré
jaasRealm.authenticateFailure=Le nom d''utilisateur [{0}] n''a pas été authentifié avec succès
jaasRealm.authenticateSuccess=le nom d''utilisateur [{0}] a été authentifié avec succès
jaasRealm.beginLogin=La connection avec le JAASRealm a demandé le nom d''utilisateur [{0}] en utilisant le LoginContext de l''application [{1}]
jaasRealm.checkPrincipal=Vérification du principal [{0}] [{1}]
jaasRealm.credentialExpired=le nom d''utilisateur [{0}] N''A PAS été authentifié car son crédit a expiré (expired credential)
jaasRealm.failedLogin=le nom d''utilisateur [{0}] N''A PAS été authentifié car son contrôle d''accès (login) a échoué
jaasRealm.loginContextCreated=Le LoginContext JAAS a été crée pour le nom d''utilisateur [{0}]
jaasRealm.loginException=Exception lors de l''authentification par login du nom d''utilisateur [{0}]
jaasRealm.rolePrincipalAdd=Ajout du rôle Pincipal [{0}] aux rôles du principal de l''utilisateur
jaasRealm.rolePrincipalFailure=Aucun principal avec un rôle valide trouvé
jaasRealm.unexpectedError=Erreur inattendue
jaasRealm.userPrincipalFailure=Aucun principal valide trouvé
jaasRealm.userPrincipalSuccess=Le principal [{0}] est une classe utilisateur valide, elle sera utilisée comme principal de l''utilisateur

jdbcRealm.authenticateFailure=le nom d''utilisateur [{0}] N''A PAS été authentifié
jdbcRealm.authenticateSuccess=le nom d''utilisateur [{0}] a été authentifié avec succès
jdbcRealm.close=Exception lors de la fermeture de la connexion à la base de données
jdbcRealm.exception=Exception pendant le traitement de l'authentification
jdbcRealm.open=Exception lors de l'ouverture de la base de données
jdbcRealm.open.invalidurl=Le pilote [{0}] ne supporte pas l''URL [{1}]

jndiRealm.authenticateFailure=Le nom d''utilisateur [{0}] N''A PAS été authentifié
jndiRealm.authenticateSuccess=Le nom d''utilisateur [{0}] a été authentifié avec succès
jndiRealm.cipherSuites=La suite de chiffres [{0}] a été activée pour la connection TLS
jndiRealm.close=Exception lors de la fermeture de la connexion au serveur d'accès (directory server)
jndiRealm.emptyCipherSuites=Une chaîne vide est donnée comme suite de chiffres, la suite de chiffres par défaut sera utilisée
jndiRealm.exception=Exception pendant le traitement de l'authentification
jndiRealm.exception.retry=Erreur pendant l'authentification, nouvel essai
jndiRealm.invalidHostnameVerifier=[{0}] n''est pas un nom de classe valide pour un HostnameVerifier
jndiRealm.invalidSslProtocol=Le protocole fourni [{0}] est invalide, il doit être parmi [{1}]
jndiRealm.invalidSslSocketFactory=[{0}] n''est pas un nom de classe valide pour une SSLSocketFactory
jndiRealm.multipleEntries=Le nom d''utilisateur [{0}] a plusieurs entrées
jndiRealm.negotiatedTls=La connection TLS a été négociée en utilisant le protocole [{0}]
jndiRealm.open=Exception lors de l'ouverture de la connexion au serveur d'accès (directory server)
jndiRealm.tlsClose=Exception en fermant la réponse TLS

lockOutRealm.authLockedUser=Une tentative d''authentification a été effectuée pour l''utilisateur verrouillé ("locked user") [{0}]
lockOutRealm.removeWarning=L''utilisateur [{0}] a été enlevé du cache des utilisateurs en échec après [{1}] secondes pour garder la taille du cache dans les limites définies

mdCredentialHandler.unknownEncoding=L''encodage [{0}] n''est pas supporté donc la configuration actuelle [{1}] va continuer à être utilisée

memoryRealm.authenticateFailure=le nom d''utilisateur [{0}] N''A PAS été authentifié
memoryRealm.authenticateSuccess=le nom d''utilisateur [{0}] a été authentifié avec succès
memoryRealm.loadExist=Le fichier base de données mémoire (memory database) [{0}] ne peut être lu
memoryRealm.loadPath=Chargement des utilisateurs depuis le fichier base de données mémoire (memory database) [{0}]
memoryRealm.readXml=Exception lors de la lecture du fichier base de données mémoire (memory database)
memoryRealm.xmlFeatureEncoding=Exception lors de la configuration du Digester pour permettre des noms d'encodage Java dans les fichiers XML, seuls le noms IANA seront supportés

pbeCredentialHandler.invalidKeySpec=Impossible de générer une clé basée sur le mot de passe

realmBase.algorithm=L''algorithme de hachage de message [{0}] indiqué est invalide
realmBase.authenticateFailure=Le nom d''utilisateur [{0}] N''A PAS été authentifié
realmBase.authenticateSuccess=Le nom d''utilisateur [{0}] a été authentifié avec succès
realmBase.cannotGetRoles=Impossible d''obtenir les rôles du principal [{0}]
realmBase.createUsernameRetriever.ClassCastException=La classe [{0}] n''est pas un X509UsernameRetriever.
realmBase.createUsernameRetriever.newInstance=Impossible de créer un objet du type [{0}]
realmBase.credentialNotDelegated=Les identifiants de l''utilisateur [{0}} n''ont pas été délégués alors que leur stockage a été requis
realmBase.delegatedCredentialFail=Impossible d''obtenir les identifiants délégués pour l''utilisateur [{0}]
realmBase.digest=Erreur lors du hachage de l''identifiant utilisateur
realmBase.forbidden=L'accès à la ressource demandée a été interdit
realmBase.gotX509Username=Obtenu le nom d''utilisateur dans le certificat X509 : [{0}]
realmBase.gssContextNotEstablished=Erreur d'implémentation de l'authenticateur : le contexte de sécurité passé n'est pas complètement établi
realmBase.gssNameFail=Impossible d'extraire le nom du GSSContext qui a été établi
realmBase.hasRoleFailure=Le nom d''utilisateur [{0}] N''A PAS de rôle [{1}]
realmBase.hasRoleSuccess=Le nom d''utilisateur [{0}] a pour rôle [{1}]

userDatabaseRealm.lookup=Exception lors de la recherche dans la base de données utilisateurs avec la clé [{0}]
userDatabaseRealm.noDatabase=Aucun composant base de données utilisateurs trouvé pour la clé [{0}]
