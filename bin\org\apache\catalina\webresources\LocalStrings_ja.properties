# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractArchiveResourceSet.setReadOnlyFalse=JARに基づくWebResourceSetなどのアーカイブベースのWebResourceSetは、読み取り専用にハードコードされており、読み取り/書き込み可能に構成されていない可能性があります。

abstractResource.getContentFail=[{0}]をバイト配列として返すことができません。
abstractResource.getContentTooLarge=リソースがバイト配列の最大サイズよりも大きいサイズの[{1}]バイトであるため、[{0}]をバイト配列として返すことができません。

abstractResourceSet.checkPath=リクエストパス[{0}]が無効です。 "/"で始まる必要があります。

cache.addFail=有効期限切れの項目を破棄しても利用可能な領域が不足するため、Web アプリケーション [{1}] のキャッシュにリソース [{0}] を追加できません。最大キャッシュサイズの増加を検討してください。
cache.backgroundEvictFail=コンテキスト [{1}] のバックグラウンドキャッシュ削除処理は全体の [{0}] % を解放できませんでした。キャッシュサイズの最大値の増加を検討してください。現在は約 [{2}] kB のデータがキャッシュに残存しています。
cache.objectMaxSizeTooBig=objectMaxSizeの[{0}] kBの値がmaxSize / 20の制限より大きいため、[{1}] kBに減少しました。
cache.objectMaxSizeTooBigBytes=キャッシュ可能なオブジェクトサイズの最大値に指定された [{0}]kB は Integer.MAX_VALUE バイトを越えています。最大値に Integer.MAX_VALUE を設定します。

cachedResource.invalidURL=URL [{0}] は不正です。CachedResourceURLStreamHandler インスタンスを生成できません

classpathUrlStreamHandler.notFound=スレッドコンテキストクラスローダー、あるいは、現在のクラスのクラスローダーでリソース [{0}] を読み込みできません。

dirResourceSet.manifestFail=[{0}]からマニフェストを読み込めませんでした。
dirResourceSet.notDirectory=ベースパスと内部パスで指定した [{0}][{1}][{2}] にディレクトリがありません。
dirResourceSet.writeNpe=入力ストリームには null を指定できません。

extractingRoot.jarFailed=JARファイル[{0}]の抽出に失敗しました
extractingRoot.targetFailed=JAR ファイルを展開するためのディレクトリ [{0}] を作成できません。

fileResource.getCanonicalPathFail=リソース [{0}] の正規化パスを取得できません。
fileResource.getCreationFail=リソース[{0}]の作成時間を特定できません。
fileResource.getUrlFail=リソース [{0}] の URL を取得できません。

fileResourceSet.notFile=基本パスおよび内部パスで指定されたファイル [{0}]{1}[{2}] がありません。

jarResource.getInputStreamFail=JAR ファイル [{1}] のリソース [{0}] の入力ストリームを取得できません。

jarResourceRoot.invalidWebAppPath=このリソースは常にディレクトリを参照するため、指定されたwebAppPathは/で終了する必要がありますが、指定されたwebAppPathは[{0}]です。

jarWarResourceSet.codingError=コーディングエラー

standardRoot.checkStateNotStarted=リソースは、現在起動されていない場合はアクセスできない場合があります
standardRoot.createInvalidFile=[{0}]からWebResourceSetを作成できません。
standardRoot.createUnknownType=未知のクラス [{0}] の WebResourceSet を作成できません。
standardRoot.invalidPath=不正なリソースパス [{0}]
standardRoot.invalidPathNormal=リソースパス[{0}]は有効ではない[{1}]に正規化されています。
standardRoot.lockedFile=Webアプリケーション[{0}]は、次のスタックトレースによって開かれたファイル[{1}]を閉じることに失敗しました。
standardRoot.noContext=この WebResourceRoot にはContext が構成されていません。
standardRoot.startInvalidMain=指定された主リソースセット[{0}]は無効です。
standardRoot.unsupportedProtocol=URLプロトコル[{0}]はこのWebリソース実装ではサポートされていません。
