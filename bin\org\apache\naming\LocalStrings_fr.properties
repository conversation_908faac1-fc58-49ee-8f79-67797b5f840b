# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

contextBindings.noContextBoundToCL=Aucun Contexte de nommage lié à ce chargeur de classes
contextBindings.noContextBoundToThread=Aucun Contexte de nommage lié à ce thread
contextBindings.unknownContext=Nom de Contexte inconnu : [{0}]

namingContext.alreadyBound=Le Nom [{0}] est déjà lié à ce Contexte
namingContext.contextExpected=Le Nom n'est pas lié à un Contexte
namingContext.failResolvingReference=Une erreur s est produite durant la résolution de la référence
namingContext.invalidName=Le Nom est invalide
namingContext.nameNotBound=Le Nom [{0}] n''est pas lié à ce Contexte
namingContext.noAbsoluteName=Impossible de générer un nom absolu pour cet espace de nommage (namespace)
namingContext.readOnly=Le Contexte est en lecture seule

selectorContext.methodUsingName=Appel de la méthode [{0}] avec le nom [{1}]
selectorContext.methodUsingString=Appel de la méthode [{0}] avec la String [{1}]
selectorContext.noJavaUrl=Ce Contexte doit être accédé par une URL commençant par 'java :'
