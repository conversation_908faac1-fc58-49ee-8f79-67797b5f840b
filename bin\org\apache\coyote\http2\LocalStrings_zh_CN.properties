# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractStream.windowSizeDec=连接[{0}]，流[{1}]，将流控制窗口减少[{2}]到[{3}]
abstractStream.windowSizeInc=连接 [{0}], 流 [{1}], 增加流量控制窗口[{2}] 到 [{3}]
abstractStream.windowSizeTooBig=连接[{0}]，流[{1}]，窗口大小从[{2}]增加到[{3}]，超过了允许的最大值

connectionPrefaceParser.eos=读取打开客户端序字节序列时出现意外的流结尾。只读取了[{0}]个字节。
connectionPrefaceParser.mismatch=请求了新的远程流ID[{0}]，但所有远程流都必须使用奇数标识符。

connectionSettings.debug=连接[{0}]，参数类型[{1}]设置为[{2}]
connectionSettings.enablePushInvalid=连接[{0}]，请求的enable push[{1}]值不是允许的值之一（零或一）
connectionSettings.headerTableSizeLimit=连接 [{0}]，尝试将 header 表大小设置为 [{1}]，但限制为 16k
connectionSettings.maxFrameSizeInvalid=连接[{0}]，请求的最大帧大小[{1}]在[{2}]到[{3}]的允许范围之外
connectionSettings.unknown=连接[{0}]，标识为[{1}]和值为[{2}]的未知设置被忽略
connectionSettings.windowSizeTooBig=连接[{0}]，请求窗口大小[{0}]，大于最大允许的值[{1}]

frameType.checkPayloadSize=对帧类型[{1}]来说，负载[{0}]是无效的
frameType.checkStream=无效的帧类型[{0}]

hpack.integerEncodedOverTooManyOctets=HPACK 可变长度整数编码过多的八位字节，最大值为[{0}]
hpack.invalidCharacter=代码点[{1}]处的Unicode字符[{0}]无法编码，因为它超出了允许的0到255范围。

hpackEncoder.encodeHeader=编码头[{0}]，值为[{1}]

hpackdecoder.headerTableIndexInvalid=头部表索引[{0}]无效，因为有[{1}]个静态实例和[{2}]个动态实例
hpackdecoder.maxMemorySizeExceeded=头表大小[{0}]超过了最大大小[{1}]
hpackdecoder.notImplemented=尚未实施
hpackdecoder.nullHeader=索引[{0}]处的头为空
hpackdecoder.tableSizeUpdateNotAtStart=任何表大小的更新都必须在头块开始时发送。
hpackdecoder.zeroNotValidHeaderTableIndex=零不是有效的头表索引

hpackhuffman.huffmanEncodedHpackValueDidNotEndWithEOS=HPACK头中的Huffman编码值没有以EOS填充结束
hpackhuffman.stringLiteralEOS=包含EOS符号的HPACK头中的Huffman编码值
hpackhuffman.stringLiteralTooMuchPadding=超过7个bits的EOS填充提供了在一个霍夫曼编码字符串的结束

http2Parser.headerLimitCount=连接[{0}]，流[{1}]，标题太多
http2Parser.headerLimitSize=连接[{0}]，Stream[{1}]，总的头信息尺寸太大
http2Parser.headers.wrongFrameType=连接{0}，正在处理流{1}的头，但收到了类型为{2}的帧
http2Parser.headers.wrongStream=连接[{0}], 头部信息对于流[{1}]正在进行但对于流[{2}]的一帧已经收到了。
http2Parser.nonZeroPadding=连接[{0}]，流[{1}]，非零填充
http2Parser.payloadTooBig=有效负载是[{0}]字节长，但最大帧大小是[{1}]。
http2Parser.preface.invalid=出现无效连接
http2Parser.preface.io=无法读取连接前言
http2Parser.processFrame=连接{0}、流{1}、帧类型{2}、标志{3}、负载大小{4}
http2Parser.processFrame.tooMuchPadding=连接[{0}]，流[{1}]，填充长度[{2}]对于负载[{3}]太大
http2Parser.processFrame.unexpectedType=需要帧类型[{0}]，但收到帧类型[{1}]
http2Parser.processFrameContinuation.notExpected=连接{0}，当没有头正在进行时，为流{1}接收到连续帧。
http2Parser.processFrameData.lengths=连接[{0}]，流[{1}]，数据长度，[{2}]，填充长度[{3}]
http2Parser.processFrameData.window=连接[{0}]，客户端发送的数据比流窗口允许的多
http2Parser.processFrameHeaders.decodingDataLeft=数据在HPACK解码后依然保留 - 它本应该被消费掉
http2Parser.processFrameHeaders.decodingFailed=对HTTP头进行HPACK解码时出错
http2Parser.processFrameHeaders.payload=连接：[{0}]，流：[{1}]，正在处理[{1}]大小的头文件负载
http2Parser.processFramePriority.invalidParent=连接[{0}]，流[{1}]，流可能不依赖于自身
http2Parser.processFramePushPromise=请求了新的远程流ID[{0}]，但所有远程流都必须使用奇数标识符\n\
\n
http2Parser.processFrameSettings.ackWithNonZeroPayload=接收到带有ACK标志设置和有效负载的设置帧
http2Parser.processFrameWindowUpdate.debug=连接[{0}]，流[{1}]，窗口大小增量[{2}]
http2Parser.processFrameWindowUpdate.invalidIncrement=接收到的窗口更新帧具有无效的增量大小[{0}]
http2Parser.swallow.debug=连接：[{0}]，流：[{1}]，吞下[{2}]字节

pingManager.roundTripTime=连接[{0}]往返时间测量为[{1}]ns

stream.clientCancel=客户端在响应完成前重置了数据流
stream.closed=连接[{0}]，流[{1}]，一旦关闭就无法写入流
stream.header.case=连接[{0}]，流[{1}]，HTTP标头名称[{2}]必须小写
stream.header.connection=HTTP/2请求中不允许连接{0}、流{1}、HTTP头[连接]
stream.header.contentLength=连接{0}，流{1}，内容长度头值{2}与接收的数据大小{3}不一致
stream.header.debug=连接[{0}]，流[{1}]，HTTP标头[{2}]，值[{3}]
stream.header.duplicate=连接[{0}]，流[{1}]，收到多个[{2}]头
stream.header.empty=连接[{0}]，流[{1}]，无效的空头名称
stream.header.invalid=连接[{0}]，流[{1}]，头[{2}]包含无效值[{3}]
stream.header.noPath=连接[{0}]，流[{1}]，[：path]伪标头为空
stream.header.required=连接 [{0}], 流 [{1}], 缺少一个或多个必要的头文件
stream.header.te=连接{0}、流{1}、HTTP头[te]在HTTP/2请求中不允许有值{2}
stream.header.unexpectedPseudoHeader=连接[{0}]，流[{1}]，伪头[{2}]在常规头之后接收
stream.header.unknownPseudoHeader=收到连接[{0}]，流[{1}]，未知伪标头[{2}]
stream.inputBuffer.copy=正在将[{0}]字节从inBuffer复制到outBuffer
stream.inputBuffer.dispatch=注册读取兴趣时将数据添加到inBuffer中。触发读取分派
stream.inputBuffer.empty=流输入缓冲区为空。等待更多数据
stream.inputBuffer.readTimeout=等待从客户端读取数据超时
stream.inputBuffer.reset=流.重置
stream.inputBuffer.signal=读线程在等待时，数据被添加到inBuffer中。 发信号通知该线程继续
stream.notWritable=连接{0}，流{1}，此流不可写
stream.outputBuffer.flush.debug=连接{0}，流{1}，用缓冲区在位置{2}刷新输出，writeInProgress[{3}]并关闭了[{4}]
stream.recycle=连接[{0}]，流[{1}]已回收
stream.reprioritisation.debug=连接[{0}]，流[{1}]，独占[{2}]，父[{3}]，权重[{4}]
stream.reset.fail=连接[{0}]，流[{1}]，重置流失败
stream.reset.receive=连接{0}，流{1}，由于{2}而收到重置
stream.reset.send=连接{0}，流{1}，由于{2}重置发送
stream.trailerHeader.noEndOfStream=连接[{0}]，流[{1}]，尾部标头不包括流结束标志
stream.writeTimeout=等待客户端增加流控制窗口以允许写入流数据的超时

streamProcessor.cancel=连接到[{0}]，Stream [{1}]，
streamProcessor.error.connection=连接[{0}]，Stream[{0}]，处理中发生错误，对连接来说是致命的。
streamProcessor.error.stream=连接{0}，流{1}，处理过程中发生对流致命的错误
streamProcessor.flushBufferedWrite.entry=连接[{0}]，流[{1}]，正在刷新缓冲写入
streamProcessor.service.error=请求处理期间出错

streamStateMachine.debug.change=(:连接[{0}]，流[{1}]，状态从[{2}]更改为[{3}]
streamStateMachine.invalidFrame=连接{0}、流{1}、状态{2}、帧类型{3}

upgradeHandler.allocate.debug=连接[{0}]，流[{1}]，已分配[{2}]字节
upgradeHandler.allocate.left=连接[{0}]，流[{1}]，[{2}]字节未分配 - 尝试分配给子项
upgradeHandler.allocate.recipient=(:连接[{0}]，流[{1}]，潜在接收者[{2}]，权重为[{3}]
upgradeHandler.connectionError=连接错误
upgradeHandler.dependency.invalid=连接{0}，流{1}，流可能不依赖于自身
upgradeHandler.goaway.debug=连接[{0}]，离开，最后的流[{1}]，错误码[{2}]，调试数据[{3}]
upgradeHandler.init=连接[{0}]，状态[{1}]
upgradeHandler.initialWindowSize.invalid=连接{0}，初始窗口大小忽略了非法值{1}。
upgradeHandler.invalidPreface=连接[{0}]，连接前言无效
upgradeHandler.ioerror=连接[{0}]
upgradeHandler.noAllocation=连接[{0}]，流[{1}]，等待分配超时
upgradeHandler.noNewStreams=连接{0}，流{1}，忽略流，因为此连接上不允许有新的流
upgradeHandler.pause.entry=连接[{0}]正在暂停
upgradeHandler.pingFailed=连接[{0}]对客户端发送ping失败.
upgradeHandler.prefaceReceived=连接[{0}]，从客户端收到连接准备。
upgradeHandler.pruneIncomplete=连接[{0}]，流[{1}]，无法完全修剪连接，因为有[{2}]个活动流太多
upgradeHandler.pruneStart=连接[{0}]正在开始修剪旧流。限制为[{1}]，当前有[{2}]个流。
upgradeHandler.pruned=连接[{0}]已修剪完成的流[{1}]
upgradeHandler.prunedPriority=连接[{0}]已经成为了属于优先级树中未使用的流[{1}]
upgradeHandler.releaseBacklog=连接[{0}]，流[{1}]已从待办事项列表中释放
upgradeHandler.rst.debug=连接[{0}]，流[{1}]，错误[{2}]，消息[{3}]，RST（关闭流）
upgradeHandler.sendPrefaceFail=连接[{0}]，给客户端发送前言失败
upgradeHandler.socketCloseFailed=关闭 socket 错误
upgradeHandler.stream.closed=流[{0}]已经关闭了一段时间
upgradeHandler.stream.even=\ 请求了新的远程流ID[{0}]，但所有远程流都必须使用奇数标识符\n\
\n
upgradeHandler.stream.notWritable=连接{0}，流{1}，此流不可写。
upgradeHandler.stream.old=请求了新的远程流ID [{0}]，但最近的流是[{1}]
upgradeHandler.tooManyRemoteStreams=客户端试图使用超过[{0}]个活动流。
upgradeHandler.tooMuchOverhead=连接[{0}]，开销过大，连接将关闭
upgradeHandler.unexpectedAck=连接[{0}]，流[{1}]，收到一个非预期的设置确认
upgradeHandler.upgrade=连接[{0}], HTTP/1.1 升级到流[1]
upgradeHandler.upgrade.fail=):连接[{0}]，http/1.1升级失败
upgradeHandler.upgradeDispatch.entry=条目，连接[{0}]，SocketStatus [{1}]
upgradeHandler.upgradeDispatch.exit=退出，连接[{0}]， SocketState[{1}]
upgradeHandler.windowSizeReservationInterrupted=连接[{0}]，流[{1}]，保留[{2}]字节
upgradeHandler.windowSizeTooBig=连接[{0}]，流[{1}]，窗口太大
upgradeHandler.writeBody=连接 [{0}],数据流[{1}], 数据长度[{2}]
upgradeHandler.writeHeaders=连接 [{0}]，流 [{1}]
upgradeHandler.writePushHeaders=连接{0}、流{1}、推送流{2}、EndOfStream{3}

windowAllocationManager.dispatched=连接[{0}]，流[{1}]，已调度
windowAllocationManager.notified=连接[{0}]，流[{1}]，已通知
windowAllocationManager.notify=连接[{0}], 流[{1}], 等待类型[{2}], 通知类型[{3}]
windowAllocationManager.waitFor.connection=连接{0}，流{1}，等待连接流控制窗口（阻塞），超时为{2}]
windowAllocationManager.waitFor.ise=连接[{0}], 流[{1}], 已经准备好
windowAllocationManager.waitFor.stream=连接[{0}]，流[{1}]，等待流控制窗口（阻塞），超时为[{2}]
windowAllocationManager.waitForNonBlocking.connection=连接[{0}]，流[{1}]，正在等待连接流控制窗口（非阻塞）
windowAllocationManager.waitForNonBlocking.stream=连接[{0}]，流[{1}]，正在等待流控制窗口（非阻塞）

writeStateMachine.endWrite.ise=写入完成后，为新状态指定[{0}]是非法的
writeStateMachine.ise=处于 [{1}] 状态时调用 [{0}()] 方法是非法的
