# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

backupManager.noCluster=pas de groupe (cluster) associé à ce contexte : [{0}]
backupManager.startFailed=Impossible de démarrer le BackupManager : [{0}]
backupManager.startUnable=Impossible de démarrer le BackupManager : [{0}]
backupManager.stopped=Le gestionnaire de session [{0}] s''est arrêté

clusterSessionListener.noManager=Le gestionnaire de session du contexte n''existe pas : [{0}]

deltaManager.createMessage.access=Gestionnaire de session [{0}] : création du message de session [{1}] d''accès
deltaManager.createMessage.accessChangePrimary=Gestionnaire de session [{0}] : création du message de session [{1}] accès pour changer le primaire
deltaManager.createMessage.allSessionData=Gestionnaire de session [{0}] envoyé toutes les données de session
deltaManager.createMessage.allSessionTransferred=Gestionnaire de session [{0}] envoi du message signalant le transfert de toutes les données de session
deltaManager.createMessage.delta=Gestionnaire de session [{0}] : création du message [{0}] de requête delta
deltaManager.createMessage.expire=Gestionnaire de session [{0}] : création du message [{1}] d''expiration de session
deltaManager.createMessage.unableCreateDeltaRequest=Impossible de sérialiser la requête delta pour l''id de session [{0}]
deltaManager.createSession.newSession=Crée une DeltaSession avec Id [{0}] Nombre total=[{1}]
deltaManager.dropMessage=Gestionnaire de session [{0}] : Abandon du message [{1}] dans GET_ALL_SESSIONS début de la phase de sync [{2}] date du message [{3}]
deltaManager.expireSessions=Gestionnaire de session [{0}] expiration des sessions lors de l''arrêt
deltaManager.foundMasterMember=Le membre maître de réplication [{1}] a été trouvé pour le contexte [{0}]
deltaManager.loading.cnfe=Exception ClassNotFoundException lors du chargement des sessions persistantes : [{0}]
deltaManager.loading.existing.session=la session existante [{0}] est surchargée
deltaManager.loading.ioe=IOException lors du chargement des session persistées : [{0}]
deltaManager.managerLoad=Exception lors du chargement des sessions depuis le stockage persistant
deltaManager.noCluster=Démarrage, pas de cluster associé à ce contexte [{0}]
deltaManager.noContextManager=Gestionnaire de session [{0}] : En réponse à l''envoi d''un message demandant toutes les données des sessions à [{0}], un message indiquant l''absence d''un gestionnaire de sessions correspondant à été reçu au bout de [{2}] ms
deltaManager.noMasterMember=Démarrage sans autre membre pour le contexte [{0}] du domaine [{1}]
deltaManager.noMembers=Gestionnaire de session [{0}] : pas de transfert d''état, il n''y a pas de membres actifs dans le cluster
deltaManager.noSessionState=Gestionnaire de session [{0}] : pas de statut de session envoyé à [{1}] reçu, délai d''attente maximum de [{2}] ms.
deltaManager.receiveMessage.accessed=Gestionnaire de session [{0}] : reçu un accès à la session [{1}]
deltaManager.receiveMessage.allSessionDataAfter=Gestionnaire de session [{0}] : l''état de la session a été désérialisé
deltaManager.receiveMessage.allSessionDataBegin=Gestionnaire de session [{0}] : : reçu les données d''état des sessions
deltaManager.receiveMessage.createNewSession=Gestionnaire de session [{0}] : reçu la création de la session [{1}]
deltaManager.receiveMessage.delta=Gestionnaire de session [{0}] : reçu le delta de session [{1}]
deltaManager.receiveMessage.delta.unknown=Gestionnaire de session [{0}] : reçu un delta pour une session inconnue [{1}]
deltaManager.receiveMessage.error=Gestionnaire de session [{0}] : impossible de recevoir un message par le canal TCP
deltaManager.receiveMessage.eventType=Gestionnaire de session [{0}] : recu un SessionMessage de type=[{1}] de [{2}]
deltaManager.receiveMessage.expired=Gestionnaire de session [{0}] : reçu l''expiration de la session [{1}]
deltaManager.receiveMessage.noContextManager=Gestionnaire de session [{0}] a reçu d''un nœud [{1} : {2}] sans gestionnaire de contexte
deltaManager.receiveMessage.transfercomplete=Gestionnaire de session [{0}] reçu du nœud [{1} : {2}] l''état de la session a été transféré
deltaManager.receiveMessage.unloadingAfter=Gestionnaire de session [{0}] : fin du déchargement des sessions
deltaManager.receiveMessage.unloadingBegin=Gestionnaire de session [{0}] : début du déchargement des sessions
deltaManager.registerCluster=Enregistrement du gestionnaire [{0}] dans l''élément du cluster [{1}] avec le nom [{2}]
deltaManager.sendMessage.newSession=Gestionnaire de session [{0}] : envoi de la nouvelle session [{1}]
deltaManager.sessionReceived=Gestionnaire de session [{0}] : l''état de session envoyé à [{0}] a été reçu en [{2}] ms
deltaManager.startClustering=Démarrage du gestionnaire du cluster à [{0}]
deltaManager.stopped=Le gestionnaire de session [{0}] s''arrête
deltaManager.unableSerializeSessionID=Impossible de sérialiser le sessionID [{0}]
deltaManager.unloading.ioe=IOException lors de la sauvegarde des sessions persistantes : [{0}]
deltaManager.waitForSessionState=Gestionnaire de session [{0}], demande de l''état de session depuis [{1}], cette opération fera un timeout si l''état de la session n''a pas été reçu en moins de [{2}] secondes

deltaRequest.invalidAttributeInfoType=Info d''attribut invalide = [{0}]
deltaRequest.removeUnable=N'a pas pu enlever l'élément :
deltaRequest.showPrincipal=Le principal [{0}] est associé à la session [{1}]
deltaRequest.ssid.mismatch=L'id de session ne correspond pas, la requête delta ne sera pas exécutée
deltaRequest.ssid.null=L'id de session est null pour setSessionId
deltaRequest.wrongPrincipalClass=Un ClusterManager n''accepte que des GenericPrincipal. Votre realm a utilisé la classe de "principal" [{0}]

deltaSession.notifying=Notification du cluster de l''expiration de la session : manager [{0}], primaire=[{1}] sessionId [{2}]
deltaSession.readSession=readObject() charge la session [{0}]
deltaSession.writeSession=writeObject() stocke la session [{0}]

jvmRoute.cannotFindSession=Impossible de trouver la session [{0}]
jvmRoute.changeSession=Changé la session de [{0}] vers [{1}]
jvmRoute.failover=Un changement de serveur a été détecté avec une jvmRoute différente, route originale : [{0}] nouvelle : [{1}] pour l''id de session [{2}]
jvmRoute.foundManager=Trouvé le gestionnaire de session du cluster [{0}] à [{1}]
jvmRoute.missingJvmRouteAttribute=Pas d'attribut jvmRoute configuré sur le moteur
jvmRoute.noCluster=La JvmRouterBinderValve est configurée mais le cluster n'est pas activé, la bascule vers un autre serveur fonctionnera tout de même à condition qu'un PersistentManager soit utilisé
jvmRoute.notFoundManager=Gestionnaire de cluster ("Cluster Manager") non trouvé à [{0}]
jvmRoute.set.originalsessionid=Fixe l''id de session d''origine dans l''attribut de requête [{0}] valeur : [{1}]
jvmRoute.turnoverInfo=Temps de vérification de turnover [{0}] ms
jvmRoute.valve.started=La JvmRouteBinderValve a démarrée
jvmRoute.valve.stopped=JvmRouteBinderValve s'est arrêté

standardSession.notSerializable=Impossible de sérialiser l''attribut de session [{0}] pour la session [{1}]
standardSession.removeAttribute.ise=removeAttribute : session déjà invalidée
standardSession.setAttribute.namenull=setAttribute : le paramètre nom ne peut pas être null
