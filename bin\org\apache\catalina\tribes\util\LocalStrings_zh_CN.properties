# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

arrays.keyoffset.outOfBounds=key的偏移.超出了界限。
arrays.length.outOfBounds=当前key下没有足够的元素，长度越界
arrays.malformed.arrays=字节数组必须表示为{1,3,4,5,6}
arrays.srcoffset.outOfBounds=srcoffset超出界限。

executorFactory.not.running=执行器没有运行，无法强制把命令送入队列
executorFactory.queue.full=队列已满

uuidGenerator.createRandom=使用[{0}]创建用于UUID生成的SecureRandom实例花费了[{1}]毫秒。
uuidGenerator.unable.fit=无法将[{0}]字节放入数组。长度：[{1}]所需长度：[{2}]
