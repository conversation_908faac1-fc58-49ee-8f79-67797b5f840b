# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

err.cookie_name_blank=Le nom de cookie ne doit pas être null ou vide
err.cookie_name_is_token=Le nom de cookie [{0}] est un "token" réservé
err.io.indexOutOfBounds=L''offset [{0}] et/ou la longueur [{1}] spécifiés pour la taille du tableau [{2}] sont invalides
err.io.nullArray=Null a été passée comme tableau d'octets à la méthode d'écriture
err.io.short_read=Lecture partielle

http.method_delete_not_supported=La méthode HTTP DELETE n'est pas supportée par cette URL
http.method_get_not_supported=La méthode HTTP GET n'est pas supportée par cette URL
http.method_not_implemented=Le méthode [{0}] n''est pas implémentée par ce Servlet pour cette URI
http.method_post_not_supported=La méthode HTTP POST n'est pas supportée par cette URL
http.method_put_not_supported=La méthode HTTP PUT n'est pas supportée par cette URL
http.non_http=Requête ou réponse non HTTP
