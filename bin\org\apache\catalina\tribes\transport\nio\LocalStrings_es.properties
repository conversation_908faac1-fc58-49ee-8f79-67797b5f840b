# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

nioReceiver.cleanup.fail=Imposible hacer limpieza en el cierre del selector
nioReceiver.start.fail=Incapaz  de iniciar el recividor del cluster
nioReceiver.stop.fail=No fue posible cerrar el selector de recepción del cluster
nioReceiver.threadpool.fail=ThreadPool no pudo ser inicializado. Escuchador no iniciado.\n

nioReplicationTask.error.register.key=Error al registrar la llave para lectura:[{0}]
nioReplicationTask.process.clusterMsg.failed=Fallo al procesar el mensaje del cluster
nioReplicationTask.unable.ack=No se pudo devolver el  ACK a travez del canal. Esta el canal desconectado?: [{0}]\n

nioSender.not.connected=El NioSender no esta conectado, esto no deberia ocurrir.\n
nioSender.unknown.state=Los Datos estan en un estado desconocido. readyOps=[{0}]

parallelNioSender.send.fail.retrying=El envió de miembro esta fallando por:[{0}] ; Fijándolo como sospechoso y reintentando.
parallelNioSender.send.failed=Fallo al enviar NIO paralelo.
parallelNioSender.sendFailed.attempt=Fallo de envío, intento:[{0}] máximo:[{1}]\n
parallelNioSender.sender.disconnected.sendFailed=Envio fallido, y el remitente esta desconctado. No intentando nuevamente.\n

pooledParallelSender.sender.disconnected=El remitente no esta conectado
pooledParallelSender.unable.retrieveSender=Incapaz de recuperar el enviador desde el pool de enviadores
