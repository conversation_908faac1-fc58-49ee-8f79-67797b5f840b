# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jmxRemoteLifecycleListener.createRegistryFailed=Création du répertoire RMI impossible pour le serveur [{0}] utilisant le port [{1}]
jmxRemoteLifecycleListener.createServerFailed=Le connecteur serveur JMX pour le serveur [{0}] n''a pas pu être créé ou n''a pas pu démarrer
jmxRemoteLifecycleListener.destroyServerFailed=Le connecteur serveur JMX pour le serveur [{0}] n''a pas pu être stoppé
jmxRemoteLifecycleListener.invalidRmiBindAddress=Adresse d''association du RMI invalide [{0}]
jmxRemoteLifecycleListener.invalidURL=L''URL demandée pour le serveur [{0}], [{1}], est incorrect
jmxRemoteLifecycleListener.start=L''écouteur distant JMX a configuré le répertoire sur le port [{0}] et le serveur sur le port [{1}] pour le serveur [{2}]

mBeanFactory.managerContext=Un gestionnaire de sessions ne peut être ajouté qu'à un contexte
