# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

nioReceiver.alreadyStarted=ServerSocketChannel already started
nioReceiver.cleanup.fail=Unable to cleanup on selector close
nioReceiver.clientDisconnect=Replication client disconnected, error when polling key. Ignoring client.
nioReceiver.requestError=Unable to process request in NioReceiver
nioReceiver.run.fail=Unable to run replication listener
nioReceiver.start.fail=Unable to start cluster receiver
nioReceiver.stop.fail=Unable to close cluster receiver selector
nioReceiver.stop.threadRunning=The NioReceiver thread did not stop in a timely manner. Errors may be observed when the selector is closed.
nioReceiver.threadpool.fail=ThreadPool cannot be initialized. Listener not started.
nioReceiver.threadsExhausted=Channel key is registered, but has had no interest ops for the last [{0}] ms. (cancelled: [{1}]):[{2}] last access:[{3}] Possible cause: all threads used, perform thread dump

nioReplicationTask.error.register.key=Error registering key for read:[{0}]
nioReplicationTask.exception.drainChannel=Exception caught in TcpReplicationThread.drainChannel.
nioReplicationTask.process.clusterMsg.failed=Processing of cluster message failed.
nioReplicationTask.unable.ack=Unable to send ACK back through channel, channel disconnected?: [{0}]
nioReplicationTask.unable.drainChannel.ioe=IOException in replication worker, unable to drain channel. Probable cause: Keep alive socket closed[{0}].

nioSender.already.connected=NioSender is already in connected state.
nioSender.datagram.already.established=Datagram channel has already been established. Connection might be in progress.
nioSender.key.inValid=Key is not valid, it must have been cancelled.
nioSender.not.connected=NioSender is not connected, this should not occur.
nioSender.receive.failedAck=Received a failed ack:org.apache.catalina.tribes.transport.Constants.FAIL_ACK_DATA
nioSender.sender.disconnected=Sender has been disconnected, can't process selection key.
nioSender.socketChannel.already.established=Socket channel has already been established. Connection might be in progress.
nioSender.unable.disconnect=Unable to disconnect NioSender. msg=[{0}]
nioSender.unable.receive.ack=Unable to receive an ack message. EOF on socket channel has been reached.
nioSender.unknown.state=Data is in unknown state. readyOps=[{0}]

parallelNioSender.error.keepalive=Error during keepalive test for sender:[{0}]
parallelNioSender.operation.timedout=Operation has timed out([{0}] ms.).
parallelNioSender.send.fail=Member send is failing for:[{0}] ; Setting to suspect.
parallelNioSender.send.fail.retrying=Member send is failing for:[{0}] ; Setting to suspect and retrying.
parallelNioSender.send.failed=Parallel NIO send failed.
parallelNioSender.sendFailed.attempt=Send failed, attempt:[{0}] max:[{1}]
parallelNioSender.sender.disconnected.notRetry=Not retrying send for:[{0}]; Sender is disconnected.
parallelNioSender.sender.disconnected.sendFailed=Send failed, and sender is disconnected. Not retrying.
parallelNioSender.unable.setup.NioSender=Unable to setup NioSender.

pooledParallelSender.sender.disconnected=Sender not connected.
pooledParallelSender.unable.open=Unable to open NIO selector.
pooledParallelSender.unable.retrieveSender=Unable to retrieve a sender from the sender pool
pooledParallelSender.unable.retrieveSender.timeout=Unable to retrieve a data sender, time out([{0}] ms) error.
