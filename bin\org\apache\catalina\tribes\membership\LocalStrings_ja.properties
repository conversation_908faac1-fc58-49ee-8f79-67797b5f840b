# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

McastService.domain=ドメイン更新の送信が出来ません。
McastService.parseSoTimeout=SoTimeoutを解析できません：[{0}]
McastService.parseTTL=TTL 値を解釈できません: [{0}]
McastService.payload=Payload更新の送信が出来ません。
McastService.stopFail=マルチキャストサービスを停止できません。レベル：[{0}]

mcastService.exceed.maxPacketSize=パケット長[{0}]は[{1}]バイトの最大パケットサイズを超えています。
mcastService.missing.property=McastService: 必須プロパティ [{0}] がありません。
mcastService.noStart=マルチキャスト送信が開始または有効になっていません。

mcastServiceImpl.bind=マルチキャストソケットの [{0}:{1}] へのバインドを試行します。
mcastServiceImpl.bind.failed=マルチキャストアドレスへのバインドに失敗しました。 ポートのみにバインドします。
mcastServiceImpl.error.receiving=マルチキャストパッケージを受信中にエラーが発生しました。 500msスリープします。
mcastServiceImpl.invalid.startLevel=不正な開始レベルです。受け付けられるのは Channel.MBR_RX_SEQ と Channel.MBR_TX_SEQ です。
mcastServiceImpl.invalid.stopLevel=無効な停止レベルです。 受け入れ可能なレベルはChannel.MBR_RX_SEQとChannel.MBR_TX_SEQのみです。
mcastServiceImpl.memberDisappeared.failed=メンバー消失メッセージを処理できません。
mcastServiceImpl.packet.tooLong=受信したマルチキャストパケットが大きすぎるためパッケージを破棄します。受信したパケットサイズは [{0}] です。
mcastServiceImpl.receive.running=McastService.receive は既に実行中です。
mcastServiceImpl.recovery=リカバリスレッドがメンバーシップを復旧するため、マルチキャストできなくなります。
mcastServiceImpl.recovery.failed=リカバリの試み[{0}]が失敗しました。[{1}]秒後に再試行します。
mcastServiceImpl.recovery.startFailed=リカバリースレッドはメンバーシップサービスを開始できません。
mcastServiceImpl.recovery.stopFailed=リカバリースレッドはメンバーシップサービスを停止できません。
mcastServiceImpl.recovery.successful=メンバーシップの回復に成功しました。
mcastServiceImpl.send.failed=マルチキャストメッセージを送信出来ません。
mcastServiceImpl.send.running=McastService.sendはすでに実行中です。
mcastServiceImpl.setInterface=マルチホームマルチキャストインターフェイスを構成しました。 : [{0}]
mcastServiceImpl.setSoTimeout=クラスタ マルチキャスト soTimeoutを[{0}]に設定します
mcastServiceImpl.setTTL=クラスターのマルチキャスト TTL を [{0}] に設定しました。
mcastServiceImpl.unable.join=マルチキャストグループに参加できません、システムでマルチキャストが有効になっていることを確認してください。
mcastServiceImpl.unableReceive.broadcastMessage=ブロードキャストメッセージを受信できません。
mcastServiceImpl.waitForMembers.done=sleep完了、メンバーシップを確立し、開始レベル：[{0}]
mcastServiceImpl.waitForMembers.start=クラスタメンバシップを確立するために[{0}]ミリ秒スリープ、開始レベル：[{1}]

memberImpl.invalid.package.begin=パッケージが不正です。[{0}] から開始しなければなりません。
memberImpl.invalid.package.end=不正なパッケージです。[{0}] で終端しなければなりません。
memberImpl.large.payload=ペイロードはtribes が処理するには大きすぎます。
memberImpl.notEnough.bytes=メンバーパッケージのバイト長が不足しています。
memberImpl.package.small=メンバーパッケージが小さすぎて検証できません。
memberImpl.unableParse.hostname=ホスト名を解析できません。

staticMember.invalid.uuidLength=UUIDは正確に16バイトでなければなりません。[{0}]
