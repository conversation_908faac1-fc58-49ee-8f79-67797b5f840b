# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

SecurityListener.checkUmaskFail=Tentative de démarrage avec un paramètre umask [{0}}, qui a été bloquée par l''écouteur org.apache.catalina.security.SecurityListener (configuré habituellement dans CATALINA_BASE/conf/server.xml) car l''umask doit être au moins aussi restreint que [{1}]
SecurityListener.checkUmaskNone=Pas de définition du "umask" trouvée dans la propriété système [{0}]. Il apparaît toutefois que Tomcat tourne sur une plateforme qui supporte l''utilisation de umask. La propriété système est typiquement définie dans CATALINA_HOME/bin/catalina.sh. Le Lifecycle Listener org.apache.catalina.security.SecurityListener (généralement configuré dans CATALINA_BASE/conf/server.xml) s''attend à un umask au moins aussi restrictif que [{1}]
SecurityListener.checkUmaskParseFail=Impossible de traiter la valeur [{0}] comme un umask valide
SecurityListener.checkUmaskSkip=Impossible de déterminer le "umask".  Il semble que Tomcat tourne ici sous Windows, donc évitez la vérification du "umask".
SecurityListener.checkUserWarning=Tentative de démarrage avec l''utilisateur [{0}}, qui a été bloquée par l''écouteur org.apache.catalina.security.SecurityListener (configuré habituellement dans CATALINA_BASE/conf/server.xml)

SecurityUtil.doAsPrivilege=Une exception s'est produite lors de l'exécution du bloc "PrivilegedExceptionAction".
