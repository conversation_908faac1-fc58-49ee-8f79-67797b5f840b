# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

accessLogValve.closeFail=No pude cerrar fichero de historial
accessLogValve.invalidLocale=Fallo al fijar locales a [{0}]\n
accessLogValve.openDirFail=No pude crear directorio [{0}] para historiales de acceso
accessLogValve.openFail=Fallo al abrir el archivo access log [{0}]\n
accessLogValve.rotateFail=No se pudo rotar el historial de acceso

errorReportValve.description=Descripción
errorReportValve.exception=excepción
errorReportValve.exceptionReport=Informe de Excepción
errorReportValve.message=mensaje
errorReportValve.note=nota
errorReportValve.rootCause=causa raíz
errorReportValve.rootCauseInLogs=La traza completa de la causa de este error se encuentra en los archivos de registro del servidor.
errorReportValve.statusHeader=Estado HTTP {0} – {1}
errorReportValve.statusReport=Informe de estado
errorReportValve.type=Tipo

http.400.desc=El requerimiento enviado por el cliente era sintácticamente incorrecto.
http.401.desc=Este requerimiento requiere autenticación HTTP.
http.401.reason=No autorizado
http.402.desc=Este código de estado está reservado para uso futuro.
http.403.desc=El acceso al recurso especificado ha sido prohibido.
http.404.desc=El recurso requerido no está disponible.
http.404.reason=No encontrado
http.405.desc=El método HTTP especificado no está permitido para el recurso requerido.
http.406.desc=El recurso identificado por este requerimiento sólo es capaz de generar respuestas con características no aceptables con arreglo a las cabeceras "accept" de requerimiento.
http.407.desc=El cliente debe de ser primero autenticado en el apoderado.
http.407.reason=Se requiere autenticación de proxy
http.408.desc=El cliente no produjo un requerimiento dentro del tiempo en que el servidor estaba preparado esperando.
http.409.desc=El requerimiento no pudo ser completado debido a un conflicto con el estado actual del recurso.
http.410.desc=El recurso requerido ya no está disponible y no se conoce dirección de reenvío.
http.411.desc=Este requerimiento no puede ser manejado sin un tamaño definido de contenido.
http.412.desc=Una precondición especificada ha fallado para este requerimiento.
http.412.reason=La precon
http.413.desc=La entidad de requerimiento es mayor de lo que el servidor quiere o puede procesar.
http.414.desc=El servidor rechazó este requerimiento porque la URI requerida era demasiado larga.
http.415.desc=El servidor rechazó este requerimiento porque la entidad requerida se encuentra en un formato no soportado por el recurso requerido para el método requerido.
http.415.reason=Tipo de medio no soportado
http.416.desc=El rango de byte requerido no puede ser satisfecho.
http.417.desc=Lo que se espera dado por la cabecera "Expect" de requerimiento no pudo ser completado.
http.421.desc=La solicitud ha sido dirigida a un servidor que no fue capaz de producir una respuesta
http.422.desc=El servidor entendió el tipo de contenido y la sintáxis del requerimiento pero no pudo procesar las instrucciones contenidas.
http.423.desc=La fuente o recurso de destino de un método está bloqueada.
http.423.reason=Bloqueado
http.426.reason=Se requiere actualización
http.428.desc=El servidor de origen requiere que la petición sea condicional
http.429.reason=Demasiadas peticiones
http.431.reason=Los campos de cabecera solicitados son muy largos
http.500.desc=El servidor encontró un error interno que hizo que no pudiera rellenar este requerimiento.
http.501.desc=El servidor no soporta la funcionalidad necesaria para rellenar este requerimiento.
http.502.desc=Este servidor recibió una respuesta inválida desde un servidor que consultó cuando actuaba como apoderado o pasarela.
http.503.desc=El servicio requerido no está disponible en este momento.
http.504.desc=El servidor recibió un Tiempo Agotado desde un servidor superior cuando actuaba como pasarela o apoderado.
http.505.desc=El servidor no soporta la versión de protocolo HTTP requerida.
http.505.reason=Versión HTTP no soportada
http.507.desc=El recurso no tiene espacio suficiente para registrar el estado del recurso tras la ejecución de este método.
http.507.reason=El storage no es suficiente
http.511.desc=El cliente se tiene que autenticar para tener accesso a la red

jdbcAccessLogValve.exception=Excepción realizando entrada de acceso a inserción

remoteIpValve.invalidPortHeader=Valor inválido [{0}] hallado para el puerto en cabecera HTTP [{1}]

requestFilterValve.configInvalid=Uno o más parámetros de configuración inválidos fueron proveídos para  Remote[Addr|Host]Valve lo cual impide que el Valve y sus contenedores padres puedan iniciar

sslValve.certError=No pude procesar cadena de certificado [{0}] para crear un objeto  java.security.cert.X509Certificate
sslValve.invalidProvider=El proveedor de SSL especificado en el conecto asociado con este requerimiento de [{0}] ies inválido. No se pueden procesar los datos del certificado.

stuckThreadDetectionValve.notifyStuckThreadCompleted=El hilo [{0}] (id=[{3}]), que previamente se reportó como atascado, se ha completado. Estuvo activo por aproximadamente [{1}] milisegundos. {2, choice,0#|0< Hay aún [{2}] hilo(s) que son monitorizados por esta Válvula y pueden estar atascados.}
stuckThreadDetectionValve.notifyStuckThreadDetected=El hilo  [{0}] (id=[{6}]) ha estado activo durante [{1}] miilisegundos (desde [{2}]) para servir el mismo requerimiento para [{4}] y puede estar atascado (el umbral configurado para este StuckThreadDetectionValve es de [{5}] segundos). Hay [{3}] hilo(s) en total que son monitorizados por esta Válvula y pueden estar atascados.
