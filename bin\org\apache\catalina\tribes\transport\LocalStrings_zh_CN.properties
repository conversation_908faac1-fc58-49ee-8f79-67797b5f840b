# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

PooledSender.senderDisconnectFail=与 sender 断开连接失败

pooledSender.closed.queue=队列已关闭

receiverBase.bind.failed=对内容类型[{2}]返回[{3}]使用[{0}]匹配[{1}]
receiverBase.socket.bind=服务器套接字接收器绑定到：[{0}]
receiverBase.udp.bind=绑定到的UDP接收器服务器套接字：[{0}]
receiverBase.unable.bind=无法绑定套接字端口:{{0}}，出现异常
receiverBase.unable.bind.udp=无法将UDP套接字绑定到：[{0}]抛出错误。
