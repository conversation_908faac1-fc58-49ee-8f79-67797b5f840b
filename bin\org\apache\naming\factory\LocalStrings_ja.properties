# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

dataSourceLinkFactory.badWrapper=クラス[{0}]のラッパーではありません。

factoryBase.factoryClassError=リソースファクトリクラスを読み込めません
factoryBase.factoryCreationError=リソースファクトリインスタンスを生成できません
factoryBase.instanceCreationError=リソースインスタンスを生成できません

lookupFactory.circularReference=[{0}]を含む循環参照が見つかりました。
lookupFactory.createFailed=JNDI lookup ファクトリークラスのインスタンスを作成できませんでした。
lookupFactory.loadFailed=JNDIルックアップファクトリクラスをロードできませんでした。
lookupFactory.typeMismatch=クラス [{1}] を期待する JNDI 参照 [{0}]に、lookup [{2}] はクラス [{3}] のオブジェクトを返却しました。

resourceFactory.factoryCreationError=リソースファクトリーのインスタンスを作成できません。

resourceLinkFactory.invalidGlobalContext=引数に不正な共通コンテキストが指定されました。
resourceLinkFactory.nullType=グローバルリソース [{1}] を参照するローカルリソースリンク [{0}] に必要な属性がありません。
resourceLinkFactory.unknownType=グローバルリソース [{1}] を参照するローカルリソースリンク [{0}] に未知のクラス [{2}] が指定されました。
resourceLinkFactory.wrongType=グローバルリソース[{1}]を参照するローカルリソースリンク[{0}]は[{2}]のインスタンスを返すと予想されましたが、[{3}]のインスタンスを返しました。
