# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

namingResources.cleanupCloseSecurity=Incapaz de obtener el método [{0}] para el recurso [{1}] en el contenedor  [{2}] debido a ello no se realizó la limpieza para ese recurso\n
namingResources.cleanupNoClose=Recurso [{0}] en contenedor [{1}] no tien un método [{2}] por lo cual no ser hace una limpieza para ese recurso
namingResources.ejbLookupLink=La referencia EJB [{0}] especifica ambas, un ejb-link y un nombre para lookup
