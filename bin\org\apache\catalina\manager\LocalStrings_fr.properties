# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

htmlManagerServlet.appsAvailable=Fonctionnelle
htmlManagerServlet.appsExpire=Expirer les sessions
htmlManagerServlet.appsName=Nom d'affichage
htmlManagerServlet.appsPath=Chemin
htmlManagerServlet.appsReload=Recharger
htmlManagerServlet.appsSessions=Sessions
htmlManagerServlet.appsStart=Démarrer
htmlManagerServlet.appsStop=Arrêter
htmlManagerServlet.appsTasks=Commandes
htmlManagerServlet.appsTitle=Applications
htmlManagerServlet.appsUndeploy=Retirer
htmlManagerServlet.appsVersion=Version
htmlManagerServlet.configReloadButton=Relire
htmlManagerServlet.configSslHostName=Nom d'hôte TLS (optionnel)
htmlManagerServlet.configSslReloadTitle=Relisant les fichiers de configuration TLS
htmlManagerServlet.configTitle=Configuration
htmlManagerServlet.connectorStateAliveSocketCount=Nombre de sockets connectés :
htmlManagerServlet.connectorStateBytesReceived=Octets reçus :
htmlManagerServlet.connectorStateBytesSent=Octets envoyés :
htmlManagerServlet.connectorStateErrorCount=Nombre d'erreurs :
htmlManagerServlet.connectorStateHint=P : Traitement et préparation de la requête S : Service F : Fin R : Prêt K : Connecté
htmlManagerServlet.connectorStateMaxProcessingTime=Temps de traitement maximal :
htmlManagerServlet.connectorStateMaxThreads=Nombre de threads maximum :
htmlManagerServlet.connectorStateProcessingTime=Temps de traitement :
htmlManagerServlet.connectorStateRequestCount=Nombre de requêtes :
htmlManagerServlet.connectorStateTableTitleBRecv=Octets Reçus
htmlManagerServlet.connectorStateTableTitleBSent=Octets Envoyés
htmlManagerServlet.connectorStateTableTitleClientAct=Client (Réel)
htmlManagerServlet.connectorStateTableTitleClientForw=Client (Forwardé)
htmlManagerServlet.connectorStateTableTitleRequest=Requête
htmlManagerServlet.connectorStateTableTitleStage=Etape
htmlManagerServlet.connectorStateTableTitleTime=Temps
htmlManagerServlet.connectorStateTableTitleVHost=Hôte virtuel (VHost)
htmlManagerServlet.connectorStateThreadBusy=Nombre de threads utilisés :
htmlManagerServlet.connectorStateThreadCount=Nombre de threads actuel :
htmlManagerServlet.deployButton=Deployer
htmlManagerServlet.deployConfig=URL du fichier XML de configuration :
htmlManagerServlet.deployPath=Chemin de context (requis):
htmlManagerServlet.deployServer=Emplacement du répertoire ou fichier WAR de déploiement sur le serveur
htmlManagerServlet.deployTitle=Deployer
htmlManagerServlet.deployUpload=Fichier WAR à déployer
htmlManagerServlet.deployUploadFail=ECHEC - Téléversement pour déploiement a échoué, exception : [{0}]
htmlManagerServlet.deployUploadFile=Choisir le fichier WAR à téléverser
htmlManagerServlet.deployUploadInServerXml=ECHEC - Fichier WAR [{0}] ne peut être téléversé lorsque le contexte est défini dans server.xml
htmlManagerServlet.deployUploadNoFile=ECHEC - Téléversement a échoué, aucun fichier
htmlManagerServlet.deployUploadNotWar=ECHEC - Fichier à téléverser, [{0}], doit être un .war
htmlManagerServlet.deployUploadWarExists=ECHEC - Fichier War [{0}] déjà existant sur le serveur
htmlManagerServlet.deployWar=URL vers WAR ou répertoire :
htmlManagerServlet.diagnosticsLeak=Vérifiez si une application web a causé une fuite de mémoire lors de son arrêt, rechargement ou déchargement
htmlManagerServlet.diagnosticsLeakButton=Trouver des fuites
htmlManagerServlet.diagnosticsLeakWarning=Le diagnostic doit démarrer une collecte complète de la mémoire, utilisez le avec précaution dans un environnement de production
htmlManagerServlet.diagnosticsSsl=Diagnostics de configuration TLS du connecteur
htmlManagerServlet.diagnosticsSslConnectorCertsButton=Certificats
htmlManagerServlet.diagnosticsSslConnectorCertsText=Entrez la liste des hôtes virtuels TLS et la chaîne de certificats pour chacun.
htmlManagerServlet.diagnosticsSslConnectorCipherButton=Chiffres
htmlManagerServlet.diagnosticsSslConnectorCipherText=Lister les hôtes virtuels TLS configurés et les chiffres utilisés par chacun
htmlManagerServlet.diagnosticsSslConnectorTrustedCertsButton=Certificats de confiance
htmlManagerServlet.diagnosticsSslConnectorTrustedCertsText=Lister les hôtes virtuels TLS configurés et les certificats de confiance utilisés par chacun
htmlManagerServlet.diagnosticsTitle=Diagnostics
htmlManagerServlet.expire.explain=inactives depuis &ge;
htmlManagerServlet.expire.unit=minutes
htmlManagerServlet.findleaksList=Les applications suivantes ont été arrêtées (redémarrées, retirées), mais certaines de leurs classes sont toujours présentes en mémoire, cela pourrait donc causer une fuite de mémoire (utiliser un profileur pour le confirmer) :
htmlManagerServlet.findleaksNone=Aucune application n'a apparemment causé de fuite de mémoire en l'arrêtant, en la rechargeant ou en la retirant
htmlManagerServlet.helpHtmlManager=Aide HTML Gestionnaire
htmlManagerServlet.helpHtmlManagerFile=../docs/html-manager-howto.html
htmlManagerServlet.helpManager=Aide Gestionnaire
htmlManagerServlet.helpManagerFile=../docs/manager-howto.html
htmlManagerServlet.jvmFreeMemory=Mémoire disponible :
htmlManagerServlet.jvmMaxMemory=Mémoire maximale :
htmlManagerServlet.jvmTableTitleInitial=Initial
htmlManagerServlet.jvmTableTitleMaximum=Maximum
htmlManagerServlet.jvmTableTitleMemoryPool=Pool mémoire
htmlManagerServlet.jvmTableTitleTotal=Total
htmlManagerServlet.jvmTableTitleType=Type
htmlManagerServlet.jvmTableTitleUsed=Utilisé
htmlManagerServlet.jvmTotalMemory=Mémoire totale :
htmlManagerServlet.list=Lister les applications
htmlManagerServlet.manager=Gestionnaire
htmlManagerServlet.messageLabel=Message :
htmlManagerServlet.noManager=-
htmlManagerServlet.noVersion=Aucun spécifié
htmlManagerServlet.osAvailableMemory=Mémoire disponible :
htmlManagerServlet.osFreePageFile=Fichier de page disponible :
htmlManagerServlet.osKernelTime=Temps noyau du processus :
htmlManagerServlet.osMemoryLoad=Charge mémoire :
htmlManagerServlet.osPhysicalMemory=Mémoire physique :
htmlManagerServlet.osTotalPageFile=Fichier de page total :
htmlManagerServlet.osUserTime=Temps utilisateur du processus :
htmlManagerServlet.serverHostname=Nom d'hôte
htmlManagerServlet.serverIPAddress=Adresse IP
htmlManagerServlet.serverJVMVendor=Fournisseur de la JVM
htmlManagerServlet.serverJVMVersion=Version de la JVM
htmlManagerServlet.serverOSArch=Architecture d'OS
htmlManagerServlet.serverOSName=Nom d'OS
htmlManagerServlet.serverOSVersion=Version d'OS
htmlManagerServlet.serverTitle=Serveur
htmlManagerServlet.serverVersion=Version de serveur
htmlManagerServlet.title=Gestionnaire d'applications WEB Tomcat

jmxProxyServlet.noBeanFound=Impossible de trouver de MBean avec le nom d''objet [{0}]
jmxProxyServlet.noOperationOnBean=Impossible de trouver l''opération [{0}] avec [{1}] arguments sur le nom d''objet [{2}], qui est un [{3}]

managerServlet.alreadyContext=ECHEC - l''application existe déjà dans le chemin [{0}]
managerServlet.certsNotAvailable=L'information sur les certificats ne peut pas être obtenu de ce connecteur au cours de son exécution
managerServlet.deleteFail=ECHEC - Impossible de supprimer [{0}], ce qui pourrait causer des problèmes
managerServlet.deployFailed=ECHEC - Echec au déploiement de l''application pour le chemin de contexte [{0}]
managerServlet.deployed=OK - Application déployée pour le chemin de contexte [{0}]
managerServlet.deployedButNotStarted=ECHEC - Application déployée pour le chemin de contexte [{0}] mais le démarrage du contexte a échoué
managerServlet.exception=ECHEC - L''exception [{0}] a été rencontrée
managerServlet.findleaksFail=ECHEC - Echec de la recherche de fuites, car l'hôte n'est pas un StandardHost
managerServlet.findleaksList=OK - De possibles fuites de mémoire ont été trouvées dans les applications suivantes :
managerServlet.findleaksNone=OK - Aucune fuite de mémoire trouvée
managerServlet.inService=ECHEC - Une opération de gestion est déjà en cours pour l''application [{0}]
managerServlet.invalidCommand=ECHEC - Des paramètres incorrects ont été fournis pour la commande [{0}]
managerServlet.invalidPath=ECHEC - Un chemin de contexte invalide [{0}] a été spécifié
managerServlet.listed=OK - Applications listées pour l''hôte virtuel (virtual host) [{0}]
managerServlet.mkdirFail=ECHEC - Le répertoire [{0}] n''a pas pu être créé
managerServlet.noCommand=ECHEC - Aucune commande n'a été spécifiée
managerServlet.noContext=ECHEC - Aucun contexte n''existe pour le chemin [{0}]
managerServlet.noGlobal=ECHEC - Aucune ressource JNDI globale n'est disponible
managerServlet.noManager=ECHEC - Aucun gestionnaire n''existe pour le chemin [{0}]
managerServlet.noSelf=ECHEC - Le gestionnaire ne peut se recharger, se retirer, s'arrêter, ou se déployer lui-même
managerServlet.noWrapper=Le conteneur n'a pas appelé "setWrapper()" pour cette servlet
managerServlet.notDeployed=ECHEC - Le contexte [{0}] est défini dans server.xml et ne peut être retiré
managerServlet.notSslConnector=SSL n'est pas activé pour ce connecteur
managerServlet.objectNameFail=ECHEC - Le nom d''objet [{0}] n''a pas pu être enregistré pour le Servlet de Gestion
managerServlet.postCommand=ECHEC - Tentative d''utilisation de la commande [{0}] via une requête GET, mais POST est requis
managerServlet.reloaded=OK - L''application associée au chemin de contexte [{0}] a été rechargée
managerServlet.renameFail=ECHEC - N''a pas pu renommer [{0}] vers [{1}], cela pourrait causer des problèmes pour de prochains déploiements
managerServlet.resourcesAll=OK - Liste des ressources globales de tout type
managerServlet.resourcesType=OK - Liste des ressources globales de type [{0}]
managerServlet.saveFail=ECHEC - La sauvegarde de la configuration a échoué : [{0}]
managerServlet.saved=OK - Configuration serveur sauvegardée
managerServlet.savedContext=OK - Configuration du contexte [{0}] sauvegardée
managerServlet.savedContextFail=ECHEC - L''enregistrement de la configuration du Contexte [{0}] a échoué
managerServlet.serverInfo=OK - Informations sur le serveur\n\
Version de Tomcat : [{0}]\n\
Nom de l''OS : [{1}]\n\
Version de l''OS : [{2}]\n\
Architecture de l''OS : [{3}]\n\
Version de la JVM : [{4}]\n\
Fournisseur de la JVM : [{5}]
managerServlet.sessiondefaultmax=La valeur par défaut du délai d''inactivité maximum d''une sessions est de [{0}] minutes
managerServlet.sessions=OK - Information de session pour l''application au chemin de contexte [{0}]
managerServlet.sessiontimeout=Inactivité pendant [{0}] minutes : [{1}] sessions
managerServlet.sessiontimeout.expired=Inactivité pendant [{0}] minutes : [{1}] sessions ont été expirées
managerServlet.sessiontimeout.unlimited=Délai illimité : [{0}] sessions
managerServlet.sslConnectorCerts=OK - Information sur le connecteur et la chaîne de certificats
managerServlet.sslConnectorCiphers=OK - Information sur le connecteur et les chiffres SSL
managerServlet.sslConnectorTrustedCerts=OK - Information sur le Connecteur / Certificat de confiance
managerServlet.sslReload=OK - La configuration TLS de [{0}] a été rechargée
managerServlet.sslReloadAll=OK - Configuration TLS rechargée pour tous les hôtes virtuels TLS
managerServlet.sslReloadFail=ECHEC - Echec lors du rechargement de la configuration TLS
managerServlet.startFailed=ECHEC - L''application pour le chemin de contexte [{0}] n''a pas pu être démarrée
managerServlet.started=OK - Application démarrée pour le chemin de contexte [{0}]
managerServlet.stopped=OK - Application arrêtée pour le chemin de contexte [{0}]
managerServlet.storeConfig.noMBean=ECHEC - Pas de mbean StoreConfig enregistré à [{0}], l''enregistrement est effectué par StoreConfigLifecycleListener
managerServlet.threaddump=OK - Etat des threads de la JVM
managerServlet.trustedCertsNotConfigured=Aucun certificat de confiance n'est configuré pour cet hôte virtuel
managerServlet.undeployed=OK - Application non déployée pour le chemin de contexte [{0}]
managerServlet.unknownCommand=ECHEC - Commande inconnue [{0}]
managerServlet.vminfo=OK - Informations sur la VM

statusServlet.complete=Etat complet du serveur
statusServlet.title=Etat du serveur
