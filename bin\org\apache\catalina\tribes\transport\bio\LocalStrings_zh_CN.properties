# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

bioReceiver.socket.closeFailed=socket.关闭失败
bioReceiver.threadpool.fail=线程池可以初始化。侦听器未启动

bioReplicationTask.messageDataReceived.error=错误抛出，来自于消息数据收到
bioReplicationTask.reader.closeFailed=无法关闭reader
bioReplicationTask.socket.closeFailed=无法关闭套接字
bioReplicationTask.unable.service=不能服务bio套接字

bioSender.ack.eof=在本地端口[{0}:{1，number，integer}]达到EOF
bioSender.ack.missing=不能读确认表格:[{0}] {1,number,integer}] in {2,number,integer} 毫秒, 失去socket连接, 重试连接.
bioSender.ack.wrong=在本地端口[{0}：{1，number，integer}]读取10个字节后丢失正确的ACK
bioSender.closeSocket=发件人关闭套接字到[{0}：{1，number，integer}]（关闭计数{2，数字，整数}）
bioSender.fail.AckReceived=收到一个失败的 ack ):org.apache.catalina.tribes.transport.Constants.FAIL_ACK_DATA
bioSender.openSocket=发件人打开套接字到[{0}：{1，number，integer}]（打开计数{2，数字，整数}）
bioSender.send.again=再次发送数据到 [{0}:{1,number,integer}]

pooledMultiSender.unable.retrieve.sender=无法获取数据发送器，超时([{0}] ms)错误
