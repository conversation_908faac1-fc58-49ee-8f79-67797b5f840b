# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

digesterFactory.missingSchema=XMLスキーマ[{0}]が見つかりませんでした。 これは、XML検証が有効な場合、XML検証を中断する可能性が非常に高いです。

localResolver.unresolvedEntity=publicID [{1}]、system ID [{2}]、およびベースURI [{3}]を持つXMLリソース[{0}]を既知のローカルエンティティに解決できませんでした。

xmlErrorHandler.error=処理[{1}]の間に致命的ではないエラー[{0}]が報告されました。
xmlErrorHandler.warning=警告[{0}]が報告されました。[{1}]を処理中
