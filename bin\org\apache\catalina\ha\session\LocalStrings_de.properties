# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

deltaManager.foundMasterMember=Für den Kontext [{0}] wurde der Replikationsmaster [{1}] gefunden
deltaManager.noContextManager=Manager [{0}]: Als Antwort auf eine um [{1}] gesendete  "Hole alle Sitzungsdaten"-Nachricht, wurde nach [{2}] ms eine "Kein passender Context-Manager"-Nachricht empfangen.
deltaManager.receiveMessage.allSessionDataBegin=Manager [{0}]: alle Sitzungsdaten empfangen
deltaManager.receiveMessage.delta.unknown=Manager [{0}]: Habe Session-Delta für unbekannte Session [{1}] empfangen
deltaManager.receiveMessage.unloadingBegin=Manager [{0}]: Beginne die Sessions zu entladen
deltaManager.unloading.ioe=IOExceptio während des Speichers der Persisted Sessions: [{0}]

deltaRequest.removeUnable=Kann Element nicht entfernen:
deltaRequest.wrongPrincipalClass=Der ClusterManager unterstützt nur GenericPrincipal. Ihr Realm benutzt die Principal Klasse [{0}].

jvmRoute.valve.started=JvmRouteBinderValve gestartet
