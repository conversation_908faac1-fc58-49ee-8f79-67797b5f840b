# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

ReplicationValve.crossContext.add=将跨上下文会话复制容器添加到replicationValve threadlocal
ReplicationValve.crossContext.registerSession=注册交叉上下文会话id =[{0}]来自上下文[{1}]
ReplicationValve.crossContext.remove=从replicationValve threadlocal中删除跨上下文会话复制容器
ReplicationValve.crossContext.sendDelta=从上下文[{0}]发送跨上下文会话增量。
ReplicationValve.filter.failure=无法编译 filter = [{0}]
ReplicationValve.filter.loading=正在加载请求筛选器=[{0}]
ReplicationValve.invoke.uri=在[{0}]上执行同步请求
ReplicationValve.nocluster=没有为此请求配置群集。
ReplicationValve.resetDeltaRequest=群集是独立的：在上下文[{0}]处重置会话请求增量
ReplicationValve.send.failure=无法执行同步请求。
ReplicationValve.send.invalid.failure=无法通过群集发送会话[id={0}]无效消息。
ReplicationValve.session.found=上下文[{0}]：找到会话[{1}]但它不是ClusterSession。
ReplicationValve.session.indicator=上下文[{0}]：请求属性[{2}]中会话[{1}]的优先级为[{3}]。
ReplicationValve.session.invalid=上下文[{0}]：请求的会话[{1}]在此节点上无效，已删除或未复制。
ReplicationValve.stats=对于[{2}]请求、[{3}]发送请求、[{4}]跨上下文请求和[{5}]筛选请求，平均请求时间为[{0}]ms，群集开销时间为[{1}]ms（总请求时间为[{6}]ms，总群集请求时间为[{7}]ms）。

simpleTcpCluster.clustermanager.cloneFailed=无法克隆群集管理器，默认为org.apache.catalina.ha.session.DeltaManager。
simpleTcpCluster.clustermanager.notImplement=连接器 [{0}] 不能继承 ClusterManager，除非集群被停止。
simpleTcpCluster.member.addFailed=无法连接到复制系统。
simpleTcpCluster.member.added=添加同步成员：[{0}]
simpleTcpCluster.member.disappeared=收到成员消失:[{0}]
simpleTcpCluster.member.removeFailed=无法从复制系统中移除集群节点
simpleTcpCluster.sendFailed=无法使用集群发送器发送消息
simpleTcpCluster.start=群集即将启动
simpleTcpCluster.startUnable=无法启动群集。
simpleTcpCluster.stopUnable=无法停止集群
simpleTcpCluster.unableSend.localMember=无法将消息发送到本地成员[{0}]
