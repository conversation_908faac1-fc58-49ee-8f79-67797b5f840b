# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

connectionFactory.lifetimeExceeded=连接的生存期[{0}]毫秒超过了允许的最大值[{1}]毫秒。

pool.close.fail=无法关闭连接池。

poolableConnection.validate.fastFail=此连接上预先抛出了致命的 SQLException。

poolableConnectionFactory.validateObject.fail=无法验证可共用连接。

poolingDataSource.factoryConfig=PoolableConnectionFactory 未连接到连接池。请调用 setPool() 修复此配置。

swallowedExceptionLogger.onSwallowedException=一个内部对象池吞并了一个异常。
