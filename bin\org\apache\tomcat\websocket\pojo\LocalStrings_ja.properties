# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

pojoEndpointBase.closeSessionFail=エラー処理中に WebSocket セッションを切断できませんでした。
pojoEndpointBase.onCloseFail=タイプ[{0}]のPOJOのPOJOエンドポイントのonCloseメソッドの呼び出しに失敗しました
pojoEndpointBase.onError=[{0}]に対してエラー処理が構成されておらず、次のエラーが発生しました。
pojoEndpointBase.onErrorFail=タイプ[{0}]のPOJOのPOJOエンドポイントのonErrorメソッドの呼び出しに失敗しました
pojoEndpointBase.onOpenFail=タイプ[{0}]のPOJOのPOJOエンドポイントのonOpenメソッドの呼び出しに失敗しました。

pojoEndpointServer.getPojoInstanceFail=POJO クラス [{0}] をインスタンス化できませんでした。

pojoMessageHandlerWhole.decodeIoFail=メッセージの復号中に入出力エラーが発生しました。
pojoMessageHandlerWhole.maxBufferSize=この実装で対応可能なメッセージサイズの上限値は Integer.MAX_VALUE です。

pojoMethodMapping.decodePathParamFail=パスパラメータの値 [{0}] を [{1}] 型として解釈できません。
pojoMethodMapping.duplicateAnnotation=クラス [{1}] にアノテーション [{0}] が重複しています。
pojoMethodMapping.duplicateLastParam=OnMessageでアノテーション付けされたクラス[{1}]のメソッド[{0}]に複数のbooleanパラメータが存在します。
pojoMethodMapping.duplicateMessageParam=OnMessage アノテーションで修飾されたクラス [{1}] のメソッド [{0}] に複数のメッセージパラメーターが存在します。
pojoMethodMapping.duplicatePongMessageParam=OnMessageでアノテーションされたクラス[{1}]のメソッド[{0}]に複数のPongMessageパラメータが存在します。
pojoMethodMapping.duplicateSessionParam=OnMessage アノテーションで修飾したクラス [{1}] のメソッド [{0}] に複数のセッションパラメーターが存在します。
pojoMethodMapping.invalidDecoder=指定されたデコーダークラス [{0}] をインスタンス化できませんでした。
pojoMethodMapping.methodNotPublic=アノテーション付きめそっぢがpublicではありません。
pojoMethodMapping.noDecoder=OnMessageでアノテーションが付けられたクラス[{1}]のメソッド[{0}]に存在するメッセージパラメータのデコーダが見つかりませんでした。
pojoMethodMapping.noPayload=OnMessage アノテーションで修飾されたクラス [{1}] のメソッド [{0}] にはペイロードに対応するパラメーターがありません。
pojoMethodMapping.onErrorNoThrowable=OnErrorでアノテーション付けされたクラス[{1}]のメソッド[{0}]に、Throwableパラメータがありませんでした。
pojoMethodMapping.paramWithoutAnnotation=@PathParamアノテーションを持たなかったクラス[{2}]のメソッド[{1}]に[{0}]型のパラメータが見つかりました
pojoMethodMapping.partialInputStream=OnMessage アノテーションで修飾されたクラス [{1}] のメソッド [{0}] に入力ストリームと boolean の不正なパラメーターが存在します。
pojoMethodMapping.partialObject=OnMessageでアノテーションされたクラス[{1}]のメソッド[{0}]に無効なオブジェクトおよびboolean パラメータがあります
pojoMethodMapping.partialPong=OnMessageでアノテーション付けされたクラス[{1}]のメソッド[{0}]に存在するPongMessageパラメータおよびbooleanパラメータが無効です。
pojoMethodMapping.partialReader=OnMessage アノテーションで修飾したクラス [{1}] のメソッド [{0}] に Reader および boolean の不正なパラメーターがあります。
pojoMethodMapping.pongWithPayload=OnMessageでアノテーションが付けられたクラス[{1}]のメソッド[{0}]に、無効なPongMessageおよびMessageパラメータがあります。

pojoPathParam.wrongType=タイプ[{0}]はパスパラメータとして許可されていません。 @PathParamでアノテーションが付けられたパラメータは、文字列、Javaプリミティブ、またはそれらのボックス版のみです。
