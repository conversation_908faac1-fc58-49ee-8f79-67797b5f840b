<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE mbeans-descriptors PUBLIC
   "-//Apache Software Foundation//DTD Model MBeans Configuration File"
   "http://jakarta.apache.org/commons/dtds/mbeans-descriptors.dtd">
<mbeans-descriptors>
  <mbean
    name="MemberImpl"
    description="Cluster member"
    domain="Catalina"
    group="Cluster"
    type="org.apache.catalina.tribes.membership.MemberImpl">
    <attribute
      name="failing"
      description="Has a problem been observed with this member (failing is worse than suspect)"
      type="boolean"
      is="true"
      writeable="false"/>
    <attribute
      name="hostname"
      description="The name of the host where this member is located"
      type="String"
      writeable="false"/>
    <attribute
      name="memberAliveTime"
      description="The number of milliseconds since this member was created"
      type="long"
      writeable="false"/>
    <attribute
      name="msgCount"
      description="The number of messages sent by this member"
      type="int"
      writeable="false"/>
    <attribute
      name="name"
      description="The unique name of this member within the cluster"
      type="String"
      writeable="false"/>
    <attribute
      name="port"
      description="The tcp port the member is listening on"
      type="int"
      writeable="false"/>
    <attribute
      name="ready"
      description="Is this member ready to send messages"
      type="boolean"
      is="true"
      writeable="false"/>
    <attribute
      name="securePort"
      description="The tcp(SSL) port the member is listening on"
      type="int"
      writeable="false"/>
    <attribute
      name="serviceStartTime"
      description="The time the member was started"
      type="long"
      writeable="false"/>
    <attribute
      name="suspect"
      description="Has a potential problem been observed with this member (failing is worse than suspect)"
      type="boolean"
      is="true"
      writeable="false"/>
    <attribute
      name="udpPort"
      description="The upd port the member is listening on"
      type="int"
      writeable="false"/>
    <attribute
      name="local"
      description="The flag indicating that this member is a local member"
      type="boolean"
      is="true"
      writeable="false"/>
  </mbean>
  <mbean
    name="StaticMember"
    description="Cluster static member"
    domain="Catalina"
    group="Cluster"
    type="org.apache.catalina.tribes.membership.StaticMember">
    <attribute
      name="failing"
      description="Has a problem been observed with this member (failing is worse than suspect)"
      type="boolean"
      is="true"
      writeable="false"/>
    <attribute
      name="hostname"
      description="The name of the host where this member is located"
      type="String"
      writeable="false"/>
    <attribute
      name="memberAliveTime"
      description="The number of milliseconds since this member was created"
      type="long"
      writeable="false"/>
    <attribute
      name="msgCount"
      description="The number of messages sent by this member"
      type="int"
      writeable="false"/>
    <attribute
      name="name"
      description="The unique name of this member within the cluster"
      type="String"
      writeable="false"/>
    <attribute
      name="port"
      description="The tcp port the member is listening on"
      type="int"
      writeable="false"/>
    <attribute
      name="ready"
      description="Is this member ready to send messages"
      type="boolean"
      is="true"
      writeable="false"/>
    <attribute
      name="securePort"
      description="The tcp(SSL) port the member is listening on"
      type="int"
      writeable="false"/>
    <attribute
      name="serviceStartTime"
      description="The time the member was started"
      type="long"
      writeable="false"/>
    <attribute
      name="suspect"
      description="Has a potential problem been observed with this member (failing is worse than suspect)"
      type="boolean"
      is="true"
      writeable="false"/>
    <attribute
      name="udpPort"
      description="The upd port the member is listening on"
      type="int"
      writeable="false"/>
    <attribute
      name="local"
      description="The flag indicating that this member is a local member"
      type="boolean"
      is="true"
      writeable="false"/>
  </mbean>
</mbeans-descriptors>
