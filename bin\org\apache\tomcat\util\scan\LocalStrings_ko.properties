# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jarScan.classloaderFail=클래스로더 계층구조로부터 [{0}]을(를) 스캔하지 못했습니다.
jarScan.classloaderJarNoScan=클래스패스로부터 파일 [{0}]에 대한 JAR 스캔을 수행하지 않습니다.
jarScan.classloaderJarScan=클래스패스로부터 JAR [{0}]을(를) 스캔합니다.
jarScan.classloaderStart=클래스로더 계층 구조에서 JAR들을 스캔합니다.
jarScan.jarUrlStart=URL [{0}]에 위치한 JAR를 스캔합니다.
jarScan.webinfclassesFail=/WEB-INF/classes를 스캔하지 못했습니다.
jarScan.webinflibFail=/WEB-INF/lib으로부터 JAR [{0}]을(를) 스캔하지 못했습니다.
jarScan.webinflibJarNoScan=/WEB-INF/lib 내의 파일 [{0}]에 대한 JAR 스캔을 수행하지 않습니다.
jarScan.webinflibJarScan=/WEB-INF/lib으로부터 JAR [{0}]을(를) 스캔합니다.
jarScan.webinflibStart=JAR들을 찾기 위해 /WEB-INF/lib을 스캔합니다.
