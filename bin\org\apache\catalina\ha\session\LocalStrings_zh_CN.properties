# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

backupManager.noCluster=没有与此上下文关联的集群：[{0}]
backupManager.startFailed=启动BackupManager: [{0}]失败
backupManager.startUnable=无法启动BackupManager: [{0}]
backupManager.stopped=管理者[{0}]正在停止。

clusterSessionListener.noManager=上下文管理器不存在：[{0}]

deltaManager.createMessage.access=管理器[{0}]：创建会话为会话[{1}]存取消息
deltaManager.createMessage.accessChangePrimary=管理器{0}：为会话{1}创建更改主节点消息。
deltaManager.createMessage.allSessionData=管理器[{0}]发送了所有会话数据。
deltaManager.createMessage.allSessionTransferred=管理器[{0}]发送了所有传输的会话数据
deltaManager.createMessage.delta=管理器[{0}] )：为会话[{1}]创建增量请求消息
deltaManager.createMessage.expire=管理器[{0}] (：为会话[{1}]创建会话过期消息
deltaManager.createMessage.unableCreateDeltaRequest=无法序列化sessionid[{0}]的增量请求
deltaManager.createSession.newSession=用id[{0}]创建一个扩展会话(DeltaSession)，总数为 [{1}]
deltaManager.dropMessage=管理器[{0}]：将消息[{1}]放入GET_所有会话同步阶段开始日期[{2}]消息日期[{3}]。
deltaManager.expireSessions=管理器[{0}]关闭时使会话过期
deltaManager.foundMasterMember=复制主master 成员在上下文中被发现.\n
deltaManager.loading.cnfe=加载持久化会话 [{0}] 时出现ClassNotFoundException
deltaManager.loading.existing.session=重载现有会话[{0}]。
deltaManager.loading.ioe=加载持久 session 时出现 IOException：[{0}]
deltaManager.managerLoad=从永久存储加载会话时发生异常
deltaManager.noCluster=启动。。。没有与此上下文关联的群集：[{0}]。
deltaManager.noContextManager=管理器[{0}]：回复[{1}]发送的“获取所有会话数据”消息，在[{2}] ms后收到“无匹配的上下文管理器”消息
deltaManager.noMasterMember=启动。。。在域[{1}]没有上下文[{0}]的其他成员
deltaManager.noMembers=管理器[{0}]：正在跳过状态传输。群集组中没有活动的成员。
deltaManager.noSessionState=管理者[{0}]：没有收到[{1}]发送的会话状态，在[{2}]毫秒之后超时。
deltaManager.receiveMessage.accessed=管理器[{0}]：接收会话为会话[{1}]存取消息
deltaManager.receiveMessage.allSessionDataAfter=Manager [{0}]: session 状态反序列化
deltaManager.receiveMessage.allSessionDataBegin=管理者[{0}]：接收到所有会话数据状态
deltaManager.receiveMessage.createNewSession=管理器{0}：已收到会话为会话{1}创建的消息
deltaManager.receiveMessage.delta=管理器[{0}]：已收到会话[{1}的会话增量消息。
deltaManager.receiveMessage.delta.unknown=管理器[{0}]：未知会话的接收会话增量[{1}]
deltaManager.receiveMessage.error=管理器[{0}]：无法通过TCP通道接收消息
deltaManager.receiveMessage.eventType=管理器[{0}]：从[{2}]接收到类型为[{1}]的SessionMessage
deltaManager.receiveMessage.expired=管理器[{0}]: 接收到的会话 [{1}] 已过期。
deltaManager.receiveMessage.noContextManager=从节点[{1}:{2}]接收的管理器[{0}]没有上下文管理器
deltaManager.receiveMessage.transfercomplete=从节点[{1}:{2}]接收的管理器[{0}]会话状态已传输。
deltaManager.receiveMessage.unloadingAfter=管理器[{0}]：卸载会话完成
deltaManager.receiveMessage.unloadingBegin=管理器[{0}]: 开始卸载会话
deltaManager.registerCluster=将管理器[{0}]注册到名为[{2}]的集群元素[{1}]
deltaManager.sendMessage.newSession=\ 管理器 [{0}] 发送新的会话 [{1}]
deltaManager.sessionReceived=管理器[{0}]；在[{1}]发送的会话状态在[{2}]毫秒内收到。
deltaManager.startClustering=在[{0}]启动群集管理器
deltaManager.stopped=管理器[{0}]已停止
deltaManager.unableSerializeSessionID=无法序列化会话ID [{0}]
deltaManager.unloading.ioe=当保存永久回话:[{0}] 时，抛出 IOException
deltaManager.waitForSessionState=管理器[{0}]，正在从[{1}]请求会话状态。如果在[{2}]秒内未收到会话状态，则此操作将超时

deltaRequest.invalidAttributeInfoType=无效的属性信息类型=[{0}]
deltaRequest.removeUnable=不能移除元素
deltaRequest.showPrincipal=Principal [{0}] 和session [{1}]产生关联。
deltaRequest.ssid.mismatch=回话ID不匹配，不执行delta请求
deltaRequest.ssid.null=setSessionId的会话Id为空
deltaRequest.wrongPrincipalClass=ClusterManager仅支持GenericPrincipal。 你的Realm使用的Principal类为[{0}]。

deltaSession.notifying=通知群集会话过期：primary=[{1}]，sessionId[{2}]
deltaSession.readSession=readObject（）正在加载会话[{0}]
deltaSession.writeSession=writeObject()存储会话[{0}]

jvmRoute.cannotFindSession=找不到会话[{0}]
jvmRoute.changeSession=会话从[{0}]切换到[{1}]
jvmRoute.failover=在会话id[{2}]检测到具有不同jvmRoute的故障转移-原始路由：[{0}]新路由：[{1}]
jvmRoute.foundManager=在[{1}]找到群集管理器[{0}]
jvmRoute.missingJvmRouteAttribute=没有配置引擎jvmRoute属性！
jvmRoute.noCluster=已配置JvmRouterBinderValve，但未使用群集。如果使用了PersistentManager，故障转移仍然有效。
jvmRoute.notFoundManager=没有在 [{0}] 找到Cluster Manager
jvmRoute.set.originalsessionid=在请求属性[{0}]值：[{1}]处设置原始会话ID
jvmRoute.turnoverInfo=周转检查时间[{0}]msec
jvmRoute.valve.started=JvmRouteBinderValve 启动
jvmRoute.valve.stopped=JvmRouteBinderValve停止

standardSession.notSerializable=无法序列化会话[{1}]的会话属性[{0}]。
standardSession.removeAttribute.ise=removeAttribute:会话已失效。
standardSession.setAttribute.namenull=setAttribute:名称属性不能为空
