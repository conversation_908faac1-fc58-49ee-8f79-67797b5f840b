# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

htmlManagerServlet.appsAvailable=Running
htmlManagerServlet.appsExpire=Expire sessions
htmlManagerServlet.appsName=Display Name
htmlManagerServlet.appsPath=Path
htmlManagerServlet.appsReload=Reload
htmlManagerServlet.appsSessions=Sessions
htmlManagerServlet.appsStart=Start
htmlManagerServlet.appsStop=Stop
htmlManagerServlet.appsTasks=Commands
htmlManagerServlet.appsTitle=Applications
htmlManagerServlet.appsUndeploy=Undeploy
htmlManagerServlet.appsVersion=Version
htmlManagerServlet.configReloadButton=Re-read
htmlManagerServlet.configSslHostName=TLS host name (optional)
htmlManagerServlet.configSslReloadTitle=Re-read TLS configuration files
htmlManagerServlet.configTitle=Configuration
htmlManagerServlet.connectorStateAliveSocketCount=Keep alive sockets count:
htmlManagerServlet.connectorStateBytesReceived=Bytes received:
htmlManagerServlet.connectorStateBytesSent=Bytes sent:
htmlManagerServlet.connectorStateErrorCount=Error count:
htmlManagerServlet.connectorStateHint=P: Parse and prepare request S: Service F: Finishing R: Ready K: Keepalive
htmlManagerServlet.connectorStateMaxProcessingTime=Max processing time:
htmlManagerServlet.connectorStateMaxThreads=Max threads:
htmlManagerServlet.connectorStateProcessingTime=Processing time:
htmlManagerServlet.connectorStateRequestCount=Request count:
htmlManagerServlet.connectorStateTableTitleBRecv=Bytes Recv
htmlManagerServlet.connectorStateTableTitleBSent=Bytes Sent
htmlManagerServlet.connectorStateTableTitleClientAct=Client (Actual)
htmlManagerServlet.connectorStateTableTitleClientForw=Client (Forwarded)
htmlManagerServlet.connectorStateTableTitleRequest=Request
htmlManagerServlet.connectorStateTableTitleStage=Stage
htmlManagerServlet.connectorStateTableTitleTime=Time
htmlManagerServlet.connectorStateTableTitleVHost=VHost
htmlManagerServlet.connectorStateThreadBusy=Current threads busy:
htmlManagerServlet.connectorStateThreadCount=Current thread count:
htmlManagerServlet.deployButton=Deploy
htmlManagerServlet.deployConfig=XML Configuration file path:
htmlManagerServlet.deployPath=Context Path (required):
htmlManagerServlet.deployServer=Deploy directory or WAR file located on server
htmlManagerServlet.deployTitle=Deploy
htmlManagerServlet.deployUpload=WAR file to deploy
htmlManagerServlet.deployUploadFail=FAIL - Deploy Upload Failed, Exception: [{0}]
htmlManagerServlet.deployUploadFile=Select WAR file to upload
htmlManagerServlet.deployUploadInServerXml=FAIL - War file [{0}] cannot be uploaded if context is defined in server.xml
htmlManagerServlet.deployUploadNoFile=FAIL - File upload failed, no file
htmlManagerServlet.deployUploadNotWar=FAIL - File uploaded [{0}] must be a .war
htmlManagerServlet.deployUploadWarExists=FAIL - War file [{0}] already exists on server
htmlManagerServlet.deployWar=WAR or Directory path:
htmlManagerServlet.diagnosticsLeak=Check to see if a web application has caused a memory leak on stop, reload or undeploy
htmlManagerServlet.diagnosticsLeakButton=Find leaks
htmlManagerServlet.diagnosticsLeakWarning=This diagnostic check will trigger a full garbage collection. Use it with extreme caution on production systems.
htmlManagerServlet.diagnosticsSsl=TLS connector configuration diagnostics
htmlManagerServlet.diagnosticsSslConnectorCertsButton=Certificates
htmlManagerServlet.diagnosticsSslConnectorCertsText=List the configured TLS virtual hosts and the certificate chain for each.
htmlManagerServlet.diagnosticsSslConnectorCipherButton=Ciphers
htmlManagerServlet.diagnosticsSslConnectorCipherText=List the configured TLS virtual hosts and the ciphers for each.
htmlManagerServlet.diagnosticsSslConnectorTrustedCertsButton=Trusted Certificates
htmlManagerServlet.diagnosticsSslConnectorTrustedCertsText=List the configured TLS virtual hosts and the trusted certificates for each.
htmlManagerServlet.diagnosticsTitle=Diagnostics
htmlManagerServlet.expire.explain=with idle &ge;
htmlManagerServlet.expire.unit=minutes
htmlManagerServlet.findleaksList=The following web applications were stopped (reloaded, undeployed), but their\n\
classes from previous runs are still loaded in memory, thus causing a memory\n\
leak (use a profiler to confirm):\n
htmlManagerServlet.findleaksNone=No web applications appear to have triggered a memory leak on stop, reload or undeploy.
htmlManagerServlet.helpHtmlManager=HTML Manager Help
htmlManagerServlet.helpHtmlManagerFile=../docs/html-manager-howto.html
htmlManagerServlet.helpManager=Manager Help
htmlManagerServlet.helpManagerFile=../docs/manager-howto.html
htmlManagerServlet.jvmFreeMemory=Free Memory:
htmlManagerServlet.jvmMaxMemory=Max Memory:
htmlManagerServlet.jvmTableTitleInitial=Initial
htmlManagerServlet.jvmTableTitleMaximum=Maximum
htmlManagerServlet.jvmTableTitleMemoryPool=Memory Pool
htmlManagerServlet.jvmTableTitleTotal=Total
htmlManagerServlet.jvmTableTitleType=Type
htmlManagerServlet.jvmTableTitleUsed=Used
htmlManagerServlet.jvmTotalMemory=Total Memory:
htmlManagerServlet.list=List Applications
htmlManagerServlet.manager=Manager
htmlManagerServlet.messageLabel=Message:
htmlManagerServlet.noManager=-
htmlManagerServlet.noVersion=None specified
htmlManagerServlet.osAvailableMemory=Available memory:
htmlManagerServlet.osFreePageFile=Free page file:
htmlManagerServlet.osKernelTime=Process kernel time:
htmlManagerServlet.osMemoryLoad=Memory load:
htmlManagerServlet.osPhysicalMemory=Physical memory:
htmlManagerServlet.osTotalPageFile=Total page file:
htmlManagerServlet.osUserTime=Process user time:
htmlManagerServlet.serverHostname=Hostname
htmlManagerServlet.serverIPAddress=IP Address
htmlManagerServlet.serverJVMVendor=JVM Vendor
htmlManagerServlet.serverJVMVersion=JVM Version
htmlManagerServlet.serverOSArch=OS Architecture
htmlManagerServlet.serverOSName=OS Name
htmlManagerServlet.serverOSVersion=OS Version
htmlManagerServlet.serverTitle=Server Information
htmlManagerServlet.serverVersion=Tomcat Version
htmlManagerServlet.title=Tomcat Web Application Manager

jmxProxyServlet.noBeanFound=Cannot find MBean with object name [{0}]
jmxProxyServlet.noOperationOnBean=Cannot find operation [{0}] with [{1}] arguments on object name [{2}], which is a [{3}]

managerServlet.alreadyContext=FAIL - Application already exists at path [{0}]
managerServlet.certsNotAvailable=Certificate information cannot be obtained from this connector at runtime
managerServlet.deleteFail=FAIL - Unable to delete [{0}]. The continued presence of this file may cause problems.
managerServlet.deployFailed=FAIL - Failed to deploy application at context path [{0}]
managerServlet.deployed=OK - Deployed application at context path [{0}]
managerServlet.deployedButNotStarted=FAIL - Deployed application at context path [{0}] but context failed to start
managerServlet.exception=FAIL - Encountered exception [{0}]
managerServlet.findleaksFail=FAIL - Find leaks failed: Host not instance of StandardHost
managerServlet.findleaksList=OK - Found potential memory leaks in the following applications:
managerServlet.findleaksNone=OK - No memory leaks found
managerServlet.inService=FAIL - The application [{0}] is already being serviced
managerServlet.invalidCommand=FAIL - Invalid parameters supplied for command [{0}]
managerServlet.invalidPath=FAIL - Invalid context path [{0}] was specified
managerServlet.listed=OK - Listed applications for virtual host [{0}]
managerServlet.mkdirFail=FAIL - Unable to create directory [{0}]
managerServlet.noCommand=FAIL - No command was specified
managerServlet.noContext=FAIL - No context exists named [{0}]
managerServlet.noGlobal=FAIL - No global JNDI resources are available
managerServlet.noManager=FAIL - No manager exists for path [{0}]
managerServlet.noSelf=FAIL - The manager cannot reload, undeploy, stop, or undeploy itself
managerServlet.noWrapper=Container has not called setWrapper() for this servlet
managerServlet.notDeployed=FAIL - Context [{0}] is defined in server.xml and may not be undeployed
managerServlet.notSslConnector=SSL is not enabled for this connector
managerServlet.objectNameFail=FAIL - Unable to register object name [{0}] for Manager Servlet
managerServlet.postCommand=FAIL - Tried to use command [{0}] via a GET request but POST is required
managerServlet.reloaded=OK - Reloaded application at context path [{0}]
managerServlet.renameFail=FAIL - Unable to rename [{0}] to [{1}]. This may cause problems for future deployments.
managerServlet.resourcesAll=OK - Listed global resources of all types
managerServlet.resourcesType=OK - Listed global resources of type [{0}]
managerServlet.saveFail=FAIL - Configuration save failed: [{0}]
managerServlet.saved=OK - Server configuration saved
managerServlet.savedContext=OK - Context [{0}] configuration saved
managerServlet.savedContextFail=FAIL - Context [{0}] configuration save failed
managerServlet.serverInfo=OK - Server info\n\
Tomcat Version: [{0}]\n\
OS Name: [{1}]\n\
OS Version: [{2}]\n\
OS Architecture: [{3}]\n\
JVM Version: [{4}]\n\
JVM Vendor: [{5}]
managerServlet.sessiondefaultmax=Default maximum session inactive interval is [{0}] minutes
managerServlet.sessions=OK - Session information for application at context path [{0}]
managerServlet.sessiontimeout=Inactive for [{0}] minutes: [{1}] sessions
managerServlet.sessiontimeout.expired=Inactive for [{0}] minutes: [{1}] sessions were expired
managerServlet.sessiontimeout.unlimited=Unlimited time: [{0}] sessions
managerServlet.sslConnectorCerts=OK - Connector / Certificate Chain information
managerServlet.sslConnectorCiphers=OK - Connector / SSL Cipher information
managerServlet.sslConnectorTrustedCerts=OK - Connector / Trusted Certificate information
managerServlet.sslReload=OK - Reloaded TLS configuration for [{0}]
managerServlet.sslReloadAll=OK - Reloaded TLS configuration for all TLS virtual hosts
managerServlet.sslReloadFail=FAIL - Failed to reload TLS configuration
managerServlet.startFailed=FAIL - Application at context path [{0}] could not be started
managerServlet.started=OK - Started application at context path [{0}]
managerServlet.stopped=OK - Stopped application at context path [{0}]
managerServlet.storeConfig.noMBean=FAIL - No StoreConfig MBean registered at [{0}]. Registration is typically performed by the StoreConfigLifecycleListener.
managerServlet.threaddump=OK - JVM thread dump
managerServlet.trustedCertsNotConfigured=No trusted certificates are configured for this virtual host
managerServlet.undeployed=OK - Undeployed application at context path [{0}]
managerServlet.unknownCommand=FAIL - Unknown command [{0}]
managerServlet.vminfo=OK - VM info

statusServlet.complete=Complete Server Status
statusServlet.title=Server Status
