# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

combinedRealm.addRealm=Realm[{0}]を追加し、合計[{1}]Realmを作成します。
combinedRealm.authFail=レルム [{1}] でユーザー [{0}] の認証に失敗しました。
combinedRealm.authStart=ユーザー [{0}] をRealm [{1}] で認証します。
combinedRealm.authSuccess=レルム [{1}] でユーザー [{0}] を認証しました。
combinedRealm.getPassword=getPassword() メソッドを呼び出してはいけません。
combinedRealm.getPrincipal=getPrincipal()メソッドは決して呼び出されるべきではありません。
combinedRealm.realmStartFail=Realm[{0}]の開始に失敗しました。
combinedRealm.setCredentialHandler=資格情報ハンドラが複合レルム（またはそのサブクラス）のインスタンスで設定されましたが、複合レルムは資格情報ハンドラを使用するように構成されていません。構成が誤っていませんか？
combinedRealm.unexpectedMethod=Combined Realmについて予期せぬメソッド呼び出しが発生しました。

credentialHandler.invalidStoredCredential=ユーザ提供の資格情報とマッチするためにRealm によって提供された無効な格納された資格情報文字列[[{0}]

dataSourceRealm.authenticateFailure=ユーザ名 [{0}] は認証に失敗しました
dataSourceRealm.authenticateSuccess=ユーザ名 [{0}] は認証に成功しました
dataSourceRealm.close=データベース接続をクローズ中の例外です
dataSourceRealm.exception=認証を実行中の例外です
dataSourceRealm.getPassword.exception=[{0}] のパスワードを取得中に例外が発生しました。
dataSourceRealm.getRoles.exception=[{0}] のロールを取得中に例外が発生しました。

jaasCallback.username=返されたユーザー名[{0}]

jaasRealm.accountExpired=ユーザ名 [{0}] はアカウントの期限が切れているために認証されません
jaasRealm.authenticateFailure=ユーザー名[{0}]は正常に認証されませんでした
jaasRealm.authenticateSuccess=ユーザ名 [{0}] は認証に成功しました
jaasRealm.beginLogin=アプリケーション[{1}]のLoginContextを使用して、ユーザー名[{0}]に対するJAASRealmログインが要求されました。
jaasRealm.checkPrincipal=プリンシパル[{0}] [{1}]をチェックします。
jaasRealm.credentialExpired=ユーザ名 [{0}] は証明書の期限が切れたために認証されません
jaasRealm.failedLogin=ユーザ名 [{0}] はログインに失敗したために認証されませんでした
jaasRealm.loginContextCreated=ユーザー名[{0}]用に作成されたJAAS LoginContext
jaasRealm.loginException=ユーザ名 [{0}] の認証中にログイン例外が発生しました
jaasRealm.rolePrincipalAdd=このユーザープリンシパルロールにロールプリンシパル[{0}]を追加
jaasRealm.rolePrincipalFailure=有効な役割はありません。
jaasRealm.unexpectedError=予期せぬエラー
jaasRealm.userPrincipalFailure=有効なユーザープリンシパルが見つかりません。
jaasRealm.userPrincipalSuccess=プリンシパル[{0}]は有効なユーザークラスです。 これをユーザープリンシパルとして使用します。

jdbcRealm.authenticateFailure=ユーザ名 [{0}] は認証に失敗しました
jdbcRealm.authenticateSuccess=ユーザ名 [{0}] は認証に成功しました
jdbcRealm.close=データベース接続クローズ中の例外です
jdbcRealm.exception=認証実行中の例外です
jdbcRealm.open=データベース接続オープン中に例外が発生しました
jdbcRealm.open.invalidurl=ドライバー [{0}] は url [{1}] に対応していません。

jndiRealm.authenticateFailure=ユーザ名 [{0}] は認証に失敗しました
jndiRealm.authenticateSuccess=ユーザ名 [{0}] は認証に成功しました
jndiRealm.cipherSuites=TLS 接続で暗号スイート [{0}] を有効化しました。
jndiRealm.close=ディレクトリサーバ接続クローズ中の例外です
jndiRealm.emptyCipherSuites=指定された暗号スイートの空の文字列。 既定の暗号スイートを使用します。
jndiRealm.exception=認証実行中の例外です
jndiRealm.exception.retry=認証中に例外が発生しました。再試行します。
jndiRealm.invalidHostnameVerifier=[{0}]はHostnameVerifierの有効なクラス名ではありません。
jndiRealm.invalidSslProtocol=指定されたプロトコル[{0}]は無効です。 [{1}]のいずれかでなければなりません。
jndiRealm.invalidSslSocketFactory=[{0}]はSSLSocketFactoryの有効なクラス名ではありません
jndiRealm.multipleEntries=ユーザ名 [{0}] は複数のエントリを持っています
jndiRealm.negotiatedTls=プロトコル[{0}]を使用して交渉されたTLS接続
jndiRealm.open=ディレクトリサーバ接続オープン中の例外です
jndiRealm.tlsClose=TLSレスポンスを閉じる際の例外

lockOutRealm.authLockedUser=ロックされたユーザー [{0}] の認証が試行されました。
lockOutRealm.removeWarning=キャッシュサイズが制限内に収まるようにするため、[{1}]秒後にユーザー[{0}]が失敗したユーザーキャッシュから削除されました。

mdCredentialHandler.unknownEncoding=エンコーディング [{0}] には未対応のため現在の設定 [{1}] を使用します。

memoryRealm.authenticateFailure=ユーザ名 [{0}] は認証に失敗しました
memoryRealm.authenticateSuccess=ユーザ名 [{0}] は認証に成功しました
memoryRealm.loadExist=メモリデータベースファイル [{0}] を読むことができません
memoryRealm.loadPath=メモリデータベースファイル [{0}] からユーザをロードします
memoryRealm.readXml=メモリデータベースファイルを読み込み中の例外です
memoryRealm.xmlFeatureEncoding=XMLファイルのJavaエンコーディング名を許可するためにdigesterを設定する例外。 IANAのエンコーディング名のみがサポートされます。

pbeCredentialHandler.invalidKeySpec=パスワードベースの鍵を生成できません。

realmBase.algorithm=無効なメッセージダイジェストアルゴリズム [{0}] が指定されています
realmBase.authenticateFailure=ユーザ名 [{0}] は認証に失敗しました
realmBase.authenticateSuccess=ユーザ名 [{0}] は認証に成功しました
realmBase.cannotGetRoles=プリンシパル[{0}]からロールを取得できません
realmBase.createUsernameRetriever.ClassCastException=クラス[{0}]はX509UsernameRetrieverではありません。
realmBase.createUsernameRetriever.newInstance=タイプ[{0}]のオブジェクトを作成できません。
realmBase.credentialNotDelegated=ユーザ [{0}] の資格情報は委任されていませんが、保存が要求されました。
realmBase.delegatedCredentialFail=ユーザー[{0}]の委任された資格情報を取得できません
realmBase.digest=ユーザ証明書のダイジェストエラー
realmBase.forbidden=要求されたリソースへのアクセスが拒否されました
realmBase.gotX509Username=X509証明書のユーザー名を取得しました：[{0}]
realmBase.gssContextNotEstablished=Authenticator 実装エラー：渡されたセキュリティコンテキストが完全に確立されていません。
realmBase.gssNameFail=確立されたGSSContextから名前を抽出できませんでした。
realmBase.hasRoleFailure=ユーザ名 [{0}] はロール [{1}] を持っていません
realmBase.hasRoleSuccess=ユーザ名 [{0}] はロール [{1}] を持っています

userDatabaseRealm.lookup=キー [{0}] でユーザデータベースを検索中の例外です
userDatabaseRealm.noDatabase=キー [{0}] でユーザデータベースコンポーネントが見つかりません
