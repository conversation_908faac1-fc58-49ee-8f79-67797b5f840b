# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractConnectionHandler.connectionsGet=Trouvé le processeur [{0}] pour le socket [{1}]
abstractConnectionHandler.error=Erreur de lecture de la requête, ignorée
abstractConnectionHandler.ioexception.debug=Les IOException sont normales et sont ignorées
abstractConnectionHandler.negotiatedProcessor.fail=Impossible de créer un processeur pour le protocole négocié [{0}]
abstractConnectionHandler.oome=Echec de la fin de traitement d'une requête
abstractConnectionHandler.process=Traitement du socket [{0}] avec le statut [{1}]
abstractConnectionHandler.processorCreate=Création d''un nouveau processeur [{0}]
abstractConnectionHandler.processorPop=Retrait du processeur [{0}] du cache
abstractConnectionHandler.protocolexception.debug=Les ProtocolExceptions sont normales et sont ignorées
abstractConnectionHandler.socketexception.debug=Les SocketException sont normales et sont ignorées
abstractConnectionHandler.upgradeCreate=Création du processeur pour l''upgrade [{0}] du wrapper du socket [{1}]

abstractProcessor.fallToDebug=\n\
\ Note : les occurrences suivantes d'erreurs d'analyse de requête seront enregistrées au niveau DEBUG.
abstractProcessor.hostInvalid=L''hôte [{0}] n''est pas valide
abstractProcessor.httpupgrade.notsupported=La promotion (upgrade) HTTP n'est pas supporté par ce protocole
abstractProcessor.pushrequest.notsupported=Le requêtes push du serveur ne sont pas supportées par ce protocole
abstractProcessor.setErrorState=Etat d''erreur [{0}] lors du traitement de la requête
abstractProcessor.socket.ssl=Exception lors de l'obtention des attributs SSL

abstractProtocol.mbeanDeregistrationFailed=Erreur lors du désenregistrement du mbean [{0}] dans le serveur [{1}]
abstractProtocol.processorRegisterError=Erreur lors de l'enregistrement du processeur de requêtes
abstractProtocol.processorUnregisterError=Erreur lors du désenregistrement du processeur de requêtes
abstractProtocol.waitingProcessor.add=Ajout du processeur [{0}] au processeurs en attente
abstractProtocol.waitingProcessor.remove=Retrait du processeur [{0}] des processeurs en attente

abstractProtocolHandler.destroy=Destruction du gestionnaire de protocole [{0}]
abstractProtocolHandler.init=Initialisation du gestionnaire de protocole [{0}]
abstractProtocolHandler.pause=Le gestionnaire de protocole [{0}] est mis en pause
abstractProtocolHandler.resume=Reprise du gestionnaire de protocole [{0}]
abstractProtocolHandler.setAttribute=Fixe l''attribut [{0}] avec la valeur [{1}]
abstractProtocolHandler.start=Démarrage du gestionnaire de protocole [{0}]
abstractProtocolHandler.stop=Arrêt du gestionnaire de protocole [{0}]

asyncStateMachine.invalidAsyncState=L''appel à [{0}] n''est pas valide pour une requête dans l''état Async [{1}]

compressionConfig.ContentEncodingParseFail=Echec du traitement de l'en-tête Content-Encoding en vérifiant si la compression était déjà utilisée

continueResponseTiming.invalid=La valeur [{0}] n''est pas valide pour continueResponseTiming

request.notAsync=Il n'est possible de passer en mode d'entrée-sorties non bloquantes que lors de traitements asynchrones ou après mise à niveau depuis HTTP
request.nullReadListener=L'écouteur passé à setReadListener() ne peut pas être null
request.readListenerSet=L'écouteur des lectures non bloquantes a déjà été défini

response.encoding.invalid=L''encodage [{0}] n''est pas reconnu par le JRE
response.notAsync=Il n'est possible de passer en mode d'entrée-sorties non bloquantes que lors de traitements asynchrones ou après mise à niveau depuis HTTP
response.notNonBlocking=Il n'est pas permis d'appeler isReady() quand la réponse n'a pas été mise en mode non-bloquant
response.nullWriteListener=L'écouteur passé à setWriteListener() ne peut pas être null
response.writeListenerSet=La cible de notifications d''écriture ("write listener") non-bloquante a déjà été définie
