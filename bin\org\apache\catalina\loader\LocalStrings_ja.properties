# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

webappClassLoader.addExportsRmi=Java 9以降で実行する場合は、JVMコマンドライン引数に "--add-opens = java.rmi / sun.rmi.transport = ALL-UNNAMED"を追加して、RMIターゲットメモリリーク検出を有効にする必要があります。 また、RMIターゲットメモリリーク検出を無効にすることで、この警告を抑制することも可能です。
webappClassLoader.addExportsThreadLocal=Java 9以降で実行する場合は、JVMコマンドライン引数に "--add-opens = java.base / java.lang = ALL-UNNAMED"を追加して、ThreadLocalメモリリーク検出を有効にする必要があります。 また、ThreadLocalメモリリーク検出を無効にすることで、この警告を抑制することもできます。
webappClassLoader.addPermissionNoCanonicalFile=URL [{0}] の正規化パスを取得できません。
webappClassLoader.addPermissionNoProtocol=URL [{1}] に含まれるプロトコル [{0}] は未対応です。この URL で参照できるリソースには読み取り権限を付与できません。
webappClassLoader.addTransformer=Web アプリケーション [{1}] にクラスファイル変換器 [{0}] を追加しました。
webappClassLoader.addTransformer.duplicate=クラスファイルトランスフォーマー[{0}]をWebアプリケーション[{1}]に追加する呼び出しが重複しました。
webappClassLoader.addTransformer.illegalArgument=Webアプリケーション[{0}]がNullクラスファイルトランスフォーマーを追加しようとしました。
webappClassLoader.checkThreadLocalsForLeaks=Webアプリケーション[{0}]はタイプ[{1}]（値[{2}]）のキーと値タイプ[{3}]（値[{4}]）のThreadLocalを作成しましたが、 それはWebアプリケーションの停止時に削除されていません。スレッドは時間の経過とともに更新され、メモリリークの可能性を回避しようとしています。
webappClassLoader.checkThreadLocalsForLeaks.badKey=キー値のクラス [{0}] の文字列表現を取得できません。
webappClassLoader.checkThreadLocalsForLeaks.badValue=クラス [{0}] の文字列表現を取得できません。
webappClassLoader.checkThreadLocalsForLeaks.unknown=不明
webappClassLoader.checkThreadLocalsForLeaksFail=Webアプリケーション[{0}]のThreadLocal参照を確認できませんでした。
webappClassLoader.checkThreadLocalsForLeaksNone=Web アプリケーション [{0}] はキー [{1}] (値は [{1}])、値 [{3}](値は [{4}])をスレッドローカルに作成しました。キーは ThreadLocalMap の弱参照として保持されるため、メモリーリークではありません。
webappClassLoader.checkThreadLocalsForLeaksNull=Webアプリケーション[{0}]はタイプ[{1}]（値[{2}]のキーを持つThreadLocalを作成しました。 ThreadLocalは正しくnullに設定され、キーはGCによって削除されます。
webappClassLoader.checkThreadsHttpClient=Webアプリケーションクラスローダーを使用しているHttpClientキープアライブスレッドを検出しました。 スレッドを親クラスローダに切り替えることで修正されました。
webappClassLoader.clearJdbc=Web アプリケーション [{0}] は自身で登録した JDBC ドライバー [{1}] を停止時に解除できませんでした。メモリーリークの発生を防ぐには JDBC ドライバーを強制的に解除してください。
webappClassLoader.clearObjectStreamClassCachesFail=Web アプリケーション [{0}] の ObjectStreamClass$Caches についてソフト参照を除去できません。
webappClassLoader.clearRmi=クラス [{0}] 値 [{1}] のスタブクラスを持つ RMI ターゲットを発見しました。メモリーリークを防ぐため発見した RMI ターゲットは強制的に削除します。
webappClassLoader.clearRmiFail=Webアプリケーション[{0}]のsun.rmi.transport.Targetから参照されるコンテキストクラスローダーのクリアに失敗しました。
webappClassLoader.clearRmiInfo=Web アプリケーション [{0}] のコンテキストクラスローダーをきれいにするための sun.rmi.transport.Target クラスが見つかりません。Sun 以外の JVM で実行している可能性があります。
webappClassLoader.getThreadGroupError=スレッドグループ [{0}] の親スレッドグループを取得できません。潜在的なメモリリークをすべてのスレッドでチェックすることはできません。
webappClassLoader.jarsAdded=1つ以上のJARがWebアプリケーション[{0}]に追加されました。
webappClassLoader.jarsModified=1つ以上のJARがWebアプリケーション[{0}]で変更されました。
webappClassLoader.jarsRemoved=Web アプリケーション [{0}] から 1 つ以上の JAR が削除されました。
webappClassLoader.javaseClassLoaderNull=j2seClassLoader属性はnullでない場合があります。
webappClassLoader.jdbcRemoveFailed=Web アプリケーション [{0}] は JDBC ドライバーの登録を解除できません。
webappClassLoader.loadedByThisOrChildFail=クラス [{0}] のインスタンスの全ての要素をチェックできませんでした。コンテキスト [{1}] でメモリーリークの発生する可能性があります。
webappClassLoader.readError=リソース読み込みエラー: [{0}] が読み込めませんでした。
webappClassLoader.removeTransformer=クラスファイル変換器 [{0}] を Web アプリケーション [{1}] から削除しました。
webappClassLoader.resourceModified=リソース [{0}] は変更されています。直前の更新日時は [{1}]、最新の更新日時は [{2}] です。
webappClassLoader.restrictedPackage=セキュリティー違反。制限されたクラス [{0}] を使おうとしました。
webappClassLoader.securityException=indClassInternal [{1}]でクラス[{0}]を検索中のセキュリティ例外です
webappClassLoader.stackTrace=Webアプリケーション[{0}]は[{1}]という名前のスレッドを開始したようですが、停止に失敗しました。 これはメモリリークを引き起こす可能性が非常に高いです。 スレッドのスタックトレース：{2}
webappClassLoader.stackTraceRequestThread=Webアプリケーション[{0}]はまだ完了していないリクエストを処理しています。 これはメモリリークを引き起こす可能性が非常に高いです。 リクエストの終了時間は、StandardContext実装のunloadDelay属性を使用して制御できます。 陸絵鵜sと処理スレッドのスタックトレース：[{2}]
webappClassLoader.stopThreadFail=Web アプリケーション [{1}] のスレッド [{0}] は終了できません。
webappClassLoader.stopTimerThreadFail=Webアプリケーション[{1}]の[{0}]という名前のTimerThreadを終了できませんでした。
webappClassLoader.stopped=不正なアクセス: このWebアプリケーションのインスタンスは既に停止されています  Could not load [{0}]. 不正なアクセスを引き起こしたスレッドを終了させ、投げられたエラーによりデバッグ用に次のスタックトレースが生成されましたが，機能に影響はありません
webappClassLoader.superCloseFail=基底クラスで close() の呼び出しに失敗しました。
webappClassLoader.transformError=instrumentation エラー：クラスファイル形式が正当でないため、クラス[{0}]を変換できませんでした。
webappClassLoader.warnTimerThread=Web アプリケーション [{0}] が java.util.Timer API で開始した TimerThread [{1}] を停止できません。メモリーリークを防ぐにはタイマー(とそれに伴って関連付けられたスレッド)を強制的にキャンセルしてください。
webappClassLoader.wrongVersion=（クラス[{0}]をロードできません）

webappClassLoaderParallel.registrationFailed=並列にクラスをロードできるorg.apache.catalina.loader.ParallelWebappClassLoaderの登録に失敗しました。

webappLoader.deploy=クラスリポジトリを作業ディレクトリ [{0}] に配備します
webappLoader.reloadable=reloadableプロパティを [{0}] に設定できません
webappLoader.setContext.ise=クラスローダーが開始したあとは Context を構成することはできません。
webappLoader.starting=このローダを起動します
webappLoader.stopping=このローダを停止します
