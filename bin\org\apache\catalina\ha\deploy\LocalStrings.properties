# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

farmWarDeployer.alreadyDeployed=webapp [{0}] are already deployed.
farmWarDeployer.deleteFail=Failed to delete [{0}]
farmWarDeployer.deployEnd=Deployment from [{0}] finished.
farmWarDeployer.fileCopyFail=Unable to copy from [{0}] to [{1}]
farmWarDeployer.hostOnly=FarmWarDeployer can only work as host cluster subelement!
farmWarDeployer.hostParentEngine=FarmWarDeployer can only work if parent of [{0}] is an engine!
farmWarDeployer.mbeanNameFail=Cannot construct MBean object name for engine [{0}] and host [{1}]
farmWarDeployer.modInstall=Installing webapp [{0}] from [{1}]
farmWarDeployer.modInstallFail=Unable to install WAR file
farmWarDeployer.modRemoveFail=No removal
farmWarDeployer.msgIoe=Unable to read farm deploy file message.
farmWarDeployer.msgRxDeploy=Receive cluster deployment path [{0}], war [{1}]
farmWarDeployer.msgRxUndeploy=Receive cluster undeployment from path [{0}]
farmWarDeployer.removeFailLocal=Local remove from [{0}] failed
farmWarDeployer.removeFailRemote=Local remove from [{0}] failed, other manager has app in service!
farmWarDeployer.removeLocal=Removing webapp [{0}]
farmWarDeployer.removeLocalFail=Unable to remove WAR file
farmWarDeployer.removeStart=Cluster wide remove of web app [{0}]
farmWarDeployer.removeTxMsg=Send cluster wide undeployment from [{0}]
farmWarDeployer.renameFail=Failed to rename [{0}] to [{1}]
farmWarDeployer.sendEnd=Send cluster war deployment path [{0}], war [{1}] finished.
farmWarDeployer.sendFragment=Send cluster war fragment path [{0}], war [{1}] to [{2}]
farmWarDeployer.sendStart=Send cluster war deployment path [{0}], war [{1}] started.
farmWarDeployer.servicingDeploy=Application [{0}] is being serviced. Touch war file [{1}] again!
farmWarDeployer.servicingUndeploy=Application [{0}] is being serviced and can''t be removed from backup cluster node
farmWarDeployer.started=Cluster FarmWarDeployer started.
farmWarDeployer.stopped=Cluster FarmWarDeployer stopped.
farmWarDeployer.undeployEnd=Undeployment from [{0}] finished.
farmWarDeployer.undeployLocal=Undeploy local context [{0}]
farmWarDeployer.watchDir=Cluster deployment is watching [{0}] for changes.

fileMessageFactory.cannotRead=Cannot read message, this factory is writing
fileMessageFactory.cannotWrite=Cannot write message, this factory is reading
fileMessageFactory.closed=Factory has been closed
fileMessageFactory.deleteFail=Failed to delete [{0}]
fileMessageFactory.duplicateMessage=Received duplicate message. Is the Sender timeout too low? context: [{0}] filename: [{1}] data: [{2}] data length: [{3}]

fileNewFail=Unable to create [{0}]

warWatcher.cantListWatchDir=Cannot list files in WatchDir [{0}]: check to see if it is a directory and has read permissions.
warWatcher.checkWarResult=WarInfo.check() returned [{0}] for [{1}]
warWatcher.checkingWar=Checking WAR file [{0}]
warWatcher.checkingWars=Checking WARs in [{0}]
warWatcher.listedFileDoesNotExist=[{0}] was detected in [{1}] but does not exist. Check directory permissions on [{1}]?
