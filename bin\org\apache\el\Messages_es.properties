# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

error.convert = No puedo convertir [{0}] desde tipo [{1}] a [{2}]
error.compare = No puedo comparar [{0}] con [{1}]
error.function = Problemas llamando a funci\u00F3n [{0}]
error.unreachable.base = Objetivo inalcanzable, identificador [{0}] resuelto a nulo
error.unreachable.property = Objetivo inalcanzable, [{0}] devolvi\u00F3 nulo
error.resolver.unhandled = ELResolver no manej\u00F3 el tipo: [{0}] con propiedad de [{1}]
error.resolver.unhandled.null = ELResolver no puede manejar un Objeto base nulo  con identificador de [{0}]
error.value.literal.write = ValueExpression es un literal y no un grabable: [{0}]
error.null = La expresi\u00F3n no puede ser nula
error.mixed = La expresi\u00F3n no puede contenera la vez ''#{..}'' y ''${..}'' : [{0}]
error.method = No es una MethodExpression v\u00E1lida: [{0}]
error.method.nullParms = Los tipos de par\u00E1metro no pueden ser nulo
error.value.expectedType = El tipo esperado no puede ser nulo
error.syntax.set = Sit\u00E1xis ilegal para Operaci\u00F3n de Poner Valor
error.method.notfound = M\u00E9todo no hallado: {0}.{1}({2})
error.method.ambiguous = No pude hallar m\u00E9todo ambiguo: {0}.{1}({2})
error.fnMapper.null = La expresi\u00F3n usa funciones, pero no se ha suministrado FunctionMapper
error.fnMapper.method = Funci\u00F3n [{0}] no hallada
error.fnMapper.paramcount = La funci\u00F3n [{0}] especifica [{1}] par\u00E9metros, pero [{2}] fueron declarados
error.context.null = ELContext era nulo
error.identifier.notjava = El identificador [{0}] no es un identificado Java v\u00E1lido seg\u00FAn se requiere en la secci\u00F3n 1.9 de la especificaci\u00F3n EL (Identificador ::= identificador de lenguaje Java). Este chequeo se puede desactivar poniendo la propiedad del sistema  org.apache.el.parser.SKIP_IDENTIFIER_CHECK a verdad (true).
