# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

beanNameELResolver.beanReadOnly=名称为[{0}]的bean只读

elProcessor.defineFunctionInvalidClass=类[{0}]不是公共的
elProcessor.defineFunctionInvalidMethod=类[{1}]的方法[{0}]不是公共静态方法
elProcessor.defineFunctionInvalidParameterList=类[{2}]的方法[{1}]的参数列表[{0}]无效
elProcessor.defineFunctionInvalidParameterTypeName=类[{2}]的方法[{1}]的参数类型[{0}]无效
elProcessor.defineFunctionNoMethod=无法找到类[{1}]的公共静态方法[{0}]
elProcessor.defineFunctionNullParams=一个或多个输入参数为null

expressionFactory.cannotCreate=无法创建类型为[{0}]的表达式工厂
expressionFactory.cannotFind=找不到类型为[{0}]的ExpressionFactory
expressionFactory.readFailed=无法读取[{0}]

importHandler.ambiguousImport=无法导入类[{0}]，因为它与已导入的[{1}]冲突
importHandler.ambiguousStaticImport=无法处理静态导入[{0}]，因为它与已导入的[{1}]冲突
importHandler.classNotFound=无法导入类[{0}]，因为无法找到它
importHandler.invalidClass=类[{0}]必须是公共的、非抽象的、非接口且（对于Java 9+）在一个导出包
importHandler.invalidClassName=要导入的类的名称[{0}]必须包含一个包。
importHandler.invalidClassNameForStatic=类[{0}]指定的静态导入[{1}]无效
importHandler.invalidStaticName=导入 [{0}] 的静态方法或字段名称必须包含类
importHandler.staticNotFound=导入[{2}]的类[{1}]中找不到静态导入[{0}]

lambdaExpression.tooFewArgs=仅为至少需要[{1}]个参数的lambda表达式提供了[{0}]个参数

objectNotAssignable=无法将类型为[{0}]的对象添加到[{1}]类型的对象数组中
propertyNotFound=类型[{0}]上找不到属性[{1}]
propertyNotReadable=属性[{1}]在类型[{0}]上不可读
propertyNotWritable=属性[{1}]在类型[{0}]上不可写
propertyReadError=在类型[{0}]上读取[{1}]时出错
propertyWriteError=在类型[{0}]上写入[{1}]时出错

staticFieldELResolver.methodNotFound=在类[{1}]上找不到名为[{0}]的匹配的公共静态方法
staticFieldELResolver.notFound=（Java  9+导出）类[{1}]上找不到名为[{0}]的公共静态字段
staticFieldELResolver.notWriteable=不允许写入静态字段（当前情况中为类[{1}]上的字段[{0}]）

util.method.ambiguous=无法找到明确的方法：{0}.{1}({2})
util.method.notfound=找不到方法：{0}.{1}({2})
