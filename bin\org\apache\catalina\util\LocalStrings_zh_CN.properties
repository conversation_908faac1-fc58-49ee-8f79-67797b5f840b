# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

SecurityUtil.doAsPrivilege=运行PrivilegedExceptionAction块时发生异常。

customObjectInputStream.logRequired=使用日志记录进行类名过滤需要一个有效的日志记录器
customObjectInputStream.nomatch=因为类允许被反序列化，类[{0}]未能匹配常规的表达式[{1}]

extensionValidator.extension-not-found-error=ExtensionValidator[{0}][{1}]: 请求的拓展[{2}]未找到。
extensionValidator.extension-validation-error=扩展验证程序[{0}]：找不到[{1}]所需的扩展。
extensionValidator.failload=加载扩展名[{0}]失败
extensionValidator.web-application-manifest=web 应用程序清单

introspection.classLoadFailed=加载 class [{0}] 失败

lifecycleBase.alreadyDestroyed=在调用destroy（）之后，在组件[{0}]上调用了destroy（）方法。第二个呼叫将被忽略。
lifecycleBase.alreadyStarted=在调用start()之后，在组件[{0}]上调用start()方法。第二个电话将被忽略。
lifecycleBase.alreadyStopped=在调用stop（）之后，对组件[{0}]调用了stop（）方法。第二个调用将被忽略。
lifecycleBase.destroyFail=未能销毁组件[{0}]
lifecycleBase.destroyStopFail=在失败组件[{0}]上调用Stop()以触发清理，但也失败了
lifecycleBase.initFail=初始化组件[{0}]失败。
lifecycleBase.invalidTransition=无效的生命周期转变被尝试 [{0}]) 组件 [{1}] 状态 [{2}]
lifecycleBase.setState=设置状态从[{0}]到[{1}]
lifecycleBase.startFail=无法启动组件[{0}]
lifecycleBase.stopFail=无法停止组件[{0}]

lifecycleMBeanBase.registerFail=在组件初始化期间，无法注册名为{1}的对象{0}]

netmask.cidrNegative=CIDR [{0}]为负数。
netmask.cidrNotNumeric=CIDR[{0}]不是数字
netmask.cidrTooBig=CIDR[{0}]大于地址长度[{1}]
netmask.invalidAddress=地址 [{0}] 无效

parameterMap.locked=不允许修改锁定的参数映射

resourceSet.locked=不允许修改锁定的资源集

sessionIdGeneratorBase.createRandom=使用[{0}]创建会话ID生成的SecureRandom实例花费了[{1}]毫秒。
sessionIdGeneratorBase.random=初始化类{0}的随机数生成器时发生异常。回到java.secure.SecureRandom
sessionIdGeneratorBase.randomAlgorithm=使用算法[{0}]初始化随机数生成器时发生异常
sessionIdGeneratorBase.randomProvider=使用程序提供的初始化随机数生成器异常[{0}]
