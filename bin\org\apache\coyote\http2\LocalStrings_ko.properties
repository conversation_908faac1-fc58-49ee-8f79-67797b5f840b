# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractStream.windowSizeDec=연결 [{0}], 스트림 [{1}], flow control 윈도우를 [{2}] 만큼 줄여 [{3}]에 이르게 합니다.
abstractStream.windowSizeInc=연결 [{0}], 스트림 [{1}]: Flow control 윈도우를 [{2}] 만큼 증가시켜 윈도우 크기가 [{3}]이(가) 되도록 합니다.
abstractStream.windowSizeTooBig=연결 [{0}], 스트림 [{1}], 윈도우 크기를 [{2}] 만큼 증가시켜 [{3}](으)로 만들었으나, 이는 허용된 최대값을 초과했습니다.

connectionPrefaceParser.eos=개시되는 클라이언트 preface 바이트 시퀀스를 읽는 동안, 예기치 않은 스트림의 끝. 단지 [{0}] 바이트만을 읽음.
connectionPrefaceParser.ioError=개시되는 클라이언트 preface 바이트 시퀀스를 읽지 못했습니다.
connectionPrefaceParser.mismatch=해당 client preface [{0}]의 시작 부분에서 예기치 않은 바이트 시퀀스를 받았습니다.

connectionSettings.debug=연결 [{0}]: 파라미터 타입 [{1}]을(를) [{2}](으)로 설정함.
connectionSettings.enablePushInvalid=연결 [{0}], enablePush를 위해 요청된 값 [{1}]은(는), 허용된 값들(0 또는 1) 중의 하나가 아닙니다.
connectionSettings.headerTableSizeLimit=연결 [{0}]: 헤더 테이블 크기로 [{1}]을(를) 설정하려 시도했으나, 한계값은 16k입니다.
connectionSettings.maxFrameSizeInvalid=연결 [{0}]: [{1}]의 요청된 최대 프레임 크기가 허용된 범위([{2}] - [{3}])의 바깥에 존재합니다.
connectionSettings.unknown=연결 [{0}]: 식별자가 [{1}](이)고 값이 [{2}]인 알 수 없는 설정이 무시되었습니다.
connectionSettings.windowSizeTooBig=연결 [{0}]: 요청된 윈도우 크기 [{1}]이(가) 최대 허용치 [{2}] 보다 큽니다.

frameType.checkPayloadSize=Payload의 크기 [{0}]은(는) 프레임 타입 [{1}]을(를) 위해 유효하지 않습니다.
frameType.checkStream=유효하지 않은 프레임 타입 [{0}]

hpack.integerEncodedOverTooManyOctets=HPACK 가변 길이 정수가 너무 많은 옥텟(octet)들로 인코딩되어 있습니다. 최대 길이는 [{0}]입니다.
hpack.invalidCharacter=code point [{1}]에 위치한 유니코드 문자 [{0}]은(는), 0에서 255까지의 허용 범위 바깥에 있으므로 인코딩될 수 없습니다.

hpackEncoder.encodeHeader=인코딩 헤더 [{0}]와(과) 그의 값 [{1}]

hpackdecoder.headerTableIndexInvalid=[{1}]개의 정적 엔트리들과 [{2}]개의 동적 엔트리들이 존재하기에, 헤더 테이블 인덱스 [{0}]은(는) 유효하지 않습니다.
hpackdecoder.maxMemorySizeExceeded=헤더 테이블 크기 [{1}]이(가) 최대 크기 [{1}]을(를) 초과합니다.
hpackdecoder.notImplemented=아직 구현 안됨
hpackdecoder.nullHeader=인덱스가 [{0}]인 위치에 널 헤더가 존재합니다.
hpackdecoder.tableSizeUpdateNotAtStart=테이블 크기 변경은, 반드시 헤더 블록의 시작 시에 전송되어야만 합니다.
hpackdecoder.zeroNotValidHeaderTableIndex=0은 유효한 헤더 테이블 인덱스가 아닙니다.

hpackhuffman.huffmanEncodedHpackValueDidNotEndWithEOS=HPACK 헤더들 내의 Huffman 알고리즘으로 인코딩된 값이, EOS padding으로 끝나지 않았습니다.
hpackhuffman.stringLiteralEOS=HPACK 헤더들 내의 Huffman 알고리즘으로 인코딩된 값이, EOS 부호를 포함했습니다.
hpackhuffman.stringLiteralTooMuchPadding=Huffman 알고리즘으로 인코딩된 문자열의 끝에 7 비트를 초과한 EOS padding입니다.

http2Parser.headerLimitCount=연결 [{0}], 스트림 [{1}], 너무 많은 헤더들이 있음
http2Parser.headerLimitSize=연결 [{0}], 스트림 [{1}], 전체 헤더 크기가 너무 큽니다.
http2Parser.headers.wrongFrameType=연결 [{0}], 스트림 [{1}]을(를) 위한 헤더들이 진행중이지만, 타입 [{2}]의 프레임을 받았습니다.
http2Parser.headers.wrongStream=연결 [{0}]: 스트림 [{1}]의 헤더들을 처리하는 과정에서, 스트림 [{2}]의 프레임을 받았습니다.
http2Parser.nonZeroPadding=연결 [{0}], 스트림 [{1}], 0이 아닌 padding을 받았습니다.
http2Parser.payloadTooBig=Payload의 길이가 [{0}]바이트이지만, 최대 프레임 크기는 [{1}]입니다.
http2Parser.preface.invalid=유효하지 않은 연결 preface 이(가) 제공되었습니다.
http2Parser.preface.io=연결 preface를 읽을 수 없습니다.
http2Parser.processFrame=연결 [{0}], 스트림 [{1}], 프레임 타입 [{2}], 플래그들 [{3}], Payload 크기 [{4}]
http2Parser.processFrame.tooMuchPadding=연결 [{0}], 스트림 [{1}], padding 길이 [{2}]은(는) payload [{3}]을(를) 위해 너무 큽니다.
http2Parser.processFrame.unexpectedType=프레임 타입 [{0}]이(가) 요구되었으나, 프레임 타입 [{1}]을(를) 받았습니다.
http2Parser.processFrameContinuation.notExpected=연결 [{0}]: 헤더들이 아무 것도 진행되지 않은 상태에서, 스트림 [{1}]을(를) 위한 Continuation 프레임을 받았습니다.
http2Parser.processFrameData.lengths=연결 [{0}], 스트림 [{1}], 데이터 길이, [{2}], Padding 길이 [{3}]
http2Parser.processFrameData.window=연결 [{0}]: 클라이언트가 스트림 윈도우가 허용하는 데이터 크기보다 더 많은 데이터를 전송했습니다.
http2Parser.processFrameHeaders.decodingDataLeft=HPACK 디코딩 후 남아있는 데이터 - 반드시 소비되었어야 합니다.
http2Parser.processFrameHeaders.decodingFailed=HTTP 헤더들의 HPACK 디코딩 과정에서 오류가 있었습니다.
http2Parser.processFrameHeaders.payload=연결 [{0}], 스트림 [{1}], 크기가 [{2}]인 헤더들의 payload를 처리합니다.
http2Parser.processFramePriority.invalidParent=연결 [{0}], 스트림 [{1}], 스트림이 그 자신을 의존할 수는 없습니다.
http2Parser.processFramePushPromise=연결 [{0}], 스트림 [{1}], Push promise 프레임들이 클라이언트에 의해 전송되어서는 안됩니다.
http2Parser.processFrameSettings.ackWithNonZeroPayload=ACK 플래그가 설정되고 payload가 존재하는, Settings 프레임을 받았습니다.
http2Parser.processFrameWindowUpdate.debug=연결 [{0}], 스트림 [{1}], 윈도우 크기를 [{2}] 만큼 증가 시킵니다.
http2Parser.processFrameWindowUpdate.invalidIncrement=유효하지 않은 증분 크기인 [{0}]와(과) 함께, 윈도우 변경 프레임을 받았습니다.
http2Parser.swallow.debug=연결 [{0}], 스트림 [{1}], [{2}] 바이트를 처리하지 않고 건너뛰었습니다.

http2Protocol.jmxRegistration.fail=HTTP/2 프로토콜을 JMX에 등록하지 못했습니다.

pingManager.roundTripTime=연결 [{0}]: 라운드 트립 시간이 [{1}] 나노초(ns)로 측정되었습니다.

stream.clientCancel=응답이 완료되기 전에 클라이언트가 스트림을 리셋했습니다.
stream.closed=연결 [{0}], 스트림 [{1}], 한번 닫힌 스트림에 쓰기를 할 수 없습니다.
stream.header.case=연결 [{0}], 스트림 [{1}], HTTP 헤더 이름 [{2}]은(는) 반드시 소문자여야 합니다.
stream.header.connection=연결 [{0}], 스트림 [{1}], HTTP 헤더 [connection]은 HTTP/2 요청에서 허용되지 않습니다.
stream.header.contentLength=연결 [{0}], 스트림 [{1}], 해당 Content-Length 헤더 값 [{2}]은(는) 수신된 데이터의 크기 [{3}]와(과) 일치하지 않습니다.
stream.header.debug=연결 [{0}], 스트림 [{1}], HTTP 헤더: [{2}], 값: [{3}]
stream.header.duplicate=연결 [{0}], 스트림 [{1}], 여러 개의 [{2}] 헤더들을 받았습니다.
stream.header.empty=연결 [{0}], 스트림 [{1}], 헤더 이름이 빈 문자열이어서 유효하지 않습니다.
stream.header.invalid=연결 [{0}], 스트림 [{1}], 헤더 [{2}]이(가) 유효하지 않은 값을 포함했습니다: [{3}]
stream.header.noPath=연결 [{0}], 스트림 [{1}], [:path] 가상 헤더가 비어 있었습니다.
stream.header.required=연결 [{0}], 스트림 [{1}], 하나 이상의 필수 헤더들이 없습니다.
stream.header.te=연결 [{0}], 스트림 [{1}], HTTP/2 요청에서, HTTP 헤더 [te]이(가) 값 [{2}]을(를) 갖는 것은 허용되지 않습니다.
stream.header.unexpectedPseudoHeader=연결 [{0}], 스트림 [{1}], 정규 헤더 다음에 가상 헤더 [{2}]을(를) 받았습니다.
stream.header.unknownPseudoHeader=연결 [{0}], 스트림 [{1}], 알 수 없는 가상 헤더 [{2}]을(를) 받았습니다.
stream.inputBuffer.copy=[{0}] 바이트를 inBuffer에서 outBuffer로 복사합니다.
stream.inputBuffer.dispatch=readInterest가 등록될 때, 데이터가 inBuffer에 추가되었습니다. 읽기 디스패치를 개시합니다.
stream.inputBuffer.empty=스트림의 입력 버퍼가 비어 있습니다. 더 많은 데이터를 기다립니다.
stream.inputBuffer.readTimeout=클라이언트로부터 데이터를 읽기를 일정 시간 동안 기다리는 중입니다.
stream.inputBuffer.reset=스트림이 재설정(reset)되었습니다.
stream.inputBuffer.signal=읽기 쓰레드가 대기하는 동안 inBuffer에 데이터가 추가되었습니다. 해당 쓰레드가 읽기를 계속하도록 시그널을 보냅니다.
stream.notWritable=연결 [{0}], 스트림 [{1}], 이 스트림은 쓰기 가능하지 않습니다.
stream.outputBuffer.flush.debug=연결 [{0}], 스트림 [{1}], 위치 [{2}]의 버퍼를 출력으로 배출합니다. 쓰기 진행 중 여부: [{3}],닫힘 여부: [{4}]
stream.recycle=연결 [{0}], 스트림 [{1}]이(가) 참조 해제되었습니다.
stream.reprioritisation.debug=연결 [{0}], 스트림 [{1}], 배타성 [{2}], 부모 [{3}], 가중치 [{4}]
stream.reset.fail=연결 [{0}], 스트림 [{1}], 스트림을 재설정(reset)하지 못했습니다.
stream.reset.receive=연결 [{0}], 스트림 [{1}], [{2}](으)로 인해 재설정(reset)을 받았습니다.
stream.reset.send=연결 [{0}], 스트림 [{1}], [{2}](으)로 인하여 재설정(reset)이 전송되었음.
stream.trailerHeader.noEndOfStream=연결 [{0}], 스트림 [{1}], Trailer 헤더들이 스트림의 끝 플래그를 포함하지 않았습니다.
stream.writeTimeout=스트림 데이터가 쓰여지도록 허용하기 위한 흐름 제어 (flow control) 윈도우를, 클라이언트가 증가시키기를 일정 시간 동안 기다리는 중입니다.

streamProcessor.cancel=연결 [{0}], 스트림 [{1}], 요청의 body가 완전히 읽히지 않고 남아 있어, 더 이상 데이터는 불필요합니다.
streamProcessor.error.connection=연결 [{0}], 스트림 [{1}]: 처리 중 해당 연결에 심각한 오류 발생
streamProcessor.error.stream=연결 [{0}], 스트림 [{1}], 처리 중 스트림에 치명적인 오류가 발생했습니다.
streamProcessor.flushBufferedWrite.entry=연결 [{0}], 스트림 [{1}], 버퍼에 쓰여진 데이터를 배출합니다.
streamProcessor.service.error=요청 처리 중 오류 발생

streamStateMachine.debug.change=연결 [{0}], 스트림 [{1}], 상태가 [{2}]에서 [{3}](으)로 변경됨.
streamStateMachine.invalidFrame=연결 [{0}], 스트림 [{1}], 상태 [{2}], 프레임 타입 [{3}]

upgradeHandler.allocate.debug=연결 [{0}], 스트림 [{1}], [{2}] 바이트를 할당함.
upgradeHandler.allocate.left=연결 [{0}], 스트림 [{1}], [{2}] 바이트들이 할당 해제되었습니다 - 자식들에 할당하려 시도합니다.
upgradeHandler.allocate.recipient=연결 [{0}], 스트림 [{1}], 가중치 [{3}]의 잠재적 수신자 [{2}]
upgradeHandler.connectionError=연결 오류
upgradeHandler.dependency.invalid=연결 [{0}], 스트림 [{1}], 스트림들은 자기 자신들에 의존해서는 안됩니다.
upgradeHandler.goaway.debug=연결 [{0}], Goaway, 마지막 스트림 [{1}], 오류 코드 [{2}], 디버그 데이터 [{3}]
upgradeHandler.init=연결 [{0}], 상태 [{1}]
upgradeHandler.initialWindowSize.invalid=연결 [{0}]: 값 [{1}]은(는), 초기 윈도우 크기로서 불허되므로, 무시됩니다.
upgradeHandler.invalidPreface=연결 [{0}]: 유효하지 않은 연결 preface
upgradeHandler.ioerror=연결 [{0}]
upgradeHandler.noAllocation=연결 [{0}], 스트림 [{1}], 연결 할당을 위해 대기하는 중 제한 시간 초과 되었습니다.
upgradeHandler.noNewStreams=연결 [{0}], 스트림 [{1}], 이 연결에는 새로운 스트림들이 허용되지 않기에, 스트림이 무시되었습니다.
upgradeHandler.pause.entry=연결 [{0}]이(가) 일시 정지 중
upgradeHandler.pingFailed=연결 [{0}]: 클라이언트에 ping 메시지를 보내지 못했습니다.
upgradeHandler.prefaceReceived=연결 [{0}]: 연결 preface를 클라이언트로부터 받았습니다.
upgradeHandler.pruneIncomplete=연결 [{0}]: 스트림들이 Priority tree에서 활성화되어 있거나 사용되고 있기 때문에, 해당 연결을 완전히 제거하지 못했습니다. 너무 많은 스트림들이 존재합니다: [{2}].
upgradeHandler.pruneStart=연결 [{0}]: 이전 스트림들에 대한 가지치기를 시작합니다. 한계값은 [{1}] 이고, 현재 [{2}]개의 스트림들이 존재합니다.
upgradeHandler.pruned=연결 [{0}]이(가) 완료된 스트림 [{1}]을(를) 제거했습니다.
upgradeHandler.prunedPriority=연결 [{0}]이(가) 사용되지 않는 스트림 [{1}]을(를) 제거합니다. 해당 스트림은 priority tree의 일부였을 수 있습니다.
upgradeHandler.releaseBacklog=연결 [{0}], 스트림 [{1}]이(가) 백로그로부터 해제되었습니다.
upgradeHandler.rst.debug=연결 [{0}], 스트림 [{1}], 오류 [{2}], 메시지 [{3}],  RST (스트림을 닫습니다)
upgradeHandler.sendPrefaceFail=연결 [{0}]: 클라이언트에 preface를 전송하지 못했습니다.
upgradeHandler.socketCloseFailed=소켓을 닫는 중 오류 발생
upgradeHandler.stream.closed=스트림 [{0}]이(가) 얼마 동안 이미 닫혀 있었습니다.
upgradeHandler.stream.even=[{0}]의 새로운 원격 스트림 ID가 요청되었으나, 모든 원격 스트림은 반드시 홀수의 ID를 사용해야 합니다.
upgradeHandler.stream.notWritable=연결 [{0}], 스트림 [{1}], 이 스트림은 쓰기 가능하지 않습니다.
upgradeHandler.stream.old=새로운 원격 스트림 ID [{0}]이(가) 요청되었지만, 가장 최근의 스트림은 [{1}]이었습니다.
upgradeHandler.tooManyRemoteStreams=클라이언트가, 활성화된 스트림들을 [{0}]개를 초과하여 사용하려 시도했습니다.
upgradeHandler.tooMuchOverhead=연결 [{0}]: 너무 많은 오버헤드로 인하여 연결이 닫힐 것입니다.
upgradeHandler.unexpectedAck=연결 [{0}], 스트림 [{1}], 예기치 않은 상황에서 settings acknowledgement를 받았습니다.
upgradeHandler.upgrade=연결 [{0}]: HTTP/1.1이 스트림 [1](으)로 업그레이드됩니다.
upgradeHandler.upgrade.fail=연결 [{0}], HTTP/1.1 업그레이드 실패
upgradeHandler.upgradeDispatch.entry=엔트리, 연결 [{0}], SocketStatus [{1}]
upgradeHandler.upgradeDispatch.exit=Exit, 연결 [{0}], SocketState [{1}]
upgradeHandler.windowSizeReservationInterrupted=연결 [{0}], 스트림 [{1}], 예비하려 한 바이트 크기: [{2}]
upgradeHandler.windowSizeTooBig=연결 [{0}], 스트림 [{1}], 윈도우 크기가 너무 큽니다.
upgradeHandler.writeBody=연결 [{0}], 스트림 [{1}], 데이터 길이 [{2}]
upgradeHandler.writeHeaders=연결 [{0}], 스트림 [{1}]
upgradeHandler.writePushHeaders=연결 [{0}], 스트림 [{1}], Push된 스트림 [{2}], EndOfStream [{3}]

windowAllocationManager.dispatched=연결 [{0}], 스트림 [{1}]에 디스패치됩니다.
windowAllocationManager.notified=연결 [{0}], 스트림 [{1}]에 통지됩니다.
windowAllocationManager.notify=연결 [{0}], 스트림 [{1}], 대기 타입 [{2}], 통지 타입 [{3}]
windowAllocationManager.waitFor.connection=연결 [{0}], 스트림 [{1}], 제한 시간 [{2}] 내에서, 연결 흐름 제어 윈도우(blocking)를 대기합니다.
windowAllocationManager.waitFor.ise=연결 [{0}], 스트림 [{1}], 이미 대기 중입니다.
windowAllocationManager.waitFor.stream=연결 [{0}], 스트림 [{1}], 제한 시간 [{2}] 내에서, 스트림 흐름 제어 윈도우(blocking)를 대기합니다.
windowAllocationManager.waitForNonBlocking.connection=연결 [{0}], 스트림 [{1}], 연결 흐름 제어 윈도우(non-blocking)를 대기합니다.
windowAllocationManager.waitForNonBlocking.stream=연결 [{0}], 스트림 [{1}], 스트림 흐름 제어 윈도우(non-blocking)를 대기합니다.

writeStateMachine.endWrite.ise=쓰기가 한번 완료되고 나면, 새로운 상태를 위해 [{0}]을(를) 지정하는 것은 불허됩니다.
writeStateMachine.ise=[{1}]인 상태에서 [{0}()]을(를) 호출하는 것은 불허됩니다.
