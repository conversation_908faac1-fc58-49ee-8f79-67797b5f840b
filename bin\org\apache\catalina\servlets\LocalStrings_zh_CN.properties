# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cgiServlet.emptyEnvVarName=初始化参数中的环境变量名为空[环境变量-]
cgiServlet.expandCloseFail=无法关闭路径[{0}]处脚本的输入流
cgiServlet.expandCreateDirFail=无法为脚本扩展创建目标目录[{0}]
cgiServlet.expandDeleteFail=扩展期间，发生IOException异常后删除文件[{0}]失败
cgiServlet.expandFail=在路径[{0}] 到[{1}] 展开脚本失败.
cgiServlet.expandNotFound=无法展开[{0}]，因为找不到它。
cgiServlet.expandOk=从路径[{0}]到[{1}]扩展脚本
cgiServlet.find.found=找到CGI:name[{0}]、path[{1}]、script name[{2}]和CGI name[{3}]
cgiServlet.find.location=在 [{0}] 查找文件
cgiServlet.find.path=在相对于CGI位置[{1}]的路径[{0}]处请求的CGI脚本
cgiServlet.invalidArgumentDecoded=解码的命令行参数[{0}]与配置的cmdLineArgumentsDecoded模式[{1}]不匹配
cgiServlet.invalidArgumentEncoded=编码的命令行参数[{0}]与配置的cmdLineArgumentsEncoded模式[{1}]不匹配
cgiServlet.runBadHeader=标头行[{0}]错误
cgiServlet.runFail=处理CGI发生I/O问题
cgiServlet.runHeaderReaderFail=I/O 问题关闭请求头读操作
cgiServlet.runInvalidStatus=无效状态 [{0}]
cgiServlet.runOutputStreamFail=关闭输出流时发生I/O问题
cgiServlet.runReaderInterrupt=对于标准错误的读线程，中断并等待。
cgiServlet.runStdErr=标准行：[{0}]。
cgiServlet.runStdErrCount=在stderr上收到了[{0}]行
cgiServlet.runStdErrFail=I/O标准错误问题

defaultServlet.blockExternalEntity=阻止访问publicId[{0}]和systemId[{0}]的外部实体。
defaultServlet.blockExternalEntity2=阻止访问名为{0}、publicId[{1}、baseURI[{2}]和systemId[{3}]的外部实体。
defaultServlet.blockExternalSubset=用名称[{0}]和baseURI[{1}]阻止对外部子集的访问
defaultServlet.missingResource=请求的资源[{0}]不可用
defaultServlet.noResources=找不到静态资源
defaultServlet.readerCloseFailed=无法关闭读卡器
defaultServlet.skipfail=读取失败，因为只有[{0}]个字节可用，但需要跳过[{1}]个字节才能到达请求范围的开始
defaultServlet.xslError=XSL转换器错误

directory.filename=文件名
directory.lastModified=上次修改时间。
directory.parent=最多[{0}]
directory.size=大小
directory.title=[{0}]的目录列表

webdavservlet.externalEntityIgnored=请求包含对PublicID[{0}]和SystemID[{1}]的外部实体的引用，该引用被忽略。
webdavservlet.inputstreamclosefail=无法关闭[{0}]的输入流
webdavservlet.jaxpfailed=JAXP 初始化失败
