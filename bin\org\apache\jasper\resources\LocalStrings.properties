# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jasper.error.emptybodycontent.nonempty=According to TLD, tag [{0}] must be empty, but is not

jsp.engine.info=Jasper JSP {0} Engine
jsp.error.action.isnottagfile=[{0}] action can be used in tag files only
jsp.error.action.istagfile=[{0}] action cannot be used in a tag file
jsp.error.attempt_to_clear_flushed_buffer=Error: Attempt to clear a buffer that's already been flushed
jsp.error.attr.quoted=Attribute value should be quoted
jsp.error.attribute.custom.non_rt_with_expr=According to TLD or attribute directive in tag file, attribute [{0}] does not accept any expressions
jsp.error.attribute.deferredmix=Cannot use both ${} and #{} EL expressions in the same attribute value
jsp.error.attribute.duplicate=Attribute qualified names must be unique within an element
jsp.error.attribute.invalidPrefix=The attribute prefix [{0}] does not correspond to any imported tag library
jsp.error.attribute.noequal=equal symbol expected
jsp.error.attribute.noescape=Attribute value [{0}] is quoted with [{1}] which must be escaped when used within the value
jsp.error.attribute.noquote=quote symbol expected
jsp.error.attribute.nowhitespace=The JSP specification requires that an attribute name is preceded by whitespace
jsp.error.attribute.null_name=Null attribute name
jsp.error.attribute.standard.non_rt_with_expr=The [{0}] attribute of the [{1}] standard action does not accept any expressions
jsp.error.attribute.unterminated=attribute value for [{0}] is not properly terminated
jsp.error.bad.scratch.dir=The scratchDir you specified: [{0}] is unusable.
jsp.error.badStandardAction=Invalid standard action
jsp.error.bad_attribute=Attribute [{0}] invalid for tag [{1}] according to TLD
jsp.error.bad_tag=No tag [{0}] defined in tag library imported with prefix [{1}]
jsp.error.beans.nomethod=Cannot find a method to read property [{0}] in a bean of type [{1}]
jsp.error.beans.nomethod.setproperty=Cannot find a method to write property [{0}] of type [{1}] in a bean of type [{2}]
jsp.error.beans.noproperty=Cannot find any information on property [{0}] in a bean of type [{1}]
jsp.error.beans.nullbean=Attempted a bean operation on a null object.
jsp.error.beans.property.conversion=Unable to convert string [{0}] to class [{1}] for attribute [{2}]: [{3}]
jsp.error.beans.propertyeditor.notregistered=Property Editor not registered with the PropertyEditorManager
jsp.error.beans.setproperty.noindexset=Cannot set indexed property
jsp.error.bug48498=Unable to display JSP extract. Probably due to an XML parser bug (see Tomcat bug 48498 for details).
jsp.error.classname=Cannot determine classname from .class file
jsp.error.coerce_to_type=Cannot coerce value [{2}] to type [{1}] for attribute [{0}].
jsp.error.compilation=Error compiling file: [{0}] [{1}]
jsp.error.compiler=No Java compiler available
jsp.error.compiler.config=No Java compiler available for configuration options compilerClassName: [{0}] and compiler: [{1}]
jsp.error.config_pagedir_encoding_mismatch=Page-encoding specified in jsp-property-group [{0}] is different from that specified in page directive [{1}]
jsp.error.corresponding.servlet=Generated servlet error:\n
jsp.error.could.not.add.taglibraries=Could not add one or more tag libraries.
jsp.error.data.file.processing=Error processing file [{0}]
jsp.error.data.file.read=Error reading file [{0}]
jsp.error.data.file.write=Error while writing data file
jsp.error.deferredmethodandvalue='deferredValue' and 'deferredMethod' cannot be both 'true'
jsp.error.deferredmethodsignaturewithoutdeferredmethod=Cannot specify a method signature if 'deferredMethod' is not 'true'
jsp.error.deferredvaluetypewithoutdeferredvalue=Cannot specify a value type if 'deferredValue' is not 'true'
jsp.error.directive.isnottagfile=[{0}] directive can only be used in a tag file
jsp.error.directive.istagfile=[{0}] directive cannot be used in a tag file
jsp.error.duplicate.name.jspattribute=The attribute [{0}] specified in the standard or custom action also appears as the value of the name attribute in the enclosed jsp:attribute
jsp.error.duplicateqname=An attribute with duplicate qualified name [{0}] was found. Attribute qualified names must be unique within an element.
jsp.error.dynamic.attributes.not.implemented=The [{0}] tag declares that it accepts dynamic attributes but does not implement the required interface
jsp.error.el.parse=[{0}] : [{1}]
jsp.error.el.template.deferred=#{...} is not allowed in template text
jsp.error.el_interpreter_class.instantiation=Failed to load or instantiate ELInterpreter class [{0}]
jsp.error.fallback.invalidUse=jsp:fallback must be a direct child of jsp:plugin
jsp.error.file.already.registered=Recursive include of file [{0}]
jsp.error.file.cannot.read=Cannot read file: [{0}]
jsp.error.file.not.found=JSP file [{0}] not found
jsp.error.flush=Exception occurred when flushing data
jsp.error.fragmentwithtype=Cannot specify both ''fragment'' and ''type'' attributes.  If ''fragment'' is present, ''type'' is fixed as ''{0}''
jsp.error.function.classnotfound=The class [{0}] specified in TLD for the function [{1}] cannot be found: [{2}]
jsp.error.include.exception=Unable to include [{0}]
jsp.error.include.tag=Invalid jsp:include tag
jsp.error.internal.filenotfound=Internal Error: File [{0}] not found
jsp.error.invalid.attribute=[{0}] has invalid attribute: [{1}]
jsp.error.invalid.bean=The value for the useBean class attribute [{0}] is invalid.
jsp.error.invalid.directive=Invalid directive
jsp.error.invalid.expression=[{0}] contains invalid expression(s): [{1}]
jsp.error.invalid.implicit=Invalid implicit TLD for tag file at [{0}]
jsp.error.invalid.implicit.version=Invalid JSP version defined in implicit TLD for tag file at [{0}]
jsp.error.invalid.scope=Illegal value of ''scope'' attribute: [{0}] (must be one of "page", "request", "session", or "application")
jsp.error.invalid.tagdir=Tag file directory [{0}] does not start with "/WEB-INF/tags"
jsp.error.invalid.version=Invalid JSP version defined for tag file at [{0}]
jsp.error.ise_on_clear=Illegal to clear() when buffer size == 0
jsp.error.java.line.number=An error occurred at line: [{0}] in the generated java file: [{1}]
jsp.error.javac=Javac exception
jsp.error.javac.env=Environment:
jsp.error.jspbody.emptybody.only=The [{0}] tag can only have jsp:attribute in its body.
jsp.error.jspbody.invalidUse=jsp:body must be the subelement of a standard or custom action
jsp.error.jspbody.required=Must use jsp:body to specify tag body for [{0}] if jsp:attribute is used.
jsp.error.jspc.missingTarget=Missing target: Must specify -webapp or -uriroot, or one or more JSP pages
jsp.error.jspc.no_uriroot=The uriroot is not specified and cannot be located with the specified JSP file(s)
jsp.error.jspc.uriroot_not_dir=The -uriroot option must specify a pre-existing directory
jsp.error.jspelement.missing.name=Mandatory XML-style 'name' attribute missing
jsp.error.jspoutput.conflict=&lt;jsp:output&gt;: illegal to have multiple occurrences of [{0}] with different values (old: [{1}], new: [{2}])
jsp.error.jspoutput.doctypenamesystem=&lt;jsp:output&gt;: 'doctype-root-element' and 'doctype-system' attributes must appear together
jsp.error.jspoutput.doctypepublicsystem=&lt;jsp:output&gt;: 'doctype-system' attribute must appear if 'doctype-public' attribute appears
jsp.error.jspoutput.invalidUse=&lt;jsp:output&gt; must not be used in standard syntax
jsp.error.jspoutput.nonemptybody=&lt;jsp:output&gt; must not have a body
jsp.error.jsproot.version.invalid=Invalid version number: [{0}], must be "1.2", "2.0", "2.1", "2.2" or "2.3"
jsp.error.jsptext.badcontent='&lt;', when appears in the body of &lt;jsp:text&gt;, must be encapsulated within a CDATA
jsp.error.lastModified=Unable to determine last modified date for file [{0}]
jsp.error.library.invalid=JSP page is invalid according to library [{0}]: [{1}]
jsp.error.literal_with_void=A literal value was specified for attribute [{0}] that is defined as a deferred method with a return type of void. JSP.2.3.4 does not permit literal values in this case
jsp.error.loadclass.taghandler=Unable to load tag handler class [{0}] for tag [{1}]
jsp.error.location=line: [{0}], column: [{1}]
jsp.error.mandatory.attribute=[{0}]: Mandatory attribute [{1}] missing
jsp.error.missing.tagInfo=TagInfo object for [{0}] is missing from TLD
jsp.error.missing_attribute=According to the TLD or the tag file, attribute [{0}] is mandatory for tag [{1}]
jsp.error.missing_var_or_varReader=Missing 'var' or 'varReader' attribute
jsp.error.namedAttribute.invalidUse=jsp:attribute must be the subelement of a standard or custom action
jsp.error.needAlternateJavaEncoding=Default java encoding [{0}] is invalid on your java platform. An alternate can be specified via the ''javaEncoding'' parameter of JspServlet.
jsp.error.nested.jspattribute=A jsp:attribute standard action cannot be nested within another jsp:attribute standard action
jsp.error.nested.jspbody=A jsp:body standard action cannot be nested within another jsp:body or jsp:attribute standard action
jsp.error.nested_jsproot=Nested &lt;jsp:root&gt;
jsp.error.no.more.content=End of content reached while more parsing required: tag nesting error?
jsp.error.no.scratch.dir=The JSP engine is not configured with a scratch dir.\n\
\ Please add "jsp.initparams=scratchdir=<dir-name>" \n\
\ in the servlets.properties file for this context.
jsp.error.no.scriptlets=Scripting elements ( &lt;%!, &lt;jsp:declaration, &lt;%=, &lt;jsp:expression, &lt;%, &lt;jsp:scriptlet ) are disallowed here.
jsp.error.noFunction=The function [{0}] cannot be located with the specified prefix
jsp.error.noFunctionMethod=Method [{0}] for function [{1}] not found in class [{2}]
jsp.error.non_null_tei_and_var_subelems=Tag [{0}] has one or more variable subelements and a TagExtraInfo class that returns one or more VariableInfo
jsp.error.not.in.template=[{0}] not allowed in a template text body.
jsp.error.outputfolder=No output folder
jsp.error.overflow=Error: JSP Buffer overflow
jsp.error.page.conflict.autoflush=Page directive: illegal to have multiple occurrences of ''autoFlush'' with different values (old: [{0}], new: [{1}])
jsp.error.page.conflict.buffer=Page directive: illegal to have multiple occurrences of ''buffer'' with different values (old: [{0}], new: [{1}])
jsp.error.page.conflict.contenttype=Page directive: illegal to have multiple occurrences of ''contentType'' with different values (old: [{0}], new: [{1}])
jsp.error.page.conflict.deferredsyntaxallowedasliteral=Page directive: illegal to have multiple occurrences of ''deferredSyntaxAllowedAsLiteral'' with different values (old: [{0}], new: [{1}])
jsp.error.page.conflict.errorpage=Page directive: illegal to have multiple occurrences of ''errorPage'' with different values (old: [{0}], new: [{1}])
jsp.error.page.conflict.extends=Page directive: illegal to have multiple occurrences of ''extends'' with different values (old: [{0}], new: [{1}])
jsp.error.page.conflict.info=Page directive: illegal to have multiple occurrences of ''info'' with different values (old: [{0}], new: [{1}])
jsp.error.page.conflict.iselignored=Page directive: illegal to have multiple occurrences of ''isELIgnored'' with different values (old: [{0}], new: [{1}])
jsp.error.page.conflict.iserrorpage=Page directive: illegal to have multiple occurrences of ''isErrorPage'' with different values (old: [{0}], new: [{1}])
jsp.error.page.conflict.isthreadsafe=Page directive: illegal to have multiple occurrences of ''isThreadSafe'' with different values (old: [{0}], new: [{1}])
jsp.error.page.conflict.language=Page directive: illegal to have multiple occurrences of ''language'' with different values (old: [{0}], new: [{1}])
jsp.error.page.conflict.session=Page directive: illegal to have multiple occurrences of ''session'' with different values (old: [{0}], new: [{1}])
jsp.error.page.conflict.trimdirectivewhitespaces=Page directive: illegal to have multiple occurrences of ''trimDirectiveWhitespaces'' with different values (old: [{0}], new: [{1}])
jsp.error.page.invalid.buffer=Page directive: invalid value for buffer
jsp.error.page.invalid.deferredsyntaxallowedasliteral=Page directive: invalid value for deferredSyntaxAllowedAsLiteral
jsp.error.page.invalid.import=Page directive: invalid value for import
jsp.error.page.invalid.iselignored=Page directive: invalid value for isELIgnored
jsp.error.page.invalid.iserrorpage=Page directive: invalid value for isErrorPage
jsp.error.page.invalid.isthreadsafe=Page directive: invalid value for isThreadSafe
jsp.error.page.invalid.session=Page directive: invalid value for session
jsp.error.page.invalid.trimdirectivewhitespaces=Page directive: invalid value for trimDirectiveWhitespaces
jsp.error.page.language.nonjava=Page directive: invalid language attribute
jsp.error.page.multi.pageencoding=Page directive must not have multiple occurrences of pageencoding
jsp.error.page.noSession=Cannot access session scope in page that does not participate in any session
jsp.error.param.invalidUse=The jsp:param action must not be used outside the jsp:include, jsp:forward, or jsp:params elements
jsp.error.paramexpected=Expecting "jsp:param" standard action with "name" and "value" attributes
jsp.error.params.emptyBody=jsp:params must contain at least one nested jsp:param
jsp.error.params.invalidUse=jsp:params must be a direct child of jsp:plugin
jsp.error.parse.error.in.TLD=Parse Error in the tag library descriptor: [{0}]
jsp.error.parse.xml=XML parsing error on file [{0}]
jsp.error.parse.xml.line=XML parsing error on file [{0}]: (line [{1}], col [{2}])
jsp.error.parse.xml.scripting.invalid.body=Body of [{0}] element must not contain any XML elements
jsp.error.plugin.badtype=Illegal value for 'type' attribute in jsp:plugin: must be 'bean' or 'applet'
jsp.error.plugin.nocode=code not declared in jsp:plugin
jsp.error.plugin.notype=type not declared in jsp:plugin
jsp.error.prefix.refined=Attempt to redefine the prefix [{0}] to [{1}], when it was already defined as [{2}] in the current scope.
jsp.error.prefix.use_before_dcl=The prefix [{0}] specified in this tag directive has been previously used by an action in file [{1}] line [{2}].
jsp.error.prolog_config_encoding_mismatch=Page-encoding specified in XML prolog [{0}] is different from that specified in jsp-property-group [{1}]
jsp.error.prolog_pagedir_encoding_mismatch=Page-encoding specified in XML prolog [{0}] is different from that specified in page directive [{1}]
jsp.error.quotes.unterminated=Unterminated quotes
jsp.error.scripting.variable.missing_name=Unable to determine scripting variable name from attribute [{0}]
jsp.error.security=Security initialization failed for context
jsp.error.servlet.destroy.failed=Exception during Servlet.destroy() for JSP page
jsp.error.servlet.invalid.method=JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS
jsp.error.setLastModified=Unable to set last modified date for file [{0}]
jsp.error.signature.classnotfound=The class [{0}] specified in the method signature in TLD for the function [{1}] cannot be found. [{2}]
jsp.error.simpletag.badbodycontent=The TLD for the class [{0}] specifies an invalid body-content (JSP) for a SimpleTag.
jsp.error.single.line.number=An error occurred at line: [{0}] in the jsp file: [{1}]
jsp.error.stream.close.failed=Failed to close stream
jsp.error.stream.closed=Stream closed
jsp.error.string_interpreter_class.instantiation=Failed to load or instantiate StringInterpreter class [{0}]
jsp.error.tag.conflict.attr=Tag directive: illegal to have multiple occurrences of the attribute [{0}] with different values (old: [{1}], new: [{2}])
jsp.error.tag.conflict.deferredsyntaxallowedasliteral=Tag directive: illegal to have multiple occurrences of ''deferredSyntaxAllowedAsLiteral'' with different values (old: [{0}], new: [{1}])
jsp.error.tag.conflict.iselignored=Tag directive: illegal to have multiple occurrences of ''isELIgnored'' with different values (old: [{0}], new: [{1}])
jsp.error.tag.conflict.language=Tag directive: illegal to have multiple occurrences of ''language'' with different values (old: [{0}], new: [{1}])
jsp.error.tag.conflict.trimdirectivewhitespaces=Tag directive: illegal to have multiple occurrences of ''trimDirectiveWhitespaces'' with different values (old: [{0}], new: [{1}])
jsp.error.tag.invalid.deferredsyntaxallowedasliteral=Tag directive: invalid value for deferredSyntaxAllowedAsLiteral
jsp.error.tag.invalid.iselignored=Tag directive: invalid value for isELIgnored
jsp.error.tag.invalid.trimdirectivewhitespaces=Tag directive: invalid value for trimDirectiveWhitespaces
jsp.error.tag.language.nonjava=Tag directive: invalid language attribute
jsp.error.tag.multi.pageencoding=Tag directive must not have multiple occurrences of pageencoding
jsp.error.tagdirective.badbodycontent=Invalid body-content [{0}] in tag directive
jsp.error.tagfile.badSuffix=Missing ".tag" suffix in tag file path [{0}]
jsp.error.tagfile.illegalPath=Illegal tag file path: [{0}], must start with "/WEB-INF/tags" or "/META-INF/tags"
jsp.error.tagfile.missingPath=Path not specified to tag file
jsp.error.tagfile.nameFrom.badAttribute=The attribute directive declared at line [{1}] with name [{0}] that matches the name-from-attribute value of this variable directive must be of type java.lang.String, must be "required" and must not be a "rtexprvalue".
jsp.error.tagfile.nameFrom.noAttribute=Cannot find an attribute directive with a name [{0}] that matches the name-from-attribute value of this variable directive
jsp.error.tagfile.nameNotUnique=The value of [{0}] and the value of [{1}] in line [{2}] are the same.
jsp.error.taglibDirective.absUriCannotBeResolved=The absolute uri: [{0}] cannot be resolved in either web.xml or the jar files deployed with this application
jsp.error.taglibDirective.both_uri_and_tagdir=Both 'uri' and 'tagdir' attributes specified
jsp.error.taglibDirective.missing.location=Neither 'uri' nor 'tagdir' attribute specified
jsp.error.taglibDirective.uriInvalid=The URI provided for a tag library [{0}] is not a valid URI
jsp.error.tei.invalid.attributes=Validation error messages from TagExtraInfo for [{0}]
jsp.error.teiclass.instantiation=Failed to load or instantiate TagExtraInfo class: [{0}]
jsp.error.text.has_subelement=&lt;jsp:text&gt; must not have any subelements
jsp.error.tld.fn.duplicate.name=Duplicate function name [{0}] in tag library [{1}]
jsp.error.tld.fn.invalid.signature=Invalid syntax for function signature in TLD.  Tag Library: [{0}], Function: [{1}]
jsp.error.tld.invalid_tld_file=Invalid tld file: [{0}], see JSP specification section 7.3.1 for more details
jsp.error.tld.mandatory.element.missing=Mandatory TLD element [{0}] missing or empty in TLD [{1}]
jsp.error.tld.missing=Unable to find taglib [{0}] for URI: [{1}]
jsp.error.tld.missing_jar=Missing JAR resource [{0}] containing TLD
jsp.error.tld.unable_to_get_jar=Unable to get JAR resource [{0}] containing TLD: [{1}]
jsp.error.tlv.invalid.page=Validation error messages from TagLibraryValidator for [{0}] in [{1}]
jsp.error.tlvclass.instantiation=Failed to load or instantiate TagLibraryValidator class: [{0}]
jsp.error.unable.compile=Unable to compile class for JSP
jsp.error.unable.deleteClassFile=Unable to delete class file [{0}]
jsp.error.unable.load=Unable to load class for JSP
jsp.error.unable.renameClassFile=Unable to rename class file from [{0}] to [{1}]
jsp.error.unable.to_find_method=Unable to find setter method for attribute: [{0}]
jsp.error.unavailable=JSP has been marked unavailable
jsp.error.unbalanced.endtag=The end tag "&lt;/{0}" is unbalanced
jsp.error.undeclared_namespace=A custom tag was encountered with an undeclared namespace [{0}]
jsp.error.unknown_attribute_type=Unknown attribute type [{1}] for attribute [{0}].
jsp.error.unsupported.encoding=Unsupported encoding: [{0}]
jsp.error.unterminated=Unterminated [{0}] tag
jsp.error.usebean.duplicate=useBean: Duplicate bean name: [{0}]
jsp.error.usebean.noSession=Illegal for useBean to use session scope when JSP page declares (via page directive) that it does not participate in sessions
jsp.error.var_and_varReader=Only one of 'var' or 'varReader' may be specified
jsp.error.variable.alias=Both or none of the name-from-attribute and alias attributes must be specified in a variable directive
jsp.error.variable.both.name=Cannot specify both name-given and name-from-attribute attributes in a variable directive
jsp.error.variable.either.name=Either name-given or name-from-attribute attribute must be specified in a variable directive
jsp.error.xml.badStandardAction=Invalid standard action: [{0}]
jsp.error.xml.bad_tag=No tag [{0}] defined in tag library associated with uri [{1}]
jsp.error.xml.closeQuoteMissingInTextDecl=closing quote in the value following [{0}] in the text declaration is missing.
jsp.error.xml.closeQuoteMissingInXMLDecl=closing quote in the value following [{0}] in the XML declaration is missing.
jsp.error.xml.encodingByteOrderUnsupported=Given byte order for encoding [{0}] is not supported.
jsp.error.xml.encodingDeclInvalid=Invalid encoding name [{0}].
jsp.error.xml.encodingDeclRequired=The encoding declaration is required in the text declaration.
jsp.error.xml.eqRequiredInTextDecl=The '' = '' character must follow [{0}] in the text declaration.
jsp.error.xml.eqRequiredInXMLDecl=The '' = '' character must follow [{0}] in the XML declaration.
jsp.error.xml.expectedByte=Expected byte [{0}] of [{1}]-byte UTF-8 sequence.
jsp.error.xml.invalidASCII=Byte [{0}] not 7-bit ASCII.
jsp.error.xml.invalidByte=Invalid byte [{0}] of [{1}]-byte UTF-8 sequence.
jsp.error.xml.invalidCharInContent=An invalid XML character (Unicode: 0x[{0}]) was found in the element content of the document.
jsp.error.xml.invalidCharInPI=An invalid XML character (Unicode: 0x[{0}]) was found in the processing instruction.
jsp.error.xml.invalidCharInTextDecl=An invalid XML character (Unicode: 0x[{0}]) was found in the text declaration.
jsp.error.xml.invalidCharInXMLDecl=An invalid XML character (Unicode: 0x[{0}]) was found in the XML declaration.
jsp.error.xml.invalidHighSurrogate=High surrogate bits in UTF-8 sequence must not exceed 0x10 but found 0x[{0}].
jsp.error.xml.morePseudoAttributes=more pseudo attributes is expected.
jsp.error.xml.noMorePseudoAttributes=no more pseudo attributes is allowed.
jsp.error.xml.operationNotSupported=Operation [{0}] not supported by [{1}] reader.
jsp.error.xml.pseudoAttrNameExpected=a pseudo attribute name is expected.
jsp.error.xml.quoteRequiredInTextDecl=The value following [{0}] in the text declaration must be a quoted string.
jsp.error.xml.quoteRequiredInXMLDecl=The value following [{0}] in the XML declaration must be a quoted string.
jsp.error.xml.reservedPITarget=The processing instruction target matching "[xX][mM][lL]" is not allowed.
jsp.error.xml.sdDeclInvalid=The standalone document declaration value must be "yes" or "no", not [{0}].
jsp.error.xml.spaceRequiredBeforeEncodingInTextDecl=White space is required before the encoding pseudo attribute in the text declaration.
jsp.error.xml.spaceRequiredBeforeEncodingInXMLDecl=White space is required before the encoding pseudo attribute in the XML declaration.
jsp.error.xml.spaceRequiredBeforeStandalone=White space is required before the encoding pseudo attribute in the XML declaration.
jsp.error.xml.spaceRequiredBeforeVersionInTextDecl=White space is required before the version pseudo attribute in the text declaration.
jsp.error.xml.spaceRequiredBeforeVersionInXMLDecl=White space is required before the version pseudo attribute in the XML declaration.
jsp.error.xml.spaceRequiredInPI=White space is required between the processing instruction target and data.
jsp.error.xml.versionInfoRequired=The version is required in the XML declaration.
jsp.error.xml.versionNotSupported=XML version [{0}] is not supported, only XML 1.0 is supported.
jsp.error.xml.xmlDeclUnterminated=The XML declaration must end with "?>".
jsp.exception=An exception occurred processing [{0}] at line [{1}]
jsp.info.ignoreSetting=Ignored setting for [{0}] of [{1}] because a SecurityManager was enabled
jsp.message.dont.modify.servlets=IMPORTANT: Do not modify the generated servlets
jsp.message.jsp_added=Adding JSP for path [{0}] to queue of context [{1}]
jsp.message.jsp_queue_created=Created jsp queue with length [{0}] for context [{1}]
jsp.message.jsp_queue_update=Updating JSP for path [{0}] in queue of context [{1}]
jsp.message.jsp_removed_excess=Removing excess JSP for path [{0}] from queue of context [{1}]
jsp.message.jsp_removed_idle=Removing idle JSP for path [{0}] in context [{1}] after [{2}] milliseconds
jsp.message.jsp_unload_check=Checking JSPs for unload in context [{0}], JSP count: [{1}] queue length: [{2}]
jsp.message.parent_class_loader_is=Parent class loader is: [{0}]
jsp.message.scratch.dir.is=Scratch dir for the JSP engine is: [{0}]
jsp.tldCache.noTldInDir=No TLD files were found in directory [{0}].
jsp.tldCache.noTldInJar=No TLD files were found in [{0}]. Consider adding the JAR to the tomcat.util.scan.StandardJarScanFilter.jarsToSkip property in CATALINA_BASE/conf/catalina.properties file.
jsp.tldCache.noTldInResourcePath=No TLD files were found in resource path [{0}].
jsp.tldCache.noTldSummary=At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
jsp.tldCache.tldInDir=TLD files were found in directory [{0}].
jsp.tldCache.tldInJar=TLD files were found in JAR [{0}].
jsp.tldCache.tldInResourcePath=TLD files were found in resource path [{0}].
jsp.warning.bad.urlpattern.propertygroup=Bad value [{0}] in the url-pattern subelement in web.xml
jsp.warning.checkInterval=Warning: Invalid value for the initParam checkInterval. Will use the default value of "300" seconds
jsp.warning.classDebugInfo=Warning: Invalid value for the initParam classdebuginfo. Will use the default value of "false"
jsp.warning.classpathUrl=Invalid URL found in class path. This URL will be ignored
jsp.warning.compiler.classfile.delete.fail=Failed to delete generated class file [{0}]
jsp.warning.compiler.classfile.delete.fail.unknown=Failed to delete generated class file(s)
jsp.warning.compiler.javafile.delete.fail=Failed to delete generated Java file [{0}]
jsp.warning.development=Warning: Invalid value for the initParam development. Will use the default value of "true"
jsp.warning.displaySourceFragment=Warning: Invalid value for the initParam displaySourceFragment. Will use the default value of "true"
jsp.warning.dumpSmap=Warning: Invalid value for the initParam dumpSmap. Will use the default value of "false"
jsp.warning.enablePooling=Warning: Invalid value for the initParam enablePooling. Will use the default value of "true"
jsp.warning.fork=Warning: Invalid value for the initParam fork. Will use the default value of "true"
jsp.warning.genchararray=Warning: Invalid value for the initParam genStringAsCharArray. Will use the default value of "false"
jsp.warning.jspIdleTimeout=Warning: Invalid value for the initParam jspIdleTimeout. Will use the default value of "-1"
jsp.warning.keepgen=Warning: Invalid value for the initParam keepgenerated. Will use the default value of "false"
jsp.warning.mappedFile=Warning: Invalid value for the initParam mappedFile. Will use the default value of "false"
jsp.warning.maxLoadedJsps=Warning: Invalid value for the initParam maxLoadedJsps. Will use the default value of "-1"
jsp.warning.modificationTestInterval=Warning: Invalid value for the initParam modificationTestInterval. Will use the default value of "4" seconds
jsp.warning.noJarScanner=Warning: No org.apache.tomcat.JarScanner set in ServletContext. Falling back to default JarScanner implementation.
jsp.warning.quoteAttributeEL=Warning: Invalid value for the initParam quoteAttributeEL. Will use the default value of "false"
jsp.warning.recompileOnFail=Warning: Invalid value for the initParam recompileOnFail. Will use the default value of "false"
jsp.warning.strictQuoteEscaping=Warning: Invalid value for the initParam strictQuoteEscaping. Will use the default value of "true"
jsp.warning.suppressSmap=Warning: Invalid value for the initParam suppressSmap. Will use the default value of "false"
jsp.warning.tagPreDestroy=Error processing preDestroy on tag instance of [{0}]
jsp.warning.tagRelease=Error processing release on tag instance of [{0}]
jsp.warning.unknown.sourceVM=Unknown source VM [{0}] ignored
jsp.warning.unknown.targetVM=Unknown target VM [{0}] ignored
jsp.warning.unsupported.sourceVM=Unsupported source VM [{0}] requested, using [{1}]
jsp.warning.unsupported.targetVM=Unsupported target VM [{0}] requested, using [{1}]
jsp.warning.xpoweredBy=Warning: Invalid value for the initParam xpoweredBy. Will use the default value of "false"

jspc.delete.fail=Failed to delete file [{0}]
jspc.error.fileDoesNotExist=The file argument [{0}] does not exist
jspc.error.generalException=ERROR-the file [{0}] generated the following general exception:
jspc.error.invalidFragment=Failing pre-compilation due to errors in web fragments
jspc.error.invalidWebXml=Failing pre-compilation due to errors in web.xml
jspc.generation.result=Generation completed with [{0}] errors in [{1}] milliseconds
jspc.implicit.uriRoot=uriRoot implicitly set to [{0}]
jspc.usage=Usage: jspc <options> [--] <jsp files>\n\
where jsp files is\n\
\    -webapp <dir>         A directory containing a web-app, whose JSP pages\n\
\                          will be processed recursively\n\
or any number of\n\
\    <file>                A file to be parsed as a JSP page\n\
where options include:\n\
\    -help                 Print this help message\n\
\    -v                    Verbose mode\n\
\    -d <dir>              Output Directory (default -Djava.io.tmpdir)\n\
\    -l                    Outputs the name of the JSP page upon failure\n\
\    -s                    Outputs the name of the JSP page upon success\n\
\    -p <name>             Name of target package (default org.apache.jsp)\n\
\    -c <name>             Name of target class name (only applies to first JSP page)\n\
\    -mapped               Generates separate write() calls for each HTML line in the JSP\n\
\    -die[#]               Generates an error return code (#) on fatal errors (default 1)\n\
\    -uribase <dir>        The uri directory compilations should be relative to\n\
\                          (default "/")\n\
\    -uriroot <dir>        Same as -webapp\n\
\    -compile              Compiles generated servlets\n\
\    -failFast             Stop on first compile error\n\
\    -webinc <file>        Creates a partial servlet mappings in the file\n\
\    -webfrg <file>        Creates a complete web-fragment.xml file\n\
\    -webxml <file>        Creates a complete web.xml in the file\n\
\    -webxmlencoding <enc> Set the encoding charset used to read and write the web.xml\n\
\                          file (default is UTF-8)\n\
\    -addwebxmlmappings    Merge generated web.xml fragment into the web.xml file of the\n\
\                          web-app, whose JSP pages we are processing\n\
\    -ieplugin <clsid>     Java Plugin classid for Internet Explorer\n\
\    -classpath <path>     Overrides java.class.path system property\n\
\    -xpoweredBy           Add X-Powered-By response header\n\
\    -trimSpaces           Remove template text that consists entirely of whitespace\n\
\    -javaEncoding <enc>   Set the encoding charset for Java classes (default UTF-8)\n\
\    -source <version>     Set the -source argument to the compiler (default 1.7)\n\
\    -target <version>     Set the -target argument to the compiler (default 1.7)\n\
\    -threadCount <count>  Number of threads to use for compilation.\n\
\                          ("2.0C" means two threads per core)\n
jspc.webfrg.footer=\n\
</web-fragment>\n\
\n
jspc.webfrg.header=<?xml version="1.0" encoding="{0}"?>\n\
<web-fragment xmlns="http://xmlns.jcp.org/xml/ns/javaee"\n\
\              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"\n\
\              xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee\n\
\                      http://xmlns.jcp.org/xml/ns/javaee/web-fragment_3_1.xsd"\n\
\              version="3.1"\n\
\              metadata-complete="true">\n\
\  <name>org_apache_jasper.jspc</name>\n\
\  <distributable/>\n\
<!--\n\
Automatically created by Apache Tomcat JspC.\n\
-->\n\
\n
jspc.webinc.footer=\n\
<!--\n\
End of content automatically created by Apache Tomcat JspC.\n\
-->\n\
\n
jspc.webinc.header=\n\
<!--\n\
Automatically created by Apache Tomcat JspC.\n\
-->\n\
\n
jspc.webinc.insertEnd=<!-- JSPC servlet mappings end -->
jspc.webinc.insertStart=<!-- JSPC servlet mappings start -->
jspc.webxml.footer=\n\
</web-app>\n\
\n
jspc.webxml.header=<?xml version="1.0" encoding="{0}"?>\n\
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"\n\
\         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"\n\
\         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee\n\
\                 http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd"\n\
\         version="3.1"\n\
\         metadata-complete="false">\n\
<!--\n\
Automatically created by Apache Tomcat JspC.\n\
-->\n\
\n

org.apache.jasper.compiler.ELParser.invalidQuotesForStringLiteral=The String literal [{0}] is not valid. It must be contained within single or double quotes.
org.apache.jasper.compiler.ELParser.invalidQuoting=The expression [{0}] is not valid. Within a quoted String only [], [''] and ["] may be escaped with [].
org.apache.jasper.compiler.TldCache.servletContextNull=The provided ServletContext was null
org.apache.jasper.servlet.JasperInitializer.onStartup=Initializing Jasper for context [{0}]
org.apache.jasper.servlet.TldScanner.webxmlAdd=Loading TLD for URI [{1}] from resource path [{0}]
org.apache.jasper.servlet.TldScanner.webxmlFailPathDoesNotExist=Failed to process TLD with path [{0}] and URI [{1}]. The specified path does not exist.
org.apache.jasper.servlet.TldScanner.webxmlSkip=Skipping load of TLD for URI [{1}] from resource path [{0}] as it has already been defined in <jsp-config>

xmlParser.skipBomFail=Failed to skip BOM when parsing XML input stream
