<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE mbeans-descriptors PUBLIC
   "-//Apache Software Foundation//DTD Model MBeans Configuration File"
   "http://jakarta.apache.org/commons/dtds/mbeans-descriptors.dtd">
<mbeans-descriptors>
  <mbean
    name="FarmWarDeployer"
    className="org.apache.catalina.mbeans.ClassNameMBean"
    description="Farm Deployer"
    domain="Catalina"
    group="Cluster"
    type="org.apache.catalina.ha.deploy.FarmWarDeployer">
    <attribute
      name="deployDir"
      description="Deployment directory."
      type="java.lang.String"/>
    <attribute
      name="tempDir"
      description="The temporaryDirectory to store binary data when downloading a war from the cluster"
      type="java.lang.String"/>
    <attribute
      name="watchDir"
      description="The directory where we watch for changes"
      type="java.lang.String"/>
    <attribute
      name="watchEnabled"
      description="Is watching enabled?"
      type="boolean"/>
    <attribute
      name="processDeployFrequency"
      description="Frequency of the Farm watchDir check."
      type="int"/>
    <attribute
      name="maxValidTime"
      description="The maximum valid time of FileMessageFactory."
      type="int"/>
  </mbean>
</mbeans-descriptors>
