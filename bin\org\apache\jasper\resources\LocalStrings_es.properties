# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jasper.error.emptybodycontent.nonempty=Según el TLD, el tag [{0}] debe de estar vacío, pero no lo está

jsp.engine.info=Motor Jasper JSP {0}
jsp.error.action.isnottagfile=La acción [{0}] sólo se puede usar en archivos tag
jsp.error.action.istagfile=La acción [{0}] no se puede usar en un archivo tag
jsp.error.attempt_to_clear_flushed_buffer=Error: Se ha intentado limpiar un buffer que ya había sido escrito
jsp.error.attr.quoted=El valor del atributo debería ir entre comillas
jsp.error.attribute.custom.non_rt_with_expr=Según el TLD o la directiva attribute del archivo tag, el atributo [{0}] no acepta expresiones
jsp.error.attribute.deferredmix=No puedo sar ambas espresiones EL ${} y #{} en el mismo valor de atributo
jsp.error.attribute.duplicate=Los nombre cualificados de atributo deben de ser únicos dentro de un elemento
jsp.error.attribute.invalidPrefix=El prefijo de atributo [{0}] no se correponde con ninguna biblioteca importada
jsp.error.attribute.noequal=se esperaba símbolo igual
jsp.error.attribute.noescape=El valor de atributo [{0}] está entrecomillado con [{1}] que debe de usar escape al usarse dentro del valor
jsp.error.attribute.noquote=se esperaba símbolo comillas
jsp.error.attribute.nowhitespace=La especificación JSP requiere que un nombre de atributo sea precedido por un espacio en blanco
jsp.error.attribute.null_name=Nombre de atributo nulo
jsp.error.attribute.standard.non_rt_with_expr=El atributo [{0}] de la acción estándar [{1}] no acepta expresiones
jsp.error.attribute.unterminated=el atributo para [{0}] no está terminado correctamente
jsp.error.bad.scratch.dir=El directorio de trabajo especificado: [{0}] no es utilizable.
jsp.error.badStandardAction=Acción estándar incorrecta
jsp.error.bad_attribute=El atributo [{0}] no es válido según el TLD especificado
jsp.error.bad_tag=No existe el tag [{0}] en la biblioteca importada con prefijo [{1}]
jsp.error.beans.nomethod=No puedo encontrar un método para leer la propiedad [{0}] en un bean del tipo [{1}]
jsp.error.beans.nomethod.setproperty=No puedo encontrar un método para escribir la propiedad [{0}] en un bean del tipo [{2}]
jsp.error.beans.noproperty=No puedo encontrar información de la propiedad [{0}] en un bean del tipo [{1}]
jsp.error.beans.nullbean=Se ha intentado una operación de bean en un objeto nulo
jsp.error.beans.property.conversion=No puedo convertir cadena [{0}] a clase [{1}] para atributo [{2}]: [{3}]
jsp.error.beans.propertyeditor.notregistered=Editor de Propiedades no registrado con el PropertyEditorManager
jsp.error.beans.setproperty.noindexset=No puedo poner la propiedad indexada
jsp.error.bug48498=No puedo mostrar extracto de JSP. Probablemente debido a un error de analizador XML (ver error 48498 de Tomcat para detalles).
jsp.error.classname=No pude determinar el nombre de clase desde el fichero .class
jsp.error.coerce_to_type=No puedo coaccionar el valor [{2}] a tipo [{1}] para atributo [{0}].
jsp.error.compilation=Error compilando fichero: [{0}] [{1}]
jsp.error.compiler=No hay compilador Java disponible
jsp.error.config_pagedir_encoding_mismatch=El Page-encoding especificado en jsp-property-group [{0}] es diferente del especificado en la diectiva page [{1}]
jsp.error.corresponding.servlet=Error de servlet generado:
jsp.error.could.not.add.taglibraries=No pude añadir una o más bibliotecas.
jsp.error.data.file.processing=Error al procesar el archivo [{0}]
jsp.error.data.file.read=Error leyendo archivo [{0}]
jsp.error.data.file.write=Error mientras escribía el archivo de datos
jsp.error.deferredmethodandvalue='deferredValue' y 'deferredMethod' no pueden ser ambos 'verdadero'
jsp.error.deferredmethodsignaturewithoutdeferredmethod=No puedo especificar firma de método si 'deferredMethod' no es 'verdadero'
jsp.error.deferredvaluetypewithoutdeferredvalue=No puedo especificar un tipo de valor si 'deferredValue' no es 'verdadero'
jsp.error.directive.isnottagfile=La Directiva [{0}] sólo se puede usar en un archivo de tag
jsp.error.directive.istagfile=La Directiva [{0}] no puede usarse en archivo de tag
jsp.error.duplicate.name.jspattribute=El atributo [{0}] especificado en la acción standard o custom también aparece como el valor del atributo name en jsp:attribute
jsp.error.duplicateqname=Se ha hallado un atributo con nombre cualificado duplicado [{0}]. Los nombres de atributos cuallificados deben de se únicos dentro de un elemento.
jsp.error.dynamic.attributes.not.implemented=El tag [{0}] declara que acepta atributos dinámicos pero no implementa la interfaz requerida
jsp.error.el.parse=[{0}] : [{1}]
jsp.error.el.template.deferred=#{..} no está permitido en texto de plantilla
jsp.error.el_interpreter_class.instantiation=No se puede cargar la clase ELInterpreter llamada [{0}]
jsp.error.fallback.invalidUse=jsp:fallback debe de ser un hijo directo de jsp:plugin
jsp.error.file.already.registered=El archivo [{0}] ya se ha visto, ¿podría ser un include recursivo?
jsp.error.file.cannot.read=No se puede leer el archivo: [{0}]
jsp.error.file.not.found=Archivo JSP [{0}] no encontrado
jsp.error.flush=Excepción sucedida al vaciar los datos
jsp.error.fragmentwithtype=No puede especificar ambos atributos ''fragment'' y ''type''. Si está presente ''fragment'', ''type'' se pone como ''{0}''
jsp.error.function.classnotfound=La clase [{0}] especificada en el TLD para la función [{1}] no se puede hallar: [{2}]
jsp.error.include.exception=No se puede incluir [{0}]
jsp.error.include.tag=Tag jsp:include no válido
jsp.error.internal.filenotfound=Error Interno: Archivo [{0}] no hallado
jsp.error.invalid.attribute=[{0}]: Atributo incorrecto, [{1}]
jsp.error.invalid.bean=El valor el atributo de clsae useBean [{0}] es inválido.
jsp.error.invalid.directive=Directiva no válida
jsp.error.invalid.expression=[{0}] contiene expresiones incorrectas: [{1}]
jsp.error.invalid.implicit=TLD implícito inválido para fichero de marca en [{0}]
jsp.error.invalid.implicit.version=Versión inválida de JSP definida en TLD implícito para fichero de marca en [{0}]
jsp.error.invalid.scope=Valor ilegal de atributo ''scope'': [{0}] (debe de ser uno de "page", "request", "session", o "application")
jsp.error.invalid.tagdir=El directorio de archivo Tag [{0}] no comienza con "/WEB-INF/tags"
jsp.error.invalid.version=Versión inválida de JSP definida para fichero de marca en [{0}]
jsp.error.ise_on_clear=Es ilegal usar clear() cuando el tamaño del buffer es cero
jsp.error.java.line.number=Ha tenido lugar un error en la línea: [{0}] en el fichero java generado: [{1}]
jsp.error.javac=Excepción de Javac
jsp.error.javac.env=Entorno
jsp.error.jspbody.emptybody.only=El tag [{0}] sólo puede tener jsp:attribute en su cuerpo.
jsp.error.jspbody.invalidUse=jsp:body debe de ser el subelemento de una acción estándar o de cliente
jsp.error.jspbody.required=Se debe de usar jsp:body para especificar cuerpo tag para [{0}] si se usa jsp:attribute.
jsp.error.jspc.missingTarget=Falta target: Debe de especificar -webapp o -uriroot o una o más páginas JSP
jsp.error.jspc.no_uriroot=No se ha especificado uriroot y no puede ser localizado en los archivos JSP especificados
jsp.error.jspc.uriroot_not_dir=La opción -uriroot debe de especificar un directorio ya existente
jsp.error.jspelement.missing.name=Falta atributo obligatorio XML-style 'name'
jsp.error.jspoutput.conflict=&lt;jsp:output&gt;: ilegal tener ocurrencias múltiples de [{0}] con diferentes valores (viejo: [{1}], nuevo: [{2}])
jsp.error.jspoutput.doctypenamesystem=&lt;jsp:output&gt;: atributos 'doctype-root-element' y 'doctype-system' deben de aparecer juntos
jsp.error.jspoutput.doctypepublicsystem=&lt;jsp:output&gt;: atributo 'doctype-system' debe de aparecer si aparece atributo 'doctype-public'
jsp.error.jspoutput.invalidUse=&lt;jsp:output&gt; no se debe de usar en sintáxis estándar
jsp.error.jspoutput.nonemptybody=&lt;jsp:output&gt; no debe de tener un cuerpo
jsp.error.jsproot.version.invalid=Número incorrecto de versión: [{0}], debe de ser "1.2" o "2.0" o "2.1" o "2.2" o "2.3"
jsp.error.jsptext.badcontent='&lt;', cuando aparece en el cuerpo de &lt;jsp:text&gt;, debe de estar encapsulado dentro de un CDATA
jsp.error.lastModified=No puedo determinar la última fecha de modificación para el fichero [{0}]
jsp.error.library.invalid=La página JSP es incorrecta de acuerdo a la biblioteca [{0}]: [{1}]
jsp.error.literal_with_void=Se especificó un valor literal para el atributo [{0}] que está definido como un método diferido con un tipo nulo de retorno. JSP.2.3.4 no permite valores de literal en este caso
jsp.error.loadclass.taghandler=No se puede cargar la clase [{0}]
jsp.error.location=línea: [{0}], columna: [{1}]
jsp.error.mandatory.attribute=[{0}]: Falta atributo obligatorio [{1}]
jsp.error.missing.tagInfo=El objeto TagInfo para [{0}] falta del TLD
jsp.error.missing_attribute=De acuerdo con el TLD el atributo [{0}] es obligatorio para el tag [{1}]
jsp.error.missing_var_or_varReader=Falta atributo 'var' o 'varReader'
jsp.error.namedAttribute.invalidUse=jsp:attribute debe de ser el subelemento de una acción estándar o de cliente
jsp.error.needAlternateJavaEncoding=La codificación java por defecto [{0}] es incorrecta en tu plataforma java. Se puede especificar una alternativa vía parámetro ''javaEncoding'' de JspServlet.
jsp.error.nested.jspattribute=Una acción estándar jsp:attribute no puede estar anidada dentro de otra acción estándar jsp:attribute
jsp.error.nested.jspbody=Una acción estándar jsp:body no puede estar anidada dentro de otra acción estándar jsp:body o jsp:attribute
jsp.error.nested_jsproot=&lt;jsp:root&gt; anidado
jsp.error.no.more.content=Alcanzado fin de contenido mietras se requería más análisis: ¿error de anidamiento de tag?
jsp.error.no.scratch.dir=El motor JSP no tiene configurado un directorio de trabajo.\n\
\ Añada "jsp.initparams=scratchdir=<dir-name>" \n\
\ en el fichero servlets.properties para este contexto.
jsp.error.no.scriptlets=Los elementos de Scripting (&lt;%!, &lt;jsp:declaration, &lt;%=, &lt;jsp:expression, &lt;%, &lt;jsp:scriptlet ) no están permitidos aquí.
jsp.error.noFunction=La función [{0}] no puede ser localizada mediante el prefijo especificado
jsp.error.noFunctionMethod=El método [{0}] para la función [{1}] no se pudo hallar en la clase [{2}]
jsp.error.non_null_tei_and_var_subelems=Tag [{0}] tiene uno o más subelementos variable y una clase TagExtraInfo que devuelve una o más VariableInfo
jsp.error.not.in.template=[{0}] no permitido en una plantilla cuerpo de texto.
jsp.error.outputfolder=No hay carpeta de salida
jsp.error.overflow=Error:Buffer de JSP desbordado
jsp.error.page.conflict.autoflush=Directiva Page: es ilegal tener múltiples ocurrencias de ''autoFlush'' con valores distintos (viejo: [{0}], nuevo: [{1}])
jsp.error.page.conflict.buffer=Directiva Page: es ilegal tener múltiples ocurrencias de ''buffer'' con valores distintos (viejo: [{0}], nuevo: [{1}])
jsp.error.page.conflict.contenttype=Directiva Page: es ilegal tener múltiples ocurrencias de ''contentType'' con valores distintos (viejo: [{0}], nuevo: [{1}])
jsp.error.page.conflict.deferredsyntaxallowedasliteral=Directiva de página: es ilegal tener múltiples ocurrencias de ''deferredSyntaxAllowedAsLiteral'' con diferentes valores (viejo: [{0}], nuevo: [{1}])
jsp.error.page.conflict.errorpage=Directiva Page: es ilegal tener múltiples ocurrencias de ''errorPage'' con valores distintos (viejo: [{0}], nuevo: [{1}])
jsp.error.page.conflict.extends=Directiva Page: es ilegal tener múltiples ocurrencias de ''extends'' con valores distintos (viejo: [{0}], nuevo: [{1}])
jsp.error.page.conflict.info=Directiva Page: es ilegal tener múltiples ocurrencias de ''info'' con valores distintos (viejo: [{0}], nuevo: [{1}])
jsp.error.page.conflict.iselignored=Directiva Page: es ilegal tener múltiples ocurrencias de ''isELIgnored'' con valores distintos (viejo: [{0}], nuevo: [{1}])
jsp.error.page.conflict.iserrorpage=Directiva Page: es ilegal tener múltiples ocurrencias de ''isErrorPage'' con valores distintos (viejo: [{0}], nuevo: [{1}])
jsp.error.page.conflict.isthreadsafe=Directiva Page: es ilegal tener múltiples ocurrencias de ''isThreadSafe'' con valores distintos (viejo: [{0}], nuevo: [{1}])
jsp.error.page.conflict.language=Directiva Page: es ilegal tener múltiples ocurrencias de ''language'' con valores distintos (viejo: [{0}], nuevo: [{1}])
jsp.error.page.conflict.session=Directiva Page: es ilegal tener múltiples ocurrencias de ''session'' con valores distintos (viejo: [{0}], nuevo: [{1}])
jsp.error.page.conflict.trimdirectivewhitespaces=Directiva de página: es ilegal tener múltiples ocurrencias de ''trimDirectivewhitespaces'' con diferentes valores (viejo: [{0}], nuevo: [{1}])
jsp.error.page.invalid.buffer=Directiva Page: valor incorrecto para búfer
jsp.error.page.invalid.deferredsyntaxallowedasliteral=Directiva de página: valor inválido para deferredSyntaxAllowedAsLiteral
jsp.error.page.invalid.import=Directiva de página: valor inválido para importar
jsp.error.page.invalid.iselignored=Directiva Page: valor inválido para isELIgnored
jsp.error.page.invalid.iserrorpage==Directiva Page: valor incorrecto para isErrorPage
jsp.error.page.invalid.isthreadsafe==Directiva Page: valor incorrecto para isThreadSafe
jsp.error.page.invalid.session=Directiva Page: valor incorrecto para session
jsp.error.page.invalid.trimdirectivewhitespaces=Directiva de página: valor inválido para trimDirectiveWhitespaces
jsp.error.page.language.nonjava=Directiva Page: atributo language incorrecto
jsp.error.page.multi.pageencoding=La directiva Page no debe de tener múltiples ocurrencias de pageencoding
jsp.error.page.noSession=No puedo acceder al ámbito de sesión en una página que no participa en una sesión
jsp.error.param.invalidUse=La acción jsp:param no debe de ser usada fuera de los elementos jsp:include, jsp:forward o jsp:params
jsp.error.paramexpected=El tag "param" era esperado con los atributos "name" y "value" después del tag "params".
jsp.error.params.emptyBody=jsp:params debe de contener al menos un jsp:param anidado
jsp.error.params.invalidUse=jsp:params debe de ser un hijo directo de jsp:plugin
jsp.error.parse.error.in.TLD=Error de análisis en el descriptor de biblioteca de tags: [{0}]
jsp.error.parse.xml=Error de análisis XML en archivo [{0}]
jsp.error.parse.xml.line=Error de análisis XML en archivo [{0}]: (línea [{1}], col [{2}])
jsp.error.parse.xml.scripting.invalid.body=El cuerpo de elemento [{0}] no debe de contener elementos XML
jsp.error.plugin.badtype=Valor ilegal para atributo 'type' en jsp:plugin: debe de ser 'bean' o 'applet'
jsp.error.plugin.nocode=Código no declarado en jsp:plugin
jsp.error.plugin.notype=Tipo no declarado en jsp:plugin
jsp.error.prefix.refined=Intento de redefinir el prefijo [{0}] por [{1}], cuando ya estaba definido como [{2}] en el ámbito en curso.
jsp.error.prefix.use_before_dcl=El prefijo [{0}] especificado en esta directiva de marca ha sido usado previamente mediante un fichero de acción [{1}] línea [{2}].
jsp.error.prolog_config_encoding_mismatch=El Page-encoding especificado en XML prolog [{0}] difiere del especificado en jsp-property-group [{1}]
jsp.error.prolog_pagedir_encoding_mismatch=El Page-encoding especificado en XML prolog [{0}] difiere del especificado en la directiva page [{1}]
jsp.error.quotes.unterminated=Comillas no terminadas
jsp.error.scripting.variable.missing_name=Imposible determinar nombre de variable de scripting desde atributo [{0}]
jsp.error.signature.classnotfound=La clase [{0}] especificada en la firma del método en el TLD para la función [{1}] no se puede hallar. [{2}]
jsp.error.simpletag.badbodycontent=El TLD para la clase [{0}] especifica un body-content es incorrecto (JSP) para un SimpleTag.
jsp.error.single.line.number=Ha tenido lugar un error en la línea: [{0}] en el archivo jsp: [{1}]
jsp.error.stream.close.failed=No pude cerrar el flujo
jsp.error.stream.closed=Stream cerrado
jsp.error.tag.conflict.attr=Directiva Tag: es ilegal tener múltiples ocurrencias del atributo [{0}] con valores distintos (viejo: [{1}], nuevo: [{2}])
jsp.error.tag.conflict.deferredsyntaxallowedasliteral=Directiva de marca: es ilegal tener múltiples ocurrencias de ''deferredSyntaxAllowedAsLiteral'' con diferentes valores (viejo: [{0}], nuevo: [{1}])
jsp.error.tag.conflict.iselignored=Directiva Tag: es ilegal tener múltiples ocurrencias de ''isELIgnored'' con valores distintos (viejo: [{0}], nuevo: [{1}])
jsp.error.tag.conflict.language=Directiva Tag: es ilegal tener múltiples ocurrencias de ''language'' con valores distintos (viejo: [{0}], nuevo: [{1}])
jsp.error.tag.conflict.trimdirectivewhitespaces=Directiva de marca: es ilegal tener múltiples ocurrencias de ''trimDirectivewhitespaces'' con diferentes valores (viejo: [{0}], nuevo: [{1}])
jsp.error.tag.invalid.deferredsyntaxallowedasliteral=Directiva de marca: valor inválido para deferredSyntaxAllowedAsLiteral
jsp.error.tag.invalid.iselignored=Directiva Tag: valor incorrecto para isELIgnored
jsp.error.tag.invalid.trimdirectivewhitespaces=Directiva de marca: valor inválido para trimDirectiveWhitespaces
jsp.error.tag.language.nonjava=Directiva Tag: atributo language incorrecto
jsp.error.tag.multi.pageencoding=La directiva Tag no debe de tener múltiples ocurrencias de pageencoding
jsp.error.tagdirective.badbodycontent=body-content incorrecto [{0}] en directiva tag
jsp.error.tagfile.badSuffix=Falta sufijo ".tag" en trayectoria de archivo de tag [{0}]
jsp.error.tagfile.illegalPath=Trayectoria de archivo de tag: [{0}], debe de comenzar con "/WEB-INF/tags" o "/META-INF/tags"
jsp.error.tagfile.nameFrom.badAttribute=La directiva attribute (declarada en la línea [{1}] y cuyo nombre de atributo es [{0}], el valor de este atributo name-from-attribute) debe de ser del tipo java.lang.String, es "requerido" y no "rtexprvalue".
jsp.error.tagfile.nameFrom.noAttribute=No puedo hallar una directiva attribute con un atributo name con un valor [{0}], el valor de este atributo name-from-attribute.
jsp.error.tagfile.nameNotUnique=El valor de [{0}] y el valor de [{1}] en la línea [{2}] son el mismo.
jsp.error.taglibDirective.absUriCannotBeResolved=La uri absoluta: [{0}] no puede resolverse o en web.xml o el los archivos jar desplegados con esta aplicación
jsp.error.taglibDirective.both_uri_and_tagdir=Se han especificado ambos atributos 'uri' y 'tagdir'
jsp.error.taglibDirective.missing.location=No se ha especificado ni el atributo 'uri' ni el 'tagdir'
jsp.error.taglibDirective.uriInvalid=La URI proveida por la etiqueta librería [{0}] no es una URI válida
jsp.error.tei.invalid.attributes=Mensajes de error de validación desde TagExtraInfo para [{0}]
jsp.error.teiclass.instantiation=No se puede cargar la clase TagExtraInfo llamada: [{0}]
jsp.error.text.has_subelement=&lt;jsp:text&gt; no debe de tener subelementos
jsp.error.tld.fn.duplicate.name=Nombre duplicado de función [{0}] en biblioteca de tag [{1}]
jsp.error.tld.fn.invalid.signature=Sintáxis incorrecta para firma de función en TLD. Biblioteca de Tag: [{0}], Función: [{1}]
jsp.error.tld.mandatory.element.missing=El elemento TLD [{0}] es obligatorio pero falta o está vacío en TLD [{1}]
jsp.error.tld.missing_jar=Falta recurso JAR [{0}] conteniendo TLD
jsp.error.tld.unable_to_get_jar=Imposible obtener recurso JAR [{0}] conteniendo TLD: [{1}]
jsp.error.tlv.invalid.page=Mensajes de error de validación desde TagLibraryValidator para [{0}] in [{1}]
jsp.error.tlvclass.instantiation=No pude cargar o instanciar clase TagLibraryValidator: [{0}]
jsp.error.unable.compile=No se puede compilar la clase para JSP
jsp.error.unable.load=No se puede cargar la clase para JSP
jsp.error.unable.to_find_method=No se puede encontrar el método de escritura para el atributo: [{0}]
jsp.error.unavailable=JSP ha sido marcado como no disponible
jsp.error.unbalanced.endtag=El tgag final "&lt;/{0}" está desequilibrado
jsp.error.undeclared_namespace=Se ha encontrado una etiqueta con espacio de nombre [{0}] sin declarar
jsp.error.unknown_attribute_type=Tipo de atributo desconocido [{1}] para atributo [{0}].
jsp.error.unsupported.encoding=Codificación no soportada: [{0}]
jsp.error.unterminated=Tag [{0}] no terminado
jsp.error.usebean.duplicate=useBean: Nombre de bean duplicado: [{0}]
jsp.error.usebean.noSession=Es ilegal para useBean el usar ámbito de sesión cuando la página JSP declara (vía directiva de página) que no participa en sesiones
jsp.error.var_and_varReader=Sólo se puede especificar uno de 'var' o 'varReader'
jsp.error.variable.alias=Ambos atributos o ninguno de name-from-attribute y alias pueden ser especificados en una directiva variable
jsp.error.variable.both.name=No se puede especificar ambos atributos name-given o name-from-attribute en una directiva variable
jsp.error.variable.either.name=O el atributo name-given o name-from-attribute deben de ser especificados en una directiva variable
jsp.error.xml.badStandardAction=Acción estándar incorrecta: [{0}]
jsp.error.xml.bad_tag=No se ha definido el tag [{0}] en la biblioteca tag asociada con uri [{1}]
jsp.error.xml.closeQuoteMissingInTextDecl=Faltan las comillas de cierre en el valor que sigue a [{0}] en la declaración text.
jsp.error.xml.closeQuoteMissingInXMLDecl=Faltan las comillas de cierre en el valor que sigue a  [{0}] en la declaración XML.
jsp.error.xml.encodingByteOrderUnsupported=El orden de byte dado para encoding [{0}] no está soportado
jsp.error.xml.encodingDeclInvalid=Nombre de codificación [{0}] incorrecto.
jsp.error.xml.encodingDeclRequired=Se necesita la declaración encoding en la declaración de texto
jsp.error.xml.eqRequiredInTextDecl=El carácter '' = '' debe de serguir a [{0}] en la declaración text.
jsp.error.xml.eqRequiredInXMLDecl=El carácter '' = '' debe de serguir a [{0}] en la declaración XML.
jsp.error.xml.expectedByte=Se esperaba byte [{0}] de [{1}]-byte de secuencia UTF-8.
jsp.error.xml.invalidASCII=El Byte [{0}] no es ASCII de 7-bit.
jsp.error.xml.invalidByte=Incorrecto byte [{0}] de [{1}]-byte de secuencia UTF-8.
jsp.error.xml.invalidCharInContent=Un carácter XML incorrecto (Unicode: 0x[{0}]) se halló en el contenido del elemento del documento.
jsp.error.xml.invalidCharInPI=Se halló un carácter XML incorrecto (Unicode: 0x[{0}]) en la instrucción de procesamiento
jsp.error.xml.invalidCharInTextDecl=Un carácter XML incorrecto (Unicode: 0x[{0}]) se halló en la declaración text
jsp.error.xml.invalidCharInXMLDecl=Un carácter XML incorrecto (Unicode: 0x[{0}]) se halló en la declaración XML
jsp.error.xml.invalidHighSurrogate=Los bits de surrogación alta en secuencai UTF-8 no deben de exceder 0x10 pero se halló 0x[{0}].
jsp.error.xml.morePseudoAttributes=se esperan más pseudo-atributos
jsp.error.xml.noMorePseudoAttributes=no se permiten más pseudo-atributos.
jsp.error.xml.operationNotSupported=La operación [{0}] no está soportada por lector [{1}].
jsp.error.xml.pseudoAttrNameExpected=se esperaba un pseudo-atributo name.
jsp.error.xml.quoteRequiredInTextDecl=El valor que sigue a [{0}] en la declaración text debe de ser una cadena entre comillas.
jsp.error.xml.quoteRequiredInXMLDecl=El valor que sigue a [{0}] en la declaración XML debe de ser un cadena entre comillas.
jsp.error.xml.reservedPITarget=La instrucción de procesamiento que coincide con "[xX][mM][lL]" no está permitida.
jsp.error.xml.sdDeclInvalid=El valor de declaración de documento standalone debe de ser "yes" o "no", no [{0}].
jsp.error.xml.spaceRequiredBeforeEncodingInTextDecl=Se necesita espacio en blanco antes del pseudo-atributo encoding en la declaración text.
jsp.error.xml.spaceRequiredBeforeEncodingInXMLDecl=Se necesita espacio en blanco antes del pseudo-atributo encoding en la declaración XML.
jsp.error.xml.spaceRequiredBeforeStandalone=Se necesita un espacio en blanco antes del pseudo-atributo encoding en la declaración XML.
jsp.error.xml.spaceRequiredBeforeVersionInTextDecl=Se necesita espacio en blanco antes del pseudo-atributo version en la declaración text.
jsp.error.xml.spaceRequiredBeforeVersionInXMLDecl=Se necesita espacio en blanco antes del pseudo-atributo version en la declaración XML.
jsp.error.xml.spaceRequiredInPI=Se necesita un espacio en blanco entre la instrucción de procesamiento y los datos.
jsp.error.xml.versionInfoRequired=Se requiere la versión en la declaración XML.
jsp.error.xml.versionNotSupported=No se soporta la versión XML [{0}], sólo se soporta XML 1.0
jsp.error.xml.xmlDeclUnterminated=La declaración XML debe de terminar con "?>".
jsp.exception=Ha sucedido una excepción al procesar la página JSP [{0}] en línea [{1}]
jsp.info.ignoreSetting=Valor de configuración ignorado para [{0}] de [{1}] debido a que SecurityManager estaba habilitado
jsp.message.dont.modify.servlets=IMPORTANTE: No modifique los servlets generados
jsp.message.jsp_added=Añadiendo JSP para ruta [{0}] a cola de contexto [{1}]
jsp.message.jsp_queue_created=Creada cola jsp con tamaño [{0}] para el contexto [{1}]
jsp.message.jsp_queue_update=Actuallizando JSP para ruta [{0}] en cola de contexto [{1}]
jsp.message.jsp_removed_excess=Quitando exceso de JSP para ruta [{0}] desde cola de contexto [{1}]
jsp.message.jsp_removed_idle=Quitando JSP ocioso para ruta [{0}] en contexto [{1}] tras [{2}] segundos");
jsp.message.jsp_unload_check=Revisando JSPs para descaga en contexto [{0}], contador JSP: [{1}] tamalo de cola: [{2}]
jsp.message.parent_class_loader_is=El cargador de clases es: [{0}]
jsp.message.scratch.dir.is=El directorio de trabajo para el motor JSP es: [{0}]
jsp.tldCache.noTldInJar=No se han hallado ficheros TLD en [{0}]. Considera añadir el JAR a la propiedad tomcat.util.scan.StandardJarScanFilter.jarsToSkip en el fichero  CATALINA_BASE/conf/catalina.propeperties.
jsp.tldCache.noTldSummary=Al menos un JAR, que se ha explorado buscando TLDs, aún no contenía TLDs. Activar historial de depuración para este historiador para una completa lista de los JARs que fueron explorados y de los que nos se halló TLDs. Saltarse JARs no necesarios durante la exploración puede dar lugar a una mejora de tiempo significativa en el arranque y compilación de JSP .
jsp.tldCache.tldInDir=Se encontraron archivos TLD en el directorio [{0}].\n
jsp.warning.bad.urlpattern.propertygroup=Valor malo [{0}] en el subelemento url-pattern en web.xml
jsp.warning.checkInterval=Aviso: valor incorrecto para el initParam checkInterval. Se usará el valor por defecto de "300" segundos
jsp.warning.classDebugInfo=Aviso: valor incorrecto para el initParam classdebuginfo. Se usará el valor por defecto de "false"
jsp.warning.compiler.classfile.delete.fail=No pude borrar el fichero generado de clase [{0}]
jsp.warning.compiler.classfile.delete.fail.unknown=No pude borrar los ficheros generados de clase
jsp.warning.compiler.javafile.delete.fail=No pude borrar el fichero generado de Java [{0}]
jsp.warning.development=Aviso: valor incorrecto para el initParam development. Se usará el valor por defecto de "true"
jsp.warning.displaySourceFragment=Aviso: valor incorrecto para el initParam displaySourceFragment. Se usará el valor por defecto de "verdadero"
jsp.warning.dumpSmap=Aviso: valor incorrecto para el initParam dumpSmap. Se usará el valor por defecto de "false"
jsp.warning.enablePooling=Aviso: valor incorrecto para initParam enablePooling. Se usará el valor por defecto de "true"
jsp.warning.fork=Aviso: valor incorrecto para el initParam fork. Se usará el valor por defecto de "true"
jsp.warning.genchararray=Aviso: valor incorrecto para el initParam genStringAsCharArray. Se usará el valor por defecto de "false"
jsp.warning.jspIdleTimeout=Aviso: Valor inválido para el initParam jspIdleTimeout. Usaré el valor por defecto de "-1"
jsp.warning.keepgen=Aviso: valor incorrecto para el initParam keepgen. Se usará el valor por defecto de "false"
jsp.warning.mappedFile=Aviso: valor incorrecto para el initParam mappedFile. Se usará el valor por defecto de "false"
jsp.warning.maxLoadedJsps=Aviso: Valor inválido para el initParam maxLoadedJsps. Usaré el valor por defecto de "-1"
jsp.warning.modificationTestInterval=Aviso: valor incorrecto para el initParam modificationTestInterval. Se usará el valor por defecto de "4" segundos
jsp.warning.noJarScanner=Aviso: No se ha puesto org.apache.tomcat.JarScanner en ServletContext. Volviendo a la implementación por defecto de JarScanner.
jsp.warning.recompileOnFail=Aviso: Valor inválido para el initParam recompileOnFail. Usaré el valor por defecto de "falso "false"
jsp.warning.suppressSmap=Aviso: valor incorrecto para el initParam suppressSmap. Se usará el valor por defecto de "false"
jsp.warning.xpoweredBy=Aviso: valor incorrecto para el initParam xpoweredBy. Se usará el valor por defecto de "false"

jspc.delete.fail=No pude borrar el fichero [{0}]
jspc.error.fileDoesNotExist=El archivo [{0}] utilizado como argumento no existe.
jspc.error.generalException=ERROR-el archivo [{0}] ha generado la excepción general siguiente:
jspc.implicit.uriRoot=uriRoot implicitamente puesto a [{0}]
jspc.usage=Uso: jspc <opciones> [--] <Archivos JSP>\n\
donde <Archivos JSP> son:\n\
\    -webapp <dir>         Un directorio conteniendo una web-app. Todas las\n\
\                          páginas jsp serán compiladas recursivamente\n\
o cualquier número de\n\
\    <Archivo>             Un Archivo para ser interpretado como una página jsp\n\
y donde <opciones> incluyen:\n\
\    -help                 Muestra este mensaje de ayuda\n\
\    -v                    Modo detallado\n\
\    -d <dir>              Directorio de salida\n\
\    -l                    Muestra el nombre de la página JSP al ocurrir un fallo\n\
\    -s                    Muestra el nombre de la página JSP al tener éxito\n\
\    -p <name>             Nombre del package objetivo\n\
\                          (por defecto org.apache.jsp)\n\
\    -c <name>             Nombre de la clase objetivo\n\
\                          (sólo se aplica a la primera página JSP)\n\
\    -mapped               Genera llamadas separadas a write() para cada línea de\n\
\                          HTML en el JSP\n\
\    -die[#]               Genera un código de retorno de error (#) en errores\n\
\                          fatales. (por defecto 1).\n\
\    -uribase <dir>        El directorio uri de donde deben de partir las\n\
\                          compilaciones. (por defecto "/")\n\
\    -uriroot <dir>        Igual que -webapp\n\
\    -compile              Compila los servlets generados\n\
\    -failFast             Stop on first compile error\n\
\    -webinc <file>        Crea unos mapeos parciales de servlet en el archivo\n\
\    -webxml <file>        Crea un web.xml completo en el archivo.\n\
\    -webxmlencoding <enc> Set the encoding charset used to read and write the web.xml\n\
\                          file (default is UTF-8)\n\
\    -addwebxmlmappings    Merge generated web.xml fragment into the web.xml file of the\n\
\                          web-app, whose JSP pages we are processing\n\
\    -ieplugin <clsid>     Java Plugin classid para Internet Explorer\n\
\    -classpath <path>     Pasa por alto la propiedad de sistema java.class.path\n\
\    -xpoweredBy           Añade cabecera de respuesta  X-Powered-By\n\
\    -trimSpaces           Remove template text that consists entirely of whitespace\n\
\    -javaEncoding <enc>   Set the encoding charset for Java classes (default UTF-8)\n\
\    -source <version>     Set the -source argument to the compiler (default 1.7)\n\
\    -target <version>     Set the -target argument to the compiler (default 1.7)\n
jspc.webfrg.footer=\n\
</web-fragment>\n\
\n
jspc.webfrg.header=<?xml version="1.0" encoding="{0}"?>\n\
<web-fragment xmlns="http://xmlns.jcp.org/xml/ns/javaee"\n\
\              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"\n\
\              xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee\n\
\                      http://xmlns.jcp.org/xml/ns/javaee/web-fragment_3_1.xsd"\n\
\              version="3.1"\n\
\              metadata-complete="true">\n\
\  <name>org_apache_jasper.jspc</name>\n\
\  <distributable/>\n\
<!--\n\
Creado automaticamente mediante Apache Tomcat JspC.\n\
-->\n\
\n
jspc.webinc.header=\n\
<!--\n\
Creado automaticamente mediante Apache Tomcat JspC.\n\
-->\n\
\n
jspc.webinc.insertEnd=<!-- Fin de mapeos de servlet JSPC -->
jspc.webinc.insertStart=<!-- Inicio de mapeos de servlet JSPC -->
jspc.webxml.footer=\n\
</web-app>\n\
\n
jspc.webxml.header=<?xml version="1.0" encoding="{0}"?>\n\
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"\n\
\         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"\n\
\         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee\n\
\                 http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd"\n\
\         version="3.1"\n\
\         metadata-complete="false">\n\
<!--\n\
Creado automaticamente mediante Apache Tomcat JspC.\n\
-->\n\
\n

org.apache.jasper.compiler.TldCache.servletContextNull=El contenido proporcionado para ServletContext tiene un valor nulo

xmlParser.skipBomFail=No pude saltar BOM al analizar flujo de entrada XML
