# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

sc.100=Continue
sc.101=Switching Protocols
sc.102=Processing
sc.200=OK
sc.201=Created
sc.202=Accepted
sc.203=Non-Authoritative Information
sc.204=No Content
sc.205=Reset Content
sc.206=Partial Content
sc.207=Multi-Status
sc.208=Already Reported
sc.226=IM Used
sc.300=Multiple Choices
sc.301=Moved Permanently
sc.302=Found
sc.303=See Other
sc.304=Not Modified
sc.305=Use Proxy
sc.307=Temporary Redirect
sc.308=Permanent Redirect
sc.400=Bad Request
sc.401=Unauthorized
sc.402=Payment Required
sc.403=Forbidden
sc.404=Not Found
sc.405=Method Not Allowed
sc.406=Not Acceptable
sc.407=Proxy Authentication Required
sc.408=Request Timeout
sc.409=Conflict
sc.410=Gone
sc.411=Length Required
sc.412=Precondition Failed
sc.413=Request Entity Too Large
sc.414=Request-URI Too Long
sc.415=Unsupported Media Type
sc.416=Requested Range Not Satisfiable
sc.417=Expectation Failed
sc.422=Unprocessable Entity
sc.423=Locked
sc.424=Failed Dependency
sc.426=Upgrade Required
sc.428=Precondition Required
sc.429=Too Many Requests
sc.431=Request Header Fields Too Large
sc.500=Internal Server Error
sc.501=Not Implemented
sc.502=Bad Gateway
sc.503=Service Unavailable
sc.504=Gateway Timeout
sc.505=HTTP Version Not Supported
sc.506=Variant Also Negotiates (Experimental)
sc.507=Insufficient Storage
sc.508=Loop Detected
sc.510=Not Extended
sc.511=Network Authentication Required
