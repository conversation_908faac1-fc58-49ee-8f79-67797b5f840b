# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

SecurityUtil.doAsPrivilege=Una excepción se ha producido durante la ejecución del bloque PrivilegedExceptionAction.

customObjectInputStream.logRequired=Se requiere un logeador váliso para el nombre de la clase filtrado con logeo
customObjectInputStream.nomatch=La clase  [{0}] no concuerda con la expresión regular [{1}] para clases que tienen permitido el deserializamiento

extensionValidator.extension-not-found-error=ExtensionValidator[{0}][{1}]: La extensión no encuentra el [{2}] requerido.
extensionValidator.extension-validation-error=ExtensionValidator[{0}]: Imposible de hallar la(s) extension(es) [{1}] requerida(s).
extensionValidator.failload=No pude cargar la extensión [{0}]
extensionValidator.web-application-manifest=Manifiesto de Aplicación Web

hexUtil.bad=Dígito hexadecimal incorrecto
hexUtil.odd=Número de dígitos hexadecimales impar

introspection.classLoadFailed=Fallo al cargar la clase [{0}]

lifecycleBase.initFail=Fallo al iniciar el componente [{0}]

netmask.cidrNegative=El CIDR [{0}] es negativo

parameterMap.locked=No se permiten modificaciones en un ParameterMap bloqueado

resourceSet.locked=No se permiten modificaciones en un ResourceSet bloqueado

sessionIdGeneratorBase.random=Excepción inicializando generador de números aleatorios de clase [{0}]
