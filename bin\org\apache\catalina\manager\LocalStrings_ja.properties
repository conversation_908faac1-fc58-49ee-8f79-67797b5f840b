# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

htmlManagerServlet.appsAvailable=実行中
htmlManagerServlet.appsExpire=期限切れセッション
htmlManagerServlet.appsName=表示名
htmlManagerServlet.appsPath=パス
htmlManagerServlet.appsReload=再ロード
htmlManagerServlet.appsSessions=セッション
htmlManagerServlet.appsStart=起動
htmlManagerServlet.appsStop=停止
htmlManagerServlet.appsTasks=コマンド
htmlManagerServlet.appsTitle=アプリケーション
htmlManagerServlet.appsUndeploy=配備解除
htmlManagerServlet.appsVersion=バージョン
htmlManagerServlet.configReloadButton=再読み込み
htmlManagerServlet.configSslHostName=TLSホスト名（オプション）
htmlManagerServlet.configSslReloadTitle=TLS構成ファイルを再読み込みします。
htmlManagerServlet.configTitle=構成ファイル
htmlManagerServlet.connectorStateAliveSocketCount=キープアライブソケット数：
htmlManagerServlet.connectorStateBytesReceived=受信バイト数：
htmlManagerServlet.connectorStateBytesSent=送信バイト：
htmlManagerServlet.connectorStateErrorCount=エラー数：
htmlManagerServlet.connectorStateHint=P：パースとリクエスト準備  S：サービス  F：終了  R：Ready  K：キープアライブ
htmlManagerServlet.connectorStateMaxProcessingTime=最大処理時間：
htmlManagerServlet.connectorStateMaxThreads=最大スレッド：
htmlManagerServlet.connectorStateProcessingTime=処理時間：
htmlManagerServlet.connectorStateRequestCount=リクエスト数：
htmlManagerServlet.connectorStateTableTitleBRecv=B Recv
htmlManagerServlet.connectorStateTableTitleBSent=送信バイト数
htmlManagerServlet.connectorStateTableTitleClientAct=Client (Actual)
htmlManagerServlet.connectorStateTableTitleClientForw=Client (Forwarded)
htmlManagerServlet.connectorStateTableTitleRequest=リクエスト
htmlManagerServlet.connectorStateTableTitleStage=ステージ
htmlManagerServlet.connectorStateTableTitleTime=時間
htmlManagerServlet.connectorStateTableTitleVHost=仮想ホスト
htmlManagerServlet.connectorStateThreadBusy=現在のBusyスレッド：
htmlManagerServlet.connectorStateThreadCount=現在のスレッド数：
htmlManagerServlet.deployButton=配備
htmlManagerServlet.deployConfig=XML設定ファイルのURL:
htmlManagerServlet.deployPath=コンテキストパス (省略可):
htmlManagerServlet.deployServer=サーバ上のWARファイル又はディレクトリの配備
htmlManagerServlet.deployTitle=配備
htmlManagerServlet.deployUpload=WARファイルの配備
htmlManagerServlet.deployUploadFail=FAIL - 配備のアップロードが失敗しました、例外: [{0}]
htmlManagerServlet.deployUploadFile=アップロードするWARファイルの選択
htmlManagerServlet.deployUploadInServerXml=FAIL  -  server.xmlでコンテキストが定義されている場合、warファイル[{0}]をアップロードできません
htmlManagerServlet.deployUploadNoFile=FAIL - ファイルのアップロードが失敗しました、ファイルが存在しません
htmlManagerServlet.deployUploadNotWar=FAIL - アップロードするファイル [{0}] はWARファイルでなければいけません
htmlManagerServlet.deployUploadWarExists=FAIL - WARファイル [{0}] は既にサーバ上に存在します
htmlManagerServlet.deployWar=WARファイル又はディレクトリのURL:
htmlManagerServlet.diagnosticsLeak=Web アプリケーションの停止や再読み込み、配備解除でメモリーリークが発生しているか確認します。
htmlManagerServlet.diagnosticsLeakButton=メモリリーク発見
htmlManagerServlet.diagnosticsLeakWarning=この診断チェックにより、Full GCがトリガーされます。本番システムには非常に注意して使用してください。
htmlManagerServlet.diagnosticsSsl=TLSコネクタ構成の診断
htmlManagerServlet.diagnosticsSslConnectorCertsButton=証明書
htmlManagerServlet.diagnosticsSslConnectorCertsText=設定済みのTLS仮想ホストとそれぞれの証明書チェーンを一覧表示します。
htmlManagerServlet.diagnosticsSslConnectorCipherButton=暗号
htmlManagerServlet.diagnosticsSslConnectorCipherText=設定済みのTLS仮想ホストとそれぞれの暗号を一覧表示します。
htmlManagerServlet.diagnosticsSslConnectorTrustedCertsButton=信頼できる証明書
htmlManagerServlet.diagnosticsSslConnectorTrustedCertsText=設定済みのTLS仮想ホストとそれぞれの信頼できる証明書を一覧表示します。
htmlManagerServlet.diagnosticsTitle=診断
htmlManagerServlet.expire.explain=非アクティブ　&ge;
htmlManagerServlet.expire.unit=分
htmlManagerServlet.findleaksList=次のWebアプリケーションは停止しました（再ロード、アンデプロイ）。\n\
以前の起動からのクラスはまだメモリにロードされているため、メモリ\n\
リークを引き起こします。（確認のためにプロファイラを使用して下さい）：
htmlManagerServlet.findleaksNone=停止、再読み込み、配備解除によりメモリーリークの発生した Web アプリケーションはありません。
htmlManagerServlet.helpHtmlManager=HTMLマネージャヘルプ
htmlManagerServlet.helpHtmlManagerFile=../docs/html-manager-howto.html
htmlManagerServlet.helpManager=マネージャヘルプ
htmlManagerServlet.helpManagerFile=../docs/manager-howto.html
htmlManagerServlet.jvmFreeMemory=フリーメモリ：
htmlManagerServlet.jvmMaxMemory=最大メモリ：
htmlManagerServlet.jvmTableTitleInitial=初期
htmlManagerServlet.jvmTableTitleMaximum=最大値
htmlManagerServlet.jvmTableTitleMemoryPool=メモリプール
htmlManagerServlet.jvmTableTitleTotal=合計
htmlManagerServlet.jvmTableTitleType=Type
htmlManagerServlet.jvmTableTitleUsed=使用中
htmlManagerServlet.jvmTotalMemory=合計メモリ：
htmlManagerServlet.list=アプリケーションの一覧
htmlManagerServlet.manager=マネージャ
htmlManagerServlet.messageLabel=メッセージ
htmlManagerServlet.noManager=-
htmlManagerServlet.noVersion=指定なし
htmlManagerServlet.osAvailableMemory=利用可能なメモリ
htmlManagerServlet.osFreePageFile=フリーページファイル：
htmlManagerServlet.osKernelTime=プロセスのカーネル時間：
htmlManagerServlet.osMemoryLoad=メモリロード：
htmlManagerServlet.osPhysicalMemory=物理メモリ：
htmlManagerServlet.osTotalPageFile=合計ページファイル：
htmlManagerServlet.osUserTime=プロセスユーザ名：
htmlManagerServlet.serverHostname=ホスト名
htmlManagerServlet.serverIPAddress=IP アドレス
htmlManagerServlet.serverJVMVendor=JVMベンダ
htmlManagerServlet.serverJVMVersion=JVMバージョン
htmlManagerServlet.serverOSArch=OSアーキテクチャ
htmlManagerServlet.serverOSName=OS名
htmlManagerServlet.serverOSVersion=OSバージョン
htmlManagerServlet.serverTitle=サーバ情報
htmlManagerServlet.serverVersion=Tomcatバージョン
htmlManagerServlet.title=Tomcat Webアプリケーションマネージャ

jmxProxyServlet.noBeanFound=オブジェクト名 [{0}] の MBean が見つかりません
jmxProxyServlet.noOperationOnBean=クラス名 [{3}]、オブジェクト名 [{2}] の MBean で引数が [{1}] 個の操作 [{0}] が見つかりません

managerServlet.alreadyContext=FAIL - アプリケーションは、既にパス [{0}] に存在します
managerServlet.certsNotAvailable=実行時にこのコネクタから証明書情報を取得できません。
managerServlet.deleteFail=FAIL  -  [{0}]を削除できません。 このファイルが継続して存在すると、問題が発生する可能性があります。
managerServlet.deployFailed=FAIL - コンテキストパス [{0}] にアプリケーションを配備できません。
managerServlet.deployed=OK - コンテキストパス [{0}] でアプリケーションを配備しました
managerServlet.deployedButNotStarted=FAIL  - コンテキストパス[{0}]にアプリケーションを配備しましたが、コンテキストを開始できませんでした。
managerServlet.exception=FAIL - 例外 [{0}] が発生しました
managerServlet.findleaksFail=FAIL - リークを検出できません。Hostは StandardHost のインスタンスめはありません。
managerServlet.findleaksList=OK - 次のアプリケーションで潜在的なメモリリークが見つかりました：
managerServlet.findleaksNone=OK - メモリリークは見つかりませんでした
managerServlet.inService=FAIL - アプリケーション [{0}] はすでにサービスを開始しています。
managerServlet.invalidCommand=FAIL  - コマンド[{0}]に無効なパラメータが指定されました
managerServlet.invalidPath=FAIL - 無効なコンテキストパス [{0}] が指定されました
managerServlet.listed=OK - バーチャルホスト [{0}] のアプリケーション一覧です
managerServlet.mkdirFail=FAIL - ディレクトリ [{0}] は作成できません。
managerServlet.noCommand=FAIL - コマンドが指定されていません
managerServlet.noContext=FAIL - パス [{0}] のコンテキストが存在しません
managerServlet.noGlobal=FAIL - グローバルなJNDIリソースが利用できません
managerServlet.noManager=FAIL  - パス[{0}]のマネージャが存在しません
managerServlet.noSelf=FAIL - マネージャ自身を再ロード、削除、停止、又は配備解除できません
managerServlet.noWrapper=コンテナはこのサーブレットに対して呼び出されたsetWrapper()を持っていません
managerServlet.notDeployed=FAIL  - Context[{0}]はserver.xmlで定義されており、展開されていない可能性があります。
managerServlet.notSslConnector=このコネクターでは SSL が有効化されていません。
managerServlet.objectNameFail=FAIL - オブジェクト名 [{0}] を ManagerServlet として登録できません。
managerServlet.postCommand=FAIL - コマンド [{0}] を GET リクエストで実行しようとしましたが、POST リクエストでなければなりません。
managerServlet.reloaded=OK - コンテキストパス [{0}] のアプリケーションを再ロードしました
managerServlet.renameFail=FAIL  -  [{0}]の名前を[{1}]に変更できません。 これにより、今後の展開で問題が発生する可能性があります。
managerServlet.resourcesAll=OK - すべてのタイプのグローバルリソースを列挙しました
managerServlet.resourcesType=OK - タイプ [{0}] のグローバルリソースを列挙しました
managerServlet.saveFail=FAIL - 設定の保存に失敗しました: [{0}]
managerServlet.saved=OK - サーバの設定を保存しました
managerServlet.savedContext=OK - コンテキスト [{0}] の設定を保存しました
managerServlet.savedContextFail=FAIL - コンテキスト [{0}] の構成を保存できませんでした
managerServlet.serverInfo=OK - サーバー情報\n\
Tomcatバージョン：[{0}]\n\
OS名：[{1}]\n\
OSバージョン：[{2}]\n\
OSアーキテクチャ：[{3}]\n\
JVMバージョン：[{4}]\n\
JVMベンダー：[{5}]
managerServlet.sessiondefaultmax=既定の最大セッション非活性時間は[{0}]分です
managerServlet.sessions=OK - コンテキストパス [{0}] のアプリケーションのセッション情報です
managerServlet.sessiontimeout=[{0}]分: [{1}]セッション
managerServlet.sessiontimeout.expired=[{0}]分: expired [{1}]セッション
managerServlet.sessiontimeout.unlimited=unlimited 分: [{0}]セッション
managerServlet.sslConnectorCerts=OK - コネクタ/証明書チェーンの情報
managerServlet.sslConnectorCiphers=OK - Connector/ SSL暗号情報
managerServlet.sslConnectorTrustedCerts=OK - コネクタ/信頼された証明書情報
managerServlet.sslReload=OK - [{0}]のTLS構成をリロードしました
managerServlet.sslReloadAll=OK - すべてのTLS仮想ホストのTLS構成をリロードしました
managerServlet.sslReloadFail=FAIL  -  TLS設定を再ロードできませんでした
managerServlet.startFailed=FAIL - コンテキストパス [{0}] のアプリケーションが起動できません
managerServlet.started=OK - コンテキストパス [{0}] でアプリケーションを起動しました
managerServlet.stopped=OK - コンテキストパス [{0}] でアプリケーションを停止しました
managerServlet.storeConfig.noMBean=FAIL - [{0}] に StoreConfig Mbean が登録されていません。一般的には StoreConfigLifecycleListener が登録します。
managerServlet.threaddump=OK - JVMスレッドダンプ
managerServlet.trustedCertsNotConfigured=この仮想ホストには信頼できる証明書が構成されていません。
managerServlet.undeployed=OK - コンテキストパス [{0}] のアプリケーションを配備解除しました
managerServlet.unknownCommand=FAIL - 未知のコマンド [{0}] です
managerServlet.vminfo=OK - VM 情報

statusServlet.complete=サーバの全状態
statusServlet.title=サーバの状態
