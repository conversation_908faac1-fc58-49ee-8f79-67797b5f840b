# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

JDBCStore.SQLException=SQL Error [{0}]
JDBCStore.checkConnectionClassNotFoundException=JDBC driver class not found [{0}]
JDBCStore.checkConnectionDBClosed=The database connection is null or was found to be closed. Trying to re-open it.
JDBCStore.checkConnectionDBReOpenFail=The re-open on the database failed. The database could be down.
JDBCStore.checkConnectionSQLException=A SQL exception occurred [{0}]
JDBCStore.close=Exception closing database connection [{0}]
JDBCStore.commitSQLException=SQLException committing connection before closing
JDBCStore.connectError=Cannot connect to database [{0}]
JDBCStore.loading=Loading Session [{0}] from database [{1}]
JDBCStore.missingDataSource=No data source available
JDBCStore.missingDataSourceName=No valid JNDI name was given.
JDBCStore.removing=Removing Session [{0}] at database [{1}]
JDBCStore.saving=Saving Session [{0}] to database [{1}]
JDBCStore.wrongDataSource=Cannot open JNDI DataSource [{0}]

fileStore.createFailed=Unable to create directory [{0}] for the storage of session data
fileStore.deleteFailed=Unable to delete file [{0}] which is preventing the creation of the session storage location
fileStore.deleteSessionFailed=Unable to delete file [{0}] which is no longer required
fileStore.invalid=Invalid persistence file [{0}] for session ID [{1}]
fileStore.loading=Loading Session [{0}] from file [{1}]
fileStore.removing=Removing Session [{0}] at file [{1}]
fileStore.saving=Saving Session [{0}] to file [{1}]

managerBase.container.noop=Managers added to containers other than Contexts will never be used
managerBase.contextNull=The Context must be set to a non-null value before the Manager is used
managerBase.createSession.ise=createSession: Too many active sessions
managerBase.sessionAttributeNameFilter=Skipped session attribute named [{0}] because it did not match the name filter [{1}]
managerBase.sessionAttributeValueClassNameFilter=Skipped session attribute named [{0}] because the value type [{1}] did not match the filter [{2}]
managerBase.sessionNotFound=The session [{0}] was not found
managerBase.sessionTimeout=Invalid session timeout setting [{0}]
managerBase.setContextNotNew=It is illegal to call setContext() to change the Context associated with a Manager if the Manager is not in the NEW state

persistentManager.backupMaxIdle=Backing up session [{0}] to Store, idle for [{1}] seconds
persistentManager.deserializeError=Error deserializing Session [{0}]
persistentManager.isLoadedError=Error checking if session [{0}] is loaded in memory
persistentManager.loading=Loading [{0}] persisted sessions
persistentManager.removeError=Error removing session [{0}] from the store
persistentManager.serializeError=Error serializing Session [{0}]: [{1}]
persistentManager.storeClearError=Error clearning all sessions from the store
persistentManager.storeKeysException=Unable to determine the list of session IDs for sessions in the session store, assuming that the store is empty
persistentManager.storeLoadError=Error swapping in sessions from the store
persistentManager.storeLoadKeysError=Error loading sessions keys from the store
persistentManager.storeSizeException=Unable to determine the number of sessions in the session store, assuming that the store is empty
persistentManager.swapIn=Swapping session [{0}] in from Store
persistentManager.swapInException=Exception in the Store during swapIn: [{0}]
persistentManager.swapInInvalid=Swapped session [{0}] is invalid
persistentManager.swapMaxIdle=Swapping session [{0}] to Store, idle for [{1}] seconds
persistentManager.swapTooManyActive=Swapping out session [{0}], idle for [{1}] seconds too many sessions active
persistentManager.tooManyActive=Too many active sessions, [{0}], looking for idle sessions to swap out
persistentManager.unloading=Saving [{0}] persisted sessions

standardManager.deletePersistedFileFail=Unable to delete [{0}] after reading the persisted sessions. The continued presence of this file may cause future attempts to persist sessions to fail.
standardManager.loading=Loading persisted sessions from [{0}]
standardManager.loading.exception=Exception while loading persisted sessions
standardManager.managerLoad=Exception loading sessions from persistent storage
standardManager.managerUnload=Exception unloading sessions to persistent storage
standardManager.unloading=Saving persisted sessions to [{0}]
standardManager.unloading.debug=Unloading persisted sessions
standardManager.unloading.nosessions=No persisted sessions to unload

standardSession.attributeEvent=Session attribute event listener threw exception
standardSession.bindingEvent=Session binding event listener threw exception
standardSession.getAttribute.ise=getAttribute: Session already invalidated
standardSession.getAttributeNames.ise=getAttributeNames: Session already invalidated
standardSession.getCreationTime.ise=getCreationTime: Session already invalidated
standardSession.getIdleTime.ise=getIdleTime: Session already invalidated
standardSession.getLastAccessedTime.ise=getLastAccessedTime: Session already invalidated
standardSession.getThisAccessedTime.ise=getThisAccessedTime: Session already invalidated
standardSession.getValueNames.ise=getValueNames: Session already invalidated
standardSession.invalidate.ise=invalidate: Session already invalidated
standardSession.isNew.ise=isNew: Session already invalidated
standardSession.logoutfail=Exception logging out user when expiring session
standardSession.notDeserializable=Cannot deserialize session attribute [{0}] for session [{1}]
standardSession.notSerializable=Cannot serialize session attribute [{0}] for session [{1}]
standardSession.principalNotDeserializable=Cannot deserialize Principal object for session [{0}]
standardSession.principalNotSerializable=Cannot serialize Principal object for session [{0}]
standardSession.removeAttribute.ise=removeAttribute: Session already invalidated
standardSession.sessionEvent=Session event listener threw exception
standardSession.setAttribute.iae=setAttribute: Non-serializable attribute [{0}]
standardSession.setAttribute.ise=setAttribute: Session [{0}] has already been invalidated
standardSession.setAttribute.namenull=setAttribute: name parameter cannot be null
