# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

ReplicationValve.crossContext.add=クロスコンテキストセッションレプリケーションコンテナをreplicationValveスレッドローカルに追加
ReplicationValve.crossContext.registerSession=コンテキスト[{1}]からクロスコンテキストセッションID = [{0}]を登録する
ReplicationValve.crossContext.remove=replication Contextセッションレプリケーションコンテナをスレッドローカルから削除します。
ReplicationValve.crossContext.sendDelta=コンテキスト[{0}]からのクロスコンテキストセッションデルタを送信します。
ReplicationValve.filter.failure=フィルター文字列=[{0}] がコンパイルできません。
ReplicationValve.filter.loading=リクエストフィルタ= [{0}]のロード
ReplicationValve.invoke.uri=[{0}]のレプリケーションリクエストを呼び出します。
ReplicationValve.nocluster=このリクエストに対して構成されたクラスタはありません。
ReplicationValve.resetDeltaRequest=クラスタはスタンドアロンである：コンテキスト[{0}]でセッションのデルタリクエストをリセットします。
ReplicationValve.send.failure=レプリケーションリクエストを実行できません。
ReplicationValve.send.invalid.failure=セッション[id = {0}]無効メッセージをクラスタに送信できません。
ReplicationValve.session.found=コンテキスト [{0}]: セッション [{1}] は ClusterSession ではありません。
ReplicationValve.session.indicator=Context [{0}]：リクエスト属性[{2}]のセッション[{1}]のプライマリは[{3}]です。
ReplicationValve.session.invalid=コンテキスト [{0}]: 不正なセッション [{1}] が要求されました。消去された、あるいは、このノードに複製されなかった可能性があります。
ReplicationValve.stats=[{2}]リクエストの平均要求時間= [{0}] ms、クラスタオーバーヘッド時間= [{1}] ms、[{3}]リクエストの送信、[{4}]クロスコンテキストリクエスト、[{5} }]フィルタリクエスト（合計リクエスト= [{6}] ms、クラスタ全体リクエスト= [{7}] ms）。

simpleTcpCluster.clustermanager.cloneFailed=クラスタマネージャをクローンできません。既定はorg.apache.catalina.ha.session.DeltaManagerです。
simpleTcpCluster.clustermanager.notImplement=クラス [{0}] は ClusterManager を実装していません。それにクラスターはすでに停止しています。
simpleTcpCluster.member.addFailed=レプリケーションシステムに接続できません。
simpleTcpCluster.member.added=レプリケーションメンバーを追加しました: [{0}]
simpleTcpCluster.member.disappeared=メッセージ消失を受信しました: [{0}]
simpleTcpCluster.member.removeFailed=レプリケーションシステムからクラスターノードを削除できませんでした。
simpleTcpCluster.sendFailed=クラスタセンダ経由でメッセージを送信できませんでした。
simpleTcpCluster.start=Clusterを起動します。
simpleTcpCluster.startUnable=クラスタを起動出来ません。
simpleTcpCluster.stopUnable=クラスタを停止できません。
simpleTcpCluster.unableSend.localMember=ローカルメンバー [{0}] にメッセージを送信できません。
