# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

coyoteAdapter.accesslogFail=Excepción al intentar añadir una entrada al historial de acceso
coyoteAdapter.checkRecycled.response=Se encontró una respuesta no reciclable y se crecicló a la fuerza
coyoteAdapter.debug=La variable [{0}] tiene el valor [{1}]

coyoteConnector.invalidEncoding=La codificación  [{0}]  no esta reconocida por  JRE. El conector continuará usando [{1}]\n
coyoteConnector.invalidPort=El conector no puede inciar debido a que el valor del puerto especificado [{0}] no es válido
coyoteConnector.parseBodyMethodNoTrace=El método TRACE NO DEBE DE incluir una entidad (vea RFC 2616 Sección 9.6)
coyoteConnector.protocolHandlerDestroyFailed=Falló la destrucción del manejador de protocolo
coyoteConnector.protocolHandlerInitializationFailed=Falló la inicialización del manejador de protocolo
coyoteConnector.protocolHandlerInstantiationFailed=Falló la instanciación del manejador de protocolo
coyoteConnector.protocolHandlerPauseFailed=Ha fallado la pausa del manejador de protocolo
coyoteConnector.protocolHandlerResumeFailed=Ha fallado el rearranque del manejador de protocolo
coyoteConnector.protocolHandlerStartFailed=Falló el arranque del manejador de protocolo
coyoteConnector.protocolHandlerStopFailed=Ocurrió un fallo al detener el manejador del protocolo

coyoteInputStream.nbNotready=En modo non-blocking usted no puede leer desde ServletInputStream hasta que la lectura previa haya sido completada y  isReady() devuelva verdadero

coyoteRequest.alreadyAuthenticated=Este requerimiento ya ha sido autenticado
coyoteRequest.attributeEvent=Excepción lanzada mediante el escuchador de eventos de atributos
coyoteRequest.authenticate.ise=No puedo llamar a authenticate() tras haberse acometido la respuesta
coyoteRequest.changeSessionId=No se puede cambiar el ID de sesión. No hay sesión asociada con esta solicitud
coyoteRequest.chunkedPostTooLarge=No se han analizado los parámetros porque la medida de los datos enviados meiante "post" era demasiado grande. Debido a que este requerimiento es una parte del original, no puede ser procesado. Utiliza el atributo "maxPostSize" del conector para resolver esta situación, en caso de que la aplicación deba de aceptar POSTs mayores.
coyoteRequest.filterAsyncSupportUnknown=Imposible determinar si algún filtro no soporta procesamiento asincrónico
coyoteRequest.getInputStream.ise=getReader() ya ha sido llamado para este requerimiento
coyoteRequest.getReader.ise=getInputStream() ya ha sido llamado para este requerimiento
coyoteRequest.gssLifetimeFail=Fallo al obtener el tiempo de vida restante para el usuario principal [{0}]\n
coyoteRequest.noMultipartConfig=Imposible procesar partes debido a que se ha proveído una configuración no multipartes
coyoteRequest.parseParameters=Excepción lanzada al procesar parámetros POST
coyoteRequest.postTooLarge=No se analizaron los parámetros porque la medida de los datos enviados era demasiado grande. Usa el atributo maxPostSize del conector para resolver esto en caso de que la aplicación debiera de aceptar POSTs más grandes.
coyoteRequest.sendfileNotCanonical=Incapaz de determinar el nombre canónico del archivo  [{0}] especificado para ser usado con sendfile
coyoteRequest.sessionCreateCommitted=No puedo crear una sesión después de llevar a cabo la respueta
coyoteRequest.sessionEndAccessFail=Excepción disparada acabando acceso a sesión mientras se reciclaba el requerimiento
coyoteRequest.setAttribute.namenull=No pudeo llamar a setAttribute con un nombre nulo
coyoteRequest.uploadLocationInvalid=No es válida la localización [{0}] de carga temporal

coyoteResponse.getOutputStream.ise=getWriter() ya ha sido llamado para esta respuesta
coyoteResponse.getWriter.ise=getOutputStream() ya ha sido llamado para esta respuesta
coyoteResponse.resetBuffer.ise=No puedo limpiar el búfer después de que la respuesta ha sido llevada a cabo
coyoteResponse.sendError.ise=No puedo llamar a sendError() tras llevar a cabo la respuesta
coyoteResponse.sendRedirect.ise=No puedo llamar a sendRedirect() tras llevar a cabo la respuesta
coyoteResponse.setBufferSize.ise=No puedo cambiar la medida del búfer tras escribir los datos

inputBuffer.streamClosed=Flujo cerrado

requestFacade.nullRequest=El objeto de requerimiento ha sido reciclado y ya no está asociado con esta fachada

responseFacade.nullResponse=El objeto de respuesta ha sido reciclado y ya no está asociado con esta fachada
