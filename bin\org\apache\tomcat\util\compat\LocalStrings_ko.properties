# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jre9Compat.invalidModuleUri=[{0}](으)로 제공된 모듈 URI는 JarScanner가 처리할 수 있는 URL로 변환될 수 없습니다.
jre9Compat.javaPre9=Java 9 클래스가 발견되지 않습니다. Java 9 이전 버전에서 동작하고 있는 것으로 보입니다.
jre9Compat.unexpected=Java 9 클래스들과 메소드들을 참조할 수 없습니다.

jreCompat.noApplicationProtocol=자바 런타임이 SSLEngine.getApplicationProtocol()을 지원하지 않습니다. 이 기능을 사용하려면 Java 9를 사용해야 합니다.
jreCompat.noApplicationProtocols=자바 런타임이 SSLParameters.setApplicationProtocols()을 지원하지 않습니다. 이 기능을 사용하려면 Java 9를 사용해야 합니다.
