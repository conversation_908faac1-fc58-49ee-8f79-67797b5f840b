# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

memoryUserDatabase.fileClose=Failed to close [{0}]
memoryUserDatabase.fileDelete=Failed to delete [{0}]
memoryUserDatabase.fileNotFound=The specified user database [{0}] could not be found
memoryUserDatabase.notPersistable=User database is not persistable - no write permissions on directory
memoryUserDatabase.nullGroup=Null or zero length group name specified. The group will be ignored.
memoryUserDatabase.nullRole=Null or zero length role name specified. The role will be ignored.
memoryUserDatabase.nullUser=Null or zero length user name specified. The user will be ignored.
memoryUserDatabase.readOnly=User database has been configured to be read only. Changes cannot be saved
memoryUserDatabase.reload=Reloading memory user database [{0}] from updated source [{1}]
memoryUserDatabase.reloadError=Error reloading memory user database [{0}] from updated source [{1}]
memoryUserDatabase.renameNew=Cannot rename new file to [{0}]
memoryUserDatabase.renameOld=Cannot rename original file to [{0}]
memoryUserDatabase.restoreOrig=Cannot restore [{0}] to original file
memoryUserDatabase.writeException=IOException writing to [{0}]
memoryUserDatabase.xmlFeatureEncoding=Exception configuring digester to permit java encoding names in XML files. Only IANA encoding names will be supported.
