# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

diagnostics.threadDumpTitle=完全なスレッドダンプ
diagnostics.vmInfoClassCompilation=Class コンパイル
diagnostics.vmInfoClassLoading=クラスローディング
diagnostics.vmInfoGarbageCollectors=ガベージコレクタ [{0}]
diagnostics.vmInfoLogger=Logger 情報
diagnostics.vmInfoMemory=メモリ情報
diagnostics.vmInfoMemoryManagers=メモリマネージャ[{0}]
diagnostics.vmInfoMemoryPools=メモリプール [{0}]
diagnostics.vmInfoOs=OS情報
diagnostics.vmInfoPath=パス情報
diagnostics.vmInfoRuntime=Runtime 情報
diagnostics.vmInfoStartup=起動引数
diagnostics.vmInfoSystem=システムプロパティ
diagnostics.vmInfoThreadCounts=スレッドカウント
diagnostics.vmInfoThreadMxBean=ThreadMXBeanの機能
