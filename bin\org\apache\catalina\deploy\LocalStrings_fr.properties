# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

namingResources.cleanupCloseFailed=Impossible d''invoquer la méthode [{0}] de la ressource [{1}] dans le conteneur [{2}] donc aucun nettoyage n''a été effectué pour cette ressource
namingResources.cleanupCloseSecurity=Incapacité de récupérer la méthode [{0}] pour la resource [{1}] dans le conteneur [{2}]. Aucun nettoyage effectué pour cette resource.
namingResources.cleanupNoClose=La ressource [{0}] du container [{1}] n''a pas de [{2}] méthode donc aucun nettoyage de la ressource n''a pu être effectué
namingResources.cleanupNoContext=Impossible de récupérer le contexte de nommage JNDI dans le conteneur [{0}] donc aucun nettoyage de ce conteneur n''a pu être effectué
namingResources.cleanupNoResource=Impossible de récupérer la ressource JNDI [{1}] dans le conteneur [{2}] donc aucun nettoyage de la ressource n''a pu être effectué
namingResources.ejbLookupLink=La référence à un EJB [{0}] spéficie à la fois un ejb-link and et un lookup-name
namingResources.envEntryLookupValue=L''entrée d''environnement [{0}] spécifie à la fois un lookup-name et une valeur
namingResources.mbeanCreateFail=Échec de création d''un MBean pour la resource nommée ("naming resource") [{0}]
namingResources.mbeanDestroyFail=Echec de destruction du mbean de la ressource [{0}]
namingResources.resourceTypeFail=La ressource JNDI nommée [{0}] est de type [{1}] mais ce type est inconsistant avec le(s) type(s) de cible d''injection configuré(s) pour cette ressource
