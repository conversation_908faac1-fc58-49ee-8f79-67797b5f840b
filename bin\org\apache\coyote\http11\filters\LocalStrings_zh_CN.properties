# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

chunkedInputFilter.eos=读取请求主体时流的意外结束
chunkedInputFilter.eosTrailer=读取 trailer 头时出现意外的流结束
chunkedInputFilter.error=没有数据可用由于先前的错误
chunkedInputFilter.invalidCrlf=行尾序列无效（找到除CR或LF以外的字符）
chunkedInputFilter.invalidCrlfCRCR=无效的结束的行序列（CRCR)
chunkedInputFilter.invalidCrlfNoCR=无效的行尾结束符(换行前缺少回车)
chunkedInputFilter.invalidCrlfNoData=无效的行尾序列（没有可读取的数据）
chunkedInputFilter.invalidHeader=无效的块
chunkedInputFilter.maxExtension=超过最大扩展数
chunkedInputFilter.maxTrailer=超过最大数

inputFilter.maxSwallow=最大吞咽数据大小超出异常
