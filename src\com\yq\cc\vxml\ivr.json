{"ivrCode": "vxml01", "servicePhoneId": ["83025636802081594212506"], "bakup": "", "ivrName": "vxml测试流程", "ivrType": "1", "lineList": [{"lx_tj": "eq", "ifSelect": false, "expression": {"param": "SERVICE_TASK_RESULT", "bakup": "", "val1": "1", "conditions": "eq"}, "line": {"ifSelect": false}, "lx_disable": false, "lx_cs": "SERVICE_TASK_RESULT", "label": "用户输入1", "lx_fz": "1", "lx_ms": "", "isqj": false, "from": "SERVICETASK_sphixznd8m", "to": "USERTASK_cz2qayk8o5", "id": "LINE_y433jd2izg"}, {"lx_tj": "neq", "ifSelect": false, "expression": {"param": "SERVICE_TASK_RESULT", "bakup": "", "val1": "1", "conditions": "neq"}, "line": {"ifSelect": false}, "lx_disable": false, "lx_cs": "SERVICE_TASK_RESULT", "label": "用户输入其他", "lx_fz": "1", "lx_ms": "", "isqj": false, "from": "SERVICETASK_sphixznd8m", "to": "USERTASK_33wwad1far", "id": "LINE_1iec9hyrug"}, {"line": {"ifSelect": false}, "from": "USERTASK_cz2qayk8o5", "to": "END_6b84mxn1hq", "id": "LINE_bekcgb524f"}, {"line": {"ifSelect": false}, "from": "START_779nicddqt", "to": "USERTASK_l19jasa6r", "id": "LINE_yodwaztnuq"}, {"line": {"ifSelect": false}, "from": "USERTASK_l19jasa6r", "to": "USERTASK_8fl0k0gy2n", "id": "LINE_9p0xpwx0u1"}, {"line": {"ifSelect": false}, "from": "USERTASK_8fl0k0gy2n", "to": "SERVICETASK_sphixznd8m", "id": "LINE_ejzshr8sxg"}, {"line": {"ifSelect": false}, "from": "USERTASK_33wwad1far", "to": "USERTASK_8fl0k0gy2n", "id": "LINE_gt3bik07dt"}], "icon": "layui-icon-app", "id": "82889025482981182066612", "idxNum": "1", "enableStatus": "4", "nodeList": [{"color": "#23D982", "extensionElements": [], "targetType": "assignee", "type": "start", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "isjiami": "1", "fy_text7": "", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "voicePlayType": "1", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "fy_text10": "", "fy_text50": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "", "jb_value_select": "", "loopCardinality": 0, "sh_input3": "", "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "", "scriptMsg": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "", "sh_value_select": "", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": ""}, "ico": "el-icon-video-play", "top": "93px", "left": "609px", "service": {}, "name": "开始节点", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "operaFuncName": ""}], "id": "START_779nicddqt", "state": ""}, {"ico": "el-icon-video-pause", "color": "#FF5454", "top": "655px", "left": "594px", "extensionElements": [], "service": {}, "name": "结束节点", "targetType": "assignee", "id": "END_6b84mxn1hq", "state": "", "type": "end", "target": ""}, {"paramMinSize": "1", "color": "#93BB49", "extensionElements": [], "targetType": "assignee", "paramName": "SERVICE_TASK_RESULT", "type": "serviceTask", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "isjiami": "1", "fy_text7": "", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "paramFmt": "1", "voicePlayType": "1", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "paramMinSize": "1", "fy_text10": "", "fy_text50": "", "params": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "", "jb_value_select": "", "loopCardinality": 0, "sh_input3": "", "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "1", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "", "paramName": "SERVICE_TASK_RESULT", "paramMaxSize": "1", "scriptMsg": "", "httpUrl": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "", "sh_value_select": "SERVICE_TASK_RESULT", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": "1"}, "paramFmt": "1", "paramMaxSize": "1", "ico": "el-icon-circle-plus-outline", "top": "363px", "left": "643px", "voicePlayType": "1", "service": {}, "name": "用户按键", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "operaFuncName": ""}], "id": "SERVICETASK_sphixznd8m", "state": "", "voicePlayInterruput": "N"}, {"formkey": "", "color": "#76CAFB", "extensionElements": [], "voicePlayNum": "1", "targetType": "assignee", "voicePlayContent": "感谢您对我们的评价，再见！", "type": "userTask", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "isjiami": "1", "fy_text7": "", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "voicePlayType": "2", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "fy_text10": "", "fy_text50": "", "params": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "感谢您对我们的评价，再见！", "jb_value_select": "", "loopCardinality": 0, "sh_input3": "", "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "感谢您对我们的评价，再见！", "scriptMsg": "", "httpUrl": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "1", "sh_value_select": "", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": ""}, "ico": "el-icon-headset", "top": "512px", "left": "444px", "voicePlayType": "2", "service": {}, "name": "正确输入", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "operaFuncName": ""}], "style": {}, "id": "USERTASK_cz2qayk8o5", "state": "", "voicePlayInterruput": "N"}, {"formkey": "", "color": "#76CAFB", "extensionElements": [], "targetType": "assignee", "voicePlayContent": "抱歉，您的输入无效。", "type": "userTask", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "isjiami": "1", "fy_text7": "", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "voicePlayType": "2", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "fy_text10": "", "fy_text50": "", "params": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "抱歉，您的输入无效。", "jb_value_select": "", "loopCardinality": 0, "sh_input3": "", "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "抱歉，您的输入无效。", "scriptMsg": "", "httpUrl": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "", "sh_value_select": "", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": ""}, "ico": "el-icon-headset", "top": "508px", "left": "822px", "voicePlayType": "2", "service": {}, "name": "错误输入", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "operaFuncName": ""}], "style": {}, "id": "USERTASK_33wwad1far", "state": "", "voicePlayInterruput": "N"}, {"formkey": "", "color": "#76CAFB", "extensionElements": [], "voicePlayNum": "1", "targetType": "assignee", "voicePlayContent": "满意请按1，否则请挂机。", "type": "userTask", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "isjiami": "1", "fy_text7": "", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "voicePlayType": "2", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "fy_text10": "", "fy_text50": "", "params": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "满意请按1，否则请挂机。", "jb_value_select": "", "loopCardinality": 0, "sh_input3": "", "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "满意请按1，否则请挂机。", "scriptMsg": "", "httpUrl": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "1", "sh_value_select": "", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": ""}, "ico": "el-icon-headset", "top": "211px", "left": "769px", "voicePlayType": "2", "service": {}, "name": "放音2", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "operaFuncName": ""}], "style": {}, "id": "USERTASK_8fl0k0gy2n", "state": "", "voicePlayInterruput": "N"}, {"formkey": "", "color": "#76CAFB", "extensionElements": [], "voicePlayNum": "1", "targetType": "assignee", "voicePlayContent": "客户您好，这里是10086热线，请问您对我们的服务满意吗？", "type": "userTask", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "isjiami": "1", "fy_text7": "", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "voicePlayType": "2", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "fy_text10": "", "fy_text50": "", "params": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "客户您好，这里是10086热线，请问您对我们的服务满意吗？", "jb_value_select": "", "loopCardinality": 0, "sh_input3": "", "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "客户您好，这里是10086热线，请问您对我们的服务满意吗？", "scriptMsg": "", "httpUrl": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "1", "sh_value_select": "", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": ""}, "ico": "el-icon-headset", "top": "170px", "left": "538px", "voicePlayType": "2", "service": {}, "name": "放音1", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "operaFuncName": ""}], "style": {}, "id": "USERTASK_l19jasa6r", "state": "", "voicePlayInterruput": "N"}], "ivrImg": "/ivr_img/2025/07/03/15/82484735876983603999417.png"}