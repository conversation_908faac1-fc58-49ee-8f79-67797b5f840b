<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<body>

<h2>Overview</h2>


<p>This implementation of commons-logging uses a  commons-logging.jar
 specific to a particular logging framework, instead of discovery. This takes
out the guessing, is simpler, faster and more robust. Just like you chose a
logging implementation, you should also use a matching commons-logging - for
example you download log4j.jar and commons-logging-log4j.jar, or use jdk
logging and use commons-logging-jdk.jar.</p>

<p>A similar packaging is used by Eclipse SWT - they provide a common widget API,
 but each platform uses a different implementation jar - instead of using a complex
 discovery/plugin mechanism.
</p>

<p>This package generates commons-logging-jdk14.jar - i.e. the java.util implementation
of commons-logging api.</p>

</body>
