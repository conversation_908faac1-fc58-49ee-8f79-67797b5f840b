<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN">
<html>
<head>
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
</head>
<body bgcolor="white">

Classes and interfaces for the definition of JavaServer Pages Tag Libraries.

<p>
The JavaServer Pages(tm) (JSP) 2.0 specification provides a portable
mechanism for the description of tag libraries.
<p>
A JSP tag library contains
<ul>
<li>A Tag Library Descriptor</li>
<li>A number of Tag Files or Tag handler classes defining
    request-time behavior</li>
<li>Additional classes and resources used at runtime</li>
<li>Possibly some additional classes to provide extra translation
    information</li>
</ul>
<p>
The JSP 2.0 specification and the reference implementation both contain
simple and moderately complex examples of actions defined using this
mechanism.  These are available at JSP's web site, at
<a href="http://java.sun.com/products/jsp">http://java.sun.com/products/jsp</a>.
Some readers may want to consult those to get a quick feel for how
the mechanisms work together.

</body>
</html>
