# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

authConfigFactoryImpl.load=从[{0}]加载持久化提供者注册信息
authConfigFactoryImpl.registerClass=正在为层[{1}]和应用程序上下文[{2}]注册类[{0}]
authConfigFactoryImpl.registerInstance=正在为层[{1}]和应用程序上下文[{2}]注册类型为[{0}]的实例
authConfigFactoryImpl.zeroLengthAppContext=应用上下文名称的长度为0是无效的
authConfigFactoryImpl.zeroLengthMessageLayer=零长度的消息层名称是无效的

callbackHandlerImpl.jaspicCallbackMissing=接收到不支持的类型为{0}的JASPIC回调，该回调被忽略

jaspicAuthenticator.authenticate=通过JASPIC验证[{0}]的请求

persistentProviderRegistrations.deleteFail=无法删除临时文件[{0}]
persistentProviderRegistrations.existsDeleteFail=临时文件[{0}]已存在且无法删除
persistentProviderRegistrations.moveFail=无法将[{0}]移至[{1}]

simpleServerAuthConfig.noModules=“没有配置ServerAuthModules”
