# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

corsFilter.invalidPreflightMaxAge=preflightMaxAge konnte nicht geparst werden.
corsFilter.nullRequestType=CORSRequestType Objekt ist null

csrfPrevention.invalidRandomClass=Kann keine Zufallsquelle mithilfe der Klasse [{0}] generieren

expiresFilter.noExpirationConfigured=Request [{0}] mit Response Status [{1}] und Content-Type [{2}], es wurde keine Gültigkeitsdauer konfiguriert
expiresFilter.noExpirationConfiguredForContentType=Keine Konfiguration für Expire von Content-Type [{0}] gefunden
expiresFilter.numberError=Ausnahme beim Parsen einer Zahl an Position [{0}] (mit Null beginnend) in Komma-separierter Liste [{1}]
expiresFilter.unsupportedStartingPoint=Nicht unterstützter Startpunkt [{0}]

http.403=Zugriff auf die angeforderte Resource [{0}] wurde verboten.

httpHeaderSecurityFilter.clickjack.invalid=Es wurde ein ungültiger Wert [{0}] für den Anti-Click-Jacking Header angegeben

remoteCidrFilter.noRemoteIp=Client verfügt über keine IP Adresse. Zugriff verweigert.

requestFilter.deny=Anfrage für  [{0}] abgelehnt wegen Eigenschaft  [{1}]
