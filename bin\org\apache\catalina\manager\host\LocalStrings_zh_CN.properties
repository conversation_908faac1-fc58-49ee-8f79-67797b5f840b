# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

hostManagerServlet.add=添加：添加主机[{0}]
hostManagerServlet.addFailed=失败 - 添加主机 [{0}] 失败
hostManagerServlet.addSuccess=确定-添加主机[{0}]
hostManagerServlet.alreadyHost=失败 - 主机名称[{0}]已经存在
hostManagerServlet.alreadyStarted=失败 - Host[{0}]已经启动。
hostManagerServlet.alreadyStopped=失败 - 主机[{0}]已经停止
hostManagerServlet.appBaseCreateFail=失败 - 创建appBase目录[{0}]失败，主机：[{1}]
hostManagerServlet.cannotRemoveOwnHost=失败 - 无法移除你的主机[{0}]
hostManagerServlet.cannotStartOwnHost=失败 - 无法启动主机[{0}]
hostManagerServlet.cannotStopOwnHost=失败 - 无法停止主机[{0}]
hostManagerServlet.configBaseCreateFail=失败 - 无法识别主机[{0}]的基础配置
hostManagerServlet.exception=失败 - 出现异常[{0}]
hostManagerServlet.invalidHostName=失败 - 指定的主机名称[{0}]无效
hostManagerServlet.list=列表：列出引擎[{0}]的主机
hostManagerServlet.listed=OK - 已列出Host
hostManagerServlet.managerXml=FAIL - 无法安装manager.xml
hostManagerServlet.noCommand=失败 - 未指定命令
hostManagerServlet.noHost=失败 - 主机名称[{0}]不存在
hostManagerServlet.noWrapper=容器未给当前servlet设置setWrapper()
hostManagerServlet.persist=持久化: 正在持久化当前配置
hostManagerServlet.persistFailed=失败 - 无法持久化配置
hostManagerServlet.persisted=OK - 配置持久化了.
hostManagerServlet.postCommand=失败 - 尝试通过GET请求使用命令[{0}]，但是需要使用POST请求
hostManagerServlet.remove=移除:正在移除主机 [{0}]
hostManagerServlet.removeFailed=失败 - 无法移除主机 [{0}]
hostManagerServlet.removeSuccess=确定-已删除主机[{0}]
hostManagerServlet.start=启动：启动主机[{0}]
hostManagerServlet.startFailed=失败 - 无法启动主机 [{0}]
hostManagerServlet.started=OK - 主机 [{0}] 已启动
hostManagerServlet.stop=停止：停止主机[{0}]
hostManagerServlet.stopFailed=失败 - 无法停止主机 [{0}]
hostManagerServlet.stopped=OK - 主机 [{0}] 已停止
hostManagerServlet.unknownCommand=失败 - 未知命令 [{0}]

htmlHostManagerServlet.addAliases=别名：
htmlHostManagerServlet.addAppBase=应用程序库：
htmlHostManagerServlet.addAutoDeploy=自动.部署
htmlHostManagerServlet.addButton=添加
htmlHostManagerServlet.addCopyXML=拷贝XML
htmlHostManagerServlet.addDeployOnStartup=启动.部署
htmlHostManagerServlet.addDeployXML=部署XML
htmlHostManagerServlet.addHost=主机
htmlHostManagerServlet.addManager=管理 App
htmlHostManagerServlet.addName=名称：
htmlHostManagerServlet.addTitle=添加.虚拟主机
htmlHostManagerServlet.addUnpackWARs=解压WARs
htmlHostManagerServlet.helpHtmlManager=HTML主机管理器帮助
htmlHostManagerServlet.helpHtmlManagerFile=../docs/html-host-manager-howto.html
htmlHostManagerServlet.helpManager=主机管理器帮助
htmlHostManagerServlet.helpManagerFile=../docs/host-manager-howto.html
htmlHostManagerServlet.hostAliases=主机别名
htmlHostManagerServlet.hostName=主机名称
htmlHostManagerServlet.hostTasks=命令
htmlHostManagerServlet.hostThis=主机管理器已安装 - 命令不可用
htmlHostManagerServlet.hostsRemove=移除
htmlHostManagerServlet.hostsStart=启动
htmlHostManagerServlet.hostsStop=停止
htmlHostManagerServlet.list=列出虚拟主机
htmlHostManagerServlet.manager=主机.管理器
htmlHostManagerServlet.messageLabel=消息：
htmlHostManagerServlet.persistAll=保存当前配置信息（包括虚拟主机）到server.xml和每个web应用程序context.xml文件里。
htmlHostManagerServlet.persistAllButton=全部
htmlHostManagerServlet.persistTitle=持久化配置
htmlHostManagerServlet.serverJVMVendor=JVM供应商
htmlHostManagerServlet.serverJVMVersion=JVM版本
htmlHostManagerServlet.serverOSArch=操作系统架构
htmlHostManagerServlet.serverOSName=操作系统名称
htmlHostManagerServlet.serverOSVersion=操作系统版本
htmlHostManagerServlet.serverTitle=服务器信息
htmlHostManagerServlet.serverVersion=Tomcat版本
htmlHostManagerServlet.title=Tomcat虚拟主机管理员

statusServlet.complete=服务器全部状态
statusServlet.title=服务器状态
