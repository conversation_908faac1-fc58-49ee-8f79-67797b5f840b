# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

accessLogValve.alreadyExists=접근 로그 파일을 [{0}]에서 [{1}](으)로 이름을 변경하지 못했습니다. 파일이 이미 존재합니다.
accessLogValve.closeFail=접근 로그 파일을 닫지 못했습니다.
accessLogValve.deleteFail=이전 접근 로그 파일 [{0}]을(를) 삭제하지 못했습니다.
accessLogValve.invalidLocale=로케일을 [{0}](으)로 설정하지 못했습니다.
accessLogValve.invalidPortType=유효하지 않은 포트 타입 [{0}]. 서버 (로컬) 포트를 사용합니다.
accessLogValve.invalidRemoteAddressType=유효하지 않은 원격 주소 타입 [{0}]. Peer가 아닌 원격 주소로 간주합니다.
accessLogValve.openDirFail=접근 로그 파일(들)을 위한 디렉토리 [{0}]을(를) 생성하지 못했습니다.
accessLogValve.openFail=접근 로그 파일 [{0}]을(를) 열지 못했습니다.
accessLogValve.renameFail=접근 로그 파일을 [{0}]에서 [{1}](으)로 이름을 변경하지 못했습니다.
accessLogValve.rotateFail=접근 로그를 순환시키지 못했습니다.
accessLogValve.unsupportedEncoding=인코딩을 [{0}](으)로 설정하지 못했습니다. 시스템 기본 문자셋을 사용할 것입니다.
accessLogValve.writeFail=다음 로그 메시지를 쓰지 못했습니다: [{0}]

errorReportValve.description=설명
errorReportValve.exception=예외
errorReportValve.exceptionReport=예외 보고
errorReportValve.message=메시지
errorReportValve.noDescription=설명이 없습니다.
errorReportValve.note=비고
errorReportValve.rootCause=근본 원인 (root cause)
errorReportValve.rootCauseInLogs=근본 원인(root cause)의 풀 스택 트레이스를, 서버 로그들에서 확인할 수 있습니다.
errorReportValve.statusHeader=HTTP 상태 {0} – {1}
errorReportValve.statusReport=상태 보고
errorReportValve.type=타입
errorReportValve.unknownReason=알 수 없는 사유

extendedAccessLogValve.badXParam=유효하지 않은 x 파라미터 포맷. 포맷은 'x-#(...) 이어야 합니다.
extendedAccessLogValve.badXParamValue=서블릿 요청을 위해 유효하지 않은 x 파라미터 값: [{0}]
extendedAccessLogValve.decodeError=[{0}](으)로 시작하는 문자들의 나머지 부분을 디코드할 수 없습니다.
extendedAccessLogValve.emptyPattern=패턴이 그저 빈 문자열이었거나, 공백 문자로만 채워진 문자열이었습니다.
extendedAccessLogValve.noClosing=디코드된 접근 로그 행에서 닫는 중괄호, '')'', 가 없습니다.
extendedAccessLogValve.patternParseError=패턴 [{0}]을(를) 파싱하는 중 오류 발생

http.400.desc=클라이언트 오류로서 인지된 어떤 문제로 인하여, 서버가 해당 요청을 처리할 수 없거나, 처리하지 않을 것입니다. (예: 잘못된 요청 문법, 유효하지 않은 요청 메시지 framing, 또는 신뢰할 수 없는 요청 라우팅).
http.400.reason=잘못된 요청
http.401.desc=대상 리소스에 접근하기 위한 유효한 인증 credentials가 없기 때문에, 요청에 적용되지 않았습니다.
http.401.reason=인가 안됨
http.402.desc=이 상태 코드는 미래의 사용을 위해 예약되어 있습니다.
http.402.reason=지불이 요구됨
http.403.desc=서버가 요청을 이해했으나 승인을 거부합니다.
http.403.reason=금지됨
http.404.desc=Origin 서버가 대상 리소스를 위한 현재의 representation을 찾지 못했거나, 그것이 존재하는지를 밝히려 하지 않습니다.
http.404.reason=찾을 수 없음
http.405.desc=요청 행에 포함된 해당 메소드는, origin 서버에 의해 인지되었으나, 대상 리소스에 의해 지원되지 않습니다.
http.405.reason=허용되지 않는 메소드
http.406.desc=요청으로부터 받은 proactive negotiation 헤더에 따르면, 대상 리소스는 해당 user agent가 받아들일만한 현재의 representation이 없고, 서버 또한 기본 representation을 제공하지 않으려 합니다.
http.406.reason=받아들일 수 없음
http.407.desc=이 상태 코드는 401 (인증 안됨)과 유사하나, 이는 클라이언트가 프록시를 사용하기 위하여 스스로를 인증할 필요가 있음을 알려줍니다.
http.407.reason=프록시 인증이 요구됨
http.408.desc=대기하도록 준비된 시간 이내에, 서버가 완전한 요청 메시지를 수신하지 못했습니다.
http.408.reason=요청 제한 시간 초과
http.409.desc=대상 리소스의 현재 상태와의 충돌 때문에, 요청이 완료될 수 없었습니다.
http.409.reason=충돌됨
http.410.desc=대상 리소스에 대한 접근이 해당 origin 서버에서 더이상 가용하지 않으며, 이러한 조건은 아마도 영구적일 것으로 보입니다.
http.410.reason=사라졌음
http.411.desc=Content-Length가 정의되지 않은 요청을, 서버가 받아들이기를 거부했습니다.
http.411.reason=Length가 요구됨
http.412.desc=서버에서 검사될 때, 요청 헤더 필드들 내에 주어진 하나 이상의 조건(들)이, false로 평가되었습니다.
http.412.reason=사전 조건 충족 실패
http.413.desc=요청의 payload가 서버가 처리하려 하거나 처리할 수 있는 것 보다 크기 때문에, 서버가 요청 처리를 거부합니다.
http.413.reason=Payload가 너무 큽니다.
http.414.desc=서버가 처리할 수 있는 것보다 request-target이 더 길기 때문에, 요청에 대한 서비스를 거부합니다.
http.414.reason=URI가 너무 깁니다.
http.415.desc=Payload가 대상 리소스에 대한 이 메소드에 의해 지원되지 않는 포맷이기 때문에, Origin 서버가 요청을 서비스하기를 거부합니다.
http.415.reason=지원되지 않는 Media Type
http.416.desc=요청의 Range 헤더 필드 내의 범위들 중 어느 것도, 선택된 리소스의 현재 범위와 겹치지 않거나, 요청된 범위들의 집합이 유효하지 않은 범위들, 또는 과도하게 작거나 겹치는 범위들이기 때문에 거절되었습니다.
http.416.reason=충족될 수 없는 범위
http.417.desc=요청의 Expect 헤더 필드에 주어진 expectation이, 적어도 하나 이상의 inbound 서버들에 의해 충족될 수 없었습니다.
http.417.reason=Expectation Failed
http.421.desc=요청이 응답을 생성할 수 없는 서버로 전달되었습니다.
http.421.reason=잘못 안내된 요청
http.422.desc=서버가 요청 엔티티의 Content-Type을 이해하고, 요청 엔티티의 문법이 올바르게 되어 있지만, 포함된 instruction들을 처리할 수 없었습니다.
http.422.reason=처리할 수 없는 엔티티
http.423.desc=메소드의 원본 또는 대상 리소스가 잠금 상태입니다.
http.423.reason=잠겨짐
http.424.desc=요청된 액션이 이미 실패한 또 다른 액션에 의존하고 있었기 때문에, 해당 리소스에 대해 이 메소드를 수행할 수 없습니다.
http.424.reason=실패한 의존적 요청
http.426.desc=서버가 현재의 프로토콜을 사용하여 요청을 처리하기를 거부했지만, 클라이언트가 다른 프로토콜로 업그레이드한 후에 처리하려 할 수도 있습니다.
http.426.reason=업그레이드가 요구됨
http.428.desc=Origin 서버는 요청이 사전 조건적이기를 요구합니다 (예: If-Match와 같은 헤더).
http.428.reason=사전조건이 필수적입니다.
http.429.desc=사용자가 주어진 시간 동안 너무 많은 요청을 보냈습니다. ("rate limiting")
http.429.reason=너무 많은 요청들
http.431.desc=요청 내의 헤더 필드들이 너무 커서 서버가 처리하려 하지 않습니다.
http.431.reason=요청의 헤더 필드들이 너무 큼
http.451.desc=서버가 법적인 사유들로 이 요청을 거부했습니다.
http.451.reason=법적인 사유들로 인하여 가용하지 않음
http.500.desc=서버가, 해당 요청을 충족시키지 못하게 하는 예기치 않은 조건을 맞닥뜨렸습니다.
http.500.reason=내부 서버 오류
http.501.desc=서버가 이 요청을 충족시키는데 필요한 필수적인 기능을 지원하지 않습니다.
http.501.reason=구현되지 않음
http.502.desc=서버가 게이트웨이 또는 프록시로서 동작하면서 요청을 처리하려 시도하는 동안, inbound 서버로부터 유효하지 않은 응답을 받았습니다.
http.502.reason=잘못된 게이트웨이
http.503.desc=일시적인 서버 부하 또는 예정된 유지보수 작업으로 인하여, 서버가 현재 요청을 처리할 수 없습니다. 잠시 지연된 뒤에 상황이 나아질 것으로 보입니다.
http.503.reason=서비스가 가용하지 않음
http.504.desc=서버가 게이트웨이 또는 프록시로 동작하는 동안, 요청을 처리 완료하기 위해 접근해야 하는 상위 서버로부터, 필요한 응답을 적절한 시간 내에 받지 못했습니다.
http.504.reason=게이트웨이 제한 시간 초과
http.505.desc=서버가 요청 메시지에서 사용된 HTTP의 major 버전을 지원하지 않거나, 또는 지원하기를 거부합니다.
http.505.reason=HTTP 버전이 지원되지 않음
http.506.desc=서버에 내부 설정 오류가 있습니다: 선택된 변형(variant) 리소스가, 투명한 컨텐트 교섭(negotiation) 그 자체에 관여하도록 설정되어 있는데, 그로 인하여 교섭 프로세스에 적절한 엔드포인트가 아닙니다.
http.506.reason=Variant Also Negotiates
http.507.desc=서버가 요청 처리를 성공적으로 완료하기 위해 필요한 representation을 저장할 수 없기 때문에, 해당 메소드가 해당 리소스에 대해 처리될 수 없었습니다.
http.507.reason=충분하지 않은 저장 공간
http.508.desc=서버가 "Depth: infinity"를 가진 요청을 처리하는 도중, 무한 루프를 맞닥뜨리는 바람에 오퍼레이션을 종료시켰습니다.
http.508.reason=루프가 탐지됨
http.510.desc=요청이, 리소스에 접근하기 위한 policy를 충족시키지 않습니다.
http.510.reason=확장 안됨
http.511.desc=클라이언트가 네트워크에 접근하기 위해서는 인증을 해야 합니다.
http.511.reason=네트워크 인증이 필요함

jdbcAccessLogValve.close=데이터베이스를 닫지 못했습니다.
jdbcAccessLogValve.exception=접근 엔트리를 추가하는 중 예외 발생

persistentValve.filter.failure=필터를 컴파일할 수 없습니다: [{0}]

remoteCidrValve.invalid=[{0}]을(를) 위해 유효하지 않은 설정이 제공되었습니다. 상세 정보를 보시려면 이전 메시지들을 확인하십시오.
remoteCidrValve.noPort=유효한 서버 포트를 포함하지 않은 해당 요청은 거부됩니다.
remoteCidrValve.noRemoteIp=클라이언트가 IP 주소를 가지고 있지 않습니다. 요청은 거절되었습니다.
remoteCidrValve.unexpectedPort=Connector 설정 속성인 addConnectorPort가 false인데도 불구하고, 요청이 서버 포트를 포함하고 있어서, 해당 요청은 거부됩니다.

remoteIpValve.invalidHostHeader=HTTP 헤더 [{1}] 내에 유효하지 않은 값이 발견되었습니다: [{0}]
remoteIpValve.invalidHostWithPort=HTTP 헤더 [{1}] 내의 호스트 값 [{0}]이(가) 포트 번호를 포함했는데, 이는 무시될 것입니다.
remoteIpValve.invalidPortHeader=HTTP 헤더 [{1}] 내에 유효하지 않은 포트 번호 값입니다: [{0}]
remoteIpValve.invalidRemoteAddress=보고된 원격 주소 [{0}](이)가 유효하지 않아서 원격 호스트를 식별할 수 없습니다.

requestFilterValve.configInvalid=Remote[Addr|Host]Valve를 위해 하나 이상의 유효하지 않은 설정이 제공되었는데, 이는 해당 Valve와 부모 컨테이너들이 시작되지 못하게 했습니다.
requestFilterValve.deny=프로퍼티 [{1}]에 기반하여, [{0}]을(를) 위한 요청을 거절합니다.

sslValve.certError=java.security.cert.X509Certificate 객체를 생성하기 위한 인증서 문자열 [{0}]을(를) 처리하지 못했습니다.
sslValve.invalidProvider=[{0}]의 이 요청과 연관된 Connector에 지정된 SSL provider는 유효하지 않습니다. 해당 인증서 데이터가 처리될 수 없었습니다.

stuckThreadDetectionValve.notifyStuckThreadCompleted=쓰레드 [{0}] (ID=[{3}])이(가) 이전에 stuck 상태로 보고된 바 있으나 이제 완료되었습니다. 해당 쓰레드는 대략 [{1}] 밀리초 동안 활성화되어 있었습니다. {2,choice,0#|0< 이 Valve에 의해 모니터링되는 쓰레드들이 여전히 [{2}]개가 있고, 그것들은 어쩌면 stuck 상태에 있을 수 있습니다.}
stuckThreadDetectionValve.notifyStuckThreadDetected=쓰레드 [{0}] (id=[{6}])이(가), [{4}]을(를) 위한 동일한 요청을 처리하기 위해, ([{2}] 이후) [{1}] 밀리초 동안 활성화되어 있었으며, 해당 쓰레드가 stuck된 상태에 있을 수 있습니다.\n\
(이 StuckThreadDetectionValve를 위한 stuck 상태 진입 기준점은 [{5}] 초입니다.) 이 Valve에 의해 모니터되는 전체 쓰레드들 중 [{3}] 개의 쓰레드가 stuck 상태일 수 있습니다.
stuckThreadDetectionValve.notifyStuckThreadInterrupted=쓰레드 [{0}](id=[{5}])이(가), [{1}] 밀리초 동안 동일 요청을 처리하기 위해 ([{2}] 이후로) [{3}] 동안 활성화되어 있었으나, 필시 stuck 상태에 있을 법한 쓰레드이기 때문에 중단되었습니다. (이 StuckThreadDetectionValve를 위한 중단 한계치는 [{4}] 초로 설정되어 있습니다.)
