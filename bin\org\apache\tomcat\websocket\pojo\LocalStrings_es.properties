# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

pojoEndpointBase.onCloseFail=Fallo al llamar el método onClose del endpoint POJO para el tipo POJO [{0}]
pojoEndpointBase.onOpenFail=Fallo al llamar el método onOpen del end point POJO para el tipo POJO [{0}]\n

pojoEndpointServer.getPojoInstanceFail=Fallo al crear la instancia POJO de tipo [{0}]\n

pojoMethodMapping.duplicateAnnotation=Anotaciones duplicadas [{0}] presente en la clase [{1}]\n
pojoMethodMapping.duplicatePongMessageParam=Varios parámetros de  PongMessage  estan presentes en el método [{0}] de la clase [{1}] que fue anotado con OnMessage
pojoMethodMapping.invalidDecoder=El decodificador especificado de tipo [{0}]  no puede ser instanciado\n
pojoMethodMapping.onErrorNoThrowable=Parámetro no descartable estaba presente en el método [{0}] de clase [{1}] que fue apuntado con OnError
