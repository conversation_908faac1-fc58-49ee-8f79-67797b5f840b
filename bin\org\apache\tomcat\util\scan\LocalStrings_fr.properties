# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jarScan.classloaderFail=Echec de recherche dans [{0}] de la hiérarchie de chargeurs de classes
jarScan.classloaderJarNoScan=Le JAR [{0}] dans le chemin de classes ne sera pas analysé
jarScan.classloaderJarScan=Analyse du JAR [{0}] du chemin de classes
jarScan.classloaderStart=Recherche dans les JARs de la hiérarchie de chargeurs de classe
jarScan.jarUrlStart=Recherche dans le JAR à l''URL [{0}]
jarScan.webinfclassesFail=Impossible de parcourir /WEB-INF/classes
jarScan.webinflibFail=Échec de scan du JAR [{0}] de /WEB-INF/lib
jarScan.webinflibJarNoScan=Le JAR [{0}] dans /WEB-INF/lib ne sera pas analysé
jarScan.webinflibJarScan=Analyse du JAR [{0}] dans /WEB-INF/lib
jarScan.webinflibStart=Recherche de JARs dans /WEB-INF/lib
