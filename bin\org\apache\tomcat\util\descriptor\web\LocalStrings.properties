# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

filterDef.invalidFilterName=Invalid <filter-name> [{0}] in filter definition.

securityConstraint.uncoveredHttpMethod=For security constraints with URL pattern [{0}] only the HTTP methods [{1}] are covered. All other methods are uncovered.
securityConstraint.uncoveredHttpMethodFix=Adding security constraints with URL pattern [{0}] to deny access with the uncovered HTTP methods that are not one of the following [{1}]
securityConstraint.uncoveredHttpOmittedMethod=For security constraints with URL pattern [{0}] the HTTP methods [{1}] are uncovered.
securityConstraint.uncoveredHttpOmittedMethodFix=Adding security constraints with URL pattern [{0}] to deny access with the uncovered HTTP methods [{1}]

servletDef.invalidServletName=Invalid <servlet-name> [{0}] in servlet definition.

webRuleSet.absoluteOrdering=<absolute-ordering> element not valid in web-fragment.xml and will be ignored
webRuleSet.absoluteOrderingCount=<absolute-ordering> element is limited to 1 occurrence
webRuleSet.nameCount=<name> element is limited to 1 occurrence
webRuleSet.postconstruct.duplicate=Duplicate post construct method definition for class [{0}]
webRuleSet.predestroy.duplicate=Duplicate @PreDestroy method definition for class [{0}]
webRuleSet.relativeOrdering=<ordering> element not valid in web.xml and will be ignored
webRuleSet.relativeOrderingCount=<ordering> element is limited to 1 occurrence

webXml.duplicateEnvEntry=Duplicate env-entry name [{0}]
webXml.duplicateFilter=Duplicate filter name [{0}]
webXml.duplicateFragment=More than one fragment with the name [{0}] was found. This is not legal with relative ordering. See section 8.2.2 2c of the Servlet specification for details. Consider using absolute ordering.
webXml.duplicateMessageDestination=Duplicate message-destination name [{0}]
webXml.duplicateMessageDestinationRef=Duplicate message-destination-ref name [{0}]
webXml.duplicateResourceEnvRef=Duplicate resource-env-ref name [{0}]
webXml.duplicateResourceRef=Duplicate resource-ref name [{0}]
webXml.duplicateServletMapping=The servlets named [{0}] and [{1}] are both mapped to the url-pattern [{2}] which is not permitted
webXml.duplicateTaglibUri=Duplicate tag library URI [{0}]
webXml.mergeConflictDisplayName=The display name was defined in multiple fragments with different values including fragment with name [{0}] located at [{1}]
webXml.mergeConflictFilter=The Filter [{0}] was defined inconsistently in multiple fragments including fragment with name [{1}] located at [{2}]
webXml.mergeConflictLoginConfig=A LoginConfig was defined inconsistently in multiple fragments including fragment with name [{0}] located at [{1}]
webXml.mergeConflictOrder=Fragment relative ordering contains circular references. This can be resolved by using absolute ordering in web.xml.
webXml.mergeConflictResource=The Resource [{0}] was defined inconsistently in multiple fragments including fragment with name [{1}] located at [{2}]
webXml.mergeConflictServlet=The Servlet [{0}] was defined inconsistently in multiple fragments including fragment with name [{1}] located at [{2}]
webXml.mergeConflictSessionCookieComment=The session cookie comment was defined inconsistently in multiple fragments with different values including fragment with name [{0}] located at [{1}]
webXml.mergeConflictSessionCookieDomain=The session cookie domain was defined inconsistently in multiple fragments with different values including fragment with name [{0}] located at [{1}]
webXml.mergeConflictSessionCookieHttpOnly=The session cookie http-only flag was defined inconsistently in multiple fragments with different values including fragment with name [{0}] located at [{1}]
webXml.mergeConflictSessionCookieMaxAge=The session cookie max-age was defined inconsistently in multiple fragments with different values including fragment with name [{0}] located at [{1}]
webXml.mergeConflictSessionCookieName=The session cookie name was defined inconsistently in multiple fragments with different values including fragment with name [{0}] located at [{1}]
webXml.mergeConflictSessionCookiePath=The session cookie path was defined inconsistently in multiple fragments with different values including fragment with name [{0}] located at [{1}]
webXml.mergeConflictSessionCookieSecure=The session cookie secure flag was defined inconsistently in multiple fragments with different values including fragment with name [{0}] located at [{1}]
webXml.mergeConflictSessionTimeout=The session timeout was defined inconsistently in multiple fragments with different values including fragment with name [{0}] located at [{1}]
webXml.mergeConflictSessionTrackingMode=The session tracking modes were defined inconsistently in multiple fragments including fragment with name [{0}] located at [{1}]
webXml.mergeConflictString=The [{0}] with name [{1}] was defined inconsistently in multiple fragments including fragment with name [{2}] located at [{3}]
webXml.multipleOther=Multiple <others> entries nested in <ordering> element
webXml.reservedName=A web.xml file was detected using a reserved name [{0}]. The name element will be ignored for this fragment.
webXml.unrecognisedPublicId=The public ID [{0}] did not match any of the known public ID''s for web.xml files so the version could not be identified
webXml.version.unknown=Unknown version string [{0}]. Default version will be used.
webXml.wrongFragmentName=Used a wrong fragment name [{0}] at web.xml absolute-ordering tag!

webXmlParser.applicationParse=Parse error in application web.xml file at [{0}]
webXmlParser.applicationPosition=Occurred at line [{0}] column [{1}]
webXmlParser.applicationStart=Parsing application web.xml file at [{0}]

xmlEncodingBase.encodingInvalid=The encoding [{0}] is not recognised by this JRE. The existing value of [{1}] will be used
