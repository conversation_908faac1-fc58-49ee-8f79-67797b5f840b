# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

upgrade.sis.errorCloseFail=在上一个错误之后无法干净地关闭InputStream
upgrade.sis.isFinished.ise=当 ServletInputStream 不处于非阻塞模式时调用 isFinished() 是非法的（即必须首先调用 setReadListener()）
upgrade.sis.isReady.ise=当ServletInputStream未处于非阻塞模式时，调用isReady（）是非法的（即必须首先调用setReadListener（））
upgrade.sis.onErrorFail=对注册的readlistener的错误处理触发了这个进一步的错误，该错误被吞入
upgrade.sis.read.closed=InputStream 已被关闭
upgrade.sis.read.ise=在非阻塞模式下调用任何read()方法而不首先通过调用isready()检查是否有可用的数据是非法的
upgrade.sis.readListener.null=向setReadListener（）传递null是非法的
upgrade.sis.readListener.set=在同一个upgraded连接上调用多次setReadListener()函数是非法的
upgrade.sos.canWrite.ise=当ServletOutputStream未处于非阻塞模式时，调用canWrite（）是非法的（即必须首先调用setWriteListener）
upgrade.sos.errorCloseFail=在上一个错误之后无法干净地关闭OutputStream
upgrade.sos.onErrorFail=对注册的WriteListener 的错误处理触发了这个进一步的错误，该错误被吞入
upgrade.sos.write.closed=输出流已被关闭
upgrade.sos.write.ise=在非阻塞模式下调用任何写()方法都是非法的，而无需首先检查是否有可用的空间，只需调用isreadi()
upgrade.sos.writeListener.null=对setWriteListener()传递null是非法的
upgrade.sos.writeListener.set=对于同一个升级的连接，多次调用setWriteListener（）是非法的

upgradeProcessor.isCloseFail=无法关闭与升级连接关联的输入流
upgradeProcessor.osCloseFail=无法关闭与升级连接关联的输出流。
upgradeProcessor.requiredClose=由于流的closeRequired状态，因此关闭升级的连接：输入[{0}]，输出[{1}]
upgradeProcessor.stop=正在关闭升级的连接，因为传入的套接字状态为“停止”。
upgradeProcessor.unexpectedState=因传入套接字状态为[{0}]而意外关闭升级连接
