# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractStream.windowSizeInc=Conexión [{0}], Flujo [{1}], aumente el control de flujo de la ventana en  [{2}] para [{3}]\n

connectionPrefaceParser.mismatch=Una sequencia de byte no esperada fue recibida al inicio del prefacio de cliente [{0}]

connectionSettings.debug=Conexión  [{0}], Parámetro tipo [{1}] fijado a [{2}]
connectionSettings.headerTableSizeLimit=La conexión [{0}], intentó fijar un tamaño de cabecera de [{1}] pero el límite es  16k\n
connectionSettings.maxFrameSizeInvalid=Conexión [{0}], El tamaño de cuadro máximo solicitado de [{1}] esta fuera del rango permitido de [{2}] hasta [{3}]\n
connectionSettings.unknown=Conexión [{0}], Un parámetro desconocido con identificador [{1}] y valor [{2}] fue ignorado\n

hpack.invalidCharacter=El carácter Unicode [{0}] en el punto del código [{1}] no puede ser codificado al estar fuera del rango permitido de 0 a 255.

http2Parser.headerLimitSize=Conexión [{0}], Flujo [{1}], Tamaño total de la cabecera my grade
http2Parser.headers.wrongStream=La conexión [{0}], tiene cabeceras en progreso para stream [{1}] pero un frame para stream [{2}] fue recibido
http2Parser.preface.invalid=Se presentó un prefacio de conexión inválido
http2Parser.processFrameData.window=Conexión [{0}], El cliente mandó más datos que los permitidos por el flujo de ventana.
http2Parser.processFrameHeaders.decodingDataLeft=Datos sobrantes luego de decodificar HPACK  - Estos datos se deberían haber consumido
http2Parser.processFramePushPromise=Conexión [{0}], Flujo [{1}], Push promise frames no deben ser enviadas por el cliente

stream.closed=Conexión [{0}], Flujo [{1}], Imposible escribir en el flujo una vez que ha sido crear
stream.header.debug=Conexión [{0}], Flujo [{1}], cabecera HTTP  [{2}], Valor [{3}]\n
stream.header.noPath=Conexión [{0}], Flujo [{1}], El [:path] de la seudo cabecera estaba vacía
stream.header.unknownPseudoHeader=Conexión [{0}], Flujo [{1}], Se recibió una Pseudo cabecera desconocida [{2}]
stream.inputBuffer.reset=Reinicio de flujo
stream.inputBuffer.signal=Se adicionaron datos al inBuffer cuando el hilo esta esperando. Señalizando al hilo que a continuar
stream.reprioritisation.debug=Conexión [{0}], Flujo [{1}], Exclusivo [{2}], Padre [{3}], Peso [{4}]

streamProcessor.error.connection=Conexión [{0}], Flujo [{1}], Ha ocurrido un error el procesamiento que fue fatal para la conexión

streamStateMachine.debug.change=Conexión [{0}], Flujo [{1}], Estado cambió de [{2}] a [{3}]

upgradeHandler.allocate.left=Conexión [{0}], Flujo [{1}], [{2}] bytes no asignados -  tratando de asignar en el hijo
upgradeHandler.allocate.recipient=Conexión [{0}], Flujo [{1}], recipiente potencial [{2}] con peso [{3}]
upgradeHandler.ioerror=Conexión [{0}]
upgradeHandler.pingFailed=Conexión [{0}] falló al hacer ping al cliente
upgradeHandler.prefaceReceived=Conexión [{0}], Pre face de conexión recibida del cliente\n
upgradeHandler.pruneIncomplete=La conexión [{0}] Falló al podar completamente la conexión porque existen flujos activos / usados en el árbol de priorida. Existen [{2}] muchos flujos
upgradeHandler.prunedPriority=La conexión [{0}] ha cortado el flujo en desuso [{1}] el cual podía ser parte del árbol prioritario
upgradeHandler.rst.debug=Conexión [{0}], Flujo [{1}], Error [{2}], Mensaje [{3}],  RST (cerrando flujo)
upgradeHandler.sendPrefaceFail=La conexión [{0}], Falló al enviar el prefacio al cliente\n
upgradeHandler.socketCloseFailed=Error cerrando el socket
upgradeHandler.stream.even=Un nuevo flujo remoto con  ID [{0}]  fue solicitado, pero todos los flujos remotos deben usar identificadores raros
upgradeHandler.upgrade=La conexión [{0}], HTTP/1.1 se actualizó al flujo [1]\n
upgradeHandler.upgradeDispatch.entry=Entrada, Conexión [{0}], SocketStatus [{1}]\n
upgradeHandler.upgradeDispatch.exit=Salida, Conexión  [{0}], Estado de Socket [{1}]
upgradeHandler.writeHeaders=Conexión [{0}], Flujo [{1}]
