# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

PooledSender.senderDisconnectFail=Sender의 연결을 끊지 못했습니다.

pooledSender.closed.queue=큐가 닫힌 상태입니다.

receiverBase.bind.failed=복제 리스너를 어드레스 [{0}]에 바인딩하지 못했습니다.
receiverBase.socket.bind=Receiver 서버 소켓이 [{0}]에 바인딩 되었습니다.
receiverBase.udp.bind=UDP 수신 서버 소켓이 [{0}]에 바인딩 됨.
receiverBase.unable.bind=서버 소켓을 바인드 할 수 없습니다: [{0}]에서 오류 발생.
receiverBase.unable.bind.udp=오류 발생으로 인해 UDP 소켓을 [{0}]에 바인딩할 수 없습니다.
