# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

ajpmessage.invalid=Mensaje inválido recibido con firma [{0}]
ajpmessage.null=No puedo añadir valor nulo
ajpmessage.overflow=Error de desbordamiento en búfer al añadir [{0}] bytes en posición [{1}]

ajpprocessor.certs.fail=Fallo en conversión de Certificado
ajpprocessor.header.error=Fallo en análisis de mensaje de cabecera
ajpprocessor.request.prepare=Error preparando requerimiento
ajpprocessor.request.process=Error procesando requerimiento

ajpprotocol.noUpgrade=Actualización (upgrade) no esta soportada para AJP. Se ha ignorado la configuración UpgradeProtocol para [{0}]
