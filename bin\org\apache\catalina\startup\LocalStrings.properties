# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

catalina.configFail=Unable to load server configuration from [{0}]
catalina.noCluster=Cluster RuleSet not found due to [{0}]. Cluster configuration disabled.
catalina.noNaming=Naming environment is disabled
catalina.serverStartFail=The required Server component failed to start so Tom<PERSON> is unable to start.
catalina.shutdownHookFail=The shutdown hook experienced an error while trying to stop the server
catalina.stopServer=No shutdown port configured. Shut down server through OS signal. Server not shut down.
catalina.stopServer.connectException=Could not contact [{0}:{1}]. Tomcat may not be running.

connector.noSetExecutor=Connector [{0}] does not support external executors. Method setExecutor(java.util.concurrent.Executor) not found.
connector.noSetSSLImplementationName=Connector [{0}] does not support changing the SSL implementation. Method setSslImplementationName(String) not found.

contextConfig.altDDNotFound=alt-dd file [{0}] not found
contextConfig.annotationsStackOverflow=Unable to complete the scan for annotations for web application [{0}] due to a StackOverflowError. Possible root causes include a too low setting for -Xss and illegal cyclic inheritance dependencies. The class hierarchy being processed was [{1}]
contextConfig.applicationMissing=Missing application web.xml, using defaults only
contextConfig.applicationParse=Parse error in application web.xml file at [{0}]
contextConfig.applicationPosition=Occurred at line [{0}] column [{1}]
contextConfig.applicationStart=Parsing application web.xml file at [{0}]
contextConfig.applicationUrl=Unable to determine URL for application web.xml
contextConfig.authenticatorConfigured=Configured an authenticator for method [{0}]
contextConfig.authenticatorInstantiate=Cannot instantiate an authenticator of class [{0}]
contextConfig.authenticatorMissing=Cannot configure an authenticator for method [{0}]
contextConfig.authenticatorResources=Cannot load authenticators mapping list
contextConfig.badUrl=Unable to process context descriptor [{0}]
contextConfig.cce=Lifecycle event data object [{0}] is not a Context
contextConfig.contextClose=Error closing context.xml
contextConfig.contextMissing=Missing context.xml: [{0}]
contextConfig.contextParse=Parse error in context.xml for [{0}]
contextConfig.defaultError=Error processed default web.xml named [{0}] at [{1}]
contextConfig.defaultMissing=No global web.xml found
contextConfig.defaultPosition=Occurred at line [{0}] column [{1}]
contextConfig.destroy=ContextConfig: Destroying
contextConfig.fileUrl=Unable to create a File object from the URL [{0}]
contextConfig.fixDocBase=Exception fixing docBase for context [{0}]
contextConfig.init=ContextConfig: Initializing
contextConfig.inputStreamFile=Unable to process file [{0}] for annotations
contextConfig.inputStreamJar=Unable to process Jar entry [{0}] from Jar [{1}] for annotations
contextConfig.inputStreamJndi=Unable to process resource element [{0}] for annotations
contextConfig.inputStreamWebResource=Unable to process web resource [{0}] for annotations
contextConfig.invalidSciHandlesTypes=Unable to load class [{0}] to check against the @HandlesTypes annotation of one or more ServletContentInitializers.
contextConfig.jarFile=Unable to process Jar [{0}] for annotations
contextConfig.jndiUrl=Unable to process JNDI URL [{0}] for annotations
contextConfig.jndiUrlNotDirContextConn=The connection created for URL [{0}] was not a DirContextURLConnection
contextConfig.jspFile.error=JSP file [{0}] must start with a ''/''
contextConfig.jspFile.warning=WARNING: JSP file [{0}] must start with a ''/'' in Servlet 2.4
contextConfig.missingRealm=No Realm has been configured to authenticate against
contextConfig.noAntiLocking=The value [{0}] configured for java.io.tmpdir does not point to a valid directory. The antiResourceLocking setting for the web application [{1}] will be ignored.
contextConfig.processAnnotationsDir.debug=Scanning directory for class files with annotations [{0}]
contextConfig.processAnnotationsJar.debug=Scanning jar file for class files with annotations [{0}]
contextConfig.processAnnotationsWebDir.debug=Scanning web application directory for class files with annotations [{0}]
contextConfig.resourceJarFail=Failed to process JAR found at URL [{0}] for static resources to be included in context with name [{1}]
contextConfig.role.auth=Security role name [{0}] used in an <auth-constraint> without being defined in a <security-role>
contextConfig.role.link=Security role name [{0}] used in a <role-link> without being defined in a <security-role>
contextConfig.role.runas=Security role name [{0}] used in a <run-as> without being defined in a <security-role>
contextConfig.sci.debug=Unable to process ServletContainerInitializer for [{0}]. This is most likely due to a class defined in the @HandlesTypes annotation being missing
contextConfig.sci.info=Unable to process ServletContainerInitializer for [{0}]. This is most likely due to a class defined in the @HandlesTypes annotation being missing. Enable DEBUG level logging for the full stack trace.
contextConfig.servletContainerInitializerFail=Failed to detect ServletContainerInitializers for context with name [{0}]
contextConfig.start=ContextConfig: Processing START
contextConfig.stop=ContextConfig: Processing STOP
contextConfig.unavailable=Marking this application unavailable due to previous error(s)
contextConfig.unknownUrlProtocol=The URL protocol [{0}] was not recognised during annotation processing. URL [{1}] was ignored.
contextConfig.urlPatternValue=Both the urlPatterns and value attributes were set for the [{0}] annotation on class [{1}]
contextConfig.xmlSettings=Context [{0}] will parse web.xml and web-fragment.xml files with validation:[{1}] and namespaceAware:[{2}]

engineConfig.cce=Lifecycle event data object [{0}] is not an Engine
engineConfig.start=EngineConfig: Processing START
engineConfig.stop=EngineConfig: Processing STOP

expandWar.copy=Error copying [{0}] to [{1}]
expandWar.createFailed=Unable to create the directory [{0}]
expandWar.createFileFailed=Unable to create the file [{0}]
expandWar.deleteFailed=[{0}] could not be completely deleted. The presence of the remaining files may cause problems
expandWar.deleteOld=An expanded directory [{0}] was found with a last modified time that did not match the associated WAR. It will be deleted.
expandWar.illegalPath=The archive [{0}] is malformed and will be ignored: an entry contains an illegal path [{1}] which was not expanded to [{2}] since that is outside of the defined docBase [{3}]
expandWar.lastModifiedFailed=Unable to set the last modified time for [{0}]
expandWar.missingJarEntry=Cannot get input stream for JarEntry [{0}] - broken WAR file?

failedContext.start=Failed to process either the global, per-host or context-specific context.xml file therefore the [{0}] Context cannot be started.

hostConfig.appBase=Application base [{1}] for host [{0}] does not exist or is not a directory. deployOnStartUp and autoDeploy have been set to false to prevent deployment errors. Other errors may still occur.
hostConfig.canonicalizing=Unable to determine canonical path for [{0}] while attempting to undeploy [{1}]
hostConfig.cce=Lifecycle event data object [{0}] is not a Host
hostConfig.context.remove=Error while removing context [{0}]
hostConfig.context.restart=Error during context [{0}] restart
hostConfig.createDirs=Unable to create directory for deployment: [{0}]
hostConfig.deploy.error=Exception while deploying web application directory [{0}]
hostConfig.deployDescriptor=Deploying deployment descriptor [{0}]
hostConfig.deployDescriptor.blocked=The web application with context path [{0}] was not deployed because it contained a deployment descriptor [{1}] which may include configuration necessary for the secure deployment of the application but processing of deployment descriptors is prevented by the deployXML setting of this host. An appropriate descriptor should be created at [{2}] to deploy this application.
hostConfig.deployDescriptor.error=Error deploying deployment descriptor [{0}]
hostConfig.deployDescriptor.finished=Deployment of deployment descriptor [{0}] has finished in [{1}] ms
hostConfig.deployDescriptor.localDocBaseSpecified=A docBase [{0}] inside the host appBase has been specified, and will be ignored
hostConfig.deployDescriptor.threaded.error=Error waiting for multi-thread deployment of deployment descriptors to complete
hostConfig.deployDir=Deploying web application directory [{0}]
hostConfig.deployDir.error=Error deploying web application directory [{0}]
hostConfig.deployDir.finished=Deployment of web application directory [{0}] has finished in [{1}] ms
hostConfig.deployDir.threaded.error=Error waiting for multi-thread deployment of directories to complete
hostConfig.deployWar=Deploying web application archive [{0}]
hostConfig.deployWar.error=Error deploying web application archive [{0}]
hostConfig.deployWar.finished=Deployment of web application archive [{0}] has finished in [{1}] ms
hostConfig.deployWar.hiddenDir=The directory [{0}] will be ignored because the WAR [{1}] takes priority and unpackWARs is false
hostConfig.deployWar.threaded.error=Error waiting for multi-thread deployment of WAR files to complete
hostConfig.deploying=Deploying discovered web applications
hostConfig.docBaseUrlInvalid=The provided docBase cannot be expressed as a URL
hostConfig.expand=Expanding web application archive [{0}]
hostConfig.expand.error=Exception while expanding web application archive [{0}]
hostConfig.ignorePath=Ignoring path [{0}] in appBase for automatic deployment
hostConfig.illegalWarName=The war name [{0}] is invalid. The archive will be ignored.
hostConfig.jmx.register=Register context [{0}] failed
hostConfig.jmx.unregister=Unregister context [{0}] failed
hostConfig.reload=Reloading context [{0}]
hostConfig.resourceNotAbsolute=Unable to remove resource from context [{0}] since [{1}] is not an absolute path
hostConfig.start=HostConfig: Processing START
hostConfig.stop=HostConfig: Processing STOP
hostConfig.undeploy=Undeploying context [{0}]
hostConfig.undeployVersion=Undeploying old version of context [{0}] which has no active session

passwdUserDatabase.readFail=Failed to obtain a complete set of users from /etc/passwd

tomcat.addWebapp.conflictChild=Unable to deploy WAR at [{0}] to context path [{1}] because of existing context [{2}]
tomcat.addWebapp.conflictFile=Unable to deploy WAR at [{0}] to context path [{1}] because of existing file [{2}]
tomcat.baseDirMakeFail=Unable to create the directory [{0}] to use as the base directory
tomcat.baseDirNotDir=The location [{0}] specified for the base directory is not a directory
tomcat.defaultMimeTypeMappingsFail=Unable to load the default MIME types
tomcat.homeDirMakeFail=Unable to create the directory [{0}] to use as the home directory

userConfig.database=Exception loading user database
userConfig.deploy=Deploying web application for user [{0}]
userConfig.deploy.threaded.error=Error waiting for multi-thread deployment of user directories to complete
userConfig.deploying=Deploying user web applications
userConfig.error=Error deploying web application for user [{0}]
userConfig.start=UserConfig: Processing START
userConfig.stop=UserConfig: Processing STOP

versionLoggerListener.arg=Command line argument: {0}
versionLoggerListener.catalina.base=CATALINA_BASE:         {0}
versionLoggerListener.catalina.home=CATALINA_HOME:         {0}
versionLoggerListener.env=Environment variable:  {0} = {1}
versionLoggerListener.java.home=Java Home:             {0}
versionLoggerListener.os.arch=Architecture:          {0}
versionLoggerListener.os.name=OS Name:               {0}
versionLoggerListener.os.version=OS Version:            {0}
versionLoggerListener.prop=System property:       {0} = {1}
versionLoggerListener.serverInfo.server.built=Server built:          {0}
versionLoggerListener.serverInfo.server.number=Server version number: {0}
versionLoggerListener.serverInfo.server.version=Server version name:   {0}
versionLoggerListener.vm.vendor=JVM Vendor:            {0}
versionLoggerListener.vm.version=JVM Version:           {0}

webAnnotationSet.invalidInjection=Invalid method resource injection annotation.
