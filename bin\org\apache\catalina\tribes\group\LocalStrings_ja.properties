# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

channelCoordinator.alreadyStarted=チャンネルは既にレベル：[{0}]で開始されました。
channelCoordinator.invalid.startLevel=不正な開始レベルです。正常なレベルは SND_RX_SEQ や SND_TX_SEQ、MBR_TX_SEQ や MBR_RX_SEQ です。

groupChannel.listener.alreadyExist=チャンネルリスナー [{0}][{1}] はすでに存在します。
groupChannel.noDestination=宛先が指定されていません。
groupChannel.nullMessage=Null メッセージを送信することが出来ません。
groupChannel.optionFlag.conflict=Interceptor のoption フラグが衝突しています: [{0}]
groupChannel.receiving.error=エラー受信メッセージ：
groupChannel.sendFail.noRpcChannelReply=RPC チャンネルが見つからないため NoRpcChannelReply を送信できません。
groupChannel.unable.deserialize=メッセージをデシリアライズできません： [{0}]
groupChannel.unable.sendHeartbeat=Tribesインターセプタスタックを介してハートビートを送信できません。 もう一sleep します。

rpcChannel.replyFailed=RpcChannel へ返信を送信できません。
