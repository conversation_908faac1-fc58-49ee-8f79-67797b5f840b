# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

mapper.addHost.sameHost=동일한 호스트 [{0}]을(를) 중복하여 등록을 시도했는데, 이는 무시됩니다.
mapper.addHost.success=등록된 호스트 [{0}]
mapper.addHostAlias.sameHost=동일한 호스트 [{1}]을(를) 위한 별칭 [{0}]이(가) 중복해서 등록 시도 되었는데, 이는 무시됩니다.
mapper.addHostAlias.success=호스트 [{1}]을(를) 위해 별칭 [{0}]이(가) 등록되었습니다.
mapper.duplicateHost=중복된 호스트 [{0}]. 해당 이름은 이미 호스트 [{1}]에 의해 사용되고 있습니다. 이 호스트는 무시될 것입니다.
mapper.duplicateHostAlias=호스트 [{1}] 내에 중복된 호스트 별칭 [{0}]. 해당 이름이 이미 호스트 [{2}]에 의해 사용되고 있습니다. 이 별칭은 무시될 것입니다.
mapper.removeWrapper=경로 [{1}]의 컨텍스트 [{0}](으)로부터 wapper를 제거합니다.

mapperListener.pauseContext=컨텍스트 [{0}]을(를), 서비스 [{1}]을(를) 위해 다시 로드되고 있는 것으로 등록합니다.
mapperListener.registerContext=서비스 [{1}]을(를) 위한 컨텍스트 [{0}]을(를) 등록합니다.
mapperListener.registerHost=서비스 [{2}]을(를) 위해 도메인 [{1}]에 있는 호스트 [{0}]을(를) 등록합니다.
mapperListener.registerWrapper=서비스 [{2}]을(를) 위한 컨텍스트 [{1}]에서 Wrapper [{0}]을(를) 등록합니다.
mapperListener.unknownDefaultHost=서비스 [{1}]을(를) 위해, 알 수 없는 기본 호스트 [{0}]. Tomcat은 호스트 이름을 지정하지 않은 HTTP/1.0 요청들을 처리할 수 없을 것입니다.
mapperListener.unregisterContext=서비스 [{1}]을(를) 위한 컨텍스트 [{0}]에 대한 등록을 제거합니다.
mapperListener.unregisterHost=서비스 [{2}]을(를) 위한 도메인 [{1}]에서 호스트 [{0}]의 등록을 제거합니다.
mapperListener.unregisterWrapper=서비스 [{2}]을(를) 위한 컨텍스트 [{1}] 내의 Wrapper [{0}]에 대한 등록을 제거합니다.
