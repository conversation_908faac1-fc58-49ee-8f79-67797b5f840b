# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

channel.nio.ssl.notHandshaking=NOT_HANDSHAKING durante el handshake
channel.nio.ssl.sniDefault=Imposible almacenar los datos suficientes para determinar el SNI del host solicitado. Utilizando el default
channel.nio.ssl.sniHostName=El nombre del servidor (SNI) extraído de la conexión [{0}] es [{1}]
channel.nio.ssl.unexpectedStatusDuringWrap=Estado inesperado [{0}] durante el handshake WRAP.\n
channel.nio.ssl.unwrapFail=Incapáz de desenvolver los datos, estatus no válido  [{0}]\n

endpoint.accept.fail=Aceptación de conector falló
endpoint.apr.applyConf=Aplicando  OpenSSLConfCmd al contexto SSL
endpoint.apr.checkConf=Verificando OpenSSLConf
endpoint.apr.errMakeConf=No se pudo crear el contexto OpenSSLConf\n
endpoint.apr.invalidSslProtocol=Se ha proporcionado un valor inválido [{0}] para el atributo SSLProtocol
endpoint.apr.maxConnections.running=El endpoint APR no soporta cambiar el parámetro  maxConnections mientras está corriendo. Se continuará usando el valor existente  [{0}].
endpoint.apr.pollAddInvalid=Intento no válido de adicionar el socket [{0}] a la encuesta\n
endpoint.apr.tooManyCertFiles=Fueron configurados más archivos de ceritificados que los que AprEndpoint puede manejar
endpoint.debug.channelCloseFail=No puede cerrar el canal
endpoint.debug.socketCloseFail=No pude cerrar el enchufe (socket)
endpoint.debug.unlock.fail=Excepción cogida intentando desbloquear aceptación en puerto [{0}]
endpoint.err.close=Excepción cogida intentando cerrar conector
endpoint.err.handshake=Acuerdo fallido
endpoint.err.unexpected=Error inesperado al procesar conector
endpoint.init.bind=Ligado de conector falló: [{0}] [{1}]
endpoint.init.listen=Escucha de conector falló: [{0}] [{1}]
endpoint.init.notavail=APR no disponible
endpoint.invalidJmxNameSslHostCert=Imposible generar un nombre de objeto JMX válido para el SSLHostConfigCertificate asociado con el servidor [{0}] y el tipo de certificado [{1}]\n
endpoint.jsse.noSslContext=No se encontró ningún conexto SSLContext para el hostname [{0}]\n
endpoint.nio.stopLatchAwaitInterrupted=Este hilo fue interrumpido mientras esperaba porque  los encuestadores se detuvieran
endpoint.noSslHostConfig=No se encontró elemento SSLHostConfig con nombre de máquina [{0}] para machear el defaultSSLHostConfigName para el conector [{1}]\n
endpoint.noSslHostName=No se proveió un nombre de host para la configuración SSL
endpoint.poll.error=Error inesperado de encuestador
endpoint.poll.fail=Fallo crítico de encuestador (reiniciando encuestador): [{0}] [{1}]
endpoint.poll.initfail=Falló la creación del encuestador
endpoint.poll.limitedpollsize=No pude crear encuestador de medida específica de [{0}]
endpoint.process.fail=Error reservando procesador de conector
endpoint.sendfile.addfail=Fallo en Sendfile: [{0}] [{1}]
endpoint.sendfile.error=Error inesperado de envío de fichero
endpoint.serverSocket.closeFailed=Fallo al cerrar el socket del servidor para [{0}]
endpoint.setAttribute=Fijando [{0}] a [{1}]\n
endpoint.warn.executorShutdown=El ejecutor asociado con el hilo del pool [{0}] no fue totalmente apagado. Algunos hilos de la aplicación pudieran estar corriendo aún.
endpoint.warn.incorrectConnectionCount=Conteo de conexión errónea, hay varias llamadas a socket.close en el mismo socket
endpoint.warn.noLocalName=Imposible determinar el nombre de la máquina local para el socket [{0}]\n
endpoint.warn.noLocalPort=Uncapaz de determinar el puerto local para el socket [{0}]\n
endpoint.warn.unlockAcceptorFailed=El hilo aceptador [{0}] falló al desbloquear. Forzando apagado de enchufe (socket).

sniExtractor.clientHelloTooBig=El ClientHello no fue presentado en un sólo registro TLS por lo cual no se pudo extraer la información SNI

socket.apr.closed=El socket [{0}] asociado con esta conexión ha sido cerrado.
socket.sslreneg=Excepción renegociando la conexión SSL

sslHostConfig.certificate.notype=Se especificaron multiples certificados y al menos uno de ellos no tiene el tipo de atributo requerido
sslHostConfig.fileNotFound=No existe el archivo configurado [{0}]
sslHostConfig.invalid_truststore_password=La clave del almacén de confianza suministrada no se pudo usar para desbloquear y/o validar el almacén de confianza. Reintentando acceder el almacén de confianza con una clave nula que se saltará la validación.
sslHostConfig.opensslconf.null=El intento de fijar OpenSSLConf en nulo fue ignorado

sslImplementation.cnfe=Incapaz de crear SSLImplementation para la clase [{0}]

sslUtilBase.alias_no_key_entry=El nombre de Alias [{0}] no identifica una entrada de clave
sslUtilBase.invalidTrustManagerClassName=El trustManagerClassName suministrado [{0}] no implementa  javax.net.ssl.TrustManager
sslUtilBase.keystore_load_failed=No pude cargar almacén de claves de tipo [{0}] con ruta [{1}] debido a [{2}]
sslUtilBase.noneSupported=Ninguno de los  [{0}] especificados es soportado por el motor SSL : [{1}]
sslUtilBase.ssl3=SSLv3 ha sido explicitamente habilitado. Se conoce que este protocolo es inseguro
sslUtilBase.trustedCertNotValid=El certificado confiable con alias [{0}] y DN [{1}] no es válido debido a [{2}]. Los certificados firmados por este certificados confiable SERAN aceptados\n
