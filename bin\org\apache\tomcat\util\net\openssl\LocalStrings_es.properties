# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

engine.emptyCipherSuite=Suite de cifrado vacía
engine.engineClosed=El notor esta cerrado
engine.noSession=El identificador de la sesión SSL no está disponible
engine.openSSLError=Error de OpenSSL: [{0}] mensage: [{1}]\n
engine.writeToSSLFailed=Fallo al escribir hacia SSL, resultado: [{0}]

openssl.addedClientCaCert=Ceritifcado CA de cliente adicionado: [{0}]
openssl.keyManagerMissing=No se encontró gerente de llave
openssl.trustManagerMissing=No se encontró un manejador confiable

opensslconf.checkFailed=Fallo mientras se chequeaba OpenSSLConf\n
