# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

channelCoordinator.alreadyStarted=채널이 이미 레벨 [{0}](으)로 시작되었습니다.
channelCoordinator.invalid.startLevel=유요하지 않은 시작 레벨입니다. 유효한 레벨은 SND_RX_SEQ, SND_TX_SEQ, MBR_TX_SEQ, MBR_RX_SEQ입니다.

groupChannel.listener.alreadyExist=리스너가 이미 존재합니다: [{0}][{1}]
groupChannel.noDestination=그룹 채널에서 전송 대상 멤버가 없습니다.
groupChannel.nullMessage=널 메시지를 전송할 수 없습니다.
groupChannel.optionFlag.conflict=인터셉터 옵션 플래그가 충돌합니다: [{0}]
groupChannel.receiving.error=메시지 수신 중 오류 발생
groupChannel.sendFail.noRpcChannelReply=RPC 채널을 찾을 수 없습니다. NoRpcChannelReply을 보내지 못했습니다.
groupChannel.unable.deserialize=메시지를 역직렬화할 수 없습니다:[{0}]
groupChannel.unable.sendHeartbeat=Tribes 인터셉터 스택을 통해 heartbeat를 보낼 수 없습니다. 다시 sleep을 시도할 것입니다.

rpcChannel.replyFailed=RpcChannel에서 응답을 되돌려 보낼 수 없습니다.
