# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

coyoteAdapter.accesslogFail=アクセスログにエントリを追加する際の例外
coyoteAdapter.asyncDispatch=非同期リクエストの処理中の例外
coyoteAdapter.authenticate=コネクターから認証済みユーザー [{0}] を取得しました。
coyoteAdapter.authorize=Tomcat のRealmでユーザー [{0}] を認証します。
coyoteAdapter.checkRecycled.request=リサイクルされていないリクエストに遭遇しました。強制的にリサイクルしました。
coyoteAdapter.checkRecycled.response=リサイクルされていないレスポンスが発生、強制的にリサイクルされました。
coyoteAdapter.debug=変数[{0}]に値[{1}]があります
coyoteAdapter.nullRequest=非同期ディスパッチは、既存のリクエストでのみ発生する可能性があります

coyoteConnector.invalidEncoding=[{0}] は JRE の理解できない符号化方式です。Connector は [{1}] で処理を続行します。
coyoteConnector.invalidPort=[{0}]の指定されたポート値が無効であるため、コネクタを開始できません
coyoteConnector.parseBodyMethodNoTrace=TRACE メソッドのリクエストはエンティティを含めることはできません (RFC 2616 の 9.6 節を参照)。
coyoteConnector.protocolHandlerDestroyFailed=プロトコルハンドラの廃棄に失敗しました
coyoteConnector.protocolHandlerInitializationFailed=プロトコルハンドラの初期化に失敗しました
coyoteConnector.protocolHandlerInstantiationFailed=プロトコルハンドラのインスタンス化に失敗しました
coyoteConnector.protocolHandlerNoAprLibrary=構成されたプロトコル[{0}]には使用できないAPR/nativeライブラリが必要です。
coyoteConnector.protocolHandlerNoAprListener=構成されたプロトコル[{0}]には、使用できないAprLifecycleListenerが必要です。
coyoteConnector.protocolHandlerPauseFailed=プロトコルハンドラの一時停止に失敗しました
coyoteConnector.protocolHandlerResumeFailed=プロトコルハンドラの再開に失敗しました
coyoteConnector.protocolHandlerStartFailed=プロトコルハンドラの起動に失敗しました
coyoteConnector.protocolHandlerStopFailed=プロトコルハンドラの停止に失敗しました。

coyoteInputStream.nbNotready=ノンブロッキングモードでは、以前の読み取りが完了して isReady() が true を返すまで、ServletInputStream から読み取りできません。

coyoteOutputStream.nbNotready=ノンブロッキングモードでは直前の書き込みが完了し isReady() が true を返すまで ServletOutputStream への書き込みはできません。

coyoteRequest.alreadyAuthenticated=認証済みのリクエストです。
coyoteRequest.attributeEvent=属性イベントリスナによって例外が投げられました
coyoteRequest.authenticate.ise=レスポンスをコミットした後は authenticate() を呼び出すことはできません。
coyoteRequest.changeSessionId=セッション ID は変更できません。リクエストに関連付けられたセッションがありません。
coyoteRequest.chunkedPostTooLarge=POST データが大きすぎるためパラメーターを解析しませんでした。リクエストはチャンク化されているため、処理を継続できません。アプリケーションが大きな POST データを受信する必要があるなら、Connector 要素の maxPostSize 属性を変更してください。
coyoteRequest.filterAsyncSupportUnknown=非同期処理をサポートしていないFilterがあるかどうかを判断できません
coyoteRequest.getContextPath.ise=標準的なコンテキストパス[{0}]とユーザーエージェント[{1}]によって提示されたURIとの一致が見つかりません。
coyoteRequest.getInputStream.ise=getReader()はこのリクエストに対して既に呼び出されています
coyoteRequest.getReader.ise=getInputStream()はこのリクエストに対して既に呼び出されています
coyoteRequest.gssLifetimeFail=ユーザープリンシパル [{0}] の残りの寿命(秒単位) を取得できません。
coyoteRequest.maxPostSizeExceeded=マルチパートリクエストには、関連付けられたコネクタで設定されたmaxPostSizeの制限を超えたパラメータデータ（アップロードされたファイルを除く）が含まれていました。
coyoteRequest.noAsync=処理チェーン内の次のクラスが非同期をサポートしていないため、非同期を開始できません。[{0}]
coyoteRequest.noMultipartConfig=multi-part 構成が提供されていないため、partを処理することができません
coyoteRequest.parseParameters=POST パラメーターの処理中に例外を投げました。
coyoteRequest.postTooLarge=POSTされたデータが大きすぎたので、パラメータが構文解析できませんでした。そのアプリケーションが巨大なPOSTを受け付けねばならない場合には、これを解決するためにコネクタのmaxPostSize属性を使用してください。
coyoteRequest.sendfileNotCanonical=sendfile に指定されたファイル [{0}] の正式名を取得できません。
coyoteRequest.sessionCreateCommitted=レスポンスをコミットした後でセッションを作成できません
coyoteRequest.sessionEndAccessFail=リクエストの再利用中に行ったセッションへのアクセス終了処理で例外が送出されました。
coyoteRequest.setAttribute.namenull=setAttributeを名前を指定せずに呼び出すことはできません
coyoteRequest.uploadCreate=サーブレット[{1}]に必要な一時アップロード場所[{0}]を作成します。
coyoteRequest.uploadCreateFail=アップロード場所[{0}]の作成に失敗しました。
coyoteRequest.uploadLocationInvalid=一時的なアップロード場所[{0}]は無効です

coyoteResponse.encoding.invalid=JRE は文字エンコーディング [{0}] を理解しません。
coyoteResponse.getOutputStream.ise=getWriter()はこのレスポンスに対して既に呼び出されています
coyoteResponse.getWriter.ise=getOutputStream()はこのレスポンスに対して既に呼び出されています
coyoteResponse.reset.ise=レスポンスがコミットされた後でreset()を呼び出すことができません。
coyoteResponse.resetBuffer.ise=レスポンスがコミットされた後でバッファをリセットすることはできません
coyoteResponse.sendError.ise=レスポンスがコミットされた後でsendError()を呼び出すことはできません
coyoteResponse.sendRedirect.ise=レスポンスがコミットされた後でsendRedirect()を呼び出すことはできません
coyoteResponse.sendRedirect.note=<html> <body> <p> <a href="{0}"> {0} </a>へのリダイレクト</ p> </ body> </ html>
coyoteResponse.setBufferSize.ise=データが既に書き込まれた後でバッファサイズを変更することはできません

inputBuffer.requiresNonBlocking=ノンブロッキングモードでは利用できません。
inputBuffer.streamClosed=ストリームはクローズしています

outputBuffer.writeNull=write(String, int, int) メソッドの String 型の引数に null を指定できません。

request.asyncNotSupported=現在のチェーンのフィルタまたはサーブレットは非同期操作をサポートしていません。
request.fragmentInDispatchPath=ディスパッチパス [{0}] 中のフラグメントは除去されました
request.illegalWrap=リクエストラッパーは getRequest() で取得したリクエストをラップしなければなりません。
request.notAsync=非同期モードではないリクエストでこのメソッドを呼び出すことはできません。(例えば isAsyncStarted() が false を返す場合)

requestFacade.nullRequest=リクエストオブジェクトは回収されこのファサードに関連付けられなくなりました。

response.illegalWrap=レスポンスラッパーは、getResponse()から取得したレスポンスをラップする必要があります。
response.sendRedirectFail=[{0}] へのリダイレクトが失敗しました。

responseFacade.nullResponse=レスポンスオブジェクトはすでに回収されたためこのファサードとは関連付けがありません。
