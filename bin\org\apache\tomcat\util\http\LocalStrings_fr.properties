# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cookies.fallToDebug=\n\
\ Note : les occurrences suivantes d'erreurs de Cookies seront enregistrées au niveau DEBUG.
cookies.invalidCookieToken=Cookie non valide. Sa valeur n'est ni un "token" ni une valeur entre guillemets
cookies.invalidSameSiteCookies=Valeur inconnue [{0}], seules possibles : unset, none, lax, strict. Valeur par défaut : unset.
cookies.invalidSpecial=Cookie spécial inconnu
cookies.maxCountFail=Le nombre maximum de cookies [{0}] est dépassé

headers.maxCountFail=Le nombre d''en-têtes [{0}] dépasse le maximum autorisé

parameters.bytes=Début du traitement avec les données [{0}]
parameters.copyFail=Echec de la copie des valeurs de paramètres originaux pour raisons de journalisation du déboguage
parameters.decodeFail.debug=Echec de décodage de caractère, le paramètre [{0}] de valeur [{1}] a été ignoré
parameters.decodeFail.info=Echec de décodage de caractère, le paramètre [{0}] avec la valeur [{1}] a été ignoré ; le nom et la valeur mentionnés ici peuvent avoir été corrompus à cause de l''erreur de décodage, utilisez le niveau debug pour voir les originaux
parameters.emptyChunk=Le bloc de paramètres vide a été ignoré
parameters.fallToDebug=\n\
\ Note : les occurrences suivantes d'erreurs de Paramètres seront enregistrées au niveau DEBUG.
parameters.invalidChunk=Morceau (chunk) invalide démarrant à l''octet [{0}] et se terminant à l''octet [{1}] avec une valeur de [{2}] ignoré
parameters.maxCountFail=Le nombre maximum de paramètres pour une seule requête (GET plus POST) [{0}] a été détecté, les paramètres supplémentaires ont été ignorés ; l''attribut maxParameterCount du Connector permet de changer cette limite
parameters.maxCountFail.fallToDebug=\n\
\ Note : les occurrences suivantes de cette erreur seront enregistrées au niveau DEBUG.
parameters.multipleDecodingFail=Echec de décodage de caractère, [{0}] erreurs ont été détectées au total mais seule la première a été logguée, activez le niveau debug pour avoir toutes les erreurs
parameters.noequal=Le paramètre qui démarre à la position [{0}] et qui se termine à la position [{1}] avec comme valeur [{2}] n''est pas suivi par un caractère ''=''

rfc6265CookieProcessor.invalidCharInValue=Un caractère invalide [{0}] était présent dans la valeur du cookie
rfc6265CookieProcessor.invalidDomain=Un domaine [{0}] invalide a été spécifié pour ce cookie
rfc6265CookieProcessor.invalidPath=Un chemin (path) invalide [{0}] a été spécifié pour ce biscuit (cookie)
