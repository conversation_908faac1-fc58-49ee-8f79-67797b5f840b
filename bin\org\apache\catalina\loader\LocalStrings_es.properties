# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

webappClassLoader.checkThreadLocalsForLeaks=La aplicación web [{0}] creó un ThreadLocal con clave del tipo [{1}] (valor [{2}]) y un valor del tipo [{3}] (valor [{4}]) pero no pudo quitarlo cuando la aplicación web se paró. Los hilos se van a renovar con el tiempo para intentar evitar in posible fallo de memoria.
webappClassLoader.checkThreadLocalsForLeaks.badKey=No puedo determinar la representación de cadena de la clave del tipo  [{0}]
webappClassLoader.checkThreadLocalsForLeaks.badValue=No puedo determinar la representación de cadena del valor del tipo  [{0}]
webappClassLoader.checkThreadLocalsForLeaks.unknown=Desconocido
webappClassLoader.checkThreadLocalsForLeaksFail=No pude revisar las referencias a ThreadLocal para la aplicación web  [{0}]
webappClassLoader.checkThreadLocalsForLeaksNull=La aplicación web [{0}] creó un ThreadLocal con clave del tipo [{1}] (valor [{2}]). El Threadlocal ha sido puesto correctamente a nulo y la clave será qutada por el GC.
webappClassLoader.checkThreadsHttpClient=Hallado hilo keep-alive de HttpClient usando cargador de clase de aplicación web. Fijado por el hilo de conmutación al cargador de la clase padre.
webappClassLoader.clearJdbc=La aplicación web [{0}] registró el conductor JDBC [{1}] pero falló al anular el registro mientras la aplicación web estaba parada. Para prevenir un fallo de memoria, se ha anulado el registro del conductor JDBC por la fuerza.
webappClassLoader.clearRmiFail=No pude limpiar el cargador del contexto de clase referenciado desde sun.rmi.transport.Target para la aplicación web [{0}]
webappClassLoader.clearRmiInfo=No pude hallar la clase sun.rmi.transport.Target para limpiar el cargador de contexto de clase para la aplicación web [{0}]. Esto es lo esperado em máquinas que no son de Sun.
webappClassLoader.jarsRemoved=Uno o más JARs han sido eliminados de la aplicación web [{0}]\n
webappClassLoader.jdbcRemoveFailed=Ha fallado el desregistro del conductor JDBC para la aplicación web [{0}]
webappClassLoader.readError=Error de lectura de recurso: No pude cargar [{0}].
webappClassLoader.stopThreadFail=No pude terminar el hilo con nombre [{0}] para la aplicación web [{1}]
webappClassLoader.stopTimerThreadFail=No pude terminar TimerThread con nombre [{0}] para la aplicación web [{1}]
webappClassLoader.stopped=Acceso ilegal: esta instancia de aplicación web ya ha sido parada.  Could not load [{0}].  La eventual traza de pila que sigue ha sido motivada por un error lanzado con motivos de depuración así como para intentar terminar el hilo que motivó el acceso ilegal y no tiene impacto funcional.
webappClassLoader.superCloseFail=Fallo a llamar close() en la clase super
webappClassLoader.warnTimerThread=La aplicación web [{0}] parece haber arrancado un TimerThread con nombre [{1}] vía de la API java.util.Timer, pero no ha podido pararlo. Para prevenir un fallo de memoria, el cronómetro (y el hilo asociado) hasido cancelado a la fuerza.
webappClassLoader.wrongVersion=(no puedo cargar clase [{0}])

webappLoader.deploy=Desplegando repositorios de clase en directorio de trabajo [{0}]
webappLoader.reloadable=No puedo poner la propiedad recargable a [{0}]
webappLoader.setContext.ise=No esta permitido fijar el Contexto mientras el cargador esta iniciado.\n
webappLoader.starting=Arrancando este Cargador
webappLoader.stopping=Parando este Cargador
