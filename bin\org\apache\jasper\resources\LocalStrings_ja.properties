# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jasper.error.emptybodycontent.nonempty=TLDによると、タグ [{0}] は空である必要がありますがそうなっていません

jsp.engine.info=Jasper JSP {0}エンジン
jsp.error.action.isnottagfile=[{0}] アクションはタグファイル中でのみ使用できます
jsp.error.action.istagfile=[{0}] アクションはタグファイル中で使用できません
jsp.error.attempt_to_clear_flushed_buffer=エラー: 既にフラッシュされているバッファをクリアしようとしました
jsp.error.attr.quoted=属性値は引用符で囲わなければいけません
jsp.error.attribute.custom.non_rt_with_expr=TLD又はタグファイル中のattributeディレクティブによると、属性[{0}]はいかなる式も受け付けません
jsp.error.attribute.deferredmix=属性値に ${} と #{} のEL式を同時に含めることはできません。
jsp.error.attribute.duplicate=属性修飾名は、要素内で一意でなければなりません
jsp.error.attribute.invalidPrefix=属性のプレフィックス [{0}] はインポートされたいずれのタグライブラリにも対応しません
jsp.error.attribute.noequal=等号記号が必要です
jsp.error.attribute.noescape=属性値[{0}]は[{1}]で引用され、値の中で使用する際はエスケープする必要があります。
jsp.error.attribute.noquote=引用符が必要です
jsp.error.attribute.nowhitespace=JSP の仕様により、属性名の前に空白が必要です。
jsp.error.attribute.null_name=空の属性名です
jsp.error.attribute.standard.non_rt_with_expr=[{1}] 標準アクションの [{0}] 属性はどんな式も受け付けません
jsp.error.attribute.unterminated=[{0}] の属性が正しく終了していません
jsp.error.bad.scratch.dir=あなたが指定したscratchDir: [{0}] は使用できません
jsp.error.badStandardAction=無効な標準アクションです
jsp.error.bad_attribute=TLDによると、タグ [{1}] の属性 [{0}] は無効です
jsp.error.bad_tag=プレフィックス [{1}]でインポートされたタグライブラリには、タグ [{0}] は存在しません
jsp.error.beans.nomethod=タイプ [{1}] のBean中の属性 [{0}] を読み込むメソッドを発見できませんでした
jsp.error.beans.nomethod.setproperty=タイプ[{2}]のBeanのタイプ [{1}] の属性 [{0}] を書き込むメソッドを発見できませんでした
jsp.error.beans.noproperty=タイプ [{1}] のbean中のプロパティ [{0}] の情報を発見できませんでした
jsp.error.beans.nullbean=nullオブジェクトにBean操作をおこなおうとしました
jsp.error.beans.property.conversion=属性[{2}]：[{3}]の文字列[{0}]をクラス[{1}]に変換できません。
jsp.error.beans.propertyeditor.notregistered=PropertyEditorManagerに登録されていないプロパティエディタ
jsp.error.beans.setproperty.noindexset=インデックス付きのプロパティを設定できません
jsp.error.bug48498=JSP抽出を表示できません。 XMLパーサーのバグが原因である可能性があります（詳細は、Tomcatバグ48498を参照してください）。
jsp.error.classname=.classファイルからクラス名を決定できません
jsp.error.coerce_to_type=属性[{0}]の値[{2}]をタイプ[{1}]に変換できません。
jsp.error.compilation=ファイルのコンパイル中にエラーが発生しました：[{0}] [{1}]
jsp.error.compiler=Java コンパイラが見つかりません。
jsp.error.compiler.config=設定オプション compilerClassName：[{0}]およびコンパイラ：[{1}]のJavaコンパイラは利用可能ではありません。
jsp.error.config_pagedir_encoding_mismatch=jsp-property-group中に指定されているPage-encoding [{0}] がpage指示子中の指定 [{1}] と違います
jsp.error.corresponding.servlet=生成されたサーブレットエラー:\n\
\n
jsp.error.could.not.add.taglibraries=1つ以上のタグライブラリを追加できません
jsp.error.data.file.processing=ファイル[{0}]を処理中のエラー
jsp.error.data.file.read=ファイル [{0}] を読み込み中にエラーが発生しました
jsp.error.data.file.write=データファイルを書き込み中のエラー
jsp.error.deferredmethodandvalue='deferredValue' と 'deferredMethod' は同時に 'true' にできません。
jsp.error.deferredmethodsignaturewithoutdeferredmethod='deferredMethod' が 'true' でない場合、メソッドシグネチャを指定できません。
jsp.error.deferredvaluetypewithoutdeferredvalue='deferredValue'が 'true'でない場合は値型を指定できません
jsp.error.directive.isnottagfile=[{0}] ディレクティブはタグファイル中でしか使用できません
jsp.error.directive.istagfile=[{0}] ディレクティブはタグファイル中では使用できません
jsp.error.duplicate.name.jspattribute=標準又はカスタムアクション中で指定されている属性 [{0}] はそれに囲まれたjsp:attribute中のname属性の値としても表れます
jsp.error.duplicateqname=重複した修飾名[{0}]を持つ属性が見つかりました。 属性修飾名は、要素内で一意でなければなりません。
jsp.error.dynamic.attributes.not.implemented=[{0}] タグはそれがdynamic属性を受け付けると宣言していますが、それに必要なインタフェースを実装していません
jsp.error.el.parse=[{0}] : [{1}]
jsp.error.el.template.deferred=テンプレート文字列に #{...} を含めることはできません。
jsp.error.el_interpreter_class.instantiation=ELInterpreter class [{0}] のロード又はインスタンス化に失敗しました
jsp.error.fallback.invalidUse=jsp:fallbackはjsp:pluginの直接の子でなければいけません
jsp.error.file.already.registered=ファイル [{0}] の再帰的な取り込みです
jsp.error.file.cannot.read=ファイルが読めません: [{0}]
jsp.error.file.not.found=JSP ファイル [{0}] が見つかりません
jsp.error.flush=データをフラッシュする際に例外が発生しました。
jsp.error.fragmentwithtype=''fragment''属性と''type''属性を両方指定できません。''fragment''が存在する場合には''type''は''{0}''に固定されます
jsp.error.function.classnotfound=TLDの中で関数 [{1}] に指定されているクラス [{0}] が見つかりません: [{2}]
jsp.error.include.exception=[{0}] を include 出来ません
jsp.error.include.tag=無効なjsp:includeタグです
jsp.error.internal.filenotfound=内部エラー: ファイル [{0}] が見つかりません
jsp.error.invalid.attribute=[{0}]は無効な属性を持っています: [{1}]
jsp.error.invalid.bean=useBeanのクラス属性 [{0}] の値が無効です
jsp.error.invalid.directive=無効なディレクティブ
jsp.error.invalid.expression=[{0}] は無効な式を含んでいます: [{1}]
jsp.error.invalid.implicit=[{0}]のタグファイルの暗黙のTLDが無効です
jsp.error.invalid.implicit.version=[{0}]のタグファイルの暗黙のTLDで無効なJSPバージョンが定義されています。
jsp.error.invalid.scope=''scope''属性の値が無効です: [{0}] ("page"、"request"、"session"又は"application"のどれかでなければいけません)
jsp.error.invalid.tagdir=タグファイルディレクトリ [{0}] が"/WEB-INF/tags"で始まっていません
jsp.error.invalid.version=タグファイル [{0}] には不正な JSP のバージョンが指定されています。
jsp.error.ise_on_clear=バッファサイズが0の時にclear()を実行しても無効です
jsp.error.java.line.number=生成されたJavaファイル：[{1}]の[{0}]行でエラーが発生しました。
jsp.error.javac=Javac 例外
jsp.error.javac.env=Environment:
jsp.error.jspbody.emptybody.only=[{0}] タグは、そのボディ中にjsp:attributeだけを持つことができます
jsp.error.jspbody.invalidUse=jsp:bodyは標準又はカスタムアクションの副要素でなければいけません
jsp.error.jspbody.required=jsp:attributeが使用された場合、[{0}]にタグボディを指定するためにjsp:bodyを使用する必要があります
jsp.error.jspc.missingTarget=ターゲットがありません: -webapp又は-uriroot、又は一つ以上のJSPページを指定しなければいけません
jsp.error.jspc.no_uriroot=urirootが指定されていないので、指定されたJSPファイル(群)を配置できません
jsp.error.jspc.uriroot_not_dir=-uriroot オプションは、既に存在するディレクトリを指定しなければいけません
jsp.error.jspelement.missing.name=必須のXMLスタイルの'name'属性がjsp:element中にありません
jsp.error.jspoutput.conflict=&lt;jsp:output&gt;: [{0}]に異なる値を複数回指定するのは無効です (旧: [{1}], 新: [{2}])
jsp.error.jspoutput.doctypenamesystem=&lt;jsp:output&gt;: 'doctype-root-element' 及び 'doctype-system' 属性は同時に指定しなければいけません
jsp.error.jspoutput.doctypepublicsystem=&lt;jsp:output&gt;: 'doctype-public'属性を指定する場合は、'doctype-system' 属性も指定しなければいけません
jsp.error.jspoutput.invalidUse=&lt;jsp:output&gt; 標準構文の中で使用してはいけません
jsp.error.jspoutput.nonemptybody=&lt;jsp:output&gt; ボディを持ってはいけません
jsp.error.jsproot.version.invalid=無効なバージョン番号です: [{0}]、"1.2"　"2.0"　"2.1"　"2.2" 又は "2.3"　でなければいけません
jsp.error.jsptext.badcontent='&lt;'が&lt;jsp:text&gt;のボディの中に現れる時は、CDATAの中にカプセル化する必要があります
jsp.error.lastModified=ファイル [{0}] の最終更新日時を取得できません。
jsp.error.library.invalid=ライブラリ[{0}]に従うとJSPページは無効です: [{1}]
jsp.error.literal_with_void=戻り値の型がvoidである遅延メソッドとして定義されている属性[{0}]にリテラル値が指定されています。 JSP.2.3.4では、この場合リテラル値は許可されません。
jsp.error.loadclass.taghandler=タグ [{1}] のタグハンドラクラス [{0}] をロードできません
jsp.error.location=行：[{0}]、列：[{1}]
jsp.error.mandatory.attribute=[{0}]: 必須属性 [{1}] がありません
jsp.error.missing.tagInfo=[{0}] に対するTagInfoオブジェクトがTLDから失われました
jsp.error.missing_attribute=TLD又はタグファイルによると、属性 [{0}] はタグ [{1}] には必須です
jsp.error.missing_var_or_varReader='var'又は'varReader'属性がありません
jsp.error.namedAttribute.invalidUse=jsp:attributeは標準又はカスタムアクションの副要素でなければいけません
jsp.error.needAlternateJavaEncoding=デフォルトのJavaエンコーディング [{0}] はあなたのプラットフォームでは無効です。JspServletの ''javaEncoding'' パラメータで別の値を指定することができます。
jsp.error.nested.jspattribute=jsp:attribute標準アクションは別のjsp:attribute標準アクションの範囲内でネストすることはできません
jsp.error.nested.jspbody=jsp:body標準アクションは別のjsp:body又はjsp:attribute標準アクションの範囲内でネストすることはできません
jsp.error.nested_jsproot=入れ子になった &lt;jsp:root&gt; です
jsp.error.no.more.content=必要な解析中に内容の最後まで達しました: タグのネストのエラーかもしれません
jsp.error.no.scratch.dir=JSPエンジンにデフォルトのscratchDirが設定されていません。\n\
\ このコンテキストのservlets.propertiesファイルに、\n\
\ "jsp.initparams=scratchdir=<dir-name>" を追加してください。
jsp.error.no.scriptlets=スクリプティング要素 ( &lt;%!、&lt;jsp:declaration、&lt;%=、&lt;jsp:expression、&lt;%、&lt;jsp:scriptlet ) はここでは許されません
jsp.error.noFunction=関数 [{0}] を指定されたプリフィクスで配置できません
jsp.error.noFunctionMethod=関数 [{1}] のメソッド [{0}] が クラス[{2}] 中で見つかりません。
jsp.error.non_null_tei_and_var_subelems=タグ [{0}] は一つ以上のvariable副要素と一つ以上のVariableInfoを返すTagExtraInfoクラスを持っています
jsp.error.not.in.template=テンプレートテキストボディ中では [{0}] は許されません
jsp.error.outputfolder=出力フォルダがありません
jsp.error.overflow=エラー: JSPバッファがオーバーフローしました
jsp.error.page.conflict.autoflush=Pageディレクティブ: ''autoFlush''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.page.conflict.buffer=Pageディレクティブ: ''buffer''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.page.conflict.contenttype=Pageディレクティブ: ''contentType''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.page.conflict.deferredsyntaxallowedasliteral=Pageディレクティブ:  "deferredSyntaxAllowedAsLiteral" を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.page.conflict.errorpage=Pageディレクティブ: ''errorPage''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.page.conflict.extends=Pageディレクティブ: ''extends''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.page.conflict.info=Pageディレクティブ: ''info''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.page.conflict.iselignored=Pageディレクティブ: ''isELIgnored''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.page.conflict.iserrorpage=Pageディレクティブ: ''isErrorPage''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.page.conflict.isthreadsafe=Tagディレクティブ:  ''isThreadSafe''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.page.conflict.language=Pageディレクティブ: ''language''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.page.conflict.session=Pageディレクティブ: ''session''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.page.conflict.trimdirectivewhitespaces=Pageディレクティブ: ''trimDirectiveWhitespaces''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.page.invalid.buffer=Pageディレクティブ: buffer属性の値が無効です
jsp.error.page.invalid.deferredsyntaxallowedasliteral=Pageディレクティブ: deferredSyntaxAllowedAsLiteral の値は不正です
jsp.error.page.invalid.import=Pageディレクティブ：importの値が無効です
jsp.error.page.invalid.iselignored=Pageディレクティブ: isELIgnored の値は不正です
jsp.error.page.invalid.iserrorpage=Pageディレクティブ: isErrorPage属性の値が無効です
jsp.error.page.invalid.isthreadsafe=Pageディレクティブ: isThreadSafeの値が無効です
jsp.error.page.invalid.session=Pageディレクティブ: session属性の値が無効です
jsp.error.page.invalid.trimdirectivewhitespaces=Pageディレクティブ：trimDirectiveWhitespacesの値が無効です
jsp.error.page.language.nonjava=Pageディレクティブ: 無効なlanguage属性です
jsp.error.page.multi.pageencoding=Pageディレクティブは複数のpageencodingを持つことはできません
jsp.error.page.noSession=セッションに加わっていないページの中ではセッションスコープにアクセスできません
jsp.error.param.invalidUse=jsp:include、jsp:forward、又はjsp:params要素の外でjsp:paramアクションを使用してはいけません
jsp.error.paramexpected="name"属性 と "value" 属性を持つ "jsp:param" 標準アクションが必要です
jsp.error.params.emptyBody=jsp:paramsは少なくとも一つのネストしたjsp:paramを含まねばいけません
jsp.error.params.invalidUse=jsp:paramsはjsp:pluginの直接の子でなければいけません
jsp.error.parse.error.in.TLD=タグライブラリ記述子 [{0}] 中の解析エラーです
jsp.error.parse.xml=ファイル[{0}]のXML解析中のエラー
jsp.error.parse.xml.line=ファイル[{0}]のXML解析エラー: (行 [{1}], 列 [{2}])
jsp.error.parse.xml.scripting.invalid.body=[{0}] 要素のボディはXML要素を含んではいけません
jsp.error.plugin.badtype=jsp:pluginの 'type'属性の値が無効です: 'bean'又は'applet'でなければいけません
jsp.error.plugin.nocode=jsp:pluginでcode属性が宣言されていません
jsp.error.plugin.notype=jsp:pluginでtype属性が宣言されていません
jsp.error.prefix.refined=プリフィックス [{0}] が現在のスコープ中で既に [{2}] と定義されているので [{1}] に再定義しました
jsp.error.prefix.use_before_dcl=このTagディレクティブで指定されているプリフィックス [{0}] は、すでにファイル [{1}] の [{2}] 行目のアクションで使用されています
jsp.error.prolog_config_encoding_mismatch=XML宣言部で指定されたpage-encoding [{0}] がjsp-property-group中の指定と異なります [{1}]
jsp.error.prolog_pagedir_encoding_mismatch=XML宣言部で指定されたpage-encoding [{0}] がpageディレクティブ中の指定 [{1}] と異なっています
jsp.error.quotes.unterminated=引用符が終了していません
jsp.error.scripting.variable.missing_name=属性 [{0}] からスクリプト変数名を決定できません
jsp.error.security=コンテキストのセキュリティの初期化に失敗しました。
jsp.error.servlet.destroy.failed=JSPページのServlet.destroy()中の例外
jsp.error.servlet.invalid.method=JSPではGET、POST、またはHEADのみが許可されます。 JasperはOPTIONSも許可しています。
jsp.error.setLastModified=ファイル[{0}]の最終更新日を設定できません
jsp.error.signature.classnotfound=TLDの中のメソッドシグネチャで関数 [{1}] に指定されているクラス [{0}] が見つかりません。 [{2}]
jsp.error.simpletag.badbodycontent=クラス [{0}] のTLDはSimpleTagに無効なbody-content (JSP)を指定しています
jsp.error.single.line.number=JSPファイル: [{1}] の中の[{0}]行目でエラーが発生しました
jsp.error.stream.close.failed=ストリームをクローズできませんでした。
jsp.error.stream.closed=ストリームがクローズされています
jsp.error.tag.conflict.attr=Tagディレクティブ: 属性[{0}]を異なる値で複数回指定するのは無効です (旧: [{1}], 新: [{2}])
jsp.error.tag.conflict.deferredsyntaxallowedasliteral=Tagディレクティブ: ''deferredSyntaxAllowedAsLiteral''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.tag.conflict.iselignored=Tagディレクティブ: ''isELIgnored''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.tag.conflict.language=Tagディレクティブ: ''language''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.tag.conflict.trimdirectivewhitespaces=Tagディレクティブ: ''trimDirectiveWhitespaces''を異なる値で複数回指定するのは無効です (旧: [{0}], 新: [{1}])
jsp.error.tag.invalid.deferredsyntaxallowedasliteral=Tagディレクティブ: deferredSyntaxAllowedAsLiteral に不正な値が指定されました。
jsp.error.tag.invalid.iselignored=Tagディレクティブ: isELIgnoredに無効な値が指定されています
jsp.error.tag.invalid.trimdirectivewhitespaces=Tagディレクティブ: trimDirectiveWhitespaces の値は不正です
jsp.error.tag.language.nonjava=Tagディレクティブ: 無効なlanguage属性です
jsp.error.tag.multi.pageencoding=Tagディレクティブは複数のpageencodingを持つことはできません
jsp.error.tagdirective.badbodycontent=tagディレクティブ中の無効なbody-content [{0}]です
jsp.error.tagfile.badSuffix=タグファイルパス [{0}] の中に".tag" 拡張子がありません
jsp.error.tagfile.illegalPath=不正なタグファイルパスです: [{0}]、これは"/WEB-INF/tags"又は"/META-INF/tags"で始まらなければいけません
jsp.error.tagfile.missingPath=タグファイルにパスが指定されていません
jsp.error.tagfile.nameFrom.badAttribute=attribute指示子 ([{1}]行目で宣言され、そのname属性が[{0}]、このname-from-attribute属性の値) はjava.lang.String型の"required" で "rtexprvalue".であってはいけません
jsp.error.tagfile.nameFrom.noAttribute=このname-from-attribute属性の値である値 [{0}] のname属性を持つattributeディレクティブが見つかりません
jsp.error.tagfile.nameNotUnique=[{2}]行目の [{0}] の値と [{1}] の値は同じです
jsp.error.taglibDirective.absUriCannotBeResolved=絶対URI:  [{0}] はweb.xmlとこのアプリケーションを配備したJARファイルのどちらかでも解決できません
jsp.error.taglibDirective.both_uri_and_tagdir='uri'属性 と 'tagdir'属性の両方が指定されています
jsp.error.taglibDirective.missing.location=taglib指示子の中に'uri'属性と'tagdir'属性のどちらも指定されていません
jsp.error.taglibDirective.uriInvalid=タグライブラリ [{0}] に不正な URI が指定されました。
jsp.error.tei.invalid.attributes=[{0}] に対するTagExtraInfoからの検証エラーメッセージです
jsp.error.teiclass.instantiation=TagExtraInfo classのロード又はインスタンス化に失敗しました: [{0}]
jsp.error.text.has_subelement=&lt;jsp:text&gt; は副要素を持ってはいけません
jsp.error.tld.fn.duplicate.name=タグライブラリ [{1}] の中の関数名 [{0}] が重複しています
jsp.error.tld.fn.invalid.signature=TLDの中の関数シグネチャに対する無効な構文です。タグライブラリ: [{0}]、関数: [{1}]
jsp.error.tld.invalid_tld_file=無効なtldファイル：[{0}]、詳細はJSP仕様のセクション7.3.1を参照してください。
jsp.error.tld.mandatory.element.missing=TLD [{1}] に必須要素の [{0}] が存在しないか空になっています。
jsp.error.tld.missing=URI [{1}]のtaglib [{0}]が見つかりません
jsp.error.tld.missing_jar=TLDを含むJARリソース [{0}] がありません
jsp.error.tld.unable_to_get_jar=TLDを含むJARリソース [{0}] を取得できません : [{1}]
jsp.error.tlv.invalid.page=[{1}] 中の [{0}] に対するTagLibraryValidatorの検証エラーメッセージです
jsp.error.tlvclass.instantiation=TagLibraryValidatorクラスのロード又はインスタンス化に失敗しました: [{0}]
jsp.error.unable.compile=JSPのクラスをコンパイルできません
jsp.error.unable.deleteClassFile=クラスファイルを削除できません
jsp.error.unable.load=JSPのクラスをロードできません
jsp.error.unable.renameClassFile=クラスファイルの名前を[{0}]から[{1}]に変更できません
jsp.error.unable.to_find_method=属性 [{0}] のsetterメソッドが見つかりません
jsp.error.unavailable=JSPは利用不可とマークされています
jsp.error.unbalanced.endtag=終了タグ "&lt;/{0}" の対応が取れていません
jsp.error.undeclared_namespace=宣言されていない名前空間でカスタムタグが検出されました[{0}]
jsp.error.unknown_attribute_type=属性 [{0}] に対する未知の属性タイプ [{1}] です。
jsp.error.unsupported.encoding=サポートされていないエンコーディングです: [{0}]
jsp.error.unterminated=[{0}] タグが終了していません
jsp.error.usebean.duplicate=useBean: beanName属性が重複しています: [{0}]
jsp.error.usebean.noSession=JSPページが(pageディレクティブにより)セッションに参加しないことを宣言している場合、セッションスコープでのuseBeanの使用は無効です
jsp.error.var_and_varReader='var'又は'varReader'のどちらか一つを指定することができます
jsp.error.variable.alias=name-from-attributeおよびalias属性の両方をvariableディレクティブ中に指定する、又はどちらも指定しないことができます
jsp.error.variable.both.name=variableディレクティブ中でname-givenとname-from-attribute属性の両方を指定することはできません
jsp.error.variable.either.name=name-given又はname-from-attribute属性のどちらかをvariableディレクティブの中で指定する必要があります
jsp.error.xml.badStandardAction=無効な標準アクションです: [{0}]
jsp.error.xml.bad_tag=URI [{1}] に関連付けられたタグライブラリの中にはタグ [{0}] は定義されていません
jsp.error.xml.closeQuoteMissingInTextDecl=テキスト宣言中の[{0}]に続く値の中の最後のクオートがありません
jsp.error.xml.closeQuoteMissingInXMLDecl=XML宣言中の[{0}]に続く値の中の最後のクオートがありません
jsp.error.xml.encodingByteOrderUnsupported=エンコーディング [{0}] に指定されたバイトオーダはサポートされていません
jsp.error.xml.encodingDeclInvalid=無効なエンコーディング名 [{0}] です
jsp.error.xml.encodingDeclRequired=テキスト宣言中にエンコーディング宣言が必要です
jsp.error.xml.eqRequiredInTextDecl=テキスト宣言中で[{0}]の次に'' = ''文字が続かなければいけません
jsp.error.xml.eqRequiredInXMLDecl=XML宣言中で[{0}]の次に'' = '' 文字が続かなければいけません
jsp.error.xml.expectedByte=[{1}]バイトUTF-8シーケンスのバイト [{0}] が必要です
jsp.error.xml.invalidASCII=バイト [{0}] は7ビットASCIIではありません
jsp.error.xml.invalidByte=[{1}]バイトUTF-8シーケンスの無効なバイト [{0}] です
jsp.error.xml.invalidCharInContent=ドキュメントの無効なXML文字 (Unicode: 0x[{0}]) がドキュメントの要素内容の中に見つかりました
jsp.error.xml.invalidCharInPI=無効なXML文字 (Unicode: 0x[{0}]) が命令処理中に見つかりました
jsp.error.xml.invalidCharInTextDecl=テキスト宣言の中に無効なXML文字 (Unicode: 0x[{0}]) が見つかりました
jsp.error.xml.invalidCharInXMLDecl=XML宣言の中に無効なXML文字 (Unicode: 0x[{0}]) が見つかりました
jsp.error.xml.invalidHighSurrogate=UTF-8シーケンスのハイサロゲートビットは0x10を越えてはいけませんが、0x[{0}]が見つかりました
jsp.error.xml.morePseudoAttributes=より多くの疑似属性が必要です
jsp.error.xml.noMorePseudoAttributes=これ以上の疑似属性は許されません
jsp.error.xml.operationNotSupported=[{1}] readerは操作 [{0}] をサポートしていません
jsp.error.xml.pseudoAttrNameExpected=疑似属性名が必要です
jsp.error.xml.quoteRequiredInTextDecl=テキスト宣言中の[{0}]に続く値はクオートで囲まれた文字列でなければいけません
jsp.error.xml.quoteRequiredInXMLDecl=XML宣言中の[{0}]に続く値はクオートで囲まれた文字列でなければいけません
jsp.error.xml.reservedPITarget="[xX][mM][lL]"に一致する処理命令ターゲットは許されていません
jsp.error.xml.sdDeclInvalid=スタンドアロン文書宣言値は"yes"又は"no"のどちらかであり、[{0}]ではいけません
jsp.error.xml.spaceRequiredBeforeEncodingInTextDecl=テキスト宣言のencoding疑似属性の前に空白が必要です
jsp.error.xml.spaceRequiredBeforeEncodingInXMLDecl=XML宣言のencoding疑似属性の前に空白が必要です
jsp.error.xml.spaceRequiredBeforeStandalone=XML宣言のencoding疑似属性の前に空白が必要です
jsp.error.xml.spaceRequiredBeforeVersionInTextDecl=テキスト宣言のversion疑似属性の前に空白が必要です
jsp.error.xml.spaceRequiredBeforeVersionInXMLDecl=XML宣言のversion疑似属性の前に空白が必要です
jsp.error.xml.spaceRequiredInPI=空白が処理命令ターゲットとデータの間に必要です
jsp.error.xml.versionInfoRequired=XML宣言の中にバージョンが必要です
jsp.error.xml.versionNotSupported=XMLバージョン [{0}] はサポートされていません、XML 1.0だけをサポートしています
jsp.error.xml.xmlDeclUnterminated=XML宣言は"?>"で終らなければいけません
jsp.exception=[{0}] の処理中に行番号 [{1}] で例外が発生しました。
jsp.info.ignoreSetting=SecurityManager が有効なため [{1}] の設定 [{0}] を無視しました。
jsp.message.dont.modify.servlets=重要: 生成されたサーブレットを変更してはいけません
jsp.message.jsp_added=コンテキスト[{1}]のキューにパス[{0}]のJSPを追加しています。
jsp.message.jsp_queue_created=コンテキスト[{1}]の長さ[{0}]の作成されたJSPキュー
jsp.message.jsp_queue_update=コンテキスト[{1}]のキューにあるパス[{0}]のJSPを更新しています
jsp.message.jsp_removed_excess=コンテキスト [{1}] のキューからパス [{0}] の JSP を削除しました。
jsp.message.jsp_removed_idle=[{2}]秒後にコンテキスト[{1}]内のパス[{0}]のアイドル状態のJSPを削除します。
jsp.message.jsp_unload_check=コンテキスト [{0}] から開放する JSP をチェックします。JSP の総数: [{1}] キュー長: [{2}]
jsp.message.parent_class_loader_is=親クラスローダ: [{0}]
jsp.message.scratch.dir.is=JSPエンジンのScratchdir: [{0}]
jsp.tldCache.noTldInDir=ディレクトリ [{0}] には TLD ファイルがありません。
jsp.tldCache.noTldInJar=[{0}]にTLDファイルが見つかりませんでした。 CATALINA_BASE/conf/catalina.propertiesファイルのtomcat.util.scan.StandardJarScanFilter.jarsToSkipプロパティにJARを追加することを検討してください。
jsp.tldCache.noTldInResourcePath=リソースパス [{0}] には TLD ファイルがありません。
jsp.tldCache.noTldSummary=少なくとも1つのJARが、まだTLDを含んでいないTLDについてスキャンされました。 スキャンしたが、そこにTLDが見つからなかったJARの完全なリストについては、このロガーのデバッグログを有効にしてください。 スキャン中に不要なJARをスキップすると、起動時間とJSPのコンパイル時間が改善されます。
jsp.tldCache.tldInDir=TLD ファイルをディレクトリ [{0}] で発見しました。
jsp.tldCache.tldInJar=JAR ファイル [{0}] の内部に TLD ファイルを発見しました。
jsp.tldCache.tldInResourcePath=リソースパス[{0}]にTLDファイルが見つかりました。
jsp.warning.bad.urlpattern.propertygroup=web.xml中のurl-pattern副要素中に誤った値 [{0}] があります
jsp.warning.checkInterval=警告: initParam checkIntervalの値は無効です。既定値 "300" 秒が使用されます
jsp.warning.classDebugInfo=警告: initParam の classDebugInfo の値は無効です。既定値 "false" が使用されます
jsp.warning.classpathUrl=クラスパスに無効なURLが見つかりました。 このURLは無視されます。
jsp.warning.compiler.classfile.delete.fail=生成されたクラスファイル[{0}]を削除できませんでした
jsp.warning.compiler.classfile.delete.fail.unknown=生成されたクラスファイルの削除に失敗しました
jsp.warning.compiler.javafile.delete.fail=生成されたJavaファイル[{0}]を削除できませんでした。
jsp.warning.development=警告: initParam の development の値は無効です。既定値 "true" が使用されます
jsp.warning.displaySourceFragment=警告: initParam の displaySourceFragmentの値は無効です。 既定値 "true"を使用します。
jsp.warning.dumpSmap=警告: initParam の dumpSmap の値は無効です。既定値 "false" が使用されます
jsp.warning.enablePooling=警告: initParam の enablePooling の値は無効です。既定値 "true" が使用されます
jsp.warning.fork=警告: initParam の fork の値は無効です。既定値 "true" が使用されます
jsp.warning.genchararray=警告: initParam の genStringAsCharArray の値は無効です。既定値 "false" が使用されます
jsp.warning.jspIdleTimeout=警告: initParam jspIdleTimeoutの値は無効です。既定値 "-1"を使用します。
jsp.warning.keepgen=警告: initParam の keepgenerated の値は無効です。既定値 "false" が使用されます
jsp.warning.mappedFile=警告: initParam の mappedFile の値は無効です。既定値 "false" が使用されます
jsp.warning.maxLoadedJsps=警告: initParam maxLoadedJspsの値は無効です。 既定値 "-1"を使用します。
jsp.warning.modificationTestInterval=警告: initParam の modificationTestInterval の値は無効です。既定値 "4" 秒が使用されます
jsp.warning.noJarScanner=警告: ServletContext に org.apache.tomcat.JarScanner が構成されていません。既定値の JarScanner 実装を使用します。
jsp.warning.quoteAttributeEL=警告: initParam の quoteAttributeEL の値は無効です。既定値 "false" が使用されます
jsp.warning.recompileOnFail=警告: initParam の recompileOnFail の値は無効です。既定値 "false" が使用されます
jsp.warning.strictQuoteEscaping=警告: initParam strictQuoteEscapingの値は無効です。 既定値 "true"を使用します。
jsp.warning.suppressSmap=警告: initParam の suppressSmap の値は無効です。既定値 "false" が使用されます
jsp.warning.tagPreDestroy=[{0}]のタグインスタンスでpreDestroyを処理中のエラー
jsp.warning.tagRelease=[{0}]のタグインスタンス解放処理中のエラー
jsp.warning.unknown.sourceVM=不明な source VM [{0}]が無視されました
jsp.warning.unknown.targetVM=不明な target  VM [{0}]が無視されました。
jsp.warning.unsupported.sourceVM=サポートされていない source VM [{0}] がリクエストされました。[{1}] が使用されます
jsp.warning.unsupported.targetVM=サポートされていない target VM [{0}] がリクエストされました。[{1}] が使用されます
jsp.warning.xpoweredBy=警告: initParam の xpoweredBy の値は無効です。既定値 "false" が使用されます

jspc.delete.fail=ファイル[{0}]を削除できませんでした
jspc.error.fileDoesNotExist=ファイル引数 [{0}] は存在しません。
jspc.error.generalException=エラー: ファイル [{0}] で以下の一般的な例外が発生しました:
jspc.error.invalidFragment=Webフラグメントのエラーによりプリコンパイルが中止されました
jspc.error.invalidWebXml=web.xmlのエラーによりプリコンパイルを中止しました
jspc.generation.result=[{1}]ミリ秒の間に[{0}]個のエラーが発生して生成が完了しました。
jspc.implicit.uriRoot=uriRootはデフォルト[{0}]に設定されます
jspc.usage=使用法: jspc <options> [--] <jsp files>\n\
JSPファイルの場所は次のオプションで指定するか、\n\
\    -webapp <dir>         web-appを含むディレクトリ。すべてのJSPファイルは\n\
\                          再帰的に解析される\n\
又は次の任意の数のファイルで指定します。\n\
\    <file>                JSPとして解析されるファイル\n\
オプションは以下の通りです\n\
\    -help                 このヘルプメッセージの表示\n\
\    -v                    Verboseモード\n\
\    -d <dir>              出力ディレクトリ\n\
\    -l                    失敗したJSPページの名前の出力\n\
\    -s                    成功したJSPページの名前の出力\n\
\    -p <name>             ターゲットパッケージの名前  (デフォルトはorg.apache.jsp)\n\
\    -c <name>             ターゲットクラスの名前 (最初のJSPページだけに適用される)\n\
\    -mapped               JSPの各HTML行ごとにwrite()コールを生成\n\
\    -die[#]               致命的エラーにエラーリターンコード(#)を生成 (デフォルトは1)\n\
\    -uribase <dir>        コンパイルが相対的におこなわれるuriディレクトリ\n\
\                          (デフォルトは"/")\n\
\    -uriroot <dir>        -webappと同じ\n\
\    -compile              生成したサーブレットのコンパイル\n\
\    -failFast             Stop on first compile error\n\
\    -webinc <file>        ファイルに部分的なサーブレットマッピングを作成\n\
\    -webxml <file>        ファイルに完全なweb.xmlを作成\n\
\    -webxmlencoding <enc> Set the encoding charset used to read and write the web.xml\n\
\                          file (default is UTF-8)\n\
\    -addwebxmlmappings    Merge generated web.xml fragment into the web.xml file of the\n\
\                          web-app, whose JSP pages we are processing\n\
\    -ieplugin <clsid>     Internet ExplorerのJava Pluginのclassid\n\
\    -classpath <path>     java.class.pathシステムプロパティの上書き\n\
\    -xpoweredBy           X-Powered-Byレスポンスヘッダの追加\n\
\    -trimSpaces           Remove template text that consists entirely of whitespace\n\
\    -javaEncoding <enc>   Set the encoding charset for Java classes (default UTF-8)\n\
\    -source <version>     Set the -source argument to the compiler (default 1.7)\n\
\    -target <version>     Set the -target argument to the compiler (default 1.7)\n
jspc.webfrg.footer=\n\
</web-fragment>\n\
\n
jspc.webinc.footer=<!--\n\
ここまで、Apache Tomcat JspC による自動生成\n\
-->\n\
\n\
\n
jspc.webinc.header=\n\
<!--\n\
Apache Tomcat JspC による自動生成\n\
-->\n\
\n\
\n
jspc.webinc.insertEnd=<!-- JSPC サーブレットマッピング ここまで -->
jspc.webinc.insertStart=<!-- JSPC サーブレットマッピング ここから -->
jspc.webxml.footer=\n\
</web-app>\n\
\n

org.apache.jasper.compiler.ELParser.invalidQuotesForStringLiteral=不正な文字列リテラル [{0}] です。シングルクォート、あるいは、ダブルクォートで囲まれていなければなりません。
org.apache.jasper.compiler.ELParser.invalidQuoting=式[{0}]は無効です。 引用符で囲まれた文字列内でのみ []、[''] 、["] は[]でエスケープすることができます。
org.apache.jasper.compiler.TldCache.servletContextNull=渡されたServletContextがnullでした
org.apache.jasper.servlet.JasperInitializer.onStartup=コンテキスト[{0}]のJasperを初期化します。
org.apache.jasper.servlet.TldScanner.webxmlAdd=リソースパス[{0}]からURI [{1}]のTLDをロードしています.\n
org.apache.jasper.servlet.TldScanner.webxmlFailPathDoesNotExist=パス[{0}]とURI [{1}]のTLDを処理できませんでした。 指定されたパスは存在しません。
org.apache.jasper.servlet.TldScanner.webxmlSkip=リソース [{0}] について URI [{1}] の TLD の読み込みを回避しました。<jsp-config> で定義済みです。

xmlParser.skipBomFail=入力ストリームの XML を解析する際、BOM の読み飛ばしに失敗しました。
