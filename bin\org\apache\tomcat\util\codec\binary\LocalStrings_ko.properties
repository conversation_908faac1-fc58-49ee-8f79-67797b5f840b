# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

base64.impossibleModulus=불가능한 계수 [{0}]
base64.inputTooLarge=입력 배열이 너무 큽니다. 출력 배열의 크기 [{0}]이(가), 지정된 최대 크기 [{1}] 보다 큰 값입니다.
base64.lineSeparator=행 구분문자 [{0}]은(는) Base64 문자들을 포함해서는 안됩니다.
base64.nullEncodeParameter=정수를 위한 파라미터가 널이라서 인코딩할 수 없습니다.
