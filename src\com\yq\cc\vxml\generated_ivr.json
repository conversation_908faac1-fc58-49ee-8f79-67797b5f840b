{"ivrCode": "vxml_generated_1751615667235", "servicePhoneId": ["generated_phone_id"], "bakup": "", "ivrName": "VXML转换生成的IVR流程", "ivrType": "1", "lineList": [{"line": {"ifSelect": false}, "from": "START_1751615667575", "id": "LINE_1751615668070", "to": "USERTASK_1751615668033"}, {"lx_tj": "neq", "ifSelect": false, "expression": {"param": "USERINPUT", "bakup": "", "val1": "1", "conditions": "neq"}, "line": {"ifSelect": false}, "lx_disable": false, "lx_cs": "USERINPUT", "label": "用户输入其他", "lx_fz": "1", "lx_ms": "", "isqj": false, "from": "SERVICETASK_1751615668233", "id": "LINE_1751615667407", "to": "END_1751615667684"}, {"lx_tj": "timeout", "ifSelect": false, "expression": {"param": "USERINPUT", "bakup": "", "val1": "", "conditions": "timeout"}, "line": {"ifSelect": false}, "lx_disable": false, "lx_cs": "USERINPUT", "label": "超时无输入", "lx_fz": "", "lx_ms": "", "isqj": false, "from": "SERVICETASK_1751615668233", "id": "LINE_1751615667455", "to": "USERTASK_1751615667782"}, {"line": {"ifSelect": false}, "from": "USERTASK_1751615667782", "id": "LINE_1751615668151", "to": "SERVICETASK_1751615668233", "label": "重新收号"}, {"lx_tj": "invalid", "ifSelect": false, "expression": {"param": "USERINPUT", "bakup": "", "val1": "", "conditions": "invalid"}, "line": {"ifSelect": false}, "lx_disable": false, "lx_cs": "USERINPUT", "label": "输入无效", "lx_fz": "", "lx_ms": "", "isqj": false, "from": "SERVICETASK_1751615668233", "id": "LINE_1751615667872", "to": "USERTASK_1751615667726"}, {"line": {"ifSelect": false}, "from": "USERTASK_1751615667726", "id": "LINE_1751615667718", "to": "SERVICETASK_1751615668233", "label": "重新收号"}, {"line": {"ifSelect": false}, "from": "USERTASK_1751615668033", "id": "LINE_1751615667707", "to": "SERVICETASK_1751615668233"}], "icon": "layui-icon-app", "id": "1751615667236", "idxNum": "1", "enableStatus": "4", "nodeList": [{"color": "#23D982", "extensionElements": [], "targetType": "assignee", "type": "start", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "fy_text7": "", "isjiami": "1", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "voicePlayType": "1", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "fy_text10": "", "fy_text50": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "", "jb_value_select": "", "sh_input3": "", "loopCardinality": 0, "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "", "scriptMsg": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "", "sh_value_select": "", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": ""}, "ico": "el-icon-video-play", "top": "93px", "left": "609px", "service": {}, "name": "开始节点", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}], "id": "START_1751615667575", "state": ""}, {"color": "#76CAFB", "formkey": "", "voicePlayNum": "1", "extensionElements": [], "targetType": "assignee", "voicePlayContent": "瀹㈡埛鎮ㄥソ锛岃繖閲屾槸10086鐑嚎锛岃闂偍瀵规垜浠殑鏈嶅姟婊℃剰鍚楋紵", "type": "userTask", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "fy_text7": "", "isjiami": "1", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "voicePlayType": "2", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "fy_text10": "", "fy_text50": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "瀹㈡埛鎮ㄥソ锛岃繖閲屾槸10086鐑嚎锛岃闂偍瀵规垜浠殑鏈嶅姟婊℃剰鍚楋紵", "jb_value_select": "", "sh_input3": "", "loopCardinality": 0, "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "瀹㈡埛鎮ㄥソ锛岃繖閲屾槸10086鐑嚎锛岃闂偍瀵规垜浠殑鏈嶅姟婊℃剰鍚楋紵", "scriptMsg": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "1", "sh_value_select": "", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": ""}, "ico": "el-icon-headset", "top": "220px", "left": "588px", "voicePlayType": "2", "service": {}, "name": "放音1", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}], "style": {}, "id": "USERTASK_1751615668033", "state": "", "voicePlayInterruput": "N"}, {"paramMinSize": "1", "color": "#93BB49", "extensionElements": [], "targetType": "assignee", "paramName": "USERINPUT", "type": "serviceTask", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "fy_text7": "", "isjiami": "1", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "paramFmt": "1", "voicePlayType": "1", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "paramMinSize": "1", "fy_text10": "", "fy_text50": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "", "jb_value_select": "", "sh_input3": "", "loopCardinality": 0, "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "1", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "", "paramName": "USERINPUT", "paramMaxSize": "1", "scriptMsg": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "", "sh_value_select": "USERINPUT", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": "1"}, "paramMaxSize": "1", "paramFmt": "1", "ico": "el-icon-circle-plus-outline", "top": "363px", "left": "643px", "voicePlayType": "1", "service": {}, "name": "用户按键", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}], "id": "SERVICETASK_1751615668233", "state": "", "voicePlayInterruput": "N"}, {"color": "#FF5454", "ico": "el-icon-video-pause", "top": "655px", "left": "594px", "extensionElements": [], "service": {}, "name": "结束节点", "targetType": "assignee", "id": "END_1751615667684", "state": "", "type": "end", "target": ""}, {"color": "#76CAFB", "formkey": "", "voicePlayNum": "1", "extensionElements": [], "targetType": "assignee", "voicePlayContent": "鎶辨瓑锛屾病鏈夋敹鍒版偍鐨勮緭鍏ャ��", "type": "userTask", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "fy_text7": "", "isjiami": "1", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "voicePlayType": "2", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "fy_text10": "", "fy_text50": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "鎶辨瓑锛屾病鏈夋敹鍒版偍鐨勮緭鍏ャ��", "jb_value_select": "", "sh_input3": "", "loopCardinality": 0, "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "鎶辨瓑锛屾病鏈夋敹鍒版偍鐨勮緭鍏ャ��", "scriptMsg": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "1", "sh_value_select": "", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": ""}, "ico": "el-icon-headset", "top": "508px", "left": "822px", "voicePlayType": "2", "service": {}, "name": "错误输入", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}], "style": {}, "id": "USERTASK_1751615667782", "state": "", "voicePlayInterruput": "N"}, {"color": "#76CAFB", "formkey": "", "voicePlayNum": "1", "extensionElements": [], "targetType": "assignee", "voicePlayContent": "鎶辨瓑锛屾偍鐨勮緭鍏ユ棤鏁堛��", "type": "userTask", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "fy_text7": "", "isjiami": "1", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "voicePlayType": "2", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "fy_text10": "", "fy_text50": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "鎶辨瓑锛屾偍鐨勮緭鍏ユ棤鏁堛��", "jb_value_select": "", "sh_input3": "", "loopCardinality": 0, "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "鎶辨瓑锛屾偍鐨勮緭鍏ユ棤鏁堛��", "scriptMsg": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "1", "sh_value_select": "", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": ""}, "ico": "el-icon-headset", "top": "508px", "left": "822px", "voicePlayType": "2", "service": {}, "name": "错误输入", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}], "style": {}, "id": "USERTASK_1751615667726", "state": "", "voicePlayInterruput": "N"}, {"color": "#FF5454", "ico": "el-icon-video-pause", "top": "655px", "left": "594px", "extensionElements": [], "service": {}, "name": "结束节点", "targetType": "assignee", "id": "END_1751615667278", "state": "", "type": "end", "target": ""}], "ivrImg": "/ivr_img/generated.png"}