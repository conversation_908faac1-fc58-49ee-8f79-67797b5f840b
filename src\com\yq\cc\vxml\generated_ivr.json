{"ivrCode": "vxml_generated_1751529395179", "servicePhoneId": ["generated_phone_id"], "bakup": "", "ivrName": "VXML转换生成的IVR流程", "ivrType": "1", "lineList": [{"line": {"ifSelect": false}, "from": "START_1751529395594", "id": "LINE_1751529395541", "to": "USERTASK_1751529395408"}, {"line": {"ifSelect": false}, "from": "USERTASK_1751529395408", "id": "LINE_1751529395956", "to": "USERTASK_1751529395348"}, {"lx_tj": "eq", "ifSelect": false, "expression": {"param": "SERVICE_TASK_RESULT", "bakup": "", "val1": "1", "conditions": "eq"}, "line": {"ifSelect": false}, "lx_disable": false, "lx_cs": "SERVICE_TASK_RESULT", "label": "用户输入1", "lx_fz": "1", "lx_ms": "", "isqj": false, "from": "SERVICETASK_1751529395517", "id": "LINE_1751529396085", "to": "USERTASK_1751529395725"}, {"line": {"ifSelect": false}, "from": "USERTASK_1751529395725", "id": "LINE_1751529395250", "to": "END_1751529395505", "label": "结束流程"}, {"lx_tj": "neq", "ifSelect": false, "expression": {"param": "SERVICE_TASK_RESULT", "bakup": "", "val1": "1", "conditions": "neq"}, "line": {"ifSelect": false}, "lx_disable": false, "lx_cs": "SERVICE_TASK_RESULT", "label": "用户输入其他", "lx_fz": "1", "lx_ms": "", "isqj": false, "from": "SERVICETASK_1751529395517", "id": "LINE_1751529396133", "to": "USERTASK_1751529395348"}, {"line": {"ifSelect": false}, "from": "USERTASK_1751529395348", "id": "LINE_1751529395802", "to": "END_1751529395505", "label": "挂机结束"}, {"line": {"ifSelect": false}, "from": "USERTASK_1751529395348", "id": "LINE_1751529395836", "to": "SERVICETASK_1751529395517"}], "icon": "layui-icon-app", "id": "1751529395180", "idxNum": "1", "enableStatus": "4", "nodeList": [{"color": "#23D982", "extensionElements": [], "targetType": "assignee", "type": "start", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "fy_text7": "", "isjiami": "1", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "voicePlayType": "1", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "fy_text10": "", "fy_text50": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "", "jb_value_select": "", "sh_input3": "", "loopCardinality": 0, "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "", "scriptMsg": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "", "sh_value_select": "", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": ""}, "ico": "el-icon-video-play", "top": "93px", "left": "609px", "service": {}, "name": "开始节点", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}], "id": "START_1751529395594", "state": ""}, {"color": "#76CAFB", "formkey": "", "voicePlayNum": "1", "extensionElements": [], "targetType": "assignee", "voicePlayContent": "瀹㈡埛鎮ㄥソ锛岃繖閲屾槸10086鐑嚎锛岃闂偍瀵规垜浠殑鏈嶅姟婊℃剰鍚楋紵", "type": "userTask", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "fy_text7": "", "isjiami": "1", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "voicePlayType": "2", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "fy_text10": "", "fy_text50": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "瀹㈡埛鎮ㄥソ锛岃繖閲屾槸10086鐑嚎锛岃闂偍瀵规垜浠殑鏈嶅姟婊℃剰鍚楋紵", "jb_value_select": "", "sh_input3": "", "loopCardinality": 0, "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "瀹㈡埛鎮ㄥソ锛岃繖閲屾槸10086鐑嚎锛岃闂偍瀵规垜浠殑鏈嶅姟婊℃剰鍚楋紵", "scriptMsg": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "1", "sh_value_select": "", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": ""}, "ico": "el-icon-headset", "top": "220px", "left": "588px", "voicePlayType": "2", "service": {}, "name": "放音1", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}], "style": {}, "id": "USERTASK_1751529395408", "state": "", "voicePlayInterruput": "N"}, {"color": "#76CAFB", "formkey": "", "voicePlayNum": "1", "extensionElements": [], "targetType": "assignee", "voicePlayContent": "婊℃剰璇锋寜1锛屽惁鍒欒鎸傛満銆�", "type": "userTask", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "fy_text7": "", "isjiami": "1", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "voicePlayType": "2", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "fy_text10": "", "fy_text50": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "婊℃剰璇锋寜1锛屽惁鍒欒鎸傛満銆�", "jb_value_select": "", "sh_input3": "", "loopCardinality": 0, "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "婊℃剰璇锋寜1锛屽惁鍒欒鎸傛満銆�", "scriptMsg": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "1", "sh_value_select": "", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": ""}, "ico": "el-icon-headset", "top": "270px", "left": "638px", "voicePlayType": "2", "service": {}, "name": "放音2", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}], "style": {}, "id": "USERTASK_1751529395348", "state": "", "voicePlayInterruput": "N"}, {"paramMinSize": "1", "color": "#93BB49", "extensionElements": [], "targetType": "assignee", "paramName": "SERVICE_TASK_RESULT", "type": "serviceTask", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "fy_text7": "", "isjiami": "1", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "paramFmt": "1", "voicePlayType": "1", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "paramMinSize": "1", "fy_text10": "", "fy_text50": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "", "jb_value_select": "", "sh_input3": "", "loopCardinality": 0, "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "1", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "", "paramName": "SERVICE_TASK_RESULT", "paramMaxSize": "1", "scriptMsg": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "", "sh_value_select": "SERVICE_TASK_RESULT", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": "1"}, "paramMaxSize": "1", "paramFmt": "1", "ico": "el-icon-circle-plus-outline", "top": "363px", "left": "643px", "voicePlayType": "1", "service": {}, "name": "用户按键", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}], "id": "SERVICETASK_1751529395517", "state": "", "voicePlayInterruput": "N"}, {"color": "#76CAFB", "formkey": "", "voicePlayNum": "1", "extensionElements": [], "targetType": "assignee", "voicePlayContent": "鎰熻阿鎮ㄥ鎴戜滑鐨勮瘎浠凤紝鍐嶈锛�", "type": "userTask", "target": "", "multiInstance": {"zrg_dx": "", "fy_text2": "", "fy_text3": "", "fy_text5": "", "fy_text6": "", "fy_text7": "", "isjiami": "1", "fy_text8": "", "transPhoneType": "", "fy_text40": "", "hs_name": "", "radio": "", "voicePlayType3": "", "voicePlayType2": "", "voicePlayType": "2", "radio3": "", "voicePlayType5": "", "voicePlayType4": "", "radio2": "", "fy_value50": "", "voicePlayInterruput": false, "voicePlayType30": "", "fy_text10": "", "fy_text50": "", "voicePlayContent4": "", "voicePlayContent3": "", "voicePlayContent5": "", "fy_value40": "", "zrg_overtime": "", "zrg_ywdm": "", "zrg_value_select": "", "fy_text": "鎰熻阿鎮ㄥ鎴戜滑鐨勮瘎浠凤紝鍐嶈锛�", "jb_value_select": "", "sh_input3": "", "loopCardinality": 0, "fy_value2": "", "sh_input2": "", "fy_value3": "", "sh_input1": "", "fy_value4": "", "hs_value_select": "", "voicePlayContent2": "", "fy_text20": "", "voicePlayContent": "鎰熻阿鎮ㄥ鎴戜滑鐨勮瘎浠凤紝鍐嶈锛�", "scriptMsg": "", "fy_value": "", "voicePlayType50": "", "fy_value5": "", "fy_value30": "", "voicePlayNum": "1", "sh_value_select": "", "fy_text30": "", "transPhone": "", "zrg_dx_zuoxi": "", "fy_text9": "", "zrg_fs": "", "fy_value20": "", "voicePlayType40": "", "sh_value": ""}, "ico": "el-icon-headset", "top": "512px", "left": "444px", "voicePlayType": "2", "service": {}, "name": "正确输入", "systemConfig": [{"configName": "NOT_WORK_TIME", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_BLACK_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}, {"configName": "NOT_RED_USER", "operaType": "", "voicePlayType": "", "configLevel": "", "voicePlayContent": "", "configValue": "", "operaFuncName": ""}], "style": {}, "id": "USERTASK_1751529395725", "state": "", "voicePlayInterruput": "N"}, {"color": "#FF5454", "ico": "el-icon-video-pause", "top": "655px", "left": "594px", "extensionElements": [], "service": {}, "name": "结束节点", "targetType": "assignee", "id": "END_1751529395505", "state": "", "type": "end", "target": ""}, {"color": "#FF5454", "ico": "el-icon-video-pause", "top": "655px", "left": "594px", "extensionElements": [], "service": {}, "name": "结束节点", "targetType": "assignee", "id": "END_1751529395952", "state": "", "type": "end", "target": ""}], "ivrImg": "/ivr_img/generated.png"}