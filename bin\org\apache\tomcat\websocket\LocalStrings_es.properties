# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

caseInsensitiveKeyMap.nullKey=No se permiten llaves nulas (Null)

perMessageDeflate.duplicateParameter=La definición del parámetro de extención  [{0}] esta duplicada
perMessageDeflate.invalidWindowSize=Una ventana inválida de tamaño  [{1}] fue especificada para [{0}]. Los valores válidos son números entre 8 y 15 incluyendo los extremos.

util.notToken=Un parámetro con extención ilegal fue especificado con nombre [{0}] y valor [{1}]
util.unknownDecoderType=No se reconoce el decodificador tipo [{0}]

wsFrame.closed=Nuevo cuadro recibido luego de cerrar el cuadro de control
wsFrame.notMasked=El cuadro del cliente no fue enmascarado, pero todos los cuadros de clientes deben ser enmascarados
wsFrame.wrongRsv=El rango del cliente fija los bits reservados a [{0}] para un mensaje con opCode [{1}] el cual no fue soportado por este endpoint\n

wsHandshakeRequest.invalidUri=La cadena [{0}] no puede ser usada para construir una URI válida

wsRemoteEndpoint.closed=El mensaje no será enviado porque la sesión WebSocket ha sido cerrada
wsRemoteEndpoint.closedDuringMessage=No se enviara el resto del mensaje debido a que la sesión WebSocket esta cerrada
wsRemoteEndpoint.flushOnCloseFailed=Los mensages de lote estan habilitados aún después de haberse cerrado la sesión. Imposible descartar los messages de lote restantes.
wsRemoteEndpoint.sendInterrupt=El hilo actual fue interrumpido mientras esperaba que se completara un envio de bloqueo
wsRemoteEndpoint.tooMuchData=Ping o pong no pueden enviar más de 125 bytes
wsRemoteEndpoint.wrongState=El endpoint remoto estaba en estado [{0}] el cual es un estado no válido para el método llamado

wsSession.closed=La sesión WebSocket [{0}] ha sido cerrada y ningún otro método (aparte de  close()) puede ser llamado en una sesión cerrada\n
wsSession.created=La sesion WebSocket  [{0}] fue creada\n
wsSession.doClose=Cerrando WebSocket sesión [{1}]
wsSession.duplicateHandlerText=Un manejador de mensaje de texto ya ha sido configurado
wsSession.instanceNew=Falló la registración de la instancia del dispoitivo final

wsWebSocketContainer.missingWWWAuthenticateHeader=Fallo al manejar el código de respuesta HTTP [{0}]. No existe la cabecera WWW-Authenticate en la respuesta
wsWebSocketContainer.pathNoHost=No se especificó ningún host en URI
wsWebSocketContainer.sessionCloseFail=La sesión con ID [{0}] no se cerró correctamente
