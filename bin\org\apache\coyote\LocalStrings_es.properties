# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractConnectionHandler.error=Error leyendo requerimiento, ignorado
abstractConnectionHandler.processorPop=Sacando procesador [{0}] de la cache

abstractProcessor.fallToDebug=\n\
\ Nota: futuras ocurrencias de la solicitud the parseo de errores será logueada con nivel DEBUG.
abstractProcessor.hostInvalid=El host [{0}] no es válido\n
abstractProcessor.httpupgrade.notsupported=La actualización HTTP no esta soportada por esta protocol
abstractProcessor.socket.ssl=Excepción obteniendo atributos SSL

abstractProtocolHandler.init=Inicializando el manejador de protocolo [{0}]\n

asyncStateMachine.invalidAsyncState=Llamando [{0}] no es una solicitud válida en el estado [{1}]\n

response.writeListenerSet=El escuchador de escritura no bloqueable ya ha sido establecido
