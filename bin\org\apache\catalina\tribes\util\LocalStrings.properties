# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

arrays.keyoffset.outOfBounds=keyoffset is out of bounds.
arrays.length.outOfBounds=not enough data elements in the key, length is out of bounds.
arrays.malformed.arrays=byte arrays must be represented as {1,3,4,5,6}
arrays.srcoffset.outOfBounds=srcoffset is out of bounds.

executorFactory.not.running=Executor not running, can't force a command into the queues
executorFactory.queue.full=Queue capacity is full.

uuidGenerator.createRandom=Creation of SecureRandom instance for UUID generation using [{0}] took [{1}] milliseconds.
uuidGenerator.unable.fit=Unable to fit [{0}] bytes into the array. length:[{1}] required length:[{2}]
