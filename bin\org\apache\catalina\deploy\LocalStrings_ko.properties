# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

namingResources.cleanupCloseFailed=컨테이너 [{2}] 내의 리소스 [{1}]을(를) 위한 메소드 [{0}]을(를) 호출하지 못했으므로, 해당 리소스를 위한 cleanup이 수행되지 않았습니다.
namingResources.cleanupCloseSecurity=컨테이너 [{2}]에서 리소스 [{1}]을(를) 위한 메소드 [{0}]을(를) 찾을 수 없어서, 해당 리소스에 대한 cleanup이 수행되지 않았습니다.
namingResources.cleanupNoClose=컨테이너 [{1}]의 리소스 [{0}]은(는) [{2}] 메소드를 가지고 있지 않아, 해당 리소스에 대한 cleanup이 수행되지 않았습니다.
namingResources.cleanupNoContext=컨테이너 [{0}]을(를) 위한 JNDI Naming 컨텍스트를 조회하지 못하여, 해당 컨테이너를 위한 cleanup이 수행되지 않았습니다.
namingResources.cleanupNoResource=컨테이터 [{1}]을(를) 위한 JNDI 리소스 [{0}]을(를) 검색하지 못하였으므로, 해당 리소스에 대한 cleanup이 수행되지 않았습니다.
namingResources.ejbLookupLink=EJB 레퍼런스 [{0}]이(가) ejb-link와 lookup-name 둘 다를 지정했습니다.
namingResources.envEntryLookupValue=Environment 엔트리 [{0}]은(는) lookup-name과 값 둘 다 지정하고 있습니다.
namingResources.mbeanCreateFail=Naming 리소스 [{0}]을(를) 위한 MBean을 생성하지 못했습니다.
namingResources.mbeanDestroyFail=Naming 리소스 [{0}]을(를) 위한 MBean을 소멸시키지 못했습니다.
namingResources.resourceTypeFail=[{0}](이)라는 이름의 JNDI 리소스는 타입이 [{1}]이지만, 해당 타입은 해당 리소스를 위해 설정된 injection 대상(들)의 타입(들)과 일관되지 않습니다.
