# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

namingResources.cleanupCloseFailed=Failed to invoke method [{0}] for resource [{1}] in container [{2}] so no cleanup was performed for that resource
namingResources.cleanupCloseSecurity=Unable to retrieve method [{0}] for resource [{1}] in container [{2}] so no cleanup was performed for that resource
namingResources.cleanupNoClose=Resource [{0}] in container [{1}] does not have a [{2}] method so no cleanup was performed for that resource
namingResources.cleanupNoContext=Failed to retrieve JNDI naming context for container [{0}] so no cleanup was performed for that container
namingResources.cleanupNoResource=Failed to retrieve JNDI resource [{0}] for container [{1}] so no cleanup was performed for that resource
namingResources.ejbLookupLink=The EJB reference [{0}] specifies both a ejb-link and a lookup-name
namingResources.envEntryLookupValue=The environment entry [{0}] specifies both a lookup-name and a value
namingResources.mbeanCreateFail=Failed to create MBean for naming resource [{0}]
namingResources.mbeanDestroyFail=Failed to destroy MBean for naming resource [{0}]
namingResources.resourceTypeFail=The JNDI resource named [{0}] is of type [{1}] but the type is inconsistent with the type(s) of the injection target(s) configured for that resource
