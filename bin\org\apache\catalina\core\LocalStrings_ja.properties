# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

applicationContext.addFilter.ise=コンテキストが初期化されたため、フィルタをコンテキスト[{0}]に追加できません
applicationContext.addJspFile.iae=不正な JSP ファイル [{0}] です。
applicationContext.addListener.iae.cnfe=クラス [{0}] のインスタンスを作成できません。
applicationContext.addListener.iae.init=[{0}]型のインスタンスをリスナーとして追加できません。
applicationContext.addListener.iae.sclNotAllowed=最初のServletContextListenerが呼び出されると、それ以上ServletContextListenerを追加することはできません。
applicationContext.addListener.iae.wrongType=指定されたクラス [{0}] はリスナークラスのインスタンスではありません。
applicationContext.addListener.ise=コンテキストが初期化されているため、リスナーをコンテキスト[{0}]に追加できません。
applicationContext.addRole.ise=コンテキスト [{0}] は初期化済みのためロールを追加できません。
applicationContext.addServlet.ise=コンテキストが初期化済みなため、Servletをコンテキスト[{0}]に追加できません。
applicationContext.attributeEvent=属性イベントリスナによって例外が投げられました
applicationContext.illegalDispatchPath=アプリケーションが、エンコードされたディレクトリトラバーサル試行を含むために拒否された不正なパス[{0}]を持つ要求ディスパッチャを取得しようとしました。
applicationContext.invalidFilterName=無効なフィルタ名[{0}]のためにフィルタ定義を追加できません。
applicationContext.invalidServletName=不正なサーブレット名 [{0}] のため、サーブレット定義を追加できません。
applicationContext.lookup.error=コンテキスト[{1}]でリソース[{0}]の場所を特定できませんでした。
applicationContext.mapping.error=マッピング中のエラー
applicationContext.requestDispatcher.iae=パス [{0}] が"/"文字で始まりません
applicationContext.resourcePaths.iae=パス[{0}]は「/」文字で始まっていません。
applicationContext.role.iae=コンテキスト[{0}]を宣言する個々のロールは、nullでも空の文字列でもありません。
applicationContext.roles.iae=コンテキスト[{0}]に対して宣言するロールの配列はnullにできません。
applicationContext.setAttribute.namenull=nameがnullではいけません
applicationContext.setInitParam.ise=初期化パラメータは、コンテキストが初期化された後に設定することはできません。
applicationContext.setRequestEncoding.ise=コンテキスト [{0}] は初期化済みのためリクエストエンコーディングを構成できません。
applicationContext.setResponseEncoding.ise=コンテキスト [{0}] は初期化済みのため、レスポンスエンコーディングを構成できません。
applicationContext.setSessionTimeout.ise=コンテキストが初期化されているため、コンテキスト[{0}]にセッションタイムアウトを設定できません
applicationContext.setSessionTracking.iae.invalid=コンテキスト[{1}]に対して要求されたセッション追跡モード[{0}]は、そのコンテキストではサポートされていません。
applicationContext.setSessionTracking.iae.ssl=コンテキスト[{0}]に対して要求されたセッショントラッキングモードには、SSLと少なくとも1つの他のモードが含まれていました。 SSLは他のモードでは設定できません。
applicationContext.setSessionTracking.ise=コンテキスト [{0}] は実行中のためセッション追跡モードを構成できません。

applicationDispatcher.allocateException=サーブレット [{0}] に例外を割り当てます
applicationDispatcher.deallocateException=サーブレット [{0}] の例外を解除します
applicationDispatcher.forward.ise=レスポンスをコミットした後でフォワードできません
applicationDispatcher.isUnavailable=サーブレット [{0}] は現在利用できません
applicationDispatcher.serviceException=サーブレット [{0}] のServlet.service()が例外を投げました
applicationDispatcher.specViolation.request=元のServletRequestまたはラップされた元のServletRequestはSRV.8.2およびSRV.14.2.5.1に違反しするのでRequestDispatcherに渡されません。
applicationDispatcher.specViolation.response=SRV.8.2 および SRV.14.2.5.1 に違反しているため、オリジナルあるいはラップされた ServletResponse は RequestDispatcher を通過しませんでした。

applicationFilterConfig.jmxRegisterFail=クラス [{0}] 名前 [{1}] のフィルターを JMX に登録できません。
applicationFilterConfig.jmxUnregister=タイプ[{0}]および名前[{1}]のフィルタのJMX登録解除が完了しました。
applicationFilterConfig.jmxUnregisterFail=クラス [{0}] 名前 [{1}] の JMX フィルターを登録解除できません。
applicationFilterConfig.preDestroy=[{1}]型の名[{0}]というフィルタのpreDestroyへの呼び出しに失敗しました
applicationFilterConfig.release=タイプ[{1}]の名前[{0}]のフィルタを破棄できませんでした。

applicationFilterRegistration.nullInitParam=NULLの名前や値のためにフィルターの初期化パラメーターを設定できません。 名前[{0}]、値[{1}]
applicationFilterRegistration.nullInitParams=キー [{0}] または値 [{1}] のいずれかが null のためフィルターの初期化パラメータを設定できませんでした。

applicationHttpRequest.fragmentInDispatchPath=ディスパッチパス [{0}] 中のフラグメントは除去されました

applicationPushBuilder.methodInvalid=プッシュリクエストの HTTP メソッドはキャッシュ可能、かつ、安全でなければなりません。[{0}] は指定できません。
applicationPushBuilder.methodNotToken=HTTP メソッド [{0}] にトークンとして利用できない文字が含まれています。

applicationServletRegistration.setServletSecurity.iae=サーブレット[{0}]に指定されたNULL制約が、名前[{1}]のコンテキストに配備されました
applicationServletRegistration.setServletSecurity.ise=コンテキストが既に初期化されているため、名前[{1}]のコンテキストに配備されたサーブレット[{0}]にセキュリティ制約を追加できません。

applicationSessionCookieConfig.ise=コンテキスト[{1}]が初期化済みなので、プロパティ[{0}]をSessionCookieConfigに追加することはできません。

aprListener.aprDestroy=APRベースのApache Tomcatネイティブライブラリのシャットダウンに失敗しました。
aprListener.aprInit=商用環境に最適な性能を発揮する APR ベースの Tomcat ネイティブライブラリが java.library.path [{0}] に存在しません。
aprListener.aprInitDebug=APRベースのApache Tomcatネイティブライブラリは、java.library.path [{1}]上の名前[{0}]を使用して見つかりませんでした。 報告されたエラーは[{2}]
aprListener.aprInitError=APRベースのApache Tomcatネイティブライブラリをロードできませんでした。 報告されたエラーは[{0}]でした
aprListener.config=APR/OpenSSL設定：useAprConnector [{0}]、useOpenSSL [{1}]
aprListener.currentFIPSMode=現在のFIPSモード：[{0}]
aprListener.enterAlreadyInFIPSMode=AprLifecycleListenerは強制的にFIPSモードに入るように設定されていますが、ライブラリはすでにFIPSモードになっています[{0}]
aprListener.flags=APR機能：IPv6 [{0}]、sendfile {1}]、受け入れフィルタ[{2}]、ランダム[{3}]
aprListener.initializeFIPSFailed=FIPS モードに変更できません。
aprListener.initializeFIPSSuccess=FIPS モードに入りました。
aprListener.initializedOpenSSL=OpenSSLは[{0}]を正常に初期化しました。
aprListener.initializingFIPS=FIPSモードを初期化しています。
aprListener.requireNotInFIPSMode=AprLifecycleListenerはライブラリが既にFIPSモードになっている必要があるように設定されていますが、FIPSモードではありませんでした。
aprListener.skipFIPSInitialization=すでにFIPSモードになっています。 FIPS初期化をスキップします。
aprListener.sslInit=SSLEngineの初期化に失敗しました。
aprListener.tcnInvalid=APRベースのApache Tomcatネイティブライブラリの互換性のないバージョン[{0}]がインストールされていますが、Tomcatにはバージョン[{1}]が必要です。
aprListener.tcnValid=APRバージョン[{1}]を使用してAPRベースのApache Tomcatネイティブライブラリ[{0}]をロードしました。
aprListener.tcnVersion=インストールされた Apache Tomcat ネイティブライブラリの APR バージョンは [{0}] ですが、推奨する最小バージョンは [{1}] です。
aprListener.tooLateForFIPSMode=FIPSModeを設定できません：SSLは既に初期化されています。
aprListener.tooLateForSSLEngine=setSSLEngine出来ません：SSLはすでに初期化されています
aprListener.tooLateForSSLRandomSeed=setSSLRandomSeedできません：SSLは既に初期化されています。
aprListener.wrongFIPSMode=AprLifecycleListener の設定 FIPSMode に予期せぬ値 [{0}] が指定されました。

asyncContextImpl.asyncDispatchError=非同期ディスパッチの処理中にエラーが発生しました。
asyncContextImpl.asyncRunnableError=AsyncContext.start() による非同期 Runnable 処理中のエラー
asyncContextImpl.dispatchingStarted=非同期ディスパッチ操作は既に呼び出されています。 同じ非同期サイクル内の追加の非同期ディスパッチ操作は許可されません。
asyncContextImpl.fireOnComplete=非同期リスナに onComploete() イベントを発火しました
asyncContextImpl.fireOnError=非同期リスナに onError() イベントを発火しました
asyncContextImpl.fireOnStartAsync=非同期リスナに onStartAsync() イベントを発火しました
asyncContextImpl.fireOnTimeout=非同期リスナに onTimeout() イベントを発火しました
asyncContextImpl.noAsyncDispatcher=ServletContextから返されたディスパッチャは非同期ディスパッチをサポートしていません。
asyncContextImpl.onCompleteError=クラス [{0}] のリスナーオブジェクトで onComplete() の呼び出しに失敗しました。
asyncContextImpl.onErrorError=クラス [{0}] のリスナーオブジェクトで onError() の呼び出しに失敗しました。
asyncContextImpl.onStartAsyncError=クラス [{0}] のリスナーオブジェクトで onStartAsync() の呼び出しに失敗しました。
asyncContextImpl.onTimeoutError=クラス [{0}] のリスナーオブジェクトで onTimeout() の呼び出しに失敗しました。
asyncContextImpl.request.ise=complete()の後、またはdispatch()メソッドのどれかが呼び出された不正なgetRequest()の呼び出し
asyncContextImpl.requestEnded=AsyncContextに関連付けられたリクエストは、すでに処理を完了しています。
asyncContextImpl.response.ise=complete()、またはdispatch()メソッドのいずれかが呼び出された後での、不正なgetResponse()呼び出し。

containerBase.backgroundProcess.cluster=クラスター [{0}] でバックグランド処理を実行中に例外が発生しました。
containerBase.backgroundProcess.realm=Realm [{0}] のバックグラウンド処理中に例外が発生しました。
containerBase.backgroundProcess.unexpectedThreadDeath=バックグラウンドスレッド [{0}] は予期せぬ理由で終了しました。
containerBase.backgroundProcess.valve=Valve [{0}] のバックグラウンドプロセス処理中の例外
containerBase.nullName=コンテナー名は null にできません。
containerBase.threadedStartFailed=子コンテナーを開始できません。
containerBase.threadedStopFailed=停止中に子コンテナが失敗しました。

defaultInstanceManager.invalidInjection=不正なメソッドリソースアノテーションです。
defaultInstanceManager.restrictedClass=制限されたクラス [{0}] へのアクセスは拒否されました。制限されたクラスを読み込むには Web アプリケーションに特権を構成しなければなりません。
defaultInstanceManager.restrictedContainerServlet=クラス [{0}] へのアクセスは禁止されています。制限クラスです (ContainerServlet インターフェイスを実装しています)。アクセス可能にするにはWebアプリケーションに特権を構成しなければなりません。
defaultInstanceManager.restrictedFiltersResource=制限フィルターのプロパティファイルが見つかりません [{0}]
defaultInstanceManager.restrictedListenersResource=制限付きリスナープロパティファイルが見つかりませんでした[{0}]
defaultInstanceManager.restrictedServletsResource=Restricted サーブレットプロパティファイルが見つかりません。[{0}]
defaultInstanceManager.restrictedWrongValue=クラス名[{1}]の制限付きクラスのプロパティファイル[{0}]の値が間違っています。 期待値：[制限]、実際値：[{2}]

filterChain.filter=フィルタの実行により例外を投げました
filterChain.servlet=サーブレットの実行により例外を投げました

jreLeakListener.authPolicyFail=javax.security.auth.Policyクラスのメモリリークの防止を試みる際にエラーが発生しました。
jreLeakListener.classToInitializeFail=Tomcat起動中に可能なメモリーリークを防止するためのクラス[{0}]をロードすることに失敗しました。
jreLeakListener.gcDaemonFail=Tomcat の開始時に潜在的なメモリーリークを防ぐ GC デーモンスレッドの作成を開始できませんでした。Sun の JVM ではない可能性があります。
jreLeakListener.jarUrlConnCacheFail=既定でJar URL接続のキャッシュを無効にできませんでした。
jreLeakListener.ldapPoolManagerFail=Tomcatの起動時にメモリリークを防ぐ為にcom.sun.jndi.ldap.LdapPoolManagerクラスの作成をトリガーすることができませんでした。 これはSun以外のJVMであることが予想されます。
jreLeakListener.xmlParseFail=XML解析中のメモリリークを防止を試行する際のエラー

naming.addEnvEntry=環境変数 [{0}] を追加します。
naming.addResourceEnvRef=リソースenv ref [{0}]を追加しています。
naming.bindFailed=オブジェクトのバインドに失敗しました: [{0}]
naming.invalidEnvEntryType=環境エントリ [{0}] は無効な型を持っています
naming.invalidEnvEntryValue=環境エントリ [{0}] は無効な値を持っています
naming.jmxRegistrationFailed=JMX に登録できませんでした: [{0}]
naming.namingContextCreationFailed=名前付きコンテキストの生成に失敗しました: [{0}]
naming.unbindFailed=オブジェクトのアンバインドに失敗しました: [{0}]
naming.wsdlFailed=wsdl ファイル [{0}] が見つかりませんでした。

noPluggabilityServletContext.notAllowed=Servlet 3.0仕様の4.4節では、web.xmlに定義されていないServletContextListener、web-fragment.xmlファイル、@WebListenerアノテーションからこのメソッドを呼び出すことはできません。

pushBuilder.noPath=path を設定する前に push() を呼び出すことはできません。

standardContext.applicationListener=クラス [{0}] のアプリケーションリスナの設定中にエラーが発生しました
standardContext.applicationSkipped=前のエラーのためにアプリケーションリスナのインストールをスキップします
standardContext.backgroundProcess.instanceManager=インスタンスマネージャ[{0}]のバックグラウンドプロセス処理中の例外
standardContext.backgroundProcess.loader=Loader[{0}]のバックグラウンドプロセス処理中に例外が発生
standardContext.backgroundProcess.manager=マネージャ[{0}]のバックグラウンドプロセス処理中の例外
standardContext.backgroundProcess.resources=リソース[{0}]バックグラウンドプロセス処理中の例外
standardContext.cluster.noManager=Managerが見つかりませんでした。 ClusterManagerを使用するかどうかの確認。 クラスタ構成：[{0}]、アプリケーション配布可能：[{1}]
standardContext.configurationFail=コンテキストが正しく設定されていないとマークされた1つ以上のコンポーネント
standardContext.cookieProcessor.null=コンテキストの CookieProcessor には null を指定できません。
standardContext.duplicateListener=リスナー[{0}]は、すでにこのコンテキストに構成されています。 重複定義は無視されました。
standardContext.errorPage.error=エラーページの位置 [{0}] は''/''で始まらなければいけません
standardContext.errorPage.required=ErrorPage は null に出来ません
standardContext.errorPage.warning=警告: Servlet 2.4ではエラーページの場所 [{0}] は''/''で始まらなければいけません
standardContext.extensionValidationError=アプリケーション拡張に必要な検証を試行中のエラー
standardContext.filterFail=1つまたは複数のフィルタを開始できませんでした。 完全な詳細は適切なコンテナログファイルにあります。
standardContext.filterMap.either=フィルタマッピングは<url-pattern>又は<servlet-name>のどちらかを指定しなければいけません
standardContext.filterMap.name=フィルタマッピングは未知のフィルタ名 [{0}] を指定しました
standardContext.filterMap.pattern=フィルタマッピング中に無効な <url-pattern> [{0}] があります
standardContext.filterStart=フィルタ [{0}] の起動中の例外です
standardContext.invalidWrapperClass=クラス [{0}] は StandardWrapper の派生クラスではありません。
standardContext.isUnavailable=このアプリケーションは現在利用できません
standardContext.listenerFail=1つまたは複数のリスナーが開始に失敗しました。 完全な詳細は適切なコンテナログファイルにあります。
standardContext.listenerStart=クラス [{0}] のリスナインスタンスにコンテキスト初期化イベントを送信中の例外です
standardContext.listenerStop=クラス [{0}] のリスナインスタンスにコンテキスト破棄イベントを送信中の例外です
standardContext.loadOnStartup.loadException=Web アプリケーション [{0}] のサーブレット [{1}] の load() メソッドは例外を投げました。
standardContext.loginConfig.errorPage=フォームのエラーページ [{0}] は''/''で始まらなければいけません
standardContext.loginConfig.errorWarning=警告: Servlet 2.4ではフォームのエラーページ [{0}] は''/''で始まらなければいけません
standardContext.loginConfig.loginPage=フォームのログインページ [{0}] は''/''で始まらなければいけません
standardContext.loginConfig.loginWarning=警告: Servlet 2.4ではフォームのログインページ [{0}] は''/''で始まらなければいけません
standardContext.loginConfig.required=LoginConfigはnullではいけません
standardContext.manager=クラス [{0}] をManagerとして構成しました。
standardContext.managerFail=セッションマネージャーを開始できません。
standardContext.namingResource.destroy.fail=以前の名前付きリソースを破棄できませんでした。
standardContext.namingResource.init.fail=新しいネーミングリソースの初期化に失敗しました。
standardContext.notStarted=コンテキストはまだ起動されていません
standardContext.notWrapper=コンテキストの子供はラッパでなければいけません
standardContext.parameter.duplicate=コンテキスト初期化パラメタ [{0}] が重複しています
standardContext.parameter.required=パラメタ名とパラメタ値の両方が必要です
standardContext.pathInvalid=コンテキストパスは空文字列あるいは "/" で開始し "/" で終了しない文字列でなければなりません。パス [{0}] はこの条件を満たさないため [{1}] へ変更しました。
standardContext.postconstruct.duplicate=クラス[{0}]のpost constructメソッド定義が重複しています。
standardContext.postconstruct.required=完全限定クラス名とメソッド名の両方が必要です。
standardContext.predestroy.duplicate=クラス [{0}] のメソッド定義に @PreDestroy が重複しています。
standardContext.predestroy.required=完全修飾クラス名とメソッド名の両方が必要です。
standardContext.reloadingCompleted=このコンテキストの再ロードが完了しました
standardContext.reloadingStarted=このコンテキストの再ロードを開始しました
standardContext.requestListener.requestInit=クラス [{0}] のリスナインスタンスに初期化するライフサイクルイベントのリクエストを送信中の例外です
standardContext.resourcesInit=静的リソースの初期化エラー
standardContext.resourcesStart=静的リソース開始中のエラー
standardContext.resourcesStop=静的リソースの停止中のエラー
standardContext.sciFail=ServletContainerInitializer の処理中にエラーが発生しました。
standardContext.securityConstraint.mixHttpMethod=1つの Web リソースコレクションに <http-method> と <http-method-omission> を両方指定することはできません。
standardContext.securityConstraint.pattern=セキュリティの制約の中に無効な <url-pattern> [{0}] があります
standardContext.servletFail=起動時に一つ以上のサーブレットの読み込みが失敗しました。詳細は適切なコンテナーのログファイルを確認してください。
standardContext.servletMap.name=サーブレットマッピングは未知のサーブレット名 [{0}] を指定しています
standardContext.servletMap.pattern=サーブレットマッピング中に無効な <url-pattern> [{0}] があります
standardContext.startFailed=以前のエラーのためにコンテキストの起動が失敗しました [{0}]
standardContext.startingContext=名前[{0}]のコンテキストを開始する際の例外
standardContext.stop.asyncWaitInterrupted=実行中の非同期要求が完了するのをunloadDelayミリ秒待っている間に割り込みを受信しました。 コンテキスト停止はさらに遅れることなく続行されます。
standardContext.stoppingContext=ローダを停止中の例外です
standardContext.threadBindingListenerError=コンテキスト [{0}] に構成されたリスナーを束縛するスレッドで異常が発生しました。
standardContext.urlPattern.patternWarning=警告: Servlet 2.4ではURLパターン [{0}] は''/''で始まらなければいけません
standardContext.webappClassLoader.missingProperty=プロパティが存在しないため、Webアプリケーションクラスローダプロパティ[{0}]を[{1}]に設定できません。
standardContext.workCreateException=コンテキスト[{2}]のディレクトリ[{0}]とCATALINA_HOME [{1}]からの絶対workディレクトリを特定できませんでした
standardContext.workCreateFail=コンテキスト [{1}] の作業ディレクトリ [{0}] を作成できません。
standardContext.workPath=コンテキスト[{0}]のworkパスを取得中の例外

standardContextValve.acknowledgeException=100 (Continue) レスポンスでリクエストを確認できませんでした。

standardEngine.jvmRouteFail=EngineのjvmRoute属性をシステムプロパティから設定できませんでした
standardEngine.notHost=Engineの子供はHostでなければいけません
standardEngine.notParent=エンジンは親のコンテナを持つことはできません

standardHost.clientAbort=リモートクライアントがリクエストを中止しました, IOException: [{0}]
standardHost.invalidErrorReportValveClass=指定されたErrorReportValveクラスをロードできません: [{0}]
standardHost.noContext=このリクエストを処理するために設定されたコンテキストがありません
standardHost.notContext=Host の子供はContextでなければいけません
standardHost.nullName=ホスト名が必要です
standardHost.problematicAppBase=ホスト[{0}]のappBaseに空の文字列を使用すると、CATALINA_BASEに設定されますが、これは悪い考えです。

standardHostValue.customStatusFailed=カスタムエラーページ[{0}]を正しくディスパッチできませんでした。

standardPipeline.basic.start=新しい基本 Valve を開始できません。
standardPipeline.basic.stop=古い基本 Valve を停止できません。
standardPipeline.valve.destroy=Valve を破棄できません。
standardPipeline.valve.start=Valve を開始できません。
standardPipeline.valve.stop=Valve を停止できません。

standardServer.accept.error=シャットダウンコマンドを受信するソケットの accept で入出力例外が発生しました。
standardServer.accept.readError=シャットダウンコマンドの読み取り時に入出力例外が発生しました。
standardServer.accept.security=シャットダウンコマンドを受信するソケットの accept でセキュリティエラーを発生しました。
standardServer.accept.timeout=シャットダウンコマンドをリスンするソケットは、accept()の呼び出し後に予期しないタイムアウト[{0}]ミリ秒を経験しました。 これはバグ56684の一例ですか？
standardServer.invalidShutdownCommand=不正なシャットダウンコマンド [{0}] を受信しました。
standardServer.shutdownViaPort=有効なシャットダウンコマンドがシャットダウンポート経由で受信されました。 サーバーインスタンスを停止します。
standardServer.storeConfig.contextError=コンテキスト [{0}] の構成格納中のエラー
standardServer.storeConfig.error=サーバー構成格納中のエラー
standardServer.storeConfig.notAvailable=StoreConfig実装は[{0}]という名前のMBeanとして登録されていないため、設定を保存することはできません。 適切なMBeanは通常StoreConfigLifecycleListenerを介して登録されます。

standardService.engine.startFailed=関連付けられたEngineの起動に失敗しました
standardService.engine.stopFailed=関連付けられたEngineの停止に失敗しました。
standardService.mapperListener.startFailed=関連付けられたMapperListenerの起動に失敗しました。
standardService.mapperListener.stopFailed=関連付けられたMapperListenerの停止に失敗しました。
standardService.start.name=サービス [{0}] を起動します
standardService.stop.name=サービス [{0}] を停止します

standardWrapper.allocate=サーブレットインスタンスを割り当て中のエラーです
standardWrapper.allocateException=サーブレット [{0}] に例外を割り当てます
standardWrapper.deallocateException=サーブレット [{0}] に対する例外の割り当てを解除します
standardWrapper.destroyException=サーブレット [{0}] のServlet.destroy()が例外を投げました
standardWrapper.destroyInstance=サーブレット [{0}] の InstanceManager.destroy() が例外を送出しました。
standardWrapper.initException=サーブレット [{0}] のServlet.init()が例外を投げました
standardWrapper.instantiate=サーブレットクラス [{0}] を初期化中のエラー
standardWrapper.isUnavailable=サーブレット [{0}] は現在利用できません
standardWrapper.notChild=Wrapper コンテナは子供のコンテナを持つことはできません
standardWrapper.notClass=サーブレット [{0}] に指定されたサーブレットクラスがありません
standardWrapper.notContext=Wrapper の親のコンテナはContextでなければいけません
standardWrapper.notFound=サーブレット [{0}] が利用できません
standardWrapper.notServlet=クラス [{0}] はServletではありません
standardWrapper.serviceException=サーブレット [{0}] のServlet.service()が例外を投げました
standardWrapper.serviceExceptionRoot=パス[{1}]を持つコンテキスト内のサーブレット[{0}]のServlet.service() が例外[{2}]が根本的要因と共に投げられました。
standardWrapper.unavailable=サーブレット [{0}] を利用不可能にマークします
standardWrapper.unloadException=サーブレット [{0}] がunload()例外を投げました
standardWrapper.unloading=サーブレット [{0}] がロードされていないので、割り当てることができません
standardWrapper.waiting=サーブレット [{1}] の [{0}] インスタンスが割り当て解除されるのを待機しています

threadLocalLeakPreventionListener.containerEvent.error=コンテナーイベント [{0}] の処理中に例外が発生しました。
threadLocalLeakPreventionListener.lifecycleEvent.error=ライフサイクルイベント [{0}] を処理中の例外
