# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

digesterFactory.missingSchema=XML 스키머 [{0}]을(를) 찾을 수 없었습니다. XML validation이 사용 가능 상태라면, 이는 XML validation 실패를 일으킬 가능성이 큽니다.

localResolver.unresolvedEntity=Public ID가 [{1}]이고 system ID가 [{2}]이며 base URI가 [{3}]인 XML 리소스 [{0}]을(를), 알려진 로컬 엔티티로 결정할 수 없습니다.

xmlErrorHandler.error=[{1}]을(를) 처리 중, 치명적이지 않은 오류 [{0}]이(가) 보고되었습니다.
xmlErrorHandler.warning=[{1}]을(를) 처리 중 경고가 보고됨: [{0}]
