# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

filterDef.invalidFilterName=<filter-name> [{0}] inválido en la definición de filtros.\n

securityConstraint.uncoveredHttpOmittedMethod=Debido a restricciones de seguridad con el patrón de URL [{0}] los métodos HTTP [{1}] no estan cubiertos.

webRuleSet.absoluteOrdering=Elemento <absolute-ordering> no válido en web-fragment.xml y será ignorado
webRuleSet.postconstruct.duplicate=La definición del metodo the post construcción para la clase [{0}] esta duplicada\n
webRuleSet.relativeOrdering=elemento <ordering> no válido en web.xml y será ignorado

webXml.duplicateFilter=Filtro de nombre duplicado [{0}]\n
webXml.duplicateServletMapping=Los servlets llamados [{0}] y [{1}] estan ambos mapeados al patrón de URL [{2}] el cual no esta permitido
webXml.mergeConflictSessionTrackingMode=Los modos de seguimiento fueron definidos inconsistentemente en multiples fragmentos, incluyendo fragmentos con el nombre [{0}] localizado en [{1}]\n
webXml.reservedName=Un archivo web.xml fue detectado usando un nombre reservado [{0}]. El nombre será ignorado para este fragmento.

webXmlParser.applicationParse=Error de evaluación (parse) en el archivo web.xml de la aplicación a [{0}]
webXmlParser.applicationPosition=Se ha producido en la línea [{0}] columna [{1}]
webXmlParser.applicationStart=Analizando fichero de aplicación web.xml en [{0}]
