# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

digesterFactory.missingSchema=Le schema XML [{0}] n''a pu être trouvé, cela empêchera certainement la validation de fonctionner si elle est activée

localResolver.unresolvedEntity=Impossible de résoudre vers une entité locale connue la ressource XML [{0}] avec un identifiant public [{1}], un identifiant système [{2}] et un URI de base [{3}]

xmlErrorHandler.error=L''erreur non fatale [{0}] a été retournée lors du traitement [{1}]
xmlErrorHandler.warning=L''avertissement [{0}] a été retournée en traitant [{1}]
