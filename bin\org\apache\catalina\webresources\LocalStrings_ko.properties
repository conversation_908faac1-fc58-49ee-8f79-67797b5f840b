# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractArchiveResourceSet.setReadOnlyFalse=JAR 파일들에 기반한 것들과 같은 아카이브 기반 WebResourceSet들은, 읽기 전용으로 하드코드되어 있으며, 읽기 및 쓰기 용으로 설정될 수 없습니다.

abstractFileResourceSet.canonicalfileCheckFailed=경로 [{1}]에 위치한 웹 애플리케이션 [{0}]을(를) 위한 리소스가, 표준 경로 [{2}]와(과) 일치하지 않아서, 로드되지 못했습니다. 심벌릭 링크 사용이 원인 중 하나일 수 있습니다.

abstractResource.getContentFail=[{0}]을(를) 바이트 배열로 반환할 수 없습니다.
abstractResource.getContentTooLarge=리소스의 크기가 [{1}] 바이트로서, 이는 바이트 배열 최대 크기보다 크기 때문에, [{0}]을(를) 바이트 배열로서 반환할 수 없습니다.

abstractResourceSet.checkPath=요청된 경로 [{0}]은(는) 유효하지 않습니다. 반드시 "/"로 시작해야 합니다.

cache.addFail=[{0}]에 위치한 리소스를 웹 애플리케이션 [{1}]을(를) 위한 캐시에 추가할 수 없습니다. 왜냐하면 만료된 캐시 엔트리들을 없애버린 이후에도 여유 공간이 충분하지 않기 때문입니다. 캐시의 최대 크기를 증가시키는 것을 고려해 보십시오.
cache.backgroundEvictFail=백그라운드 캐시 퇴거 (cache eviction) 프로세스가, 컨텍스트 [{1}]을(를) 위한 캐시의 [{0}] 퍼센트를 해제시킬 수 없었습니다. 캐시의 최대 크기를 증가시킬 것을 고려해 보십시오. 캐시 퇴거 작업 이후, 대략 [{2}] KB의 데이터가 캐시에 남아 있습니다.
cache.objectMaxSizeTooBig=objectMaxSize를 위한 값 [{0}]kB이, maxSize/20인 최대한계값 보다 커서, [{1}]kB로 줄여졌습니다.
cache.objectMaxSizeTooBigBytes=[{0}]kB를 캐시하기 위해, 최대 객체 크기로서 지정된 값이 Integer.MAX_VALUE 바이트보다 큰데, Integer.MAX_VALUE는 캐시될 수 있는 최대 크기입니다. 한계 값을 Integer.MAX_VALUE 바이트로 설정하겠습니다.

cachedResource.invalidURL=URL [{0}]이(가) 유효하지 않기 때문에 CachedResourceURLStreamHandler 인스턴스를 생성할 수 없습니다.

classpathUrlStreamHandler.notFound=쓰레드 컨텍스트 클래스로더 또는 현재 클래스의 클래스로더를 사용하여, 리소스 [{0}]을(를) 로드할 수 없습니다.

dirResourceSet.manifestFail=[{0}](으)로부터 manifest를 읽지 못했습니다.
dirResourceSet.notDirectory=base와 internal path [{0}]{1}[{2}](으)로 지정된 디렉토리가 존재하지 않습니다.
dirResourceSet.writeNpe=입력 스트림이 널일 수는 없습니다.

extractingRoot.jarFailed=JAR 파일 [{0}]을(를) 추출하지 못했습니다.
extractingRoot.targetFailed=JAR 파일들의 압축을 풀기 위한 디렉토리 [{0}]을(를) 생성할 수 없습니다.

fileResource.getCanonicalPathFail=리소스 [{0}]에 대한 canonical 경로를 결정할 수 없습니다.
fileResource.getCreationFail=리소스 [{0}]의 생성 시간을 결정할 수 없습니다.
fileResource.getUrlFail=리소스 [{0}]을(를) 위한 URL을 결정할 수 없습니다.

fileResourceSet.notFile=base와 내부 경로 [{0}]{1}[{2}]에 의해 지정된 파일이 존재하지 않습니다.

jarResource.getInputStreamFail=JAR [{1}] 내의 리소스 [{0}]을(를) 위한 InputStream을 얻을 수 없습니다.

jarResourceRoot.invalidWebAppPath=이 리소스는 언제나 디렉토리를 가리켜서, 제공된 webAppPath가 반드시 ''/'' 로 끝나야 하지만, 제공된 webAppPath는 [{0}]이었습니다.

jarWarResourceSet.codingError=코딩 오류

standardRoot.checkStateNotStarted=현재 시작되어 있는 상태가 아니라면, 리소스들은 접근될 수 없습니다.
standardRoot.createInvalidFile=[{0}](으)로부터 WebResourceSet을 생성할 수 없습니다.
standardRoot.createUnknownType=알 수 없는 타입 [{0}]의 WebResourceSet을 생성할 수 없습니다.
standardRoot.invalidPath=리소스 경로 [{0}]은(는) 유효하지 않습니다.
standardRoot.invalidPathNormal=리소스 경로 [{0}]이(가) [{1}](으)로 정규화되어 있는데, 이는 유효하지 않습니다.
standardRoot.lockedFile=웹 애플리케이션 [{0}]이(가) 파일 [{1}]을(를) 닫지 못했습니다. 해당 파일은 다음과 같은 스택 트레이스 내에서 열렸었습니다.
standardRoot.noContext=컨텍스트가 이 WebResourceRoot를 위해 설정되지 않았습니다.
standardRoot.startInvalidMain=지정된 주요 리소스셋 [{0}]은(는) 유효하지 않습니다.
standardRoot.unsupportedProtocol=URL 프로토콜 [{0}]은(는), 이 웹 리소스 구현에 의해 지원되지 않습니다.
