# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cookie.fallToDebug=\n\
\ Note : les occurrences suivantes de cette erreur seront enregistrées au niveau DEBUG.
cookie.invalidCookieValue=Un en-tête de cookie a été reçu [{0}] qui contenait un cookie invalide, celui ci sera ignoré
cookie.invalidCookieVersion=Un en-tête de cookie a été reçu utilisant une version [{0}] non reconnue, les cookies seront ignorés
cookie.valueNotPresent=<non présent>

http.closingBracket=Un crochet ']' a été trouvé dans un nom d'hôte non IPv6
http.illegalAfterIpv6=Le caractère [{0}] n''est pas permis dans un nom d''hôte à la suite d''une adresse IPv6
http.illegalCharacterDomain=Le caractère [{0}] n''est jamais valide pour un nom de domaine
http.illegalCharacterIpv4=Le caractère [{0}] n''est pas valide pour une adresse IPV4.
http.illegalCharacterIpv6=Le caractère [{0}] n''est jamais valide dans une adresse IPv6
http.invalidCharacterDomain.afterColon=Le caractère [{0}] n''est pas valide après deux-point pour un nom de domaine
http.invalidCharacterDomain.afterHyphen=Le caractère [{0}] n''est pas valide après un trait d''union pour un nom de domaine
http.invalidCharacterDomain.afterLetter=Le caractère [{0}] n''est pas valide après une lettre pour un nom de domaine
http.invalidCharacterDomain.afterNumber=Le caractère [{0}] n''est pas valide après un nombre pour un nom de domaine
http.invalidCharacterDomain.afterPeriod=Le caractère [{0}] n''est pas valide après une virgule pour un nom de domaine
http.invalidCharacterDomain.atEnd=Le caractère [{0}] n''est pas valide à la fin d''un nom de domaine
http.invalidCharacterDomain.atStart=Le caractère [{0}] n''est pas valide au début d''un nom de domaine
http.invalidHextet="hextet" invalide.  Un "hextet" doit consister au maximum de 4 caractères hexadécimaux.
http.invalidIpv4Location=L'adresse IPv6 contient une adresse IPv4 incluse à un endroit invalide
http.invalidLeadingZero=Un octet IPv4 non nul ne doit pas commencer par un zéro
http.invalidOctet=Octet [{0}] invalide. L''éventail valide pour les octets IPv4 est 0-255.
http.invalidSegmentEndState=L''état [{0}] n''est pas valide à la fin d''un segment
http.noClosingBracket=L'adresse IPv6 n'a pas de crochet de fermeture
http.noOpeningBracket=Cette adresse IPv6 n'a pas de crochet d'ouverture '['
http.singleColonEnd=Une adresse IPv6 ne doit pas se terminer par un seul ':'
http.singleColonStart=Une adresse IPv6 ne doit pas commencer par un seul ':'
http.tooFewHextets=Une adresse IPv6 doit être constitué de 8 groupes de 4 octets mais cette adresse en contient [{0}] et pas de séquence "::" pour représenter un ou plusieurs groupes de 4 octets
http.tooManyColons=Une adresse IPv6 ne peut pas contenir plus de deux caractères deux-points à la suite
http.tooManyDoubleColons=Une adresse IPv6 ne peut contenir qu'une seule séquence "::"
http.tooManyHextets=L''adresse IPv6 contient [{0}] groupes de 4 octets mais une adresse IPv6 valide ne doit pas en avoir plus de 8
