# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jmxRegistry.no.domain=Le nom de domaine JMX n'est pas spécifié
jmxRegistry.objectName.failed=L''ObjectName [{0}] demandé est invalide
jmxRegistry.registerJmx.failed=Echec de l''enregistrement de l''objet [{0}] avec le nom [{1}]
jmxRegistry.registerJmx.notCompliant=L''objet demandé [{0}] n''est pas compatible avec la spécification JMX
jmxRegistry.unregisterJmx.failed=Echec du désenregistrement du MBean nommé [{0}]
jmxRegistry.unregisterJmx.notFound=L''ObjectName [{0}] demandé n''a pas été enregistré dans le serveur JMX
