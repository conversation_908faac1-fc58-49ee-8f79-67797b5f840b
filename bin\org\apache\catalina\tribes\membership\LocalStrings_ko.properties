# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

McastService.domain=도메인 변경 메시지를 보낼 수 없습니다.
McastService.parseSoTimeout=SoTimeout을 파싱할 수 없습니다: [{0}]
McastService.parseTTL=TTL을 파싱할 수 없습니다: [{0}]
McastService.payload=Payload 변경 메시지를 보낼 수 없습니다.
McastService.stopFail=멀티캐스트 서비스를 중단시킬 수 없습니다. 레벨: [{0}]

mcastService.exceed.maxPacketSize=패킷 길이 [{0}]이(가), 최대 패킷 크기인 [{1}] 바이트를 초과합니다.
mcastService.missing.property=McastService: 필수 프로퍼티 [{0}]이(가) 없습니다.
mcastService.noStart=멀티캐스트 전송이 시작되지 않았거나 사용가능 상태가 아닙니다.

mcastServiceImpl.bind=멀티캐스트 소켓을 [{0}:{1}](으)로 바인딩하려 시도 중
mcastServiceImpl.bind.failed=멀티캐스트 주소로 바인딩하지 못했습니다. 포트로만 바인딩합니다.
mcastServiceImpl.error.receiving=멀티캐스트 패키지를 받는 중 오류 발생. 500 밀리초 동안 잠에 들겠습니다.
mcastServiceImpl.invalid.startLevel=유효하지 않은 시작 레벨. 허용되는 레벨들은 Channel.MBR_RX_SEQ와 Channel.MBR_TX_SEQ 뿐입니다.
mcastServiceImpl.invalid.stopLevel=유효하지 않은 중지 레벨. 허용 레벨들은 Channel.MBR_RX_SEQ와 Channel.MBR_TX_SEQ입니다.
mcastServiceImpl.memberDisappeared.failed=멤버 사라짐 메시지를 처리할 수 없습니다.
mcastServiceImpl.packet.tooLong=수신된 멀티캐스트 패킷이 너무 깁니다. 패키지를 무시합니다: [{0}]
mcastServiceImpl.receive.running=McastService.receive가 이미 실행 중입니다.
mcastServiceImpl.recovery=Tribes 멤버십을 복구하려고, 별도의 쓰레드에서 멀티캐스팅을 시도하고 있습니다.
mcastServiceImpl.recovery.failed=복구 시도가 [{0}]회 실패했습니다. [{1}] 초 후에 다시 시도 하겠습니다.
mcastServiceImpl.recovery.startFailed=복구 쓰레드가 멤버십 서비스를 시작하지 못했습니다.
mcastServiceImpl.recovery.stopFailed=복구 쓰레드가 멤버십 서비스를 중지시키지 못했습니다.
mcastServiceImpl.recovery.successful=멤버십 복구가 성공적이었습니다.
mcastServiceImpl.send.failed=멀티캐스트 메시지를 보낼 수 없습니다.
mcastServiceImpl.send.running=McastService.send가 이미 실행 중입니다.
mcastServiceImpl.setInterface=멀티홈 멀티캐스트 인터페이스를 [{0}]에 설정합니다.
mcastServiceImpl.setSoTimeout=클러스터 멀티캐스트의 soTimeout을 [{0}](으)로 설정합니다.
mcastServiceImpl.setTTL=클러스터 멀티캐스트 TTL을 [{0}](으)로 설정합니다.
mcastServiceImpl.unable.join=멀티캐스트 그룹에 참가할 수 없습니다. 귀하의 시스템이 멀티캐스팅을 사용 가능하도록 설정되었는지 확인하십시오.
mcastServiceImpl.unableReceive.broadcastMessage=브로드캐스트 메시지를 수신할 수 없습니다.
mcastServiceImpl.waitForMembers.done=잠들기가 끝났습니다. 멤버십이 확립되었고, 시작 레벨은 [{0}]입니다.
mcastServiceImpl.waitForMembers.start=클러스터 멤버십을 확립하기 위해, [{0}] 밀리초 동안 잠에 듭니다. 시작 레벨: [{1}]

memberImpl.invalid.package.begin=유효하지 않은 패키지입니다. [{0}](으)로 시작해야 합니다.
memberImpl.invalid.package.end=유효하지 않은 패키지. [{0}](으)로 끝나야 합니다.
memberImpl.large.payload=Payload가 너무 커서 tribe들이 처리할 수 없습니다.
memberImpl.notEnough.bytes=멤버 데이터 바이트 배열에 충분한 데이터가 존재하지 않습니다.
memberImpl.package.small=멤버 패키지가 너무 작아서, 유효한지 확인할 수 없습니다.
memberImpl.unableParse.hostname=호스트 이름을 파싱할 수 없습니다.

staticMember.invalid.uuidLength=UUID는 정확히 16 바이트여야만 합니다. [{0}]이어서는 안됩니다.
