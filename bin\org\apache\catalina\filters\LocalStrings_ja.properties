# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

addDefaultCharset.unsupportedCharset=指定された文字セット[{0}]はサポートされていません。

corsFilter.invalidPreflightMaxAge=preflightMaxAgeを解析出来ません
corsFilter.invalidSupportsCredentials=allowedOrigins = [*]の場合、supportsCredentials = [true]を設定することはできません。
corsFilter.nullRequest=HttpServletRequestオブジェクトがnullです
corsFilter.nullRequestType=CORSRequestType オブジェクトが null です。
corsFilter.onlyHttp=CORSは非HTTPリクエストまたはレスポンスをサポートしていません。
corsFilter.wrongType1=[{0}]型のHttpServletRequestオブジェクトが必要です
corsFilter.wrongType2=[{0}]型または[{1}]型のHttpServletRequest オブジェクトが必要です。

csrfPrevention.invalidRandomClass=乱数生成器クラス [{0}] のインスタンスが作成できませんでした。

expiresFilter.exceptionProcessingParameter=構成パラメータ[{0}]：[{1}]処理中の例外
expiresFilter.expirationHeaderAlreadyDefined=レスポンスステータス[{1}]がコンテンツタイプ[{2}]のリクエスト[{0}、expirationヘッダーがすでに定義されています。
expiresFilter.filterInitialized=設定[{0}]で初期化されたFilter
expiresFilter.invalidDurationNumber=ディレクティブ[{1}]の無効な期間（number）[{0}]
expiresFilter.invalidDurationUnit=ディレクティブ [{1}] で期間の単位が無効です。(years|months|weeks|days|hours|minutes|seconds) [{0}]
expiresFilter.noDurationFound=ディレクティブ[{0}]にDuration が見つかりません
expiresFilter.noDurationUnitAfterAmount=ディレクティブ[{1}]のamount[{0}]の後にDuration 単位が見つかりません。
expiresFilter.noExpirationConfigured=リクエスト [{0}] に対するレスポンスは応答コード [{1} コンテントタイプ [{2}] です。有効期限は未設定です。
expiresFilter.noExpirationConfiguredForContentType=Content-Type [{0}] に有効期限が設定されていません。
expiresFilter.numberError=カンマ区切りリスト [{1}] の [{0}] 番目（ゼロ開始）の数値を解析中の例外
expiresFilter.responseAlreadyCommitted=リクエスト[{0}]は、すでにコミットされたレスポンスに対してExpiresFilterを適用できません。
expiresFilter.setExpirationDate=レスポンスステータス[{1}]のコンテンツタイプ[{2}]、有効期限[{3}]を設定するリクエスト[{0}]
expiresFilter.skippedStatusCode=レスポンスステータス[{1}] content-type [{1}]のリクエスト[{0}]、指定されたステータスのexpiration ヘッダーの生成をスキップします。
expiresFilter.startingPointInvalid=ディレクティブ[{1}]の無効な開始点（access|now|modification|a<seconds>|m<seconds>）[{0}]
expiresFilter.startingPointNotFound=ディレクティブ[{0}]に開始点(access|now|modification|a<seconds>|m<seconds>)が見つかりません
expiresFilter.unknownParameterIgnored=値[{1}]を持つ未知のパラメータ[{0}]は無視されます！
expiresFilter.unsupportedStartingPoint=[{0}] は未対応の開始点です。
expiresFilter.useDefaultConfiguration=コンテンツタイプ[{1}]に既定[{0}]を使用すると[{2}]が返されます。
expiresFilter.useMatchingConfiguration=content-type[{2}]が[{3}]を返すため、[{1}]と一致する[{0}]を使用します

filterbase.noSuchProperty=[{1}]タイプのフィルタにはプロパティ[{0}]が定義されていません。

http.403=指定されたリソース [{0}] へのアクセスは拒否されました。

httpHeaderSecurityFilter.clickjack.invalid=アンチクリックジャッキングヘッダーに不正な値 [{0}] が指定されました。
httpHeaderSecurityFilter.committed=HTTPヘッダーセキュリティフィルターへの入力時に既に応答がコミットされているため、HTTPヘッダーを追加できません。

remoteCidrFilter.invalid=[{0}] に不正な値が指定されました。詳細は直前のメッセージを参照してください。
remoteCidrFilter.noRemoteIp=クライアントは IP アドレスを持っていません。リクエストを拒否します。

remoteIpFilter.invalidHostHeader=HTTP ヘッダ [{1}] 中の Host に無効な値 [{0}] が見つかりました
remoteIpFilter.invalidHostWithPort=HTTP ヘッダ [{1}] 中の Host の値 [{0}] はポート番号を含んでいますが無視されます
remoteIpFilter.invalidNumber=パラメータ[{0}]: [{1}]に不正な番号があります。

requestFilter.deny=プロパティ [{1}] に従い  [{0}] へのリクエストを拒否しました。

restCsrfPreventionFilter.invalidNonce=CSRF nonce の検証に失敗しました。

webDavFilter.xpProblem=WebdavFixFilter：XP-x64-SP2クライアントはWebDAVサーブレットで動作しないことが知られています
webDavFilter.xpRootContext=WebdavFixFilter：XP-x64-SP2クライアントはルートコンテキストでのみ動作します
