# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

domainFilterInterceptor.member.refused=Le membre [{0}] a été refusé dans le cluster
domainFilterInterceptor.message.refused=Le message reçu du cluster [{0}] a été refusé

encryptInterceptor.algorithm.required=Un algorithme de cryptage est requis, avec une spécification complète telle que AES/CBC/PKCS5Padding
encryptInterceptor.algorithm.unsupported-mode=L''EncryptInterceptor ne supporte pas le mode de chiffrage de bloc [{0}]
encryptInterceptor.decrypt.error.short-message=Echec du décryptage du message : fin de message prématuré
encryptInterceptor.decrypt.failed=Echec de décryptage du message
encryptInterceptor.encrypt.failed=Erreur de cryptage du message
encryptInterceptor.init.failed=Echec de l'initalisation d'EncryptInterceptor
encryptInterceptor.key.required=Une clé de cryptage est requise
encryptInterceptor.tcpFailureDetector.ordering=EncryptInterceptor doit être en amont de TcpFailureDetector, l'EncryptInterceptor doit être repositionné pour être listé avant TcpFailureDetector dans le pipeline d'intercepteurs du canal

fragmentationInterceptor.fragments.missing=Les fragments sont manquants
fragmentationInterceptor.heartbeat.failed=Impossible d'effectuer le nettoyage périodique de l'intercepteur de fragments

gzipInterceptor.compress.failed=Impossible de compresser un contenu binaire
gzipInterceptor.decompress.failed=Impossible de décompresser le contenu des octets

messageDispatchInterceptor.AsyncMessage.failed=Erreur lors du traitement du message asynchrone
messageDispatchInterceptor.completeMessage.failed=Impossible de renvoyer le message complet
messageDispatchInterceptor.errorMessage.failed=Impossible d'envoyer le message d'erreur
messageDispatchInterceptor.queue.full=La file d''attente asynchrone est pleine, ayant atteint sa limite de [{0}] octets. Actuellement : [{1}] octets.
messageDispatchInterceptor.unableAdd.queue=Impossible d'ajouter le message à la file asynchrone.  Bogue de file ?
messageDispatchInterceptor.warning.optionflag=Attention, vous passez outre le drapeau d'option d'asynchronicité ("asynchronous option flag"), cela désactivera Channel.SEND_OPTIONS_ASYNCHRONOUS, que d'autres applications sont susceptibles d'utiliser.

nonBlockingCoordinator.electionMessage.sendfailed=Impossible d''envoyer le message d''élection à : [{0}]
nonBlockingCoordinator.heartbeat.failed=Impossible d'effectuer le signal périodique
nonBlockingCoordinator.heartbeat.inconsistency=Le coordinateur à trouvé un état inconsistant, redémarrage de l'élection
nonBlockingCoordinator.memberAdded.failed=Impossible de démarrer une élection quand le membre a été ajouté
nonBlockingCoordinator.memberAlive.failed=Impossible d'effectuer le test de vie du membre, assume membre inactif.
nonBlockingCoordinator.memberDisappeared.failed=Impossible de démarrer une élection lorsqu'un membre a été enlevé
nonBlockingCoordinator.processCoordinationMessage.failed=Echec de traitement de message de coordination. Pourrait être fatal.

orderInterceptor.messageAdded.sameCounter=Le message ajouté a le même compteur, à cause d'un bug de synchronisation, l'intercepteur d'ordre doit être désactivé

staticMembershipInterceptor.no.failureDetector=Il n'y a pas de détecteur TcpFailureDetector. La détection automatique de membres statiques ne fonctionne pas correctement.  Par la définition d'un intercepteur StaticMembershipInterceptor sous le TcpFailureDetector, cette détection automatique fonctionnera.
staticMembershipInterceptor.no.pingInterceptor=Il n'y a pas de TcpPingInterceptor. Le test de bonne santé des membres statiques ne fonctionne pas correctement. En définissant le TcpPingInterceptor, le test de bonne santé des membres statiques fonctionnera.
staticMembershipInterceptor.sendLocalMember.failed=La notification du membre local a échouée
staticMembershipInterceptor.sendShutdown.failed=La notification d''arrêt a échoué.

tcpFailureDetector.already.disappeared=La vérification est terminée, le membre avait déjà disparu [{0}]
tcpFailureDetector.failureDetection.failed=Impossible d''effectuer le test de détection de faute.  Membre [{0}] supposé inactif.
tcpFailureDetector.heartbeat.failed=Incapable de faire une pulsation ("heatbeat") sur le TcpFailureDector
tcpFailureDetector.member.disappeared=La vérfication est complète, le membre a disparu [{0}]
tcpFailureDetector.memberDisappeared.verify=Reçu un message memberDisappeared[{0}], qui sera vérifié
tcpFailureDetector.performBasicCheck.memberAdded=Le membre a été ajouté bien qu''aucune notification n''ait été reçue : [{0}]
tcpFailureDetector.still.alive=Vérification terminée. Le membre [{0}] vit toujours
tcpFailureDetector.suspectMember.alive=Membre suspect, confirmé vivant.[{0}]
tcpFailureDetector.suspectMember.dead=Un membre suspect a été confirmé mort [{0}]

tcpPingInterceptor.ping.failed=Impossible d'envoyer un ping TCP.
tcpPingInterceptor.pingFailed.pingThread=Impossible d'envoyer un ping à partir du thread des ping TCP

throughputInterceptor.report=Rapport de l''intercepteur du débit ("ThroughputInterceptor Report") [\n\
\tMsg Transmis (Tx Msg) : {0} messages\n\
\tEnvoyé (Sent) : {1} MB (total)\n\
\tEnvoyé (Sent) : {2} MB (application)\n\
\tDurée (Time) : {3} secondes\n\
\tVitesse d''écriture (Tx Speed) : {4} MB/sec (total)\n\
\tVitesse d''écriture (Tx Speed) : {5} MB/sec (application)\n\
\tMsg d''erreur (Error Msg) : {6}\n\
\tMsg Reçus (Rx Msg) : {7} messages\n\
\tVitesse de Réception (Rx Speed) : {8} MB/sec (depuis le 1er message)\n\
\tReçu : {9} MB]

twoPhaseCommitInterceptor.heartbeat.failed=Impossible d'exécuter un battement de coeur (heartbeat) sur l'intercepteur (interceptor) "TwoPhaseCommit".
twoPhaseCommitInterceptor.originalMessage.missing=Reçue une confirmation mais le message d''origine manque, id : [{0}]
