# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

farmWarDeployer.alreadyDeployed=webapp [{0}]は既に配備されています。
farmWarDeployer.deleteFail=[{0}] を削除できません。
farmWarDeployer.deployEnd=[{0}]からの配備が完了しました。
farmWarDeployer.fileCopyFail=[{0}] から [{1}] へコピーできません。
farmWarDeployer.hostOnly=FarmWarDeployer はHostクラスタのサブエレメントとしてのみ機能します
farmWarDeployer.hostParentEngine=FarmWarDeployer は親 [{0}] が Engine のインスタンスでなければ機能しません。
farmWarDeployer.mbeanNameFail=エンジン[{0}]とホスト[{1}]のMBeanオブジェクト名を構築できません
farmWarDeployer.modInstall=[{1}]からwebapp [{0}]をインストールしています。
farmWarDeployer.modInstallFail=WAR ファイルをインストールできませんでした。
farmWarDeployer.msgIoe=ファームデプロイファイルメッセージの読み取りに失敗しました。
farmWarDeployer.msgRxDeploy=クラスタ配備パス[{0}]、WAR[{1}]を受信しました。
farmWarDeployer.msgRxUndeploy=パス[{0}]からクラスタの配備解除を受信しました。
farmWarDeployer.removeFailLocal=[{0}]からのローカル削除に失敗しました
farmWarDeployer.removeFailRemote=ローカルの [{0}] を削除できませんでした。他のマネージャーノードでサービス中です !
farmWarDeployer.removeLocal=webapp [{0}]を削除しています
farmWarDeployer.removeLocalFail=WAR ファイルを削除できません。
farmWarDeployer.removeStart=Webアプリケーション[{0}]のクラスタ削除。
farmWarDeployer.removeTxMsg=コンテキスト [{0}] からクラスターに配備解除メッセージを送信しました。
farmWarDeployer.renameFail=[{0}] から [{1}] へ名前を変更できません。
farmWarDeployer.sendEnd=クラスタWar配備 パス[{0}]を送信しました。WAR[{1}]を完了しました。
farmWarDeployer.sendFragment=コンテキスト [{0}] の war [{1}] をクラスターメンバー [{2}] へ送信します。
farmWarDeployer.sendStart=クラスタWar配備パス[{0}]を送信し、War[{1}]を開始しました。
farmWarDeployer.servicingDeploy=アプリケーション [{0}] はすでにサービスを開始しています。もう一度 WAR ファイル [{1}] を更新してください。
farmWarDeployer.servicingUndeploy=アプリケーション [{0}] はサービス中のためバックアップクラスタノードから削除できません。
farmWarDeployer.started=クラスターの FarmWarDeployer を開始しました。
farmWarDeployer.stopped=Cluster FarmWarDeployer が停止しました。
farmWarDeployer.undeployEnd=コンテキスト [{0}] の配備解除が完了しました。
farmWarDeployer.undeployLocal=ローカルコンテキスト [{0}] を配備解除します。
farmWarDeployer.watchDir=クラスタ配備の監視[{0}]が変更されています。

fileMessageFactory.cannotRead=メッセージを読むことができません。このFactoryは書き込み中です。
fileMessageFactory.cannotWrite=メッセージを書き込めません、このFactoryは読み込み中です。
fileMessageFactory.closed=FileMessageFactoryはクローズされています。
fileMessageFactory.deleteFail=[{0}]を削除できませんでした
fileMessageFactory.duplicateMessage=メッセージをもう一度受信します。SenderのActTimeoutが短すぎます。 名前：[{0}] war：[{1}] データ：[{2}] データ長：[{3}]

fileNewFail=[{0}]を作成できません。

warWatcher.cantListWatchDir=監視ディレクトリ [{0}] のファイル一覧を取得できません : ディレクトリの読み取り権限を確認してください。
warWatcher.checkWarResult=WAR ファイル [{1}] について WarInfo.check() は [{0}] を返しました。
warWatcher.checkingWar=WAR ファイル [{0}] をチェックしています。
warWatcher.checkingWars=[{0}]のWARを確認します
warWatcher.listedFileDoesNotExist=[{1}] にあるはずの [{0}] が存在しません。[{1}] のディレクトリ権限を確認してください。
