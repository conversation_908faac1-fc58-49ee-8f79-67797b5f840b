# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

authenticator.certificates=このリクエストにはクライアント認証チェーンがありません
authenticator.changeSessionId=認証時に[{0}]から[{1}]にセッションIDが変更されました。
authenticator.check.authorize=Connector から取得したユーザー名 [{0}] を正当なものとして信頼します。ユーザーのロールは Tomcat Realmから取得します。
authenticator.check.authorizeFail=Realm がユーザー[{0}]を認識しませんでした。 その名前とロールのないプリンシパルを作成します。
authenticator.check.found=既に認証された[{0}]
authenticator.check.sso=認証されていませんが、SSOセッションID [{0}]が見つかりました。 再認証を試みます。
authenticator.formlogin=フォームログインページへの無効な直接参照です
authenticator.jaspicCleanSubjectFail=JASPIC の cleanSubject が失敗しました。
authenticator.jaspicSecureResponseFail=JASPIC処理中のsecure レスポンスに失敗しました
authenticator.jaspicServerAuthContextFail=JASPIC ServerAuthContext インスタンスの取得に失敗しました。
authenticator.loginFail=ログイン失敗
authenticator.manager=トラストマネージャを初期化中の例外です
authenticator.noAuthHeader=クライアントは認証ヘッダーを送信しませんでした。
authenticator.notContext=設定エラー: コンテキストに指定しなければいけません
authenticator.requestBodyTooBig=認証処理中にリクエストボディが大きすぎてキャッシュされませんでした。
authenticator.sessionExpired=ログインプロセスに認められていた時間が過ぎました。継続したいならば，バックボタンを2度押してから再度リンクを押すか，ブラウザを立ち上げ直してください
authenticator.tomcatPrincipalLogoutFail=TomcatPrincipal インスタンスによるログアウトが失敗しました。
authenticator.unauthorized=提供された証明書で認証できません

basicAuthenticator.invalidCharset=指定できる値は、null、空の文字列またはUTF-8です。

digestAuthenticator.cacheRemove=有効なエントリがクライアントのnonceキャッシュから削除され、新しいエントリのためのスペースが確保されました。 リプレイ攻撃が可能になりました。 リプレイ攻撃の可能性を防ぐには、nonceValidityを減らすか、nonceCacheSizeを増やしてください。 このタイプの警告は5分間表示されなくなります。

formAuthenticator.forwardErrorFail=エラーページへ転送中の予期せぬエラー
formAuthenticator.forwardLogin=リクエストメソッドGETを使用してコンテキスト[{3}]のページ[{2}]にメソッド[{1}]で行われた[{0}]の要求をフォワードします。
formAuthenticator.forwardLoginFail=ログインページへの転送での予期しないエラー
formAuthenticator.noErrorPage=コンテキスト[{0}]のFORM認証にエラーページが定義されていません
formAuthenticator.noLoginPage=コンテキスト[{0}]のFORM認証にログインページが定義されていません。

singleSignOn.debug.associate=SSOはアプリケーションセッション[{1}]をSSOセッション[{0}]に関連付けます
singleSignOn.debug.associateFail=SSOセッション[{1}]が存在しないため、SSOはアプリケーションセッション[{0}]を関連付けられませんでした。
singleSignOn.debug.cookieCheck=SSOはSSOクッキーをチェックしています
singleSignOn.debug.cookieNotFound=SSOはSSO Cookieを検出しませんでした。
singleSignOn.debug.deregister=SSOセッション[{1}]に関連付けられたアプリケーションセッション[{0}]を破棄します。
singleSignOn.debug.deregisterFail=キャッシュにないため、SSOセッション[{0}]の登録を解除できませんでした。
singleSignOn.debug.deregisterNone=SSOセッション[{0}]の登録を解除しましたが、関連付けられたアプリケーションセッションは見つかりませんでした。
singleSignOn.debug.hasPrincipal=SSOが以前に認証されたプリンシパル[{0}]を検出しました
singleSignOn.debug.invoke=SSO は [{0}] に対するリクエストを処理しています
singleSignOn.debug.principalCheck=SSO は SSO セッション [{0}] のキャッシュされたプリンシパルを探索しています
singleSignOn.debug.principalFound=SSO のキャッシュされたプリンシパル [{0}] を取得しました。認証タイプは [{1}] です。
singleSignOn.debug.principalNotFound=SSOはキャッシュされたプリンシパルを検出しませんでした。 セッション[{0}]のSSO Cookieを消去しています。
singleSignOn.debug.register=SSOは認証タイプ[{2}]のユーザー[{1}]のSSOセッション[{0}]を登録しています
singleSignOn.debug.removeSession=SSOはSSOセッション[{1}]からアプリケーションセッション[{0}]を削除しています
singleSignOn.debug.sessionLogout=SSOはSSOセッション[{0}]とアプリケーションセッション[{1}]をログアウト処理しています
singleSignOn.debug.sessionTimeout=SSOはSSOセッション[{0}]とアプリケーションセッション[{1}]のタイムアウトを処理しています
singleSignOn.debug.update=SSOはSSOセッション[{0}]を認証タイプ[{1}]に更新します。
singleSignOn.sessionExpire.contextNotFound=Contextが見つからないため、SSOはセッション[{0}]を破棄できません
singleSignOn.sessionExpire.engineNull=Engine がNullだったため、SSOはセッション[{0}]を破棄できません。
singleSignOn.sessionExpire.hostNotFound=ホストが見つからないため SSO セッション [{0}] を破棄できません。
singleSignOn.sessionExpire.managerError=セッションを検索するときにManagerが例外をスローしたため、SSOはセッション[{0}]を破棄できません
singleSignOn.sessionExpire.managerNotFound=Managerが見つからなかったので、SSOはセッション[{0}]を破棄できません。
singleSignOn.sessionExpire.sessionNotFound=セッションが見つからないため、SSOはセッション[{0}]を破棄できません。

spnegoAuthenticator.authHeaderNoToken=クライアントから受信した Negoiate 認証ヘッダにはトークンがありません。
spnegoAuthenticator.authHeaderNotNego=クライアントから受信した認証ヘッダーは Negotiate から始まっていません。
spnegoAuthenticator.serviceLoginFail=サービスプリンシパルとしてログインできません
spnegoAuthenticator.ticketValidateFail=クライアント提供のチケットの検証に失敗しました。
