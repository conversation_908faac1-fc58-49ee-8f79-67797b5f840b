<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE mbeans-descriptors PUBLIC
   "-//Apache Software Foundation//DTD Model MBeans Configuration File"
   "http://jakarta.apache.org/commons/dtds/mbeans-descriptors.dtd">
<mbeans-descriptors>

  <mbean         name="WebappLoader"
          description="Classloader implementation which is specialized for handling web applications"
               domain="Catalina"
                group="Loader"
                 type="org.apache.catalina.loader.WebappLoader">

    <attribute   name="className"
          description="Fully qualified class name of the managed object"
                 type="java.lang.String"
            writeable="false"/>

    <attribute   name="delegate"
          description="The 'follow standard delegation model' flag that will be used to configure our ClassLoader"
                 type="boolean"/>

    <attribute   name="reloadable"
          description="The reloadable flag for this Loader"
                 type="boolean"/>

    <attribute   name="stateName"
          description="The name of the LifecycleState that this component is currently in"
                 type="java.lang.String"
                 writeable="false"/>

    <attribute   name="loaderRepositories"
          description="Repositories set in the real loader"
                 type="[Ljava.lang.String;"
            writeable="false" />

    <attribute   name="loaderRepositoriesString"
          description="Repositories set in the real loader"
                 type="java.lang.String"
             writeable="false" />

    <operation   name="toString"
          description="Info about the loader"
               impact="INFO"
           returnType="String">
    </operation>
  </mbean>


  <mbean         name="WebappClassLoader"
          description="Classloader implementation which is specialized for handling web applications"
               domain="Catalina"
                group="Loader"
                 type="org.apache.catalina.loader.WebappClassLoader">

    <attribute   name="className"
          description="Fully qualified class name of the managed object"
                 type="java.lang.String"
            writeable="false"/>

    <attribute   name="contextName"
          description="Name of the webapp context"
                 type="java.lang.String"
            writeable="false"/>

    <attribute   name="delegate"
          description="The 'follow standard delegation model' flag that will be used to configure our ClassLoader"
                 type="boolean"/>

    <attribute   name="stateName"
          description="The name of the LifecycleState that this component is currently in"
                 type="java.lang.String"
                 writeable="false"/>

    <attribute   name="URLs"
          description="The URLs of this loader"
                 type="[Ljava.net.URL;"/>

  </mbean>


  <mbean         name="ParallelWebappClassLoader"
          description="Classloader implementation which is specialized for handling web applications and is capable of loading classes in parallel"
               domain="Catalina"
                group="Loader"
                 type="org.apache.catalina.loader.ParallelWebappClassLoader">

    <attribute   name="className"
          description="Fully qualified class name of the managed object"
                 type="java.lang.String"
            writeable="false"/>

    <attribute   name="contextName"
          description="Name of the webapp context"
                 type="java.lang.String"
            writeable="false"/>

    <attribute   name="delegate"
          description="The 'follow standard delegation model' flag that will be used to configure our ClassLoader"
                 type="boolean"/>

    <attribute   name="stateName"
          description="The name of the LifecycleState that this component is currently in"
                 type="java.lang.String"
                 writeable="false"/>

    <attribute   name="URLs"
          description="The URLs of this loader"
                 type="[Ljava.net.URL;"/>

  </mbean>
</mbeans-descriptors>
