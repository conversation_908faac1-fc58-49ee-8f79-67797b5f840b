# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cookies.fallToDebug=\n\
\ 비고: 쿠키 오류들이 더 발생하는 경우 DEBUG 레벨 로그로 기록될 것입니다.
cookies.invalidCookieToken=쿠키들: 유효하지 않은 쿠키입니다. 유효한 토큰 또는 인용부호로 처리된 값이 아닙니다.
cookies.invalidSameSiteCookies=알 수 없는 설정 값: [{0}]. 반드시 다음 중 하나여야 합니다: none, lax, strict. 기본 값은 none입니다.
cookies.invalidSpecial=쿠키들: 알 수 없는 특별한 쿠키
cookies.maxCountFail=허용된 최대 쿠키 개수 [{0}]을(를) 초과한 쿠키들이 탐지되었습니다.

headers.maxCountFail=최대 허용 헤더 개수 [{0}]보다 더 많은 헤더들이 탐지되었습니다.

parameters.bytes=입력 [{0}]을(를) 사용하여 처리를 시작합니다.
parameters.copyFail=디버그 로그를 위한 원래의 파라미터 값들을 복사하지 못했습니다.
parameters.decodeFail.debug=문자 디코딩 실패. 값 [{1}](으)로 설정된 파라미터 [{0}]은(는) 무시됩니다.
parameters.decodeFail.info=문자 디코딩이 실패했습니다. 값 [{1}]을(를) 가진 파라미터 [{0}]은(는) 무시되었습니다. 주의: 여기서 인용된 이름과 값은 디코딩 실패로 인해 데이터가 손상되었을 수 있습니다. 손상되지 않은 원본 데이터를 보시려면, 로그 레벨을 디버그 레벨로 하십시오.
parameters.emptyChunk=빈 파라미터 chunk는 무시됩니다.
parameters.fallToDebug=\n\
\ 비고: 파라미터 오류들이 더 발생하는 경우 DEBUG 레벨 로그로 기록될 것입니다.
parameters.invalidChunk=[{0}] 바이트에서 시작하고 [{1}] 바이트에서 끝나며 값이 [{2}]인, 유효하지 않은 chunk는 무시됩니다.
parameters.maxCountFail=단일 요청 ([{0}])에 허용되는 최대 요청 파라미터들의 개수 보다 더 많은 파라미터들이 탐지되었습니다. 이 한계값을 초과하는 파라미터들은 무시되었습니다. 이 한계값을 변경하기 위해서는 Connector의 maxParameterCount 속성을 설정하십시오.
parameters.maxCountFail.fallToDebug=\n\
\ 비고: 이 오류가 더 발생하는 경우 DEBUG 레벨 로그로 기록될 것입니다.
parameters.multipleDecodingFail=문자 디코딩이 실패했습니다. 전체 [{0}]개의 실패가 탐지되었지만, 오직 첫번째 실패만 로그에 기록되었습니다. 모든 실패들을 로그에 남기려면 로그 레벨을 디버그 레벨로 설정하십시오.
parameters.noequal=위치 [{0}]에서 시작하고 위치 [{1}]에서 끝나며 값이 [{2}]인 파라미터 다음에, ''='' 문자가 뒤따르지 않습니다.

rfc6265CookieProcessor.invalidCharInValue=쿠키 값에 유효하지 않은 문자 [{0}]이(가) 있습니다.
rfc6265CookieProcessor.invalidDomain=이 쿠키를 위해, 유효하지 않은 도메인 [{0}]이(가) 지정되었습니다.
rfc6265CookieProcessor.invalidPath=이 쿠키를 위해 유효하지 않은 경로가 설정되었습니다: [{0}]
