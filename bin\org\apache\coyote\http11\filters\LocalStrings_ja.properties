# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

chunkedInputFilter.eos=リクエストボディの読み取り中に予期せぬストリームの終端が見つかりました。
chunkedInputFilter.eosTrailer=トレーラーヘッダーの読み込み中に突然ストリームが終了しました。
chunkedInputFilter.error=以前のエラーのため利用できるデータがありません。
chunkedInputFilter.invalidCrlf=無効なEOLシーケンス（CRまたはLF以外の文字が見つかりました）
chunkedInputFilter.invalidCrlfCRCR=無効なEOLシーケンス（CRCR）
chunkedInputFilter.invalidCrlfNoCR=不正な改行コードです (LFの前にCRがありません)。
chunkedInputFilter.invalidCrlfNoData=無効なEOL順序（読み込み可能なデータがありません）
chunkedInputFilter.invalidHeader=不正なチャンクヘッダーです。
chunkedInputFilter.maxExtension=maxExtensionSizeを超過しました
chunkedInputFilter.maxTrailer=maxTrailerSize を超過しています。

inputFilter.maxSwallow=maxShallowSize を超えました。
