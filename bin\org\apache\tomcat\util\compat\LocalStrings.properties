# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jre8Compat.javaPre8=Class not found so assuming code is running on a pre-Java 8 JVM
jre8Compat.unexpected=Failed to create references to Java 8 classes and methods

jre9Compat.invalidModuleUri=The module URI provided [{0}] could not be converted to a URL for the JarScanner to process
jre9Compat.javaPre9=Class not found so assuming code is running on a pre-Java 9 JVM
jre9Compat.unexpected=Failed to create references to Java 9 classes and methods

jreCompat.noApplicationProtocol=Java Runtime does not support SSLEngine.getApplicationProtocol(). You must use Java 9 to use this feature.
jreCompat.noApplicationProtocols=Java Runtime does not support SSLParameters.setApplicationProtocols(). You must use Java 9 to use this feature.
jreCompat.noDomainLoadStoreParameter=Java Runtime does not support DKS key store type. You must use Java 8 or later to use this feature.
jreCompat.noServerCipherSuiteOrder=Java Runtime does not support "useServerCipherSuitesOrder". You must use Java 8 or later to use this feature.
