# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

catalina.serverStartFail=<PERSON><PERSON> kann nicht starten, da eine benötigte Server Komponente Startprobleme hat

contextConfig.altDDNotFound=alt-dd Datei [{0}] konnte nicht gefunden werden
contextConfig.applicationUrl=Kann die URL für web.xml der Applikation nicht ermitteln
contextConfig.authenticatorConfigured=Es wurde ein Authenticator für Methode [{0}] konfiguriert
contextConfig.contextMissing=Fehlende context.xml: [{0}]
contextConfig.defaultMissing=Keine globale web.xml gefunden
contextConfig.defaultPosition=Vorgekommen in Zeile [{0}] Spalte [{1}]
contextConfig.inputStreamWebResource=Kann Annotationen für Web Ressource [{0}] nicht verarbeiten
contextConfig.missingRealm=Es wurde kein Realm konfiguriert gegen den man sich authentifizieren kann
contextConfig.processAnnotationsDir.debug=Durchsuche Verzeichnis nach Klassen mit Annotationen [{0}]

engineConfig.start=EngineConfig: verarbeite START
engineConfig.stop=EngineConfig: verarbeite STOP

hostConfig.deployDir=Deploye Web-Applikations-Verzeichnis [{0}]
hostConfig.deployWar.error=Fehler beim deployen des Web-Applikationsarchivs [{0}]
hostConfig.docBaseUrlInvalid=Die angegebene docBase kann nicht als URL ausgedrückt werden
hostConfig.jmx.unregister=Unregistrierter Kontext [{0}] fehlgeschlagen
hostConfig.stop=HostConfig: Verarbeitung GESTOPPT

userConfig.database=Fehler beim Laden der Benutzer Datenbank.
userConfig.error=Fehler beim deployen einer Web-Applikation für den Benutzer [{0}]
userConfig.start=UserConfig: Verarbeite START

versionLoggerListener.catalina.base=CATALINA_BASE:         {0}
versionLoggerListener.catalina.home=CATALINA_HOME:         {0}
versionLoggerListener.os.arch=Architektur:           {0}
versionLoggerListener.serverInfo.server.version=Server Version:        {0}
versionLoggerListener.vm.vendor=JVM Hersteller:        {0}
