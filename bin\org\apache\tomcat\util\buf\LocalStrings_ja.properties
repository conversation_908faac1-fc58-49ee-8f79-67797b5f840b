# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

asn1Parser.lengthInvalid=入力データバイト長 [{1}] に対して無効なバイト長 [{0}] が報告されました
asn1Parser.tagMismatch=期待値は [{0}] でしたが、実際に見つかった値は [{1}] でした

b2cConverter.unknownEncoding=文字エンコーディング [{0}] は未対応です。

byteBufferUtils.cleaner=直接ByteBufferクリーナーを使用することはできません、メモリリークが発生する可能性があります。

encodedSolidusHandling.invalid=値 [{0}] は認識されません

hexUtils.fromHex.nonHex=入力は16進数でなければなりません
hexUtils.fromHex.oddDigits=入力は、偶数の16進数で構成する必要があります。

uDecoder.eof=予期せぬ場所で終端に達しました。
uDecoder.noSlash="/" を符号化して含めることはできません。
uDecoder.urlDecode.conversionError=文字セット[{1}]を使用した[{0}]のデコードに失敗しました
uDecoder.urlDecode.missingDigit=％文字の後ろに2つの16進数字が続く必要があるため、[{0}]のデコードに失敗しました。
