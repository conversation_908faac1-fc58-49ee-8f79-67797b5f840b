# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

bioReceiver.already.started=サーバーソケットはすでに開始しています。
bioReceiver.run.fail=レプリケーションリスナーを実行できません。
bioReceiver.socket.closeFailed=ソケットを切断できませんでした。
bioReceiver.start.fail=クラスタレシーバを起動できません
bioReceiver.threadpool.fail=スレッドプールを初期化できません。リスナーを開始しませんでした。
bioReceiver.threads.busy=全ての BIO サーバーレプリケーションスレッドがビジー状態です。スレッドが解放されるまで新しいリクエストは処理できません。

bioReplicationTask.messageDataReceived.error=messageDataReceivedから送出されたエラー
bioReplicationTask.reader.closeFailed=Readerを閉じることに失敗しました。
bioReplicationTask.socket.closeFailed=ソケットクロースに失敗
bioReplicationTask.unable.sendAck=チャンネルへACKを送信できません。チャンネルが切断されているかもしれません: [{0}]
bioReplicationTask.unable.service=bio ソケットを開始できません。

bioSender.ack.eof=ローカルポート[{0}：{1、number、integer}]でEOF
bioSender.ack.missing=[{0}:{1,number,integer}] から [{2,number,integer}] ms 以内に確認応答(ACK)を読み取ることができませんでした。ソケットを切断して再試行してください。
bioSender.ack.wrong=ローカルポート [{0}:{1,number,integer}] から 10 バイト読み込んだ後に正しい ACK が見つかりません。
bioSender.closeSocket=[{0}：{1、number、integer}]（close count {2、number、integer}）に送信側のクローズソケット
bioSender.disconnect=[{0}:{1,number,integer}](切断カウント{2,number,integer})からのSender切断
bioSender.fail.AckReceived=失敗したACK：org.apache.catalina.tribes.transport.Constants.FAIL_ACK_DATAの受信
bioSender.openSocket=Sender オブジェクトが [{0}:{1,number,integer}] (接続数 {2,number,integer}) へソケット接続を開始しました。
bioSender.openSocket.failure=開いているSender側ソケット[{0}：{1,number,integer}]失敗！ （オープン失敗カウント{2,number,integer})）
bioSender.send.again=[{0}:{1,number,integer}] へデータを再送します。

pooledMultiSender.retrieve.fail=SenderプールからSenderを取得できません。
pooledMultiSender.unable.retrieve.sender=データSenderを取得できません。タイムアウト（[{0}] ms）エラー。
