# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

chunkedInputFilter.eos=Fin du flux inattendue durant la lecture du corps de la requête
chunkedInputFilter.eosTrailer=Fin inattendue de flux lors de la lecture des en-têtes de fin (trailer headers)
chunkedInputFilter.error=Aucune donnée disponible suite à l'erreur précédente
chunkedInputFilter.invalidCrlf=Séquence de fin de ligne invalide, un caractère autre que CR ou LF a été trouvé
chunkedInputFilter.invalidCrlfCRCR=Séquence de fin de ligne invalide, CR CR
chunkedInputFilter.invalidCrlfNoCR=Séquence de fin de ligne incorrecte (manque CR devant LF)
chunkedInputFilter.invalidCrlfNoData=Séquence de fin de ligne invalide (aucune donnée disponible en lecture)
chunkedInputFilter.invalidHeader=En-tête de morceau (chunk) invalide
chunkedInputFilter.maxExtension=maxExtensionSize a été dépassé
chunkedInputFilter.maxTrailer=maxTrailerSize a été dépassé

inputFilter.maxSwallow=maxSwallowSize a été dépassé
