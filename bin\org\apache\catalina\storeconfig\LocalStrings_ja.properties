# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

config.storeContextError=コンテキスト[{0}]を格納中にエラーが発生しました。
config.storeServerError=server.xml 書き込み中のエラー

factory.storeNoDescriptor=要素クラス[{0}]の記述子が構成されていません！
factory.storeTag=ストアタグ[{0}]（オブジェクト：[{1}]）

storeConfigListener.notServer=Server 以外のコンポーネントに指定されたリスナーは無視します。

storeFileMover.directoryCreationError=ディレクトリ [{0}] を作成できません
storeFileMover.renameError=ファイル名 [{0}] を [{1}] に変更できません。
