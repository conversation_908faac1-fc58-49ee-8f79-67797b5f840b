# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

mapper.addHostAlias.success=Alias [{0}] registrado para el servidor [{1}]

mapperListener.pauseContext=El registro de contexto [{0}] como siendo recargado para el servicio [{1}]\n
mapperListener.registerHost=Registrar máquina [{0}] en dominio [{1}] para el conector [{2}]
mapperListener.registerWrapper=Registre la envoltura [{0}] en el contexto [{1}] para el servicio [{2}]
mapperListener.unknownDefaultHost=Máquina por defecto desconocida: [{0}] para el conector [{1}]
mapperListener.unregisterHost=Desregistrar máquina [{0}] en dominio [{1}] para el conector [{2}]
