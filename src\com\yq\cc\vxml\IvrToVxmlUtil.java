package com.yq.cc.vxml;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * IVR 流程转 VXML 工具类
 * 将 JSON 格式的 IVR 呼叫流程配置转换为 VXML 格式
 */
public class IvrToVxmlUtil {

    /**
     * 从文件读取 IVR JSON 配置并转换为 VXML
     *
     * @param jsonFilePath JSON 文件路径
     * @return VXML 字符串
     * @throws IOException 文件读取异常
     */
    public static String convertFromFile(String jsonFilePath) throws IOException {
        String jsonContent = readFileContent(jsonFilePath);
        return convertJsonToVxml(jsonContent);
    }

    /**
     * 将 JSON 字符串转换为 VXML
     *
     * @param jsonContent JSON 字符串
     * @return VXML 字符串
     */
    public static String convertJsonToVxml(String jsonContent) {
        try {
            JSONObject ivrConfig = JSON.parseObject(jsonContent);
            return generateVxml(ivrConfig);
        } catch (Exception e) {
            throw new RuntimeException("转换 IVR JSON 到 VXML 失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成 VXML 内容
     */
    private static String generateVxml(JSONObject ivrConfig) {
        StringBuilder vxml = new StringBuilder();

        // VXML 头部
        vxml.append("<?xml version=\"1.0\" encoding=\"GBK\"?>\n");
        vxml.append("<vxml version=\"2.1\" xmlns=\"http://www.w3.org/2001/vxml\">\n");

        // 解析节点和连线
        JSONArray nodeList = ivrConfig.getJSONArray("nodeList");
        JSONArray lineList = ivrConfig.getJSONArray("lineList");

        // 构建流程图
        Map<String, IvrNode> nodeMap = parseNodes(nodeList);
        Map<String, List<IvrLine>> nodeConnections = parseConnections(lineList);

        // 生成主表单
        vxml.append("  <form id=\"mainForm\">\n");

        // 找到开始节点
        IvrNode startNode = findStartNode(nodeMap);
        if (startNode != null) {
            generateFormContent(vxml, startNode, nodeMap, nodeConnections, new HashSet<>());
        }

        vxml.append("  </form>\n\n");

        // 生成结束块
        vxml.append("  <!-- 结束语 -->\n");
        vxml.append("  <block id=\"endBlock\">\n");

        // 查找结束节点的语音内容
        IvrNode endNode = findEndNode(nodeMap);
        if (endNode != null && endNode.voicePlayContent != null && !endNode.voicePlayContent.isEmpty()) {
            vxml.append("    <prompt>").append(escapeXml(endNode.voicePlayContent)).append("</prompt>\n");
        } else {
            vxml.append("    <prompt>谢谢，再见！</prompt>\n");
        }

        vxml.append("    <exit/>\n");
        vxml.append("  </block>\n");
        vxml.append("</vxml>\n");

        return vxml.toString();
    }

    /**
     * 解析节点列表
     */
    private static Map<String, IvrNode> parseNodes(JSONArray nodeList) {
        Map<String, IvrNode> nodeMap = new HashMap<>();

        for (int i = 0; i < nodeList.size(); i++) {
            JSONObject nodeJson = nodeList.getJSONObject(i);
            IvrNode node = new IvrNode();

            node.id = nodeJson.getString("id");
            node.name = nodeJson.getString("name");
            node.type = nodeJson.getString("type");

            // 解析多实例配置
            JSONObject multiInstance = nodeJson.getJSONObject("multiInstance");
            if (multiInstance != null) {
                node.voicePlayContent = multiInstance.getString("voicePlayContent");
                node.voicePlayType = multiInstance.getString("voicePlayType");
                node.paramName = multiInstance.getString("paramName");
                node.paramMinSize = multiInstance.getString("paramMinSize");
                node.paramMaxSize = multiInstance.getString("paramMaxSize");
            }

            // 直接从节点获取语音播放内容（兼容不同的数据结构）
            if (node.voicePlayContent == null || node.voicePlayContent.isEmpty()) {
                node.voicePlayContent = nodeJson.getString("voicePlayContent");
            }

            nodeMap.put(node.id, node);
        }

        return nodeMap;
    }

    /**
     * 解析连线列表
     */
    private static Map<String, List<IvrLine>> parseConnections(JSONArray lineList) {
        Map<String, List<IvrLine>> connections = new HashMap<>();

        for (int i = 0; i < lineList.size(); i++) {
            JSONObject lineJson = lineList.getJSONObject(i);
            IvrLine line = new IvrLine();

            line.id = lineJson.getString("id");
            line.from = lineJson.getString("from");
            line.to = lineJson.getString("to");
            line.label = lineJson.getString("label");

            // 解析条件表达式
            JSONObject expression = lineJson.getJSONObject("expression");
            if (expression != null) {
                line.condition = new IvrCondition();
                line.condition.param = expression.getString("param");
                line.condition.conditions = expression.getString("conditions");
                line.condition.val1 = expression.getString("val1");
            }

            // 按起始节点分组
            connections.computeIfAbsent(line.from, k -> new ArrayList<>()).add(line);
        }

        return connections;
    }

    /**
     * 查找开始节点
     */
    private static IvrNode findStartNode(Map<String, IvrNode> nodeMap) {
        for (IvrNode node : nodeMap.values()) {
            if ("start".equals(node.type)) {
                return node;
            }
        }
        return null;
    }

    /**
     * 查找结束节点
     */
    private static IvrNode findEndNode(Map<String, IvrNode> nodeMap) {
        for (IvrNode node : nodeMap.values()) {
            if ("end".equals(node.type)) {
                return node;
            }
        }
        return null;
    }

    /**
     * 生成表单内容
     */
    private static void generateFormContent(StringBuilder vxml, IvrNode currentNode,
            Map<String, IvrNode> nodeMap, Map<String, List<IvrLine>> nodeConnections,
            Set<String> processedNodes) {

        if (processedNodes.contains(currentNode.id)) {
            return; // 避免循环处理
        }
        processedNodes.add(currentNode.id);

        switch (currentNode.type) {
            case "start":
                generateStartBlock(vxml, currentNode, nodeMap, nodeConnections, processedNodes);
                break;
            case "userTask":
                generateUserTaskBlock(vxml, currentNode, nodeMap, nodeConnections, processedNodes);
                break;
            case "serviceTask":
                generateServiceTaskBlock(vxml, currentNode, nodeMap, nodeConnections, processedNodes);
                break;
            case "end":
                generateEndBlock(vxml, currentNode);
                break;
        }
    }

    /**
     * 生成开始节点块
     */
    private static void generateStartBlock(StringBuilder vxml, IvrNode startNode,
            Map<String, IvrNode> nodeMap, Map<String, List<IvrLine>> nodeConnections,
            Set<String> processedNodes) {

        // 开始节点通常只是一个入口，直接处理下一个节点
        List<IvrLine> nextLines = nodeConnections.get(startNode.id);
        if (nextLines != null && !nextLines.isEmpty()) {
            IvrLine nextLine = nextLines.get(0);
            IvrNode nextNode = nodeMap.get(nextLine.to);
            if (nextNode != null) {
                generateFormContent(vxml, nextNode, nodeMap, nodeConnections, processedNodes);
            }
        }
    }

    /**
     * 生成用户任务块（播放语音）
     */
    private static void generateUserTaskBlock(StringBuilder vxml, IvrNode userTaskNode,
            Map<String, IvrNode> nodeMap, Map<String, List<IvrLine>> nodeConnections,
            Set<String> processedNodes) {

        // 如果有语音内容，生成播放块
        if (userTaskNode.voicePlayContent != null && !userTaskNode.voicePlayContent.isEmpty()) {
            vxml.append("    <!-- ").append(userTaskNode.name).append(" -->\n");
            vxml.append("    <block>\n");
            vxml.append("      <prompt>").append(escapeXml(userTaskNode.voicePlayContent)).append("</prompt>\n");
            vxml.append("    </block>\n\n");
        }

        // 处理下一个节点
        List<IvrLine> nextLines = nodeConnections.get(userTaskNode.id);
        if (nextLines != null && !nextLines.isEmpty()) {
            IvrLine nextLine = nextLines.get(0);
            IvrNode nextNode = nodeMap.get(nextLine.to);
            if (nextNode != null) {
                generateFormContent(vxml, nextNode, nodeMap, nodeConnections, processedNodes);
            }
        }
    }

    /**
     * 生成服务任务块（用户输入）
     */
    private static void generateServiceTaskBlock(StringBuilder vxml, IvrNode serviceTaskNode,
            Map<String, IvrNode> nodeMap, Map<String, List<IvrLine>> nodeConnections,
            Set<String> processedNodes) {

        vxml.append("    <!-- ").append(serviceTaskNode.name).append(" -->\n");

        // 生成输入字段
        String fieldName = serviceTaskNode.paramName != null ? serviceTaskNode.paramName.toLowerCase() : "userInput";
        String minLength = serviceTaskNode.paramMinSize != null ? serviceTaskNode.paramMinSize : "1";
        String maxLength = serviceTaskNode.paramMaxSize != null ? serviceTaskNode.paramMaxSize : "1";

        vxml.append("    <field name=\"").append(fieldName).append("\" type=\"digits?length=").append(maxLength).append("\">\n");

        // 如果有提示语音，添加提示
        if (serviceTaskNode.voicePlayContent != null && !serviceTaskNode.voicePlayContent.isEmpty()) {
            vxml.append("      <prompt>").append(escapeXml(serviceTaskNode.voicePlayContent)).append("</prompt>\n");
        }

        // 生成语法规则
        generateGrammarRules(vxml, serviceTaskNode, nodeConnections);

        // 生成填充处理逻辑
        vxml.append("      <filled>\n");
        generateFilledLogic(vxml, serviceTaskNode, nodeMap, nodeConnections, processedNodes);
        vxml.append("      </filled>\n");

        // 生成错误处理
        generateErrorHandling(vxml, serviceTaskNode, nodeMap, nodeConnections);

        vxml.append("    </field>\n\n");
    }

    /**
     * 生成语法规则
     */
    private static void generateGrammarRules(StringBuilder vxml, IvrNode serviceTaskNode,
            Map<String, List<IvrLine>> nodeConnections) {

        List<IvrLine> outgoingLines = nodeConnections.get(serviceTaskNode.id);
        if (outgoingLines == null || outgoingLines.isEmpty()) {
            return;
        }

        vxml.append("      <grammar mode=\"dtmf\" root=\"main\">\n");
        vxml.append("        <rule id=\"main\">\n");
        vxml.append("          <one-of>\n");

        // 收集所有条件中定义的输入值
        Set<String> validInputs = new HashSet<>();
        boolean hasEqConditions = false;

        for (IvrLine line : outgoingLines) {
            if (line.condition != null && line.condition.val1 != null) {
                if ("eq".equals(line.condition.conditions)) {
                    validInputs.add(line.condition.val1);
                    hasEqConditions = true;
                }
                // 对于 neq 条件，我们需要包含所有可能的值除了指定的值
            }
        }

        // 如果只有 neq 条件或没有条件，允许所有数字输入
        if (!hasEqConditions) {
            for (int i = 0; i <= 9; i++) {
                vxml.append("            <item>").append(i).append("</item>\n");
            }
        } else {
            // 只允许 eq 条件中明确指定的输入值
            for (String input : validInputs) {
                vxml.append("            <item>").append(escapeXml(input)).append("</item>\n");
            }
        }

        vxml.append("          </one-of>\n");
        vxml.append("        </rule>\n");
        vxml.append("      </grammar>\n");
    }

    /**
     * 生成填充逻辑（条件分支）
     */
    private static void generateFilledLogic(StringBuilder vxml, IvrNode serviceTaskNode,
            Map<String, IvrNode> nodeMap, Map<String, List<IvrLine>> nodeConnections,
            Set<String> processedNodes) {

        List<IvrLine> outgoingLines = nodeConnections.get(serviceTaskNode.id);
        if (outgoingLines == null || outgoingLines.isEmpty()) {
            vxml.append("        <goto next=\"#endBlock\"/>\n");
            return;
        }

        String fieldName = serviceTaskNode.paramName != null ? serviceTaskNode.paramName.toLowerCase() : "userInput";

        // 收集所有条件分支
        List<IvrLine> conditionalLines = new ArrayList<>();
        List<IvrLine> unconditionalLines = new ArrayList<>();

        for (IvrLine line : outgoingLines) {
            if (line.condition != null) {
                conditionalLines.add(line);
            } else {
                unconditionalLines.add(line);
            }
        }

        // 处理条件分支
        if (!conditionalLines.isEmpty()) {
            // 分离 eq 和 neq 条件
            List<IvrLine> eqLines = new ArrayList<>();
            List<IvrLine> neqLines = new ArrayList<>();

            for (IvrLine line : conditionalLines) {
                if ("eq".equals(line.condition.conditions)) {
                    eqLines.add(line);
                } else if ("neq".equals(line.condition.conditions)) {
                    neqLines.add(line);
                }
            }

            // 处理 eq 条件 - 这些是语法允许的输入
            for (IvrLine line : eqLines) {
                vxml.append("        <if cond=\"").append(fieldName).append(" == '").append(line.condition.val1).append("'\">\n");

                IvrNode targetNode = nodeMap.get(line.to);
                if (targetNode != null) {
                    generateTargetNodeProcessing(vxml, targetNode, nodeMap, nodeConnections, processedNodes);
                }

                vxml.append("        </if>\n");
            }

            // 对于 neq 条件，我们不在 filled 中处理，因为语法已经限制了输入
            // neq 条件的情况会通过 nomatch 处理

            // 如果只有 neq 条件而没有 eq 条件，说明所有有效输入都应该跳转到结束
            if (eqLines.isEmpty() && !neqLines.isEmpty()) {
                // 这种情况下，任何有效输入都直接结束
                vxml.append("        <goto next=\"#endBlock\"/>\n");
            }

            // 如果有无条件分支，作为默认处理
            if (!unconditionalLines.isEmpty()) {
                IvrNode targetNode = nodeMap.get(unconditionalLines.get(0).to);
                if (targetNode != null) {
                    generateTargetNodeProcessing(vxml, targetNode, nodeMap, nodeConnections, processedNodes);
                }
            }
        } else if (!unconditionalLines.isEmpty()) {
            // 只有无条件分支，直接处理
            IvrNode targetNode = nodeMap.get(unconditionalLines.get(0).to);
            if (targetNode != null) {
                generateTargetNodeProcessing(vxml, targetNode, nodeMap, nodeConnections, processedNodes);
            }
        } else {
            // 没有任何分支，直接结束
            vxml.append("        <goto next=\"#endBlock\"/>\n");
        }
    }

    /**
     * 生成目标节点处理逻辑
     */
    private static void generateTargetNodeProcessing(StringBuilder vxml, IvrNode targetNode,
            Map<String, IvrNode> nodeMap, Map<String, List<IvrLine>> nodeConnections,
            Set<String> processedNodes) {

        if ("userTask".equals(targetNode.type) && targetNode.voicePlayContent != null) {
            // 内联播放语音
            vxml.append("          <prompt>").append(escapeXml(targetNode.voicePlayContent)).append("</prompt>\n");

            // 继续处理下一个节点
            List<IvrLine> nextLines = nodeConnections.get(targetNode.id);
            if (nextLines != null && !nextLines.isEmpty()) {
                IvrNode nextNode = nodeMap.get(nextLines.get(0).to);
                if (nextNode != null && "end".equals(nextNode.type)) {
                    vxml.append("          <goto next=\"#endBlock\"/>\n");
                } else {
                    // 对于非结束节点，也需要跳转到结束块
                    vxml.append("          <goto next=\"#endBlock\"/>\n");
                }
            } else {
                // 没有后续节点，直接结束
                vxml.append("          <goto next=\"#endBlock\"/>\n");
            }
        } else if ("end".equals(targetNode.type)) {
            vxml.append("          <goto next=\"#endBlock\"/>\n");
        } else {
            // 其他类型的节点，直接结束
            vxml.append("          <goto next=\"#endBlock\"/>\n");
        }
    }

    /**
     * 生成错误处理逻辑
     */
    private static void generateErrorHandling(StringBuilder vxml, IvrNode serviceTaskNode,
            Map<String, IvrNode> nodeMap, Map<String, List<IvrLine>> nodeConnections) {

        vxml.append("      <noinput>\n");
        vxml.append("        <prompt>抱歉，没有收到您的输入。</prompt>\n");
        vxml.append("        <reprompt/>\n");
        vxml.append("      </noinput>\n");

        // 检查是否有 neq 条件，如果有，nomatch 需要特殊处理
        List<IvrLine> outgoingLines = nodeConnections.get(serviceTaskNode.id);
        boolean hasNeqCondition = false;
        IvrLine neqLine = null;

        if (outgoingLines != null) {
            for (IvrLine line : outgoingLines) {
                if (line.condition != null && "neq".equals(line.condition.conditions)) {
                    hasNeqCondition = true;
                    neqLine = line;
                    break;
                }
            }
        }

        vxml.append("      <nomatch>\n");
        if (hasNeqCondition && neqLine != null) {
            // 对于 neq 条件，无效输入应该触发相应的处理
            IvrNode targetNode = nodeMap.get(neqLine.to);
            if (targetNode != null && "userTask".equals(targetNode.type) && targetNode.voicePlayContent != null) {
                vxml.append("        <prompt>").append(escapeXml(targetNode.voicePlayContent)).append("</prompt>\n");
            } else {
                vxml.append("        <prompt>抱歉，您的输入无效。</prompt>\n");
            }
            vxml.append("        <exit/>\n");
        } else {
            vxml.append("        <prompt>抱歉，您的输入无效。</prompt>\n");
            vxml.append("        <exit/>\n");
        }
        vxml.append("      </nomatch>\n");
    }

    /**
     * 生成结束节点块
     */
    private static void generateEndBlock(StringBuilder vxml, IvrNode endNode) {
        // 结束块在主表单外部生成
    }

    /**
     * XML 转义
     */
    private static String escapeXml(String text) {
        if (text == null) return "";
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&apos;");
    }

    /**
     * 读取文件内容
     */
    private static String readFileContent(String filePath) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(filePath), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }

    /**
     * IVR 节点数据类
     */
    private static class IvrNode {
        String id;
        String name;
        String type;
        String voicePlayContent;
        String voicePlayType;
        String paramName;
        String paramMinSize;
        String paramMaxSize;
    }

    /**
     * IVR 连线数据类
     */
    private static class IvrLine {
        String id;
        String from;
        String to;
        String label;
        IvrCondition condition;
    }

    /**
     * IVR 条件数据类
     */
    private static class IvrCondition {
        String param;
        String conditions;
        String val1;
    }

    /**
     * 主方法，用于测试
     */
    public static void main(String[] args) {
        try {
            String vxml = convertFromFile("src/com/yq/cc/vxml/ivr.json");
            System.out.println("生成的 VXML:");
            System.out.println(vxml);

            // 可选：将结果写入文件
            try (PrintWriter writer = new PrintWriter(
                    new OutputStreamWriter(new FileOutputStream("src/com/yq/cc/vxml/generated.vxml"),
                    StandardCharsets.UTF_8))) {
                writer.print(vxml);
                System.out.println("\nVXML 已保存到 generated.vxml");
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
