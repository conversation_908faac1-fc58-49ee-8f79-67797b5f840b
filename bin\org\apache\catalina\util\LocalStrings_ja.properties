# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

SecurityUtil.doAsPrivilege=PrivilegedExceptionActionブロックを実行中に例外が発生しました。

customObjectInputStream.logRequired=ロギングによるクラス名のフィルタリングには有効なロガーが必要です
customObjectInputStream.nomatch=クラス [{0}] はデシリアライズが許可されたクラス名の正規表現 [{1}] にマッチしません。

extensionValidator.extension-not-found-error=ExtensionValidator[{0}][{1}]: 必要な拡張 [{2}] が見つかりません。
extensionValidator.extension-validation-error=ExtensionValidator[{0}]: 必要な拡張 [{1}] が見つかりません。
extensionValidator.failload=拡張 [{0}] のロードに失敗しました
extensionValidator.web-application-manifest=Webアプリケーションマニフェスト

hexUtil.bad=無効な16進数値です
hexUtil.odd=奇数桁の16進数値です

introspection.classLoadFailed=クラス [{0}] の読み込みに失敗しました。

lifecycleBase.alreadyDestroyed=destroy()メソッドが既に呼び出された後で、コンポーネント[{0}]に対して呼び出されました。 2番目の呼び出しは無視されます。
lifecycleBase.alreadyStarted=以前に start() を呼び出したコンポーネント [{0}] の start() を呼び出しました。二度目の呼び出しは無視します。
lifecycleBase.alreadyStopped=stop()メソッドが既にコールされた後、コンポーネント[{0}]に対して呼び出されました。 2番目の呼び出しは無視されます。
lifecycleBase.destroyFail=コンポーネント [{0}] を破棄できません。
lifecycleBase.destroyStopFail=失敗したコンポーネント[{0}]に対してstop()を呼び出してクリーンアップをトリガーしましたが、失敗しました。
lifecycleBase.initFail=コンポーネント[{0}] の初期化に失敗しました。
lifecycleBase.invalidTransition=状態[{2}]のコンポーネント[{1}]の無効なライフサイクル遷移が試みられました（[{0}]）。
lifecycleBase.setState=[{0}]の状態を[{1}]に設定します
lifecycleBase.startFail=コンポーネント[{0}]の開始に失敗しました。
lifecycleBase.stopFail=コンポーネント [{0}] を停止できません。

lifecycleMBeanBase.registerFail=コンポーネントの初期化時にオブジェクト [{0}] を名前 [{1}] で登録できませんでした。

netmask.cidrNegative=CIDR [{0}] に負の値が指定されています。
netmask.cidrNotNumeric=CIDR [{0}]は数値ではありません
netmask.cidrTooBig=CIDR [{0}] はアドレス範囲 [{1}] を越えています。
netmask.invalidAddress=アドレス[{0}]は無効です

parameterMap.locked=ロックされたParameterMapは変更が許されません

resourceSet.locked=ロックされたResourceSetは変更が許されません

sessionIdGeneratorBase.createRandom=セッション ID を生成するための SecureRandom インスタンスの作成に [{1}] ミリ秒かかりました。アルゴリズムは [{0}] です。
sessionIdGeneratorBase.random=クラス [{0}] の乱数発生器の初期化の例外です
sessionIdGeneratorBase.randomAlgorithm=アルゴリズム[{0}]を使用して乱数ジェネレータを初期化する際の例外
sessionIdGeneratorBase.randomProvider=プロバイダ[{0}]を使用して乱数ジェネレータを初期化中に例外が発生しました
