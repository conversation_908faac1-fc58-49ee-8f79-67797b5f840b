# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

ajpMessage.invalidPos=위치 [{0}](으)로부터 바이트들을 읽기를 요청 받았는데, 이는 해당 AJP 메시지의 끝 위치를 넘어섰습니다.

ajpmessage.invalid=서명 [{0}]와(과) 함께, 유효하지 않은 메시지를 받았습니다.
ajpmessage.invalidLength=길이가 [{0}]인 유효하지 않은 메시지를 받았습니다.
ajpmessage.null=널 값을 추가할 수 없습니다.
ajpmessage.overflow=버퍼에 [{0}] 바이트들을 위치 [{1}]에 추가하는 동안 오버플로우 오류 발생

ajpprocessor.certs.fail=인증서를 변환시키지 못했습니다.
ajpprocessor.header.error=헤더 메시지를 파싱하지 못했습니다.
ajpprocessor.header.tooLong=길이가 [{0}]인 헤더 메시지를 받았지만, 패킷 크기는 단지 [{1}]입니다.
ajpprocessor.readtimeout=소켓으로부터 데이터를 읽으려는 시도가 제한 시간 초과되었습니다.
ajpprocessor.request.prepare=요청을 준비하는 중 오류 발생
ajpprocessor.request.process=요청 처리 중 오류 발생
ajpprocessor.unknownAttribute=리버스 프록시 서버로부터 알 수 없는 요청 속성 [{0}]이(가) 접수되어 요청 처리를 거부합니다.

ajpprotocol.noSSL=AJP와 함께 SSL은 지원되지 않습니다. [{0}]을(를) 위한 SSL 호스트 설정은 무시되었습니다.
ajpprotocol.noSecret=AJP 연결자는 secretRequired="true"로 구성되었으나 보안 속성이 널 또는 ""입니다. 이 조합은 유효하지 않습니다.
ajpprotocol.noUpgrade=AJP에서 프로토콜 업그레이드는 지원되지 않습니다. [{0}]을(를) 위한 UpgradeProtocol 설정은 무시됩니다.
ajpprotocol.noUpgradeHandler=AJP를 사용할 때, 업그레이드는 지원되지 않습니다. HttpUpgradeHandler [{0}]은(는) 처리될 수 없습니다.
