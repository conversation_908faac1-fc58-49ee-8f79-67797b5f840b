# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

domainFilterInterceptor.member.refused=メンバーはクラスター [{0}] への参加を拒否されました。
domainFilterInterceptor.message.refused=クラスター [{0}] から受信したメッセージは拒否されました。

encryptInterceptor.algorithm.required=暗号化アルゴリズムが必要です。完全指定。 AES/CBC/PKCS5Padding
encryptInterceptor.algorithm.unsupported-mode=EncryptInterceptorはブロック暗号モード [{0}]をサポートしていません。
encryptInterceptor.decrypt.error.short-message=メッセージの復号に失敗: メッセージの末尾が途切れています
encryptInterceptor.decrypt.failed=メッセージの復号に失敗しました。
encryptInterceptor.encrypt.failed=メッセージを暗号化できません。
encryptInterceptor.init.failed=EncryptInterceptorの初期化に失敗しました
encryptInterceptor.key.required=暗号化キーが必要です。
encryptInterceptor.tcpFailureDetector.ordering=EncryptInterceptorはTcpFailureDetectorの上流になければなりません。 チャネルインターセプターパイプラインのTcpFailureDetectorの前にリストされるようにEncryptInterceptorを再設定してください。

fragmentationInterceptor.fragments.missing=フラグメントが見つかりません。
fragmentationInterceptor.heartbeat.failed=fragmentationInterceptorでハートビートクリーンアップを実行できません。

gzipInterceptor.compress.failed=バイトデータを圧縮できません。
gzipInterceptor.decompress.failed=圧縮されたバイトデータを展開できません。

messageDispatchInterceptor.AsyncMessage.failed=非同期メッセージ処理中のエラー
messageDispatchInterceptor.completeMessage.failed=完了したメッセージを報告できません。
messageDispatchInterceptor.errorMessage.failed=エラーメッセージを返すことができません。
messageDispatchInterceptor.queue.full=非同期キューが満杯です。現在は [{1}] バイトで上限の [{0}] バイトに達しています。
messageDispatchInterceptor.unableAdd.queue=非同期キューにメッセージを登録できませんでした。キューの不具合かもしれません。
messageDispatchInterceptor.warning.optionflag=警告: 非同期オプションフラグを上書きしたため、他のアプリケーションが使用する可能性のある Channel.SEND_OPTIONS_ASYNCHRONOUS は無効化されます。

nonBlockingCoordinator.electionMessage.sendfailed=メンバー [{0}] に調停メッセージを送信できません。
nonBlockingCoordinator.heartbeat.failed=ハートビートを実行できません。
nonBlockingCoordinator.heartbeat.inconsistency=ハートビートが不一致を発見し、イレクションを再開します。
nonBlockingCoordinator.memberAdded.failed=メンバーが追加されたときにイレクションを開始できません。
nonBlockingCoordinator.memberAlive.failed=動作チェックが実行できなかったため、メンバーは停止しているものとして扱います。
nonBlockingCoordinator.memberDisappeared.failed=メンバーが削除されたときにイレクションを開始できません。
nonBlockingCoordinator.processCoordinationMessage.failed=調停メッセージを処理中のエラー。致命的な問題が発生している可能性があります。

orderInterceptor.messageAdded.sameCounter=同じカウンタにメッセージが追加されました。同期バグがあります。 Order インターセプタを無効にして下さい。

staticMembershipInterceptor.no.failureDetector=TcpFailureDetector がありません。静的メンバーの自動検出機能は正常に動作しません。TcpFailureDetector 配下に StaticMembershipInterceptor を定義すれば、静的メンバーの自動検出機能が動作するでしょう。
staticMembershipInterceptor.no.pingInterceptor=TcpPingInterceptorが存在しないため、静的メンバーのヘルスチェックは正常に機能しません。TcpPingInterceptorを定義すれば機能するでしょう。
staticMembershipInterceptor.sendLocalMember.failed=ローカルメンバーの通知は失敗しました。
staticMembershipInterceptor.sendShutdown.failed=シャットダウン通知が失敗しました

tcpFailureDetector.already.disappeared=検証完了。メンバーはすでに離脱していることを確認しました [{0}]
tcpFailureDetector.failureDetection.failed=故障検出チェックが実行できないため、メンバーが停止しているものとして扱います。
tcpFailureDetector.heartbeat.failed=TcpFailureDetector のハートビートチェックができませんでした。
tcpFailureDetector.member.disappeared=メンバ検証が完了しました。 メンバーが消えました[{0}]
tcpFailureDetector.memberDisappeared.verify=memberDisappeared[{0}]メッセージを受信しました。 メンバ検証します。
tcpFailureDetector.performBasicCheck.memberAdded=私たちに通知されなかったにもかかわらず、メンバーが追加されました：[{0}]
tcpFailureDetector.still.alive=故障検出チェックが完了しました。メンバー [{0}] は正常です。
tcpFailureDetector.suspectMember.alive=疑わしいクラスタメンバーの生存を確認しました。 [{0}]
tcpFailureDetector.suspectMember.dead=疑義メンバが死亡したことが確認されました。[{0}]

tcpPingInterceptor.ping.failed=TCP の ping メッセージを送信できませんでした。
tcpPingInterceptor.pingFailed.pingThread=TCP pingスレッドからpingを送信できません。

throughputInterceptor.report=ThroughputInterceptor Report[\n\
\        送信メッセージ (Tx Msg):{0} messages\n\
\        送信済み (Sent):{1} MB (total)\n\
\        送信済み (Sent):{2} MB (application)\n\
\        時間 (Time):{3} seconds\n\
\        送信速度 (Tx Speed):{4} MB/sec (total)\n\
\        送信速度 (Tx Speed):{5} MB/sec (application)\n\
\        エラーメッセージ (Error Msg):{6}\n\
\        受信メッセージ (Rx Msg):{7} messages\n\
\        受信速度 (Rx Speed):{8} MB/sec (since 1st msg)\n\
\        受信済み (Received):{9} MB]

twoPhaseCommitInterceptor.heartbeat.failed=TwoPhaseCommit インターセプターのハートビートが失敗しました。
twoPhaseCommitInterceptor.originalMessage.missing=確認を受信しましたが、元のメッセージがありません。 Id：[{0}]
