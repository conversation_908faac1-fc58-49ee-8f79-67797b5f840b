# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cookie.fallToDebug=\n\
\ 注: 以降のこのエラーの発生はDEBUGレベルでログに出力されます。
cookie.invalidCookieValue=無効なCookieを含むCookieヘッダーが受信されました[{0}]。 そのクッキーは無視されます。
cookie.invalidCookieVersion=[{0}]の認識できないクッキーバージョンを使用して、Cookieヘッダーが受信されました。 ヘッダーとそれに含まれるクッキーは無視されます。
cookie.valueNotPresent=<値が存在しません>

http.closingBracket=非IPv6ホスト名に閉じ括弧 ']'が見つかりました。
http.illegalAfterIpv6=文字[{0}]はホスト名のIPv6アドレスに従うことはできません。
http.illegalCharacterDomain=文字 [{0}] をドメイン名に含めることはできません。
http.illegalCharacterIpv4=文字 [{0}] は正常な IPv4 アドレスに利用できません。
http.illegalCharacterIpv6=IPv6 アドレスに文字 [{0}] を使用することはできません。
http.invalidCharacterDomain.afterColon=ドメイン名のコロンの後の文字[{0}]は無効です。
http.invalidCharacterDomain.afterHyphen=ドメイン名のハイフンの後の文字[{0}]は無効です
http.invalidCharacterDomain.afterLetter=文字 [{0}] はドメイン名に利用できません。
http.invalidCharacterDomain.afterNumber=ドメイン名の数字の後の文字[{0}]は無効です。
http.invalidCharacterDomain.afterPeriod=ドメイン名のピリオドの後の文字[{0}]は無効です。
http.invalidCharacterDomain.atEnd=文字[{0}]はドメイン名の最後には無効です。
http.invalidCharacterDomain.atStart=文字[{0}]はドメイン名の先頭には無効です。
http.invalidHextet=不正な 16 進数文字列です。16 進数文字列に使用できるのは 4 文字以下の 16 進数だけです。
http.invalidIpv4Location=IPv6 アドレスは不正な位置に埋め込み IPv4 アドレスを含んでいます。
http.invalidLeadingZero=IPv4 アドレスの 0 でないオクテットは先行する0を含まないかもしれません。
http.invalidOctet=無効なオクテット[{0}]。 IPv4オクテットの有効範囲は0〜255です。
http.invalidSegmentEndState=状態[{0}]はセグメントの最後には無効です。
http.noClosingBracket=IPv6アドレスに閉じ括弧がありません。
http.noOpeningBracket=IPv6 アドレスに開き括弧がありません。
http.singleColonEnd=IPv6 アドレス文字列は単独のコロン (:) で終端してはなりません。
http.singleColonStart=IPv6アドレスは単一の '：'で始まらない場合があります
http.tooFewHextets=IPv6 アドレスは 8 個のヘクステットで構成しなければなりませんが [{0}] 個しかありません。また１つ以上のヘクステットを意味する "::" もありません。
http.tooManyColons=IPv6 アドレスでは文字 : を 2 つ以上連続することはできません。
http.tooManyDoubleColons=IPv6アドレスは単一の '::'シーケンスのみを含むことができます。
http.tooManyHextets=IPv6 アドレスは [{0}] ヘクステットで構成されていますが、正常な IPv6 アドレスなら 8 ヘクステット以上になりません。
