# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

engine.ciphersFailure=Cipher들의 목록을 얻지 못했습니다.
engine.emptyCipherSuite=CipherSuite 이름이 존재하지 않습니다.
engine.engineClosed=엔진이 닫혀 있습니다.
engine.failedCipherSuite=Cipher suite [{0}]을(를) 사용가능 상태로 설정하지 못했습니다.
engine.inboundClose=Peer의 close_notify를 받기 전에, Inbound가 닫혔습니다.
engine.invalidBufferArray=offset: [{0}], 길이: [{1}] (요구사항: offset <= offset + length <= srcs.length [{2}])
engine.invalidDestinationBuffersState=바이트 데이터를 unwrap하는 중, 대상 버퍼의 상태가 동시에 변경되었습니다.
engine.noRestrictSessionCreation=OpenSslEngine은, 새로운 SSL 세션을 생성할 수 없고 오직 기존 SSL 세션들을 재개만 할 수 있게 하는 옵션을 지원하지 않습니다.
engine.noSSLContext=SSL 컨텍스트가 없음.
engine.noSession=SSL 세션 ID가 존재하지 않습니다.
engine.nullBuffer=널 버퍼
engine.nullBufferInArray=배열 내에 널 버퍼임
engine.nullCipherSuite=널 cipher suite
engine.nullName=name이 널입니다.
engine.nullValue=널 값
engine.openSSLError=OpenSSL 오류: [{0}], 메시지: [{1}]
engine.oversizedPacket=암호화된 패킷이 너무 큽니다.
engine.unsupportedCipher=지원되지 않는 cipher suite: [{0}] [{1}]
engine.unsupportedProtocol=프로토콜 [{0}]은(는) 지원되지 않습니다.
engine.unverifiedPeer=검증되지 않은 Peer
engine.writeToSSLFailed=SSL에 쓰기 실패, 반환 값: [{0}]

openssl.X509FactoryError=X509 팩토리 인스턴스를 얻는 중 오류 발생
openssl.addedClientCaCert=클라이언트 CA 인증서를 추가했습니다: [{0}]
openssl.applyConf=OpenSSLConfCmd를 SSL 컨텍스트에 적용합니다.
openssl.certificateVerificationFailed=인증서 검증에 실패했습니다.
openssl.checkConf=OpenSSLConf를 점검합니다.
openssl.doubleInit=SSL 컨텍스트가 이미 초기화되어 있으므로, 초기화 호출을 무시합니다.
openssl.errApplyConf=SSL 컨텍스트에 OpenSSLConf를 적용할 수 없었습니다.
openssl.errCheckConf=OpenSSLConf 점검 중 오류 발생
openssl.errMakeConf=OpenSSLConf 컨텍스트를 생성할 수 없었습니다.
openssl.errorSSLCtxInit=SSL 컨텍스트를 초기화 하는 중 오류 발생
openssl.keyManagerMissing=키 매니저를 찾을 수 없습니다.
openssl.makeConf=OpenSSLConf 컨텍스트를 생성합니다.
openssl.nonJsseCertificate=인증서 [{0}] 또는 그것의 개인 키 [{1}]이(가) JSSE 키 매니저를 사용하여 처리되지 못하였으므로, OpenSSL에 직접 전달할 것입니다.
openssl.nonJsseChain=해당 인증서 체인 [{0}]이(가) 지정되지 않았거나 유효하지 않으며, JSSE는 유효한 인증서 체인을 요구하므로, OpenSSL을 직접 사용하려 시도합니다.
openssl.trustManagerMissing=Trust 매니저를 찾을 수 없습니다.

opensslconf.applyCommand=OpenSSLConf이 명령을 적용합니다 (이름 [{0}], 값 [{1}]).
opensslconf.applyFailed=OpenSSLConf를 SSL 컨텍스트에 적용하는 중 실패
opensslconf.checkCommand=OpenSSLConf 점검 명령 (이름 [{0}], 값 [{1}])
opensslconf.checkFailed=OpenSSLConf 점검 실패
opensslconf.failedCommand=OpenSSLConf가 명령(이름: [{0}], 값: [{1}])을 처리하지 못했습니다 (결과: [{2}]). 이는 무시될 것입니다.
opensslconf.finishFailed=OpenSSLConf의 완료가 실패했습니다 (결과 값: [{0}]).
opensslconf.noCommandName=OpenSSLConf: 명령 이름이 없습니다 - 무시될 것입니다. (명령 값 [{0}])
opensslconf.resultCommand=OpenSSLConf 명령(이름: [{0}], 값: [{1}])이 [{2}]을(를) 반환했습니다.

sessionContext.nullTicketKeys=널 키들
