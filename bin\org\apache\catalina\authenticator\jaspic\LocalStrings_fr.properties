# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

authConfigFactoryImpl.load=Chargement des enregistrements pour le fournisseurs persistants à partir de [{0}]
authConfigFactoryImpl.registerClass=Enregistrement de la classe [{0}] pour la couche [{1}] et le contexte d''application [{2}]
authConfigFactoryImpl.registerInstance=Enregistrement de l''instance de type [{0}] pour la couche [{1}] et le contexte d''application [{2}]
authConfigFactoryImpl.zeroLengthAppContext=Un nom de contexte vide n'est pas valide
authConfigFactoryImpl.zeroLengthMessageLayer=Un message vide de nom de couche est invalide

callbackHandlerImpl.containerMissing=Le rappel (callback) JASPIC de type [{0}] a un conteneur manquant et a été ignoré
callbackHandlerImpl.jaspicCallbackMissing=Le rappel (callback) JASPIC de type [{0}] reçu n''est pas supporté et a été ignoré
callbackHandlerImpl.realmMissing=Le rappel (callback) JASPIC de type [{0}] reçu n''a pas de royaume (realm) et a été ignoré

jaspicAuthenticator.authenticate=Authentification de la requête pour [{0}] avec JASPIC

persistentProviderRegistrations.deleteFail=Le fichier temporaire [{0}] n''a pas pu être effacé
persistentProviderRegistrations.existsDeleteFail=Le fichier temporaire [{0}] existe déjà et ne peut être effacé
persistentProviderRegistrations.moveFail=Echec de déplacement de [{0}] vers [{1}]

simpleServerAuthConfig.noModules=Aucun ServerAuthModules n'est configuré
