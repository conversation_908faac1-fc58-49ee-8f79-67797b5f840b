# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

mapper.addHost.sameHost=同じホスト[{0}]の登録が重複しています。 無視されます。
mapper.addHost.success=登録されたホスト[{0}]
mapper.addHostAlias.sameHost=ホスト [{1}] にエイリアス [{0}] が重複して登録されました。無視します。
mapper.addHostAlias.success=ホスト [{1}] のエイリアス [{0}] を登録しました。
mapper.duplicateHost=Host名の重複 [{0}]。この名前はHost[{1}] で使用されているため無視されます。
mapper.duplicateHostAlias=Host [{1}]のホストエイリアス[{0}]が重複しています。 名前はすでにHost [{2}]によって使用されています。 このエイリアスは無視されます。
mapper.removeWrapper=コンテキスト[{0}]からパス[{1}]を使用してラッパーを削除します

mapperListener.pauseContext=コンテキスト[{0}]をサービス[{1}]のために再ロードするものとして登録します。
mapperListener.registerContext=コンテキスト [{0}] にサービス [{1}] を登録します。
mapperListener.registerHost=サービス[{2}]のドメイン[{1}]にホスト[{0}]を登録
mapperListener.registerWrapper=サービス [{2}] のコンテキスト [{1}] にラッパー [{0}] を登録しました。
mapperListener.unknownDefaultHost=サービス [{1}] に既定ホスト [{0}] は存在しません。Tomcat はホスト名を指定しない HTTP/1.0 のリクエストを処理できません。
mapperListener.unregisterContext=サービス[{1}]のコンテキスト[{0}]の登録を解除します。
mapperListener.unregisterHost=サービス[{2}]のドメイン[{1}]のホスト[{0}]の登録を解除します。
mapperListener.unregisterWrapper=サービス[{2}]のContext [{1}]のWrapper [{0}]の登録を解除します。
