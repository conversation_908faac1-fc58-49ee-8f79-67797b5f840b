# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractConnectionHandler.connectionsGet=소켓 [{1}]을(를) 위한 프로세서 [{0}]을(를) 발견했습니다.
abstractConnectionHandler.error=요청을 읽는 중 오류 발생. 무시합니다.
abstractConnectionHandler.ioexception.debug=IOException들은 정상적이므로, 무시합니다.
abstractConnectionHandler.negotiatedProcessor.fail=Negotiate된 프로토콜 [{0}]을(를) 위한 프로세서를 생성하지 못했습니다.
abstractConnectionHandler.oome=요청 처리를 완료하지 못했습니다.
abstractConnectionHandler.process=상태가 [{1}]인 소켓 [{0}]을(를) 처리합니다.
abstractConnectionHandler.processorCreate=생성된 새 프로세서 [{0}]
abstractConnectionHandler.processorPop=캐시로부터 프로세서 [{0}]을(를) 추출했습니다.
abstractConnectionHandler.protocolexception.debug=ProtocolException들은 정상적이므로, 무시합니다.
abstractConnectionHandler.socketexception.debug=SocketException들은 정상적인 상태이므로 무시되었습니다.
abstractConnectionHandler.upgradeCreate=소켓 wrapper [{1}]을(를) 위한 업그레이드 프로세서 [{0}]을(를) 생성했습니다.

abstractProcessor.fallToDebug=\n\
\ 비고: 요청에 대한 파싱 오류들이 더 발생하는 경우 DEBUG 레벨 로그로 기록될 것입니다.
abstractProcessor.hostInvalid=호스트 [{0}]은(는) 유효하지 않습니다.
abstractProcessor.httpupgrade.notsupported=HTTP 업그레이드는 이 프로토콜에 의해 지원되지 않습니다.
abstractProcessor.pushrequest.notsupported=이 프로토콜은 서버 push 요청들을 지원하지 않습니다.
abstractProcessor.setErrorState=요청 처리 중 오류 상태 [{0}]이(가) 보고됨.
abstractProcessor.socket.ssl=SSL 속성들을 얻으려는 중 예외 발생

abstractProtocol.mbeanDeregistrationFailed=MBean 서버 [{1}](으)로부터, [{0}](이)라는 이름의 MBean의 등록을 제거하지 못했습니다.
abstractProtocol.processorRegisterError=RequestProcessor 구성요소를 등록하는 중 오류 발생
abstractProtocol.processorUnregisterError=RequestProcessor 구성요소를 등록 해제하는 중 오류 발생
abstractProtocol.waitingProcessor.add=대기 프로세서에 추가된 프로세서 [{0}]
abstractProtocol.waitingProcessor.remove=대기 프로세서에서 제거된 프로세서 [{0}]

abstractProtocolHandler.destroy=프로토콜 핸들러 [{0}]을(를) 소멸시킵니다.
abstractProtocolHandler.init=프로토콜 핸들러 [{0}]을(를) 초기화합니다.
abstractProtocolHandler.pause=프로토콜 핸들러 [{0}]을(를) 일시 정지 중
abstractProtocolHandler.resume=프로토콜 핸들러 [{0}]을(를) 재개합니다.
abstractProtocolHandler.setAttribute=속성 [{0}]에 값 [{1}]을(를) 설정
abstractProtocolHandler.start=프로토콜 핸들러 [{0}]을(를) 시작합니다.
abstractProtocolHandler.stop=프로토콜 핸들러 [{0}]을(를) 중지시킵니다.

asyncStateMachine.invalidAsyncState=비동기 상태가 [{1}]인 요청에 대하여, [{0}]을(를) 호출하는 것은 유효하지 않습니다.

compressionConfig.ContentEncodingParseFail=압축이 이미 사용되는지 여부를 점검하는 중, Content-Encoding 헤더를 파싱하지 못했습니다.

continueResponseTiming.invalid=값 [{0}]은(는) continueResponseTiming을 위한 유효한 설정 옵션이 아닙니다.

request.notAsync=오직 비동기 처리 또는 HTTP 업그레이드 처리 시에만, Non-blocking IO로의 전환이 유효합니다.
request.nullReadListener=setReadListener()에 전달된 리스너는 널일 수 없습니다.
request.readListenerSet=Non-blocking 읽기 리스너가 이미 설정되어 있습니다.

response.encoding.invalid=인코딩 [{0}]은(는) JRE에 의해 인식되지 않습니다.
response.notAsync=오직 비동기 처리 중 또는 HTTP 업그레이드 처리 중일 때에만, non-blocking IO로 전환하려는 것이 유효합니다.
response.notNonBlocking=응답이 non-blocking 모드 내에 있지 않을 때, isReady()를 호출하는 것은 유효하지 않습니다.
response.nullWriteListener=setWriteListener()에 전달되는 리스너가 널이이서는 안됩니다.
response.writeListenerSet=Non-blocking 쓰기 리스너가 이미 설정되었습니다.
