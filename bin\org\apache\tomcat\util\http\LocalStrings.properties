# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cookies.fallToDebug=\n\
\ Note: further occurrences of Cookie errors will be logged at DEBUG level.
cookies.invalidCookieToken=Cookies: Invalid cookie. Value not a token or quoted value
cookies.invalidSameSiteCookies=Unknown setting [{0}], must be one of: unset, none, lax, strict. Default value is unset.
cookies.invalidSpecial=Cookies: Unknown Special Cookie
cookies.maxCountFail=More than the maximum allowed number of cookies, [{0}], were detected.

headers.maxCountFail=More than the maximum allowed number of headers, [{0}], were detected.

parameters.bytes=Start processing with input [{0}]
parameters.copyFail=Failed to create copy of original parameter values for debug logging purposes
parameters.decodeFail.debug=Character decoding failed. Parameter [{0}] with value [{1}] has been ignored.
parameters.decodeFail.info=Character decoding failed. Parameter [{0}] with value [{1}] has been ignored. Note that the name and value quoted here may be corrupted due to the failed decoding. Use debug level logging to see the original, non-corrupted values.
parameters.emptyChunk=Empty parameter chunk ignored
parameters.fallToDebug=\n\
\ Note: further occurrences of Parameter errors will be logged at DEBUG level.
parameters.invalidChunk=Invalid chunk starting at byte [{0}] and ending at byte [{1}] with a value of [{2}] ignored
parameters.maxCountFail=More than the maximum number of request parameters (GET plus POST) for a single request ([{0}]) were detected. Any parameters beyond this limit have been ignored. To change this limit, set the maxParameterCount attribute on the Connector.
parameters.maxCountFail.fallToDebug=\n\
\ Note: further occurrences of this error will be logged at DEBUG level.
parameters.multipleDecodingFail=Character decoding failed. A total of [{0}] failures were detected but only the first was logged. Enable debug level logging for this logger to log all failures.
parameters.noequal=Parameter starting at position [{0}] and ending at position [{1}] with a value of [{2}] was not followed by an ''='' character

rfc6265CookieProcessor.invalidCharInValue=An invalid character [{0}] was present in the Cookie value
rfc6265CookieProcessor.invalidDomain=An invalid domain [{0}] was specified for this cookie
rfc6265CookieProcessor.invalidPath=An invalid path [{0}] was specified for this cookie
