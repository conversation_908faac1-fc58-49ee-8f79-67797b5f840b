# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

JDBCStore.SQLException=Error SQL [{0}]
JDBCStore.checkConnectionClassNotFoundException=No se ha hallado la clase del manejador (driver) JDBC [{0}]
JDBCStore.checkConnectionDBClosed=La conexióna a base de datos es nula o está cerrada. Intentando reabrirla.
JDBCStore.checkConnectionDBReOpenFail=Falló la reapertura de la base de datos. Puede que la base de datos esté caída.
JDBCStore.checkConnectionSQLException=Ha tenido lugar una excepción SQL [{0}]
JDBCStore.close=Excepción cerrando conexión a base de datos [{0}]
JDBCStore.loading=Cargando Sesión [{0}] desde base de datos [{1}]
JDBCStore.missingDataSourceName=No se proporcionó un nombre JNDI válido
JDBCStore.removing=Quitando Sesión [{0}] en base de datos [{1}]
JDBCStore.saving=Salvando Sesión [{0}] en base de datos [{1}]

fileStore.loading=Cargando Sesión [{0}] desde archivo [{1}]
fileStore.removing=Quitando Sesión [{0}] en archivo [{1}]
fileStore.saving=Salvando Sesión [{0}] en archivo [{1}]

managerBase.contextNull=El Çontexto debe ser fijado a un valor no nulo antes the fijar el Gerenciador
managerBase.createSession.ise=createSession: Demasiadas sesiones activas
managerBase.sessionTimeout=Valor inválido de Tiempo Agotado de sesión [{0}]
managerBase.setContextNotNew=Es ilegal llamar a setContext() para cambiar el Contexto associado con un Manejador si el Manejador no esta en el estado NEW.

persistentManager.backupMaxIdle=Respaldando sesión [{0}] a Almacén, ociosa durante [{1}] segundos
persistentManager.deserializeError=Error des-serializando Sesión [{0}]: [{1}]
persistentManager.loading=Cargando [{0}] sesiones persistidas
persistentManager.serializeError=Error serializando Sesión [{0}]: [{1}]
persistentManager.storeKeysException=Imposible determinar la lista de IDs de sesiones en la tienda de sesiones, asumiendo que la tienda esta vacia
persistentManager.storeSizeException=No se puede determinar el numero de sesiones en el almacenamiento de sesiones, asumiendo que el almacenamiento esta vacío
persistentManager.swapIn=Intercambiando sesión [{0}] a dentro desde Almacén
persistentManager.swapMaxIdle=Intercambiando sesión [{0}] a fuera a Almacén, ociosa durante [{1}] segundos
persistentManager.swapTooManyActive=Intercambiando sesión [{0}] a fuera, ociosa durante [{1}] segundos: Demasiadas sesiones activas
persistentManager.tooManyActive=Demasiadas sesiones activas, [{0}], buscando sesiones ociosas para intercambiar
persistentManager.unloading=Salvando [{0}] sesiones persistidas

standardManager.deletePersistedFileFail=Imposible borrar [{0}] luego de leer las sessiones persistentes. La prensencia continua de este archivo causará que los intentos futuros de sesiones persistentes fallen.
standardManager.loading=Cargando sesiones persistidas desde [{0}]
standardManager.loading.exception=Exception al cargar sesiones persistidas
standardManager.managerLoad=Excepción cargando sesiones desde almacenamiento persistente
standardManager.managerUnload=Excepción descargando sesiones a almacenamiento persistente
standardManager.unloading=Salvando sesiones persistidas a [{0}]

standardSession.attributeEvent=El oyente de eventos de atributo de Sesión lanzó una excepción
standardSession.bindingEvent=El oyente de eventos de ligado de Sesión lanzó una excepción
standardSession.getAttribute.ise=getAttribute: La Sesión ya ha sido invalidada
standardSession.getAttributeNames.ise=getAttributeNames: La Sesión ya ha sido invalidada
standardSession.getCreationTime.ise=getCreationTime: La Sesión ya ha sido invalidada
standardSession.getLastAccessedTime.ise=getLastAccessedTime: La Sesión ya ha sido invalidada
standardSession.getThisAccessedTime.ise=getThisAccessedTime: La Sesión ya ha sido invalidada
standardSession.getValueNames.ise=getValueNames: La Sesión ya ha sido invalidada
standardSession.invalidate.ise=invalidate: La Sesión ya ha sido invalidada
standardSession.isNew.ise=isNew: La Sesión ya ha sido invalidada
standardSession.notSerializable=No puedo serializar atributo de sesión [{0}] para sesión [{1}]
standardSession.removeAttribute.ise=removeAttribute: La Sesión ya ha sido invalidada
standardSession.sessionEvent=El oyente de evento de Sesión lanzó una execpción
standardSession.setAttribute.iae=setAttribute: Atributo [{0}] no serializable
standardSession.setAttribute.ise=setAttribute: La Sesión ya ha sido invalidada
standardSession.setAttribute.namenull=setAttribute: el nuevo parámetro no puede ser nulo
