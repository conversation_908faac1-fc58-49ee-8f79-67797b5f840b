# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractArchiveResourceSet.setReadOnlyFalse=Archive based WebResourceSets such as those based on JARs are hard-coded to be read-only and may not be configured to be read-write

abstractFileResourceSet.canonicalfileCheckFailed=Resource for web application [{0}] at path [{1}] was not loaded as the canonical path [{2}] did not match. Use of symlinks is one possible cause.

abstractResource.getContentFail=Unable to return [{0}] as a byte array
abstractResource.getContentTooLarge=Unable to return [{0}] as a byte array since the resource is [{1}] bytes in size which is larger than the maximum size of a byte array

abstractResourceSet.checkPath=The requested path [{0}] is not valid. It must begin with "/".

cache.addFail=Unable to add the resource at [{0}] to the cache for web application [{1}] because there was insufficient free space available after evicting expired cache entries - consider increasing the maximum size of the cache
cache.backgroundEvictFail=The background cache eviction process was unable to free [{0}] percent of the cache for Context [{1}] - consider increasing the maximum size of the cache. After eviction approximately [{2}] KB of data remained in the cache.
cache.objectMaxSizeTooBig=The value of [{0}]kB for objectMaxSize is larger than the limit of maxSize/20 so has been reduced to [{1}]kB
cache.objectMaxSizeTooBigBytes=The value specified for the maximum object size to cache [{0}]kB is greater than Integer.MAX_VALUE bytes which is the maximum size that can be cached. The limit will be set to Integer.MAX_VALUE bytes.

cachedResource.invalidURL=Unable to create an instance of CachedResourceURLStreamHandler because the URL [{0}] is malformed

classpathUrlStreamHandler.notFound=Unable to load the resource [{0}] using the thread context class loader or the current class''s class loader

dirResourceSet.manifestFail=Failed to read manifest from [{0}]
dirResourceSet.notDirectory=The directory specified by base and internal path [{0}]{1}[{2}] does not exist.
dirResourceSet.writeNpe=The input stream may not be null

extractingRoot.jarFailed=Failed to extract the JAR file [{0}]
extractingRoot.targetFailed=Failed to create the directory [{0}] for extracted JAR files

fileResource.getCanonicalPathFail=Unable to determine the canonical path for the resource [{0}]
fileResource.getCreationFail=Unable to determine the creation time for the resource [{0}]
fileResource.getUrlFail=Unable to determine a URL for the resource [{0}]

fileResourceSet.notFile=The file specified by base and internal path [{0}]{1}[{2}] does not exist.

jarResource.getInputStreamFail=Unable to obtain an InputStream for the resource [{0}] located in the JAR [{1}]

jarResourceRoot.invalidWebAppPath=This resource always refers to a directory so the supplied webAppPath must end with / but the provided webAppPath was [{0}]

jarWarResourceSet.codingError=Coding error

standardRoot.checkStateNotStarted=The resources may not be accessed if they are not currently started
standardRoot.createInvalidFile=Unable to create WebResourceSet from [{0}]
standardRoot.createUnknownType=Unable to create WebResourceSet of unknown type [{0}]
standardRoot.invalidPath=The resource path [{0}] is not valid
standardRoot.invalidPathNormal=The resource path [{0}] has been normalized to [{1}] which is not valid
standardRoot.lockedFile=The web application [{0}] failed to close the file [{1}] opened via the following stack trace
standardRoot.noContext=A Context has not been configured for this WebResourceRoot
standardRoot.startInvalidMain=The main resource set specified [{0}] is not valid
standardRoot.unsupportedProtocol=The URL protocol [{0}] is not supported by this web resources implementation
