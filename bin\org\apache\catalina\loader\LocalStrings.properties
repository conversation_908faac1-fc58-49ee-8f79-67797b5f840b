# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

webappClassLoader.addExportsJavaIo=When running on Java 9 or later you need to add "--add-opens=java.base/java.io={0}" to the JVM command line arguments to enable ObjectStream cache memory leak protection. Alternatively, you can suppress this warning by disabling ObjectStream class cache memory leak protection.
webappClassLoader.addExportsRmi=When running on Java 9 or later you need to add "--add-opens=java.rmi/sun.rmi.transport={0}" to the JVM command line arguments to enable RMI Target memory leak detection. Alternatively, you can suppress this warning by disabling RMI Target memory leak detection.
webappClassLoader.addExportsThreadLocal=When running on Java 9 or later you need to add "--add-opens=java.base/java.lang={0}" to the JVM command line arguments to enable ThreadLocal memory leak detection. Alternatively, you can suppress this warning by disabling ThreadLocal memory leak detection.
webappClassLoader.addPermissionNoCanonicalFile=Unable to obtain a canonical file path from the URL [{0}]
webappClassLoader.addPermissionNoProtocol=The protocol [{0}] in the URL [{1}] is not supported so no read permission was granted for resources located at this URL
webappClassLoader.addTransformer=Added class file transformer [{0}] to web application [{1}].
webappClassLoader.addTransformer.duplicate=Duplicate call to add class file transformer [{0}] to web application [{1}] ignored.
webappClassLoader.addTransformer.illegalArgument=The web application [{0}] attempted to add a null class file transformer.
webappClassLoader.checkThreadLocalsForLeaks=The web application [{0}] created a ThreadLocal with key of type [{1}] (value [{2}]) and a value of type [{3}] (value [{4}]) but failed to remove it when the web application was stopped. Threads are going to be renewed over time to try and avoid a probable memory leak.
webappClassLoader.checkThreadLocalsForLeaks.badKey=Unable to determine string representation of key of type [{0}]
webappClassLoader.checkThreadLocalsForLeaks.badValue=Unable to determine string representation of value of type [{0}]
webappClassLoader.checkThreadLocalsForLeaks.unknown=Unknown
webappClassLoader.checkThreadLocalsForLeaksFail=Failed to check for ThreadLocal references for web application [{0}]
webappClassLoader.checkThreadLocalsForLeaksNone=The web application [{0}] created a ThreadLocal with key of type [{1}] (value [{2}]) and a value of type [{3}] (value [{4}]). Since keys are only weakly held by the ThreadLocal Map this is not a memory leak.
webappClassLoader.checkThreadLocalsForLeaksNull=The web application [{0}] created a ThreadLocal with key of type [{1}] (value [{2}]). The ThreadLocal has been correctly set to null and the key will be removed by GC.
webappClassLoader.checkThreadsHttpClient=Found HttpClient keep-alive thread using web application class loader. Fixed by switching thread to the parent class loader.
webappClassLoader.clearJdbc=The web application [{0}] registered the JDBC driver [{1}] but failed to unregister it when the web application was stopped. To prevent a memory leak, the JDBC Driver has been forcibly unregistered.
webappClassLoader.clearObjectStreamClassCachesFail=Failed to clear soft references from ObjectStreamClass$Caches for web application [{0}]
webappClassLoader.clearRmi=Found RMI Target with stub class class [{0}] and value [{1}]. This RMI Target has been forcibly removed to prevent a memory leak.
webappClassLoader.clearRmiFail=Failed to clear context class loader referenced from sun.rmi.transport.Target for web application [{0}]
webappClassLoader.clearRmiInfo=Failed to find class sun.rmi.transport.Target to clear context class loader for web application [{0}]. This is expected on non-Sun JVMs.
webappClassLoader.getThreadGroupError=Unable to obtain the parent for ThreadGroup [{0}]. It will not be possible to check all threads for potential memory leaks
webappClassLoader.jarsAdded=One or more JARs have been added to the web application [{0}]
webappClassLoader.jarsModified=One or more JARs have been modified in the web application [{0}]
webappClassLoader.jarsRemoved=One or more JARs have been removed from the web application [{0}]
webappClassLoader.javaseClassLoaderNull=The j2seClassLoader attribute may not be null
webappClassLoader.jdbcRemoveFailed=JDBC driver de-registration failed for web application [{0}]
webappClassLoader.loadedByThisOrChildFail=Failed to fully check the entries in an instance of [{0}] for potential memory leaks in context [{1}]
webappClassLoader.readError=Resource read error: Could not load [{0}].
webappClassLoader.removeTransformer=Removed class file transformer [{0}] from web application [{1}].
webappClassLoader.resourceModified=Resource [{0}] has been modified. The last modified time was [{1}] and is now [{2}]
webappClassLoader.restrictedPackage=Security violation, attempt to use restricted class [{0}]
webappClassLoader.securityException=Security exception trying to find class [{0}] in findClassInternal [{1}]
webappClassLoader.stackTrace=The web application [{0}] appears to have started a thread named [{1}] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:{2}
webappClassLoader.stackTraceRequestThread=The web application [{0}] is still processing a request that has yet to finish. This is very likely to create a memory leak. You can control the time allowed for requests to finish by using the unloadDelay attribute of the standard Context implementation. Stack trace of request processing thread:[{2}]
webappClassLoader.stopThreadFail=Failed to terminate thread named [{0}] for web application [{1}]
webappClassLoader.stopTimerThreadFail=Failed to terminate TimerThread named [{0}] for web application [{1}]
webappClassLoader.stopped=Illegal access: this web application instance has been stopped already. Could not load [{0}]. The following stack trace is thrown for debugging purposes as well as to attempt to terminate the thread which caused the illegal access.
webappClassLoader.superCloseFail=Failure calling close() on super class
webappClassLoader.transformError=Instrumentation error: could not transform class [{0}] because its class file format is not legal.
webappClassLoader.warnTimerThread=The web application [{0}] appears to have started a TimerThread named [{1}] via the java.util.Timer API but has failed to stop it. To prevent a memory leak, the timer (and hence the associated thread) has been forcibly canceled.
webappClassLoader.wrongVersion=(unable to load class [{0}])

webappClassLoaderParallel.registrationFailed=Registration of org.apache.catalina.loader.ParallelWebappClassLoader as capable of loading classes in parallel failed

webappLoader.deploy=Deploying class repositories to work directory [{0}]
webappLoader.reloadable=Cannot set reloadable property to [{0}]
webappLoader.setContext.ise=Setting the Context is not permitted while the loader is started.
webappLoader.starting=Starting this Loader
webappLoader.stopping=Stopping this Loader
