# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

hostManagerServlet.add=añadir: Añadiendo máquina [{0}]
hostManagerServlet.addFailed=FALLO - No pude añadir máquina [{0}]
hostManagerServlet.addSuccess=OK - Máquina añadida [{0}]
hostManagerServlet.alreadyHost=FALLO - Ya existe máquina con nombre de máquina [{0}]
hostManagerServlet.alreadyStarted=FALLO - La máqiuina [{0}] ya ha arrancado
hostManagerServlet.alreadyStopped=FALLO - La máquina [{0}] ya se ha parado
hostManagerServlet.appBaseCreateFail=FALLO - No pude crear appBase [{0}] para la máquina [{1}]
hostManagerServlet.cannotRemoveOwnHost=FALLO - No puedo quitar máquina propia [{0}]
hostManagerServlet.cannotStartOwnHost=FALLO - No puedo empezar máquina propia [{0}]
hostManagerServlet.cannotStopOwnHost=FALLO - No puedo para máquina propia [{0}]
hostManagerServlet.configBaseCreateFail=FALLO - No pude identificar configBase para la máquina [{0}]
hostManagerServlet.exception=FALLO - Encontrada excepción [{0}]
hostManagerServlet.invalidHostName=FALLO - Se ha especificado un nombre inválido de máquina [{0}]
hostManagerServlet.list=listar: Listando máquinas para motor [{0}]
hostManagerServlet.listed=OK - Máquinas listadas
hostManagerServlet.managerXml=FALLO - no pude instalar manager.xml
hostManagerServlet.noCommand=FALLO - No se ha especificado comando
hostManagerServlet.noHost=FALLO - El nombre de máquina [{0}] no existe
hostManagerServlet.noWrapper=El contenedor no ha llamado a setWrapper() para este servlet
hostManagerServlet.postCommand=FALLO - Intenté usar el comando [{0}] vía un requerimiento GET pero es necesario POST
hostManagerServlet.remove=quitar: Quitando máquina [{0}]
hostManagerServlet.removeFailed=FALLO - No pude quitar máquina [{0}]
hostManagerServlet.removeSuccess=OK - Máquina removida [{0}]
hostManagerServlet.start=arrancar: Arrancando máquina con nombre [{0}]
hostManagerServlet.startFailed=FALLO - No pude arrancar máquina [{0}]
hostManagerServlet.started=OK - Máquina [{0}] arrancada
hostManagerServlet.stop=parar: Parando máquina con nombre [{0}]
hostManagerServlet.stopFailed=FALLO - No pude parar máquina [{0}]
hostManagerServlet.stopped=OK - Máquina [{0}] parada
hostManagerServlet.unknownCommand=FALLO - Comando desconocido [{0}]

htmlHostManagerServlet.addAliases=Aliases:
htmlHostManagerServlet.addAppBase=App base:
htmlHostManagerServlet.addAutoDeploy=AutoDeploy
htmlHostManagerServlet.addButton=Añadir
htmlHostManagerServlet.addDeployOnStartup=DeployOnStartup
htmlHostManagerServlet.addDeployXML=DeployXML
htmlHostManagerServlet.addHost=Maquina
htmlHostManagerServlet.addManager=App de Gestor
htmlHostManagerServlet.addName=Nombre:
htmlHostManagerServlet.addTitle=Añadir Máquina Virtual
htmlHostManagerServlet.addUnpackWARs=UnpackWARs
htmlHostManagerServlet.helpHtmlManager=Ayuda de Gestor de Máquina HTML (¡En breve!)
htmlHostManagerServlet.helpHtmlManagerFile=html-host-manager-howto.html
htmlHostManagerServlet.helpManager=Ayuda de Gestor de Máquina
htmlHostManagerServlet.helpManagerFile=../docs/host-manager-howto.html
htmlHostManagerServlet.hostAliases=Aliases de Máquina
htmlHostManagerServlet.hostName=Nombre de Máquina
htmlHostManagerServlet.hostTasks=Comandos
htmlHostManagerServlet.hostThis=Instalado Gestor de Máquinas - comandos deactivados
htmlHostManagerServlet.hostsRemove=Quitar
htmlHostManagerServlet.hostsStart=Iniciar
htmlHostManagerServlet.hostsStop=Parar
htmlHostManagerServlet.list=Lista de Máquinas Virtuales
htmlHostManagerServlet.manager=Gestor de Máquina
htmlHostManagerServlet.messageLabel=Mensaje:
htmlHostManagerServlet.persistTitle=Configuración peristente
htmlHostManagerServlet.serverJVMVendor=Vendedor JVM
htmlHostManagerServlet.serverJVMVersion=Versión de JVM
htmlHostManagerServlet.serverOSArch=Arquitectura del SO
htmlHostManagerServlet.serverOSName=Nombre de SO
htmlHostManagerServlet.serverOSVersion=Versión de SO
htmlHostManagerServlet.serverTitle=Información de Servidor
htmlHostManagerServlet.serverVersion=Versión de Tomcat
htmlHostManagerServlet.title=Gestor de Máquina Virtual de Tomcat

statusServlet.complete=Completar Estado de Servidor
statusServlet.title=Estado de Servidor
