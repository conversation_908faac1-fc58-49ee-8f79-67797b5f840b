# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

JDBCStore.SQLException=Erreur SQL [{0}]
JDBCStore.checkConnectionClassNotFoundException=La classe du driver JDBC n''a pas été trouvée [{0}]
JDBCStore.checkConnectionDBClosed=La connexion à la base de données est nulle ou a été trouvée fermée. Tentative de réouverture.
JDBCStore.checkConnectionDBReOpenFail=La tentative de réouverture de la base de données a échoué. La base de données est peut-être arrêtée.
JDBCStore.checkConnectionSQLException=Une exception SQL s''est produite [{0}]
JDBCStore.close=Exception lors de la fermeture de la connection vers la base de donnée [{0}]
JDBCStore.commitSQLException=Une SQLException a été retournée lors du commit de la connection avant sa fermeture
JDBCStore.connectError=Impossible de se connecter à la base de données [{0}]
JDBCStore.loading=Chargement de la Session [{0}] depuis la base de données [{1}]
JDBCStore.missingDataSourceName=Aucun nom JNDI valide n'a été donné.
JDBCStore.removing=Retrait de la Session [{0}] de la base de données [{1}]
JDBCStore.saving=Sauvegarde de la Session [{0}] vers la base de données [{1}]
JDBCStore.wrongDataSource=Impossible d''ouvrir la DataSource JNDI [{0}]

fileStore.createFailed=Impossible de créer le répertoire [{0}] pour stocker les données de session
fileStore.deleteFailed=Impossible d''effacer le fichier [{0}] qui empêche la création du support de stockage de sessions
fileStore.deleteSessionFailed=Impossible d''effacer le fichier [{0}] qui n''est plus nécessaire
fileStore.invalid=Fichier de persistance [{0}] invalide pour la session avec id [{1}]
fileStore.loading=Chargement de la Session [{0}] depuis le fichier [{1}]
fileStore.removing=Retrait de la Session [{0}] du fichier [{1}]
fileStore.saving=Sauvegarde de la Session [{0}] vers le fichier [{1}]

managerBase.container.noop=Les gestionnaires de session ajoutés à des conteneurs qui ne sont pas des contextes ne seront jamais utilisés
managerBase.contextNull=Le contexte (Context) doit être mis à une valeur non-nulle avant l'usage du Manager
managerBase.createSession.ise="createSession" : Trop de sessions actives
managerBase.sessionAttributeNameFilter=L''attribut de session nommé [{0}] sera sauté car il ne correspond pas au filtre sur les noms [{1}]
managerBase.sessionAttributeValueClassNameFilter=L''attribut de session nommé [{0}] a été passé parce que le type [{1}] de la valeur ne correspond pas au filtre [{2}]
managerBase.sessionNotFound=La session [{0}] n''a pas été trouvée
managerBase.sessionTimeout=Réglage du délai d''inactivité (timeout) de session invalide [{0}]
managerBase.setContextNotNew=Il est illégal d'appeler setContext() pour changer le contexte associé avec un gestionnaire (Manager) si le genstionnaire n'est pas dans l'état nouveau

persistentManager.backupMaxIdle=Sauvegarde de la session [{0}] vers le stockage (Store), en attente pour [{1}] secondes
persistentManager.deserializeError=Erreur lors de la désérialisation de la session [{0}]
persistentManager.isLoadedError=Erreur en vérifiant si la session [{0}] est chargée en mémoire
persistentManager.loading=Chargement de [{0}] sessions persistantes
persistentManager.removeError=Erreur en enlevant la session [{0}] du stockage
persistentManager.serializeError=Erreur lors de la sérialisation de la session [{0}] : [{1}]
persistentManager.storeClearError=Erreur en supprimant toutes les sessions du stockage
persistentManager.storeKeysException=Incapacité de déterminer la liste des ID de session, pour les sessions dans le magasin de sessions.  Supposant le magasin vide.
persistentManager.storeLoadError=Erreur en déplaçant les sessions à partir du stockage
persistentManager.storeLoadKeysError=Erreur lors du chargement des clés des sessions du stockage
persistentManager.storeSizeException=Impossible de déterminer le nombre de sessions dans le magasin de sessions, le magasin doit être vide.
persistentManager.swapIn=Basculement depuis le stockage (swap in) de la session [{0}]
persistentManager.swapInException=Exception dans la Store lors du swapIn : [{0}]
persistentManager.swapInInvalid=La session échangée [{0}] est invalide
persistentManager.swapMaxIdle=Basculement de la session [{0}] vers le stockage (Store), en attente pour [{1}] secondes
persistentManager.swapTooManyActive=Basculement vers stockage (swap out) de la session [{0}], en attente pour [{1}] secondes trop de sessions actives
persistentManager.tooManyActive=Trop de sessions actives, [{0}], à la recherche de sessions en attente pour basculement vers stockage (swap out)
persistentManager.unloading=Sauvegarde de [{0}] sessions persistantes

standardManager.deletePersistedFileFail=Impossible de supprimer [{0}] après avoir lu les sessions persistées, cela pourrait empêcher la future persistance des sessions
standardManager.loading=Chargement des sessions qui ont persisté depuis [{0}]
standardManager.loading.exception="Exception" lors du chargement de sessions persistantes
standardManager.managerLoad=Exception au chargement des sessions depuis le stockage persistant (persistent storage)
standardManager.managerUnload=Exception au déchargement des sessions vers le stockage persistant (persistent storage)
standardManager.unloading=Sauvegarde des sessions ayant persisté vers [{0}]
standardManager.unloading.debug=Déchargement des session persistées
standardManager.unloading.nosessions=Aucune session persistée à décharger

standardSession.attributeEvent=L'écouteur d'évènement Attribut de Session (attribute event listener) a généré une exception
standardSession.bindingEvent=L'écouteur d'évènements d'association de session a renvoyé une exception
standardSession.getAttribute.ise="getAttribute" : Session déjà invalidée
standardSession.getAttributeNames.ise="getAttributeNames" : Session déjà invalidée
standardSession.getCreationTime.ise="getCreationTime" : Session déjà invalidée
standardSession.getIdleTime.ise=getIdleTime : la session a déjà été invalidée
standardSession.getLastAccessedTime.ise="getLastAccessedTime" : Session déjà invalidée
standardSession.getThisAccessedTime.ise="getThisAccessedTime" : Session déjà invalidée
standardSession.getValueNames.ise="getValueNames" : Session déjà invalidée
standardSession.invalidate.ise="invalidate" : Session déjà invalidée
standardSession.isNew.ise="isNew" : Session déjà invalidée
standardSession.logoutfail=Exception lors de la déconnection de l'utilisateur, lors de l'expiration de la session
standardSession.notDeserializable=Impossible de désérialiser l''attribut de session [{0}] pour la session [{1}]
standardSession.notSerializable=Impossible de sérialiser l''attribut de session [{0}] pour la session [{1}]
standardSession.principalNotDeserializable=Impossible de désérialiser l''objet Principal pour la session [{0}]
standardSession.principalNotSerializable=Impossible de sérialiser l''objet Principal pour la session [{0}]
standardSession.removeAttribute.ise="removeAttribute" : Session déjà invalidée
standardSession.sessionEvent=L'écouteur d'évènement de session (session event listener) a généré une exception
standardSession.setAttribute.iae="setAttribute" : Attribut [{0}] non sérialisable
standardSession.setAttribute.ise="setAttribute" : Session déjà invalidée
standardSession.setAttribute.namenull="setAttribute" : le nom de paramètre ne peut être nul
