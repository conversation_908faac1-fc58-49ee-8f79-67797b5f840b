# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

memoryUserDatabase.fileClose=[{0}]을(를) 닫지 못했습니다.
memoryUserDatabase.fileDelete=[{0}]을(를) 삭제하지 못했습니다.
memoryUserDatabase.fileNotFound=지정된 사용자 데이터베이스 [{0}]을(를) 찾을 수 없었습니다.
memoryUserDatabase.notPersistable=사용자 데이터베이스에 저장할 수 없습니다 - 디렉토리에 쓰기 권한이 없음.
memoryUserDatabase.nullGroup=널 또는 길이가 0인 그룹 이름이 지정되었습니다. 해당 그룹은 무시될 것입니다.
memoryUserDatabase.nullRole=널이거나 길이가 0인 문자열로, 역할 이름이 지정되었습니다. 해당 역할은 무시됩니다.
memoryUserDatabase.nullUser=사용자 이름에 널이거나 길이가 0인 문자열이 지정되었습니다. 이 사용자는 무시될 것입니다.
memoryUserDatabase.readOnly=사용자 데이터베이스는 읽기 전용으로 설정되어 있습니다. 변경 사항들이 저장될 수 없습니다.
memoryUserDatabase.reload=변경된 소스 [{1}](으)로부터 메모리 사용자 데이터베이스 [{0}]을(를) 다시 로드합니다.
memoryUserDatabase.reloadError=메모리 사용자 데이터베이스 [{0}]을(를), 변경된 원본 [{1}](으)로부터 다시 로드하는 중 오류 발생
memoryUserDatabase.renameNew=새 파일을 [{0}](으)로 이름을 변경할 수 없습니다.
memoryUserDatabase.renameOld=원본 파일을 [{0}](으)로 이름을 변경할 수 없습니다.
memoryUserDatabase.restoreOrig=[{0}] 파일을 원본 파일로 복구할 수 없습니다.
memoryUserDatabase.writeException=[{0}]에 데이터를 쓰는 중 IOException 발생
memoryUserDatabase.xmlFeatureEncoding=XML 파일들 내에서 자바 인코딩 이름들을 허용하기 위하여, Digester를 설정하던 중 예외 발생. 오직 IANA 인코딩 이름들만 지원될 것입니다.
