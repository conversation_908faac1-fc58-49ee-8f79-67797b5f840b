# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jmxRegistry.no.domain=未指定JMX域
jmxRegistry.objectName.failed=请求的ObjectName[{0}]无效
jmxRegistry.registerJmx.failed=无法注册名称为 [{1}] 的对象 [{0}]
jmxRegistry.registerJmx.notCompliant=请求的对象[{0}]不符合JMX规范
jmxRegistry.unregisterJmx.failed=无法注销名为[{0}]的MBean
jmxRegistry.unregisterJmx.notFound=ObjectName[{0}]还未注册到MBeanServer
