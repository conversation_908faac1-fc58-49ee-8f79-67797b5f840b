# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jmxRegistry.no.domain=JMX 도메인이 지정되지 않았습니다.
jmxRegistry.objectName.failed=요청된 ObjectName [{0}]은(는) 유효하지 않습니다.
jmxRegistry.registerJmx.failed=[{1}](이)라는 이름으로 객체 [{0}]을(를) 등록하지 못했습니다.
jmxRegistry.registerJmx.notCompliant=요청된 객체 [{0}]은(는) JMX 스펙과 호환되지 않습니다.
jmxRegistry.unregisterJmx.failed=[{0}](이)라는 이름의 MBean에 대한 등록을 제거하지 못했습니다.
jmxRegistry.unregisterJmx.notFound=요청된 객체[{0}]는 MBeanServer에 등록되지 않았습니다.
