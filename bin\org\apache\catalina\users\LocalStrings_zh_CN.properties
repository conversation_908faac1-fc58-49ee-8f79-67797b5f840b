# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

memoryUserDatabase.fileClose=关闭 [{0}] 失败
memoryUserDatabase.fileDelete=无法删除 [{0}]
memoryUserDatabase.fileNotFound=指定用户数据库[{0}]未找到
memoryUserDatabase.notPersistable=用户数据库不可持久化 - 对目录没有写入权限
memoryUserDatabase.nullGroup=指定的组名为空或零长度。将忽略组。
memoryUserDatabase.nullRole=指定的角色名为Null或着长度为0。角色将被忽略，
memoryUserDatabase.nullUser=Null或者零长度的用户名称，忽略这个用户。
memoryUserDatabase.readOnly=用户数据库已被设为只读。修改无法保存
memoryUserDatabase.reload=从更新的字眼[{1}]重新加载内存用户数据库
memoryUserDatabase.reloadError=从更新后的源 [{1}] 重新加载内存用户数据库 [{0}] 时出错
memoryUserDatabase.renameNew=无法将新文件重命名为 [{0}]
memoryUserDatabase.renameOld=原文件无法改名为[{0}]
memoryUserDatabase.restoreOrig=无法往原始文件中保存[{0}]
memoryUserDatabase.writeException=向[{0}]写入IOException
memoryUserDatabase.xmlFeatureEncoding=配置digester以允许在XML文件中使用java编码名称时发生异常。只支持IANA编码名称。
