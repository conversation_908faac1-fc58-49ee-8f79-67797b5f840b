# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jre9Compat.invalidModuleUri=L''URI du module fournie [{0}] n''a pas pu être convertie en URL pour être traitée par le JarScanner
jre9Compat.javaPre9=Le code est considéré être exécuté sur une JVM antérieure à Java 9 car la classe n'a pas été trouvée
jre9Compat.unexpected=Impossible de créer les références vers les classes et méthodes de Java 9

jreCompat.noApplicationProtocol=Le Java Runtime utilisé ne supporte pas SSLEngine.getApplicationProtocol(). Il faut Java 9 pour utiliser cette option.
jreCompat.noApplicationProtocols=L'environnement Java ne supporte pas SSLParameters.setApplicationProtocols(), cette fonctionnalité demande Java 9
