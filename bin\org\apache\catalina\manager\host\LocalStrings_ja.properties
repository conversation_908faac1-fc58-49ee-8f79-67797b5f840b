# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

hostManagerServlet.add=add：ホスト[{0}]を追加
hostManagerServlet.addFailed=FAIL - ホスト [{0}] を追加できません。
hostManagerServlet.addSuccess=OK  - ホスト[{0}]を追加しました
hostManagerServlet.alreadyHost=FAIL  - ホスト名[{0}]のホストが既に存在します
hostManagerServlet.alreadyStarted=FAIL - ホスト [{0}] はすでに開始しています。
hostManagerServlet.alreadyStopped=FAIL - Host [{0}] はすでに停止しています。
hostManagerServlet.appBaseCreateFail=FAIL - ホスト [{1}] の appBase [{0}] を作成できません。
hostManagerServlet.cannotRemoveOwnHost=FAIL  - 自身のホスト[{0}]を削除できません
hostManagerServlet.cannotStartOwnHost=FAIL - 自身のホスト [{0}] は開始できません。
hostManagerServlet.cannotStopOwnHost=FAIL - 自分自身のホスト [{0}] を停止できません。
hostManagerServlet.configBaseCreateFail=FAIL - ホスト [{0}] の configBase が特定できません。
hostManagerServlet.exception=FAIL  - 例外[{0}]が発生しました
hostManagerServlet.invalidHostName=FAIL  - 無効なホスト名[{0}]が指定されました
hostManagerServlet.list=リスト：Engine[{0}]のHostのリスト
hostManagerServlet.listed=OK - 列挙されたHost
hostManagerServlet.managerXml=FAIL - manager.xml をインストールできませんでした。
hostManagerServlet.noCommand=FAIL - コマンドが指定されませんでした。
hostManagerServlet.noHost=FAIL - ホスト名 [{0}] は存在しません。
hostManagerServlet.noWrapper=Container はこのサーブレットの setWrapper() を呼び出していません。
hostManagerServlet.persist=永続化：現在の設定を保持する
hostManagerServlet.persistFailed=FAIL  - 構成を永続化できませんでした
hostManagerServlet.persisted=OK - 構成が永続化されました
hostManagerServlet.postCommand=FAIL  - コマンド[{0}]をGETリクエストで使用しようとしましたが、POSTが必要です
hostManagerServlet.remove=remove: ホスト [{0}] を削除します。
hostManagerServlet.removeFailed=FAIL - Host [{0}] を削除できません。
hostManagerServlet.removeSuccess=OK  - ホスト[{0}]を削除しました
hostManagerServlet.start=開始：名前[{0}]のホストを起動しています
hostManagerServlet.startFailed=FAIL  - ホスト[{0}]の起動に失敗しました
hostManagerServlet.started=OK - ホスト[{0}] を開始しました。
hostManagerServlet.stop=停止：名前[{0}]のホストを停止しています
hostManagerServlet.stopFailed=FAIL- ホスト [{0}] を停止できません。
hostManagerServlet.stopped=OK - ホスト[{0}]が停止しました
hostManagerServlet.unknownCommand=FAIL  - 不明なコマンド[{0}]

htmlHostManagerServlet.addAliases=エイリアス：
htmlHostManagerServlet.addAppBase=App base:
htmlHostManagerServlet.addAutoDeploy=自動配備
htmlHostManagerServlet.addButton=追加
htmlHostManagerServlet.addCopyXML=CopyXML
htmlHostManagerServlet.addDeployOnStartup=DeployOnStartup
htmlHostManagerServlet.addDeployXML=DeployXML
htmlHostManagerServlet.addHost=ホスト
htmlHostManagerServlet.addManager=Manager アプリケーション
htmlHostManagerServlet.addName=名前：
htmlHostManagerServlet.addTitle=仮想ホスト追加
htmlHostManagerServlet.addUnpackWARs=UnpackWARs
htmlHostManagerServlet.helpHtmlManager=HTMLホストマネージャ ヘルプ
htmlHostManagerServlet.helpHtmlManagerFile=../docs/html-host-manager-howto.html
htmlHostManagerServlet.helpManager=Host Manager ヘルプ
htmlHostManagerServlet.helpManagerFile=../docs/host-manager-howto.html
htmlHostManagerServlet.hostAliases=Hostエイリアス
htmlHostManagerServlet.hostName=ホスト名
htmlHostManagerServlet.hostTasks=コマンド
htmlHostManagerServlet.hostThis=Host Managerがインストールされています - コマンドが無効です
htmlHostManagerServlet.hostsRemove=削除
htmlHostManagerServlet.hostsStart=起動
htmlHostManagerServlet.hostsStop=停止
htmlHostManagerServlet.list=仮想ホスト一覧
htmlHostManagerServlet.manager=Host Manager
htmlHostManagerServlet.messageLabel=メッセージ：
htmlHostManagerServlet.persistAll=現在の設定（仮想ホストを含む）をserver.xmlおよびwebアプリケーションcontext.xmlファイルに保存します。
htmlHostManagerServlet.persistAllButton=全て
htmlHostManagerServlet.persistTitle=構成維持
htmlHostManagerServlet.serverJVMVendor=JVMベンダ
htmlHostManagerServlet.serverJVMVersion=JVM バージョン
htmlHostManagerServlet.serverOSArch=OS アーキテクチャ
htmlHostManagerServlet.serverOSName=OS 名
htmlHostManagerServlet.serverOSVersion=OS バージョン
htmlHostManagerServlet.serverTitle=Server情報
htmlHostManagerServlet.serverVersion=Tomcatバージョン
htmlHostManagerServlet.title=Tomcat仮想ホストマネージャ

statusServlet.complete=完全なサーバステータス
statusServlet.title=サーバステータス
