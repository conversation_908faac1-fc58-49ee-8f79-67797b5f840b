<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE mbeans-descriptors PUBLIC
   "-//Apache Software Foundation//DTD Model MBeans Configuration File"
   "http://jakarta.apache.org/commons/dtds/mbeans-descriptors.dtd">
<mbeans-descriptors>

  <!-- This MBean contains Connector attributes and some common attributes of
       associated ProtocolHandler instances. Common attributes extracted out
       from all ProtocolHandler implementations are denoted by a comment
       'common' above their description. Each attribute particular to given
       ProtocolHandler implementation can be found in relevant ProtocolHandler
       MBean. -->
  <mbean         name="CoyoteConnector"
            className="org.apache.catalina.mbeans.ConnectorMBean"
          description="Implementation of a Coyote connector"
               domain="Catalina"
                group="Connector"
                 type="org.apache.catalina.connector.Connector">

    <!-- Common -->
    <attribute   name="acceptCount"
          description="The accept count for this Connector"
                 type="int"/>

    <!-- Common -->
    <attribute   name="address"
          description="The IP address on which to bind"
                 type="java.lang.String"/>

    <attribute   name="ajpFlush"
          description="Send AJP flush package for each explicit flush"
                 type="boolean"/>

    <attribute   name="allowedRequestAttributesPattern"
          description="Regular expression that any custom request attributes muct match else the request will be rejected"
                 type="java.lang.String"/>

    <attribute   name="allowTrace"
          description="Allow disabling TRACE method"
                 type="boolean"/>

    <attribute   name="ciphers"
          description="Comma-separated list of requested cipher suites"
                 type="java.lang.String"/>

    <attribute   name="ciphersUsed"
          description="Array of ciphers suites in use"
                 type="[Ljava.lang.String;"
            writeable="false"/>

    <attribute   name="className"
          description="Fully qualified class name of the managed object"
                 type="java.lang.String"
            writeable="false"/>

    <!-- Common -->
    <attribute   name="connectionLinger"
          description="Linger value on the incoming connection"
                 type="int"/>

    <!-- Common -->
    <attribute   name="connectionTimeout"
          description="Timeout value on the incoming connection"
                 type="int"/>

    <attribute   name="enableLookups"
          description="The 'enable DNS lookups' flag for this Connector"
                 type="boolean"/>

    <attribute   name="executorName"
          description="The name of the executor - if any - associated with this Connector"
                 type="java.lang.String"
            writeable="false"/>

    <!-- Common -->
    <attribute   name="keepAliveTimeout"
          description="The number of milliseconds Tomcat will wait for a subsequent request before closing the connection"
                 type="int"/>

    <attribute   name="localPort"
          description="The port number on which this connector is listening to requests. If the special value for port of zero is used then this method will report the actual port bound."
                 type="int"
            writeable="false"/>

    <attribute   name="maxHeaderCount"
          description="The maximum number of headers that are allowed by the container. 100 by default. A value of less than 0 means no limit."
                 type="int"/>

    <attribute   name="maxKeepAliveRequests"
          description="Maximum number of Keep-Alive requests to honor per connection"
                 type="int"/>

    <attribute   name="maxParameterCount"
          description="The maximum number of parameters (GET plus POST) which will be automatically parsed by the container. 10000 by default. A value of less than 0 means no limit."
                 type="int"/>

    <attribute   name="maxPostSize"
          description="Maximum size in bytes of a POST which will be handled by the servlet API provided features"
                 type="int"/>

    <attribute   name="maxSavePostSize"
          description="Maximum size of a POST which will be saved by the container during authentication"
                 type="int"/>

    <attribute   name="maxSwallowSize"
          description="The maximum number of request body bytes to be swallowed by Tomcat for an aborted upload"
                 type="int"/>

    <!-- Common -->
    <attribute   name="maxThreads"
          description="The maximum number of request processing threads to be created for the internal Executor. -1 indicates an external Executor is being used."
                 type="int"/>

    <attribute   name="minSpareThreads"
          description="The number of request processing threads that will be created for the internal Executor. -1 indicates an external Executor is being used."
                 type="int"/>

    <!-- Common -->
    <attribute   name="packetSize"
          description="The ajp packet size."
                 type="int"/>

    <attribute   name="port"
          description="The port number on which this connector is configured to listen for requests. The special value of 0 means select a random free port when the socket is bound."
                type="int"/>

    <!-- Common -->
    <attribute   name="processorCache"
          description="The processor cache size."
                 type="int"/>

    <attribute   name="protocol"
          description="Coyote protocol handler in use"
                 type="java.lang.String"/>

    <attribute   name="protocolHandlerClassName"
          description="Coyote Protocol handler class name"
                 type="java.lang.String"
            writeable="false"/>

    <attribute   name="proxyName"
          description="The Server name to which we should pretend requests to this Connector"
                 type="java.lang.String"/>

    <attribute   name="proxyPort"
          description="The Server port to which we should pretend requests to this Connector"
                 type="int"/>

    <attribute   name="redirectPort"
          description="The redirect port for non-SSL to SSL redirects"
                 type="int"/>

    <attribute   name="scheme"
          description="Protocol name for this Connector (http, https)"
                 type="java.lang.String"/>

    <attribute   name="secret"
          description="Authentication secret"
            readable = "false"
                 type="java.lang.String"/>

    <attribute   name="secretRequired"
          description="Must secret be set to a non-null, non-zero-length String?"
                 type="boolean"/>

    <attribute   name="secure"
          description="Is this a secure (SSL) Connector?"
                 type="boolean"/>

    <attribute   name="sslProtocols"
          description="Comma-separated list of SSL protocol variants to be enabled"
                 type="java.lang.String"/>

    <attribute   name="stateName"
          description="The name of the LifecycleState that this component is currently in"
                 type="java.lang.String"
                 writeable="false"/>

    <!-- Common -->
    <attribute   name="tcpNoDelay"
          description="Should we use TCP no delay?"
                 type="boolean"/>

    <!-- Common -->
    <attribute    name="threadPriority"
           description="The thread priority for processors using the internal Executor. -1 indicates an external Executor is being used."
                  type="int"/>

    <attribute   name="URIEncoding"
          description="Character encoding used to decode the URI"
                 type="java.lang.String"/>

    <attribute   name="useBodyEncodingForURI"
          description="Should the body encoding be used for URI query parameters"
                 type="boolean"/>

    <attribute   name="useIPVHosts"
          description="Should IP-based virtual hosting be used? "
                 type="boolean"/>

    <attribute    name="xpoweredBy"
           description="Is generation of X-Powered-By response header enabled/disabled?"
                  type="boolean"/>

    <operation name="start" description="Start" impact="ACTION" returnType="void" />
    <operation name="stop" description="Stop" impact="ACTION" returnType="void" />
    <operation name="pause" description="Start" impact="ACTION" returnType="void" />
    <operation name="resume" description="Stop" impact="ACTION" returnType="void" />
    <operation name="init" description="Init" impact="ACTION" returnType="void" />
    <operation name="destroy" description="Destroy" impact="ACTION" returnType="void" />

  </mbean>
</mbeans-descriptors>
