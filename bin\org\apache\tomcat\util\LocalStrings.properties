# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

diagnostics.threadDumpTitle=Full thread dump
diagnostics.vmInfoClassCompilation=Class compilation
diagnostics.vmInfoClassLoading=Class loading
diagnostics.vmInfoGarbageCollectors=Garbage Collector [{0}]
diagnostics.vmInfoLogger=Logger information
diagnostics.vmInfoMemory=Memory information
diagnostics.vmInfoMemoryManagers=Memory Manager [{0}]
diagnostics.vmInfoMemoryPools=Memory Pool [{0}]
diagnostics.vmInfoOs=OS information
diagnostics.vmInfoPath=Path information
diagnostics.vmInfoRuntime=Runtime information
diagnostics.vmInfoStartup=Startup arguments
diagnostics.vmInfoSystem=System properties
diagnostics.vmInfoThreadCounts=Thread counts
diagnostics.vmInfoThreadMxBean=ThreadMXBean capabilities
