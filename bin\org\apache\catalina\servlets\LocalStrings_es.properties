# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cgiServlet.expandFail=Fallo al expandir el script [{0}] en  [{1}]\n
cgiServlet.expandOk=Expandiendo script en el path  [{0}] hacia [{1}]\n
cgiServlet.find.location=Buscando archivo en  [{0}]\n
cgiServlet.runHeaderReaderFail=Problemas de I/O  cerrando la cabecera del lector
cgiServlet.runInvalidStatus=Estado inválido [{0}]
cgiServlet.runOutputStreamFail=Errores I/O cerrando el flujo de salida
cgiServlet.runReaderInterrupt=Detenido esperando por el hilo lector stderr
cgiServlet.runStdErrFail=Problemas de I/O con stderr

defaultServlet.blockExternalSubset=Se bloqueó el acceso al subconjunt externo con nombre [{0}] y URI base  [{1}]\n
defaultServlet.missingResource=El recurso requerido [{0}] no se encuentra disponible
defaultServlet.skipfail=Sólo se han saltado [{0}] cuando se requirieron [{1}]

directory.filename=Nombre de Fichero:
directory.lastModified=Última Modificación
directory.parent=Atrás A [{0}]
directory.size=Medida
directory.title=Listado de Directorio Para [{0}]

webdavservlet.externalEntityIgnored=El requerimiento incluía una referencia a una entidad externa con PublicID [{0}] y SystemID [{1}] que fue ignorada
webdavservlet.jaxpfailed=Falló la inicialización de JAXP
