# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractConnectionHandler.oome=Verarbeitung der Anfrage fehlgeschlagen
abstractConnectionHandler.processorPop=Prozessor [{0}] wurde aus dem Cache genommen.
abstractConnectionHandler.socketexception.debug=SocketExceptions sind normal, werden ignoriert

abstractProcessor.fallToDebug=\n\
\ Info: Weitere Vorkommen von Fehlern beim <PERSON> der Anfragen werden mit DEBUG Level ausgegeben
abstractProcessor.hostInvalid=Der Host [{0}] ist nicht gültig.
abstractProcessor.httpupgrade.notsupported=HTTP-Upgrade wird von diesem Protokol nicht unterstützt

abstractProtocolHandler.init=Initialisiere ProtocolHandler[{0}]

asyncStateMachine.invalidAsyncState=Der Aufruf von [{0}] ist nicht erlaubt, während der Request im Async-Status [{1}] ist

response.writeListenerSet=Der Nicht-blockierende Schreib-Listener wurde bereits gesetzt
