# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

addDefaultCharset.unsupportedCharset=Le jeu de caractères [{0}] spécifié n''est pas supporté

corsFilter.invalidPreflightMaxAge=Incapable d'analyser "preflightMaxAge"
corsFilter.invalidSupportsCredentials=Il est interdit de configurer supportsCredentials=[true] alors que allowedOrigins=[*]
corsFilter.nullRequest=L'objet HttpServletRequest est nul
corsFilter.nullRequestType=L'objet CORSRequestType est nul
corsFilter.onlyHttp=CORS ne supporte pas des requêtes ou des réponses non HTTP
corsFilter.wrongType1=Attente d''un objet HttpServletRequest de type [{0}]
corsFilter.wrongType2=Attendu un object HttpServletRequest de type [{0}] ou [{1}]

csrfPrevention.invalidRandomClass=Impossible de créer une source aléatoire ("Random source") à l''aide de la classe [{0}]

expiresFilter.exceptionProcessingParameter=Erreur lors du traitement du paramètre de configuration [{0}] : [{1}]
expiresFilter.expirationHeaderAlreadyDefined=Requête [{0}] de statut de réponse [{1}] et de type de contenu ("content-type") [{2}], en-tête d''expiration déjà défini
expiresFilter.filterInitialized=Le filtre a été initialisé avec la configuration [{0}]
expiresFilter.invalidDurationNumber=Durée (nombre) invalide [{0}] dans la directive [{1}]
expiresFilter.invalidDurationUnit=Unité de durée invalide (years|months|weeks|days|hours|minutes|seconds) [{0}] dans la directive [{1}]
expiresFilter.noDurationFound=La durée n''a pas été trouvée dans la directive [{0}]
expiresFilter.noDurationUnitAfterAmount=L''unité de durée n''a pas été trouvée après le nombre [{0}] dans la directive [{1}]
expiresFilter.noExpirationConfigured=Requête [{0}] avec statut de réponse [{1}] et content-type [{2}], pas d''expiration configurée
expiresFilter.noExpirationConfiguredForContentType=La configuration "Expires" pour le type de contenu [{0}] n''est pas trouvée.
expiresFilter.numberError=Erreur lors de l''analyse du nombre à la position [{0}] (basée sur zéro) de la liste délimitée par une virgule [{1}]
expiresFilter.responseAlreadyCommitted=Impossible d''appliquer le ExpiresFilter pour la requête [{0}] car la réponse a déjà été envoyée
expiresFilter.setExpirationDate=La requête [{0}] avec le status de réponse [{1}] content-type [{2}], a fixé la date d''expiration [{3}]
expiresFilter.skippedStatusCode=La requête [{0}] avec le code de réponse [{1}] content-type [{2}], l''en-tête expiration ne sera pas généré pour ce statut
expiresFilter.startingPointInvalid=Point de départ invalide (access|now|modification|a<secondes>|m<secondes>) [{0}] dans la directive [{1}]\n
expiresFilter.startingPointNotFound=Le point de départ (access|now|modification|a<seconds>|m<seconds>) n''est pas présent dans la directive [{0}]
expiresFilter.unknownParameterIgnored=Le paramètre inconnu [{0}] dont la valeur est [{1}] est ignoré
expiresFilter.unsupportedStartingPoint=startingPoint [{0}] non supporté.
expiresFilter.useDefaultConfiguration=Utilisation du défaut [{0}] pour le content-type [{1}] qui renvoie [{2}]
expiresFilter.useMatchingConfiguration=Utilisation de [{0}], satisfaisant [{1}] pour le content-type [{2}] et retournant [{3}]

filterbase.noSuchProperty=La propriété [{0}] n''est pas définie pour les filtres du type [{1}]

http.403=L''accès à la ressource demandée [{0}] a été interdit.

httpHeaderSecurityFilter.clickjack.invalid=Une valeur invalide [{0}] a été spécifiée pour le header "anti click-jacking"
httpHeaderSecurityFilter.committed=Impossible d'ajouter les en-têtes HTTP car la réponse a déjà été envoyée avant l'invocation du filtre de sécurité des en-têtes

remoteCidrFilter.invalid=Une configuration invalide a été fournie pour [{0}], voir les précédents messages pour les détails
remoteCidrFilter.noRemoteIp=Le client n'a pas d'adresse IP.  Requête rejetée.

remoteIpFilter.invalidHostHeader=La valeur invalide [{0}] a été trouvée pour le Host dans l''en-tête HTTP [{1}]
remoteIpFilter.invalidHostWithPort=La valeur de Host [{0}] dans l''en-tête HTTP [{1}] contenait un numéro de port qui sera ingnoré
remoteIpFilter.invalidNumber=Nombre invalide pour le paramètre [{0}] : [{1}]
remoteIpFilter.invalidRemoteAddress=Impossible de déterminer l''hôte distant car l''adresse distante [{0}] est invalide

requestFilter.deny=Refus de la requête [{0}] d''après la propriété [{1}]

restCsrfPreventionFilter.invalidNonce=La validation du nonce de CSRF a échouée

webDavFilter.xpProblem=WebdavFixFilter : le client de XP-x64-SP2 est réputé ne pas fonctionner avec le Servlet WebDAV
webDavFilter.xpRootContext=WebdavFixFilter : le client de XP-x64-SP2 ne peut fonctionner que le le contexte racine
