# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

McastService.domain=无法发送域更新
McastService.parseSoTimeout=无法解析SoTimeout：[{0}]
McastService.parseTTL=无法分析TTL:[{0}]
McastService.payload=无法发送负载更新
McastService.stopFail=无法停止mcast服务，级别：[{0}]

mcastService.exceed.maxPacketSize=数据包长度[{0}]超过了最大数据包大小[{1}]字节。
mcastService.missing.property=McastService：缺少必需属性 [{0}]。
mcastService.noStart=多路广播发送未启动或未启用

mcastServiceImpl.bind=尝试将多播套接字绑定到 [{0}:{1}]
mcastServiceImpl.bind.failed=绑定到多播地址失败。仅绑定到端口。
mcastServiceImpl.error.receiving=接收mcast包时出错。睡眠500毫秒
mcastServiceImpl.invalid.startLevel=无效的启动级别。只接受以下级别：Channel.MBR_RX_SEQ或 Channel.MBR_TX_SEQ
mcastServiceImpl.invalid.stopLevel=无效的停止级别。只有Channel.MBR_RX_SEQ和Channel.MBR_TX_SEQ是可接受的级别
mcastServiceImpl.memberDisappeared.failed=无法处理成员已消失的消息。
mcastServiceImpl.packet.tooLong=收到的多播数据包太长，正在删除包：[{0}]
mcastServiceImpl.receive.running=McastService.receive已经运行。
mcastServiceImpl.recovery=家族成员，运行恢复线程，广播不是功能。
mcastServiceImpl.recovery.failed=恢复尝试次数[{0}]失败，请在[{1}]秒内重试
mcastServiceImpl.recovery.startFailed=恢复线程无法启动成员资格服务。
mcastServiceImpl.recovery.stopFailed=恢复线程未能停止成员服务。
mcastServiceImpl.recovery.successful=成员身份恢复成功。
mcastServiceImpl.send.failed=无法发送多播信息
mcastServiceImpl.send.running=McastService.send已经运行
mcastServiceImpl.setInterface=设置多宿主多播接口为：[{0}]
mcastServiceImpl.setSoTimeout=设置集群多播超时时间：[{0}]
mcastServiceImpl.setTTL=设置集群多播TTL：[{0}]
mcastServiceImpl.unable.join=无法加入多播组，请确保你的系统已启用多播。
mcastServiceImpl.unableReceive.broadcastMessage=无法接收广播消息。
mcastServiceImpl.waitForMembers.done=休眠完毕，成员已连接，启动等级：[{0}]
mcastServiceImpl.waitForMembers.start=休眠[{0}]毫秒后启动连接集群，启动登记：[{1}]

memberImpl.invalid.package.begin=无效的包，应以“[{0}”开头。
memberImpl.invalid.package.end=无效的包，应以“[{0}”结尾
memberImpl.large.payload=负载太大以至于难以处理
memberImpl.notEnough.bytes=成员包中的字节不够。
memberImpl.package.small=成员包太小以至于不能校验。

staticMember.invalid.uuidLength=UUID必须正好是16个字节，而不是：[{0}]
