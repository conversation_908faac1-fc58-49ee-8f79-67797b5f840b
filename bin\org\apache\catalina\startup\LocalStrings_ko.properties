# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

catalina.configFail=[{0}](으)로부터 서버 설정을 로드할 수 없습니다.
catalina.noCluster=[{0}](으)로 인하여 클러스터 RuleSet을 찾을 수 없습니다. 클러스터 설정은 사용불능 상태입니다.
catalina.noNaming=Naming 환경은 사용 불능 상태입니다.
catalina.serverStartFail=필수 항목인 서버 구성요소가 제대로 시작되지 못하여, Tomcat이 시작될 수 없습니다.
catalina.shutdownHookFail=서버를 중지시키려는 과정에서, 셧다운 훅에서 오류가 발생했습니다.
catalina.stopServer=셧다운 포트가 설정되지 않았습니다. OS 시그널을 통해 서버를 셧다운합니다. 서버는 아직 셧다운되지 않았습니다.

connector.noSetExecutor=Connector [{0}]은(는) 외부 Executor들을 지원하지 않습니다. 메소드 setExecutor(java.util.concurrent.Executor)를 찾을 수 없습니다.
connector.noSetSSLImplementationName=Connector [{0}]은(는) SSL 구현을 변경하는 것을 지원하지 않습니다. setSslImplementationName(String) 메소드를 찾을 수 없습니다.

contextConfig.altDDNotFound=alt-dd 파일 [{0}]을(를) 찾을 수 없습니다.
contextConfig.annotationsStackOverflow=StackOverflowError로 인하여, 웹 애플리케이션 [{0}]에서 annotation 스캔을 완료하지 못했습니다. 가능성 있는 근본 원인(root cause)들 중의 하나는 -Xss가 너무 적게 설정되어 있거나 불허된 순환 상속 의존관계들일 수 있습니다. 처리되는 클래스의 상속 계층구조는 [{1}]입니다.
contextConfig.applicationMissing=애플리케이션 web.xml이 없습니다. 기본 설정들만을 사용할 것입니다.
contextConfig.applicationParse=[{0}]에 위치한 애플리케이션 web.xml에서 파싱 오류 발생
contextConfig.applicationPosition=[{0}]행 [{1}]열에서 발생했음
contextConfig.applicationStart=[{0}]에 위치한 애플리케이션 web.xml을 파싱합니다.
contextConfig.applicationUrl=애플리케이션 web.xml의 URL을 결정할 수 없습니다.
contextConfig.authenticatorConfigured=메소드 [{0}]을(를) 위한 Authenticator를 설정했습니다.
contextConfig.authenticatorInstantiate=클래스 [{0}]의 Authenticator 인스턴스를 생성할 수 없습니다,
contextConfig.authenticatorMissing=인증 메소드 [{0}]을(를) 위한 Authenticator를 설정할 수 없습니다.
contextConfig.authenticatorResources=Authenticator들의 매핑 목록을 로드할 수 없습니다.
contextConfig.badUrl=컨텍스트 descriptor [{0}]을(를) 처리할 수 없습니다.
contextConfig.cce=Lifecycle 이벤트 데이터 객체 [{0}]이(가) Context 객체가 아닙니다.
contextConfig.contextClose=context.xml을 닫는 중 오류 발생
contextConfig.contextMissing=context.xml이 존재하지 않습니다: [{0}]
contextConfig.contextParse=컨텍스트 [{0}]을(를) 위한 context.xml 내에서 파싱 오류 발생
contextConfig.defaultError=[{1}]에 위치하고 [{0}](이)라는 이름의 기본 web.xml을 처리하는 중 오류 발생
contextConfig.defaultMissing=글로벌 web.xml 파일을 찾을 수 없습니다.
contextConfig.defaultPosition=[{0}] 행, [{1}] 열에서 발생
contextConfig.destroy=ContextConfig: 소멸시키는 중
contextConfig.fileUrl=URL [{0}](으)로부터 File 객체를 생성할 수 없습니다.
contextConfig.fixDocBase=컨텍스트 [{0}]을(를) 위한 docBase를 조정하는 중 예외 발생
contextConfig.init=ContextConfig: 초기화 중
contextConfig.inputStreamFile=파일 [{0}]에 대하여 annotation들을 처리할 수 없습니다.
contextConfig.inputStreamJar=Annotation들을 스캔하기 위해, Jar [{1}](으)로부터의 Jar 엔트리 [{0}]을(를) 처리할 수 없습니다.
contextConfig.inputStreamWebResource=Annotation들을 위해 웹 리소스 [{0}]을(를) 처리할 수 없습니다.
contextConfig.invalidSciHandlesTypes=하나 이상의 ServletContentInitializer들의 @HandlesTypes annotation에 대한 점검을 위해, 클래스 [{0}]을(를) 로드할 수 없습니다.
contextConfig.jarFile=Annotation들을 위해 Jar [{0}]을(를) 처리할 수 없습니다.
contextConfig.jspFile.error=JSP 파일 [{0}]은(는) 반드시 ''/''로 시작해야 합니다.
contextConfig.jspFile.warning=경고: Servlet 2.4에서 JSP 파일 [{0}]은(는) 반드시 ''/''로 시작해야 합니다.
contextConfig.missingRealm=인증 처리 시 사용할 Realm이 설정되지 않았습니다.
contextConfig.noAntiLocking=java.io.tmpdir 프로퍼티 값 [{0}]이(가) 유효한 디렉토리 경로가 아닙니다. 해당 웹 애플리케이션 [{1}]을(를) 위한 antiResourceLocking 설정은 무시됩니다.
contextConfig.processAnnotationsDir.debug=Annotation들을 가진 클래스 파일들을 찾기 위해 디렉토리 [{0}]을(를) 스캔합니다.
contextConfig.processAnnotationsJar.debug=Annotation들, [{0}]을(를) 가진 클래스 파일들을 찾기 위해, JAR 파일을 스캔합니다.
contextConfig.processAnnotationsWebDir.debug=Annotation들인 [{0}]을(를) 가진 클래스 파일들을 찾기 위해, 웹 애플리케이션 디렉토리를 스캔합니다.
contextConfig.resourceJarFail=정적 리소스들이, [{1}](이)라는 이름의 컨텍스트에 포함되게 하기 위하여, URL [{0}]에서 발견된 JAR를 처리하지 못했습니다.
contextConfig.role.auth=보안 역할 이름 [{0}]이(가), <security-role>에서 정의되지 않은 채로, <auth-constraint>에서 사용되었습니다.
contextConfig.role.link=보안 역할 이름 [{0}]이(가) <security-role>에서 정의된 적이 없는데, <role-link>에서 사용되었습니다.
contextConfig.role.runas=<security-role> 내에 정의되지 않고, 보안 역할 이름 [{0}]이(가) <run-as>에서 사용되었습니다.
contextConfig.sci.debug=[{0}]을(를) 위해 ServletContainerInitializer를 처리할 수 없습니다. 이는 필시 @HandlesTypes annotation 내에 정의된 클래스가 존재하지 않기 때문일 것입니다.
contextConfig.sci.info=[{0}]을(를) 위한 ServletContainerInitializer를 처리할 수 없습니다. 이는 필시 @HandlesTypes annotation에 정의된 클래스가 존재하지 않기 때문일 것입니다. 전체 스택 트레이스를 보시려면, 로그 레벨을 디버그 레벨로 설정하십시오.
contextConfig.servletContainerInitializerFail=이름이 [{0}]인 컨텍스트를 위한 ServletContainerInitializer들을 탐지하지 못했습니다.
contextConfig.start=ContextConfig: 시작 처리 중
contextConfig.stop=ContextConfig: STOP 처리 중
contextConfig.unavailable=이전 오류(들)로 인하여, 이 애플리케이션이 가용하지 않은 것으로 표시합니다.
contextConfig.unknownUrlProtocol=Annotation 처리 중, 인식되지 않는 프로토콜 [{0}]을(를) 포함하여, URL [{1}]이(가) 무시되었습니다.
contextConfig.urlPatternValue=클래스 [{1}]의 [{0}] annotation을 위해, urlPatterns와 value 속성, 둘 다 설정되었습니다.
contextConfig.xmlSettings=컨텍스트 [{0}]이(가), validation:[{1}]와(과) namespaceAware:[{2}]을 사용하여, web.xml과 web-fragment.xml 파일들을 파싱합니다.

engineConfig.cce=Lifecycle 이벤트 데이터 객체 [{0}]이(가) Engine 객체가 아닙니다.
engineConfig.start=EngineConfig: START 처리 중
engineConfig.stop=EngineConfig: STOP 처리 중

expandWar.copy=[{0}]을(를) [{1}]에 복사하는 중 오류 발생
expandWar.createFailed=디렉토리 [{0}]을(를) 생성할 수 없습니다.
expandWar.createFileFailed=파일 [{0}]을(를) 생성할 수 없습니다.
expandWar.deleteFailed=[{0}]이(가) 완전히 삭제될 수 없었습니다. 남아있는 파일들의 존재는 문제들을 일으킬 수 있습니다.
expandWar.deleteOld=압축이 풀려진 디렉토리 [{0}]의 최종 변경 시간이, 연관된 WAR의 최종 변경 시간과 부합하지 않습니다. 해당 디렉토리는 삭제될 것입니다.
expandWar.illegalPath=아카이브 [{0}]에 문제가 있어 무시될 것입니다: 엔트리가 불허되는 경로 [{1}]을(를) 포함하고 있고, 그 경로가 정의된 docBase [{3}] 외부에 존재하기 때문에, [{2}]에 압축을 풀지 않았습니다.
expandWar.lastModifiedFailed=[{0}]을(를) 위해, 최종 변경 시간을 설정할 수 없습니다.
expandWar.missingJarEntry=JarEntry [{0}]을(를) 위한 입력 스트림을 얻을 수 없습니다 - WAR 파일이 깨졌나요?

failedContext.start=글로벌, 또는 호스트 별, 또는 해당 컨텍스트의 context.xml 파일을 처리하지 못하였으므로, 컨텍스트 [{0}]은(는) 시작될 수 없습니다.

hostConfig.appBase=호스트 [{0}]을(를) 위한 애플리케이션 base [{1}]이(가), 존재하지 않거나 디렉토리가 아닙니다. 배치 오류들을 방지하기 위하여, deployOnStartUp과 autoDeploy가 false로 설정되어 있었습니다. 다른 오류들이 여전히 발생할 수 있습니다.
hostConfig.canonicalizing=[{1}]의 배치를 제거하려 시도하는 동안, [{0}]을(를) 위한 canonical 경로를 결정할 수 없습니다.
hostConfig.cce=Lifecycle 이벤트 데이터 객체 [{0}]이(가) 호스트 객체가 아닙니다.
hostConfig.context.remove=컨텍스트 [{0}]을(를) 제거하는 중 오류 발생
hostConfig.context.restart=컨텍스트 [{0}]이(가) 다시 시작하는 동안 오류 발생
hostConfig.createDirs=배치를 위한 디렉토리 [{0}]을(를) 생성할 수 없습니다.
hostConfig.deploy.error=웹 애플리케이션 디렉토리 [{0}]을(를) 배치하는 중 예외 발생
hostConfig.deployDescriptor=배치 descriptor [{0}]을(를) 배치합니다.
hostConfig.deployDescriptor.blocked=컨텍스트 경로 [{0}]의 웹 애플리케이션이 배치되지 않았습니다. 왜냐하면 해당 애플리케이션의 안전한 배치에 필요한 설정들이 배치 descriptor [{1}]에 포함되어 있으나, 이 호스트의 deployXML 설정에 의해 배치 descriptor들이 처리 되지 않았기 때문입니다. 이 애플리케이션을 배치하기 위해서는 적절한 descriptor가 [{2}]에 생성되어야 합니다.
hostConfig.deployDescriptor.error=배치 descriptor [{0}]을(를) 배치하는 중 오류 발생
hostConfig.deployDescriptor.finished=배치 descriptor [{0}]의 배치가 [{1}] 밀리초 내에 완료되었습니다.
hostConfig.deployDescriptor.localDocBaseSpecified=호스트 appBase 내의 docBase [{0}]이(가) 지정되었으나, 이는 무시될 것입니다.
hostConfig.deployDescriptor.threaded.error=배치 descriptor들을 배치하려는 멀티 쓰레드 작업이 완료되기를 기다리는 중 오류 발생
hostConfig.deployDir=웹 애플리케이션 디렉토리 [{0}]을(를) 배치합니다.
hostConfig.deployDir.error=웹 애플리케이션 디렉토리 [{0}]을(를) 배치하는 중 오류 발생
hostConfig.deployDir.finished=웹 애플리케이션 디렉토리 [{0}]에 대한 배치가 [{1}] 밀리초에 완료되었습니다.
hostConfig.deployDir.threaded.error=디렉토리들의 멀티 쓰레드 배치 작업이 완료되기를 기다리는 중 오류 발생
hostConfig.deployWar=웹 애플리케이션 아카이브 [{0}]을(를) 배치합니다.
hostConfig.deployWar.error=웹 애플리케이션 아카이브 [{0}]을(를) 배치하는 중 오류 발생
hostConfig.deployWar.finished=웹 애플리케이션 아카이브 [{0}]의 배치가 [{1}] 밀리초에 완료되었습니다.
hostConfig.deployWar.hiddenDir=WAR [{1}]은(는) 우선순위가 높게 처리되어야 하고, unpackWARs가 false이기 때문에, 디렉토리 [{0}]은(는) 무시될 것입니다.
hostConfig.deployWar.threaded.error=WAR 파일들에 대해 멀티 쓰레드 배치 작업들이 완료되기까지 기다리는 중 오류 발생
hostConfig.deploying=발견된 웹 애플리케이션들을 배치합니다.
hostConfig.docBaseUrlInvalid=제공된 docBase는 URL로서 표현될 수 없습니다.
hostConfig.expand=웹 애플리케이션 아카이브 [{0}]의 압축을 풉니다.
hostConfig.expand.error=웹 애플리케이션 아카이브 [{0}]의 압축을 푸는 중 예외 발생
hostConfig.ignorePath=자동 배치를 위해 appBase내의 [{0}] 경로를 무시합니다.
hostConfig.illegalWarName=War 이름[{0}]이(가) 유효하지 않습니다. 이 아카이브는 무시될 것입니다.
hostConfig.jmx.register=컨텍스트 [{0}]을(를) 등록하지 못했습니다.
hostConfig.jmx.unregister=컨텍스트 [{0}]에 대한 등록을 제거하지 못했습니다.
hostConfig.reload=컨텍스트 [{0}]을(를) 다시 로드합니다.
hostConfig.resourceNotAbsolute=[{1}]이(가) 절대 경로가 아니기 때문에, 컨텍스트 [{0}](으)로부터 리소스를 제거할 수 없습니다.
hostConfig.start=HostConfig: 시작 처리 중
hostConfig.stop=HostConfig.stop() 오퍼레이션 처리 중
hostConfig.undeploy=컨텍스트 [{0}]의 배치를 제거합니다.
hostConfig.undeployVersion=활성화된 세션이 없는, 컨텍스트 [{0}]의 이전 버전의 배치를 제거합니다.

passwdUserDatabase.readFail=/etc/passwd로부터 사용자들의 전체 집합을 구하지 못했습니다.

tomcat.addWebapp.conflictChild=이미 존재하는 컨텍스트 [{2}](으)로 인하여, [{0}]에 위치한 WAR를 컨텍스트 경로 [{1}](으)로 배치할 수 없습니다.
tomcat.addWebapp.conflictFile=이미 존재하는 파일 [{2}](으)로 인하여, [{0}]에 위치한 WAR를 컨텍스트 경로 [{1}](으)로 배치할 수 없습니다.
tomcat.baseDirMakeFail=Base 디렉토리로서 사용하기 위한, 디렉토리 [{0}]을(를) 생성할 수 없습니다.
tomcat.baseDirNotDir=base 디렉토리를 위해 지정된 위치 [{0}]은(는) 디렉토리가 아닙니다.
tomcat.defaultMimeTypeMappingsFail=기본 MIME 타입들을 로드할 수 없습니다.
tomcat.homeDirMakeFail=홈 디렉토리로 사용할 디렉토리 [{0}]을(를) 생성할 수 없습니다.

userConfig.database=사용자 데이터베이스를 로드하는 중 예외 발생
userConfig.deploy=사용자 [{0}]을(를) 위해 웹 애플리케이션을 배치합니다.
userConfig.deploy.threaded.error=사용자 디렉토리들의 멀티 쓰레드 배치 작업이 완료되기를 기다리는 중 오류 발생
userConfig.deploying=사용자 웹 애플리케이션들을 배치합니다.
userConfig.error=사용자 [{0}]을(를) 위한 웹 애플리케이션을 배치 중 오류 발생
userConfig.start=UserConfig: START 처리 중
userConfig.stop=UserConfig: STOP 처리 중

versionLoggerListener.arg=명령 행 아규먼트:  {0}
versionLoggerListener.catalina.base=CATALINA_BASE:     {0}
versionLoggerListener.catalina.home=CATALINA_HOME:     {0}
versionLoggerListener.env=환경 변수:         {0} = {1}
versionLoggerListener.java.home=자바 홈:           {0}
versionLoggerListener.os.arch=아키텍처:          {0}
versionLoggerListener.os.name=운영체제 이름:     {0}
versionLoggerListener.os.version=운영체제 버전:     {0}
versionLoggerListener.prop=시스템 프로퍼티:   {0} = {1}
versionLoggerListener.serverInfo.server.built=Server 빌드 시각:  {0}
versionLoggerListener.serverInfo.server.number=Server 버전 번호:  {0}
versionLoggerListener.serverInfo.server.version=서버 버전 이름:    {0}
versionLoggerListener.vm.vendor=JVM 벤더:          {0}
versionLoggerListener.vm.version=JVM 버전:          {0}

webAnnotationSet.invalidInjection=유효하지 않은 메소드 리소스 injection annotation입니다.
