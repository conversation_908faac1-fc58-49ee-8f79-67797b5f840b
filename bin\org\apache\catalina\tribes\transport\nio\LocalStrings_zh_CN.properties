# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

nioReceiver.alreadyStarted=ServerSocketChannel已经被启动
nioReceiver.cleanup.fail=无法清除关闭的选择器
nioReceiver.clientDisconnect=复制客户端已断开连接，轮询密钥时出错。忽略客户端。
nioReceiver.requestError=NioReceiver无法处理请求
nioReceiver.run.fail=不能允许复制监听器
nioReceiver.start.fail=无法启动集群接收器
nioReceiver.stop.fail=无法关闭集群接收的选择器
nioReceiver.stop.threadRunning=NioReceiver线程没有及时停止。关闭选择器时可能会观察到错误。
nioReceiver.threadpool.fail=ThreadPool 无法初始化。 监听器未启动。
nioReceiver.threadsExhausted=通道密钥已注册，但对上一次[{0}]ms没有兴趣的操作（已取消：[{1}]）：[{2}]最后一次访问：[{3}]可能的原因：所有使用的线程，请执行线程转储

nioReplicationTask.error.register.key=错误的注册key被读取:[{0}]
nioReplicationTask.exception.drainChannel=TcpReplicationThread.drainChannel中捕获异常
nioReplicationTask.process.clusterMsg.failed=处理集群消息失败
nioReplicationTask.unable.ack=不能通过channel发送ack，channel已经断开?[{0}]
nioReplicationTask.unable.drainChannel.ioe=复制工作器中的IOException，无法耗尽通道。可能的原因：保持活动套接字关闭[{0}]。

nioSender.already.connected=NioSender已经处于连接状态
nioSender.datagram.already.established=数据报通道已经建立。连接可能正在进行中。
nioSender.key.inValid=Key无效，它必须是已经被取消的。
nioSender.not.connected=NioSender未连接，这是不应该发生的。
nioSender.receive.failedAck=收到一个失败的ack:org.apache.catalina.tribes.transport.Constants.FAIL_ack_DATA
nioSender.sender.disconnected=发件人已断开连接，无法处理选择密钥。
nioSender.socketChannel.already.established=套接字通道已建立。连接可能正在进行中
nioSender.unable.disconnect=无法断开NioSender，消息=[{0}]
nioSender.unable.receive.ack=无法接收确认消息。已到达套接字通道上的EOF。
nioSender.unknown.state=数据处于未知状态。readyOps = [{0}]

parallelNioSender.error.keepalive=对发件人进行keepalive测试时出错：[{0}]。
parallelNioSender.operation.timedout=操作已超时（[{0}]ms）。
parallelNioSender.send.fail=[{0}]的成员发送失败；设置为可疑
parallelNioSender.send.fail.retrying=成员发送失败：[{0}]; 设置为怀疑并重试。
parallelNioSender.send.failed=并行的NIO.发送失败。
parallelNioSender.sendFailed.attempt=发送失败，尝试: [{0}] 最大: [{1}]
parallelNioSender.sender.disconnected.notRetry=未重试发送：[{0}]；发件人已断开连接。
parallelNioSender.sender.disconnected.sendFailed=发送失败且sender已断开连接，不再重试。
parallelNioSender.unable.setup.NioSender=无法设置NioSender。

pooledParallelSender.sender.disconnected=sender 未连接。
pooledParallelSender.unable.open=无法打开nio选择器。
pooledParallelSender.unable.retrieveSender=无法从sender池中获取一个sender
pooledParallelSender.unable.retrieveSender.timeout=无法检索数据发件人，超时（[{0}]ms）错误。
