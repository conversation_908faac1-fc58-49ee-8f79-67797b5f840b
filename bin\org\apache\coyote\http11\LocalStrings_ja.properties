# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractHttp11Protocol.alpnConfigured=[{0}]コネクタは、ALPN経由で[{1}]へのネゴシエーションをサポートするように構成されています
abstractHttp11Protocol.alpnWithNoAlpn=[{1}]のアップグレードハンドラ[{0}]は、ALPNによるアップグレードのみをサポートしていますが、ALPNをサポートしていない[{2}]コネクタ用に構成されています。
abstractHttp11Protocol.httpUpgradeConfigured=コネクタ[{0}]は、[{1}]へのHTTPアップグレードをサポートするように構成されています。

http11processor.fallToDebug=\n\
\ 注: 以降のHTTPリクエスト構文解析エラーの発生はDEBUGレベルでログに出力されます。
http11processor.header.parse=HTTP リクエストヘッダーを解析中のエラー
http11processor.request.finish=リクエスト終了処理エラー
http11processor.request.inconsistentHosts=リクエスト行に指定されたホストが Host ヘッダーの値と矛盾しています。
http11processor.request.invalidScheme=HTTP リクエストに不正なスキーマを指定した完全 URI が含まれています。
http11processor.request.invalidTransferEncoding=HTTP リクエストに無効な Transfer-Encoding ヘッダが含まれています
http11processor.request.invalidUri=HTTPリクエストに無効なURIが含まれています
http11processor.request.invalidUserInfo=HTTP リクエストに不正な userinfo を含む絶対 URI が指定されました。
http11processor.request.multipleContentLength=リクエストに複数の content-length ヘッダが含まれています
http11processor.request.multipleHosts=リクエストには複数の host ヘッダーが含まれていました。
http11processor.request.noHostHeader=HTTP / 1.1リクエストでhostヘッダーが提供されませんでした。
http11processor.request.nonNumericContentLength=リクエストの content-length ヘッダに数値でない値が含まれています
http11processor.request.prepare=リクエスト準備中のエラー
http11processor.request.process=リクエスト処理中のエラー
http11processor.response.finish=レスポンス終了処理のエラー
http11processor.sendfile.error=sendfileを使ってデータを送信中にエラーが発生しました。これは開始点または終了点の無効なリクエスト属性によって引き起こされる可能性があります。
http11processor.socket.info=ソケット情報を取得する際の例外

iib.available.readFail=利用できるデータがあるか確かめている途中でノンブロッキング読み込みが失敗しました。
iib.eof.error=ソケットから予期しないEOFを読み込みました
iib.failedread.apr=APR/nativeエラーコード[{0}]で読み取りが失敗しました。
iib.filter.npe=Nullフィルタを追加することはできません。
iib.invalidHttpProtocol=HTTPプロトコルで無効な文字が見つかりました。
iib.invalidPhase=リクエスト行の解析フェーズ [{0}] は無効です
iib.invalidRequestTarget=リクエストの宛先に不正な文字が含まれています。利用可能な文字は RFC 7230 および RFC 3986 に定義されています。
iib.invalidheader=HTTP ヘッダー行 [{0}]は RFC 7230 に適合しないため無視します。
iib.invalidmethod=HTTP メソッド名に不正な文字が含まれています。HTTP メソッド名は決められたトークンでなければなりません。
iib.parseheaders.ise.error=予期しない状態：ヘッダがすでに解析されています。 バッファが未回収ですか？
iib.readtimeout=ソケットからデータを読み取ろうとしている際のタイムアウト
iib.requestheadertoolarge.error=リクエストヘッダが長すぎます

iob.failedwrite=書き込みが失敗しました。
iob.failedwrite.ack=HTTP 100 continue レスポンスの送信に失敗しました
iob.responseheadertoolarge.error=レスポンスヘッダにバッファー領域より大きなデータの書き込みが発生しました。Connector の maxHttpHeaderSize を増やすか、レスポンスヘッダよりもデータを小さくして下さい。
