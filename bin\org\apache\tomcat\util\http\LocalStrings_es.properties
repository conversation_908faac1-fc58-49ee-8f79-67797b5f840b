# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

cookies.invalidCookieToken=Cookies: cookie no válida. El valor no es un token o un valor acotado

parameters.copyFail=Fallo al crear copia de los valores orignales del parámetro para propósitos de debug
parameters.decodeFail.debug=Fallo al decodificar el caracter. Parámetro [{0}] con valor [{1}] ha sido ignorado.\n
parameters.fallToDebug=\n\
\ Nota: Futuras ocurrencias de error del Parámetro serán loggueadas a nivel DEBUG.
parameters.maxCountFail=Se detectaron más del máximo número de los parámetros solicitados (GET plus POST) para una solicitud simple ([{0}]). Cualquier parámetro por encima de este límite ha sido ignorado. Para cambiar este límite, fije el atributo maxParameterCount attribute en el Conector.\n
parameters.maxCountFail.fallToDebug=\n\
\ Nota: futuras ocurrencias de este tipo de error serán logueadas a  nivel DEBUG
parameters.noequal=El parámetro que inicia en la posición [{0}] y termina en la posición [{1}] con valor de [{2}] no tiene un caracter  ''='' a continuación

rfc6265CookieProcessor.invalidPath=Se ha especificado un camino no válido [{0}] para esta cookie
