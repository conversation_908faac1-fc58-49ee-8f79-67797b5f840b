# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

hostManagerServlet.add=add: Adding host [{0}]
hostManagerServlet.addFailed=FAIL - Failed to add host [{0}]
hostManagerServlet.addSuccess=OK - Host [{0}] added
hostManagerServlet.alreadyHost=FAIL - Host already exists with host name [{0}]
hostManagerServlet.alreadyStarted=FAIL - Host [{0}] is already started
hostManagerServlet.alreadyStopped=FAIL - Host [{0}] is already stopped
hostManagerServlet.appBaseCreateFail=FAIL - Failed to create appBase directory [{0}] for host [{1}]
hostManagerServlet.cannotRemoveOwnHost=FAIL - Cannot remove your own host [{0}]
hostManagerServlet.cannotStartOwnHost=FAIL - Cannot start your own host [{0}]
hostManagerServlet.cannotStopOwnHost=FAIL - Cannot stop your own host [{0}]
hostManagerServlet.configBaseCreateFail=FAIL - Failed to identify configBase directory for host [{0}]
hostManagerServlet.exception=FAIL - Encountered exception [{0}]
hostManagerServlet.invalidHostName=FAIL - Invalid host name [{0}] was specified
hostManagerServlet.list=list: Listing hosts for engine [{0}]
hostManagerServlet.listed=OK - Listed hosts
hostManagerServlet.managerXml=FAIL - Couldn't install manager.xml
hostManagerServlet.noCommand=FAIL - No command was specified
hostManagerServlet.noHost=FAIL - Host name [{0}] does not exist
hostManagerServlet.noWrapper=Container has not called setWrapper() for this servlet
hostManagerServlet.persist=persist: Persisting current configuration
hostManagerServlet.persistFailed=FAIL - Failed to persist configuration
hostManagerServlet.persisted=OK - Configuration persisted
hostManagerServlet.postCommand=FAIL - Tried to use command [{0}] via a GET request but POST is required
hostManagerServlet.remove=remove: Removing host [{0}]
hostManagerServlet.removeFailed=FAIL - Failed to remove host [{0}]
hostManagerServlet.removeSuccess=OK - Removed host [{0}]
hostManagerServlet.start=start: Starting host with name [{0}]
hostManagerServlet.startFailed=FAIL - Failed to start host [{0}]
hostManagerServlet.started=OK - Host [{0}] started
hostManagerServlet.stop=stop: Stopping host with name [{0}]
hostManagerServlet.stopFailed=FAIL - Failed to stop host [{0}]
hostManagerServlet.stopped=OK - Host [{0}] stopped
hostManagerServlet.unknownCommand=FAIL - Unknown command [{0}]

htmlHostManagerServlet.addAliases=Aliases:
htmlHostManagerServlet.addAppBase=App base:
htmlHostManagerServlet.addAutoDeploy=AutoDeploy
htmlHostManagerServlet.addButton=Add
htmlHostManagerServlet.addCopyXML=CopyXML
htmlHostManagerServlet.addDeployOnStartup=DeployOnStartup
htmlHostManagerServlet.addDeployXML=DeployXML
htmlHostManagerServlet.addHost=Host
htmlHostManagerServlet.addManager=Manager App
htmlHostManagerServlet.addName=Name:
htmlHostManagerServlet.addTitle=Add Virtual Host
htmlHostManagerServlet.addUnpackWARs=UnpackWARs
htmlHostManagerServlet.helpHtmlManager=HTML Host Manager Help
htmlHostManagerServlet.helpHtmlManagerFile=../docs/html-host-manager-howto.html
htmlHostManagerServlet.helpManager=Host Manager Help
htmlHostManagerServlet.helpManagerFile=../docs/host-manager-howto.html
htmlHostManagerServlet.hostAliases=Host aliases
htmlHostManagerServlet.hostName=Host name
htmlHostManagerServlet.hostTasks=Commands
htmlHostManagerServlet.hostThis=Host Manager installed - commands disabled
htmlHostManagerServlet.hostsRemove=Remove
htmlHostManagerServlet.hostsStart=Start
htmlHostManagerServlet.hostsStop=Stop
htmlHostManagerServlet.list=List Virtual Hosts
htmlHostManagerServlet.manager=Host Manager
htmlHostManagerServlet.messageLabel=Message:
htmlHostManagerServlet.persistAll=Save current configuration (including virtual hosts) to server.xml and per web application context.xml files
htmlHostManagerServlet.persistAllButton=All
htmlHostManagerServlet.persistTitle=Persist configuration
htmlHostManagerServlet.serverJVMVendor=JVM Vendor
htmlHostManagerServlet.serverJVMVersion=JVM Version
htmlHostManagerServlet.serverOSArch=OS Architecture
htmlHostManagerServlet.serverOSName=OS Name
htmlHostManagerServlet.serverOSVersion=OS Version
htmlHostManagerServlet.serverTitle=Server Information
htmlHostManagerServlet.serverVersion=Tomcat Version
htmlHostManagerServlet.title=Tomcat Virtual Host Manager

statusServlet.complete=Complete Server Status
statusServlet.title=Server Status
