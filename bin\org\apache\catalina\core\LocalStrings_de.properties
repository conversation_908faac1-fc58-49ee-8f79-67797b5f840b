# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

applicationContext.addJspFile.iae=Die JSP Datei [{0}] ist nicht gültig.
applicationContext.addListener.iae.cnfe=Eine Instanz vom Typ [{0}] konnte nicht erzeugt werden
applicationContext.addListener.iae.wrongType=Der spezifierte Typ [{0}] ist keiner der erwarteten Typen für einen Listener
applicationContext.addRole.ise=Es können keine Rollen zu dem Context [{0}] hinzugefügt werden, da er nicht initialisiert wurde
applicationContext.invalidServletName=Kann Servlet-Definition nicht hinzunehmen, da der Servlet Name [{0}] ungültig ist.
applicationContext.setAttribute.namenull=Der Name darf nicht 'null' sein.

applicationFilterRegistration.nullInitParams=Kann Initialisierungs Parameter für Filter nicht setzen, da Name oder Wert null sind. Name [{0}], Wert [{0}]

aprListener.initializingFIPS=FIPS-Modus wird initialisiert...

containerBase.backgroundProcess.cluster=Ausnahme beim Abarbeiten des Cluster [{0}] Hintergrundprozesses
containerBase.backgroundProcess.unexpectedThreadDeath=Unerwarteter Tod des Hintergrund-Threads [{0}]

defaultInstanceManager.invalidInjection=Ungültige ressource injection Annotation

filterChain.filter=Während der Filter Bearbeitung trat eine Exception auf

jreLeakListener.ldapPoolManagerFail=Konnte die Erzeugung der Klasse com.sun.jndi.ldap.LdapPoolManager während des Starts von Tomcat nicht auslösen. Dies sollte zur Vermeidung von Memory-Leaks dienen. Der Fehlschlag ist ein erwartetes Verhalten bei nicht-Sun JVMs.

naming.addEnvEntry=Füge Umgebungseintrag [{0}] hinzu
naming.namingContextCreationFailed=Erzeugung des Naming-Contexts ist fehlgeschlagen: [{0}]
naming.wsdlFailed=Die WSDL Datei [{0}] wurde nicht gefunden.

standardContext.filterStart=Ausnahme beim Starten des Filters [{0}]
standardContext.invalidWrapperClass=[{0}] ist keine Unterklasse von StandardWrapper
standardContext.isUnavailable=Die Anwendung ist derzeit nicht verfügbar
standardContext.listenerStart=Fehler beim Senden der ''Context Initialized'' Benachrichtigung an den Listener aus der Klasse [{0}]
standardContext.loginConfig.errorPage=Formularfehlerseite [{0}] muss mit einem ''/'' beginnen
standardContext.loginConfig.errorWarning=WARNING: Form Fehler Seite [{0}] muss bei Servlet 2.4 mit einem ''/'' starten
standardContext.manager=Habe einen Manager der Klasse [{0}] konfiguriert
standardContext.notStarted=Context mit Name [{0}] wurde noch nicht gestartet
standardContext.parameter.duplicate=Doppelter Parameter [{0}] zur Kontext Initialisierung
standardContext.securityConstraint.mixHttpMethod=<http-method> und <http-method-ommission> dürfen nicht in derselben Web-Ressource-Kollektion verwendet werden
standardContext.securityConstraint.pattern=Ungültiges <url-pattern> [{0}] im Security Constraint
standardContext.startingContext=Ausnahme beim Starten des Kontextes [{0}]

standardEngine.notParent=Engine kann keinen Eltern-Container haben

standardHost.nullName=Hostname wird benötigt

standardWrapper.isUnavailable=Das Servlet [{0}] ist zur Zeit nicht verfügbar
standardWrapper.notFound=Servlet [{0}] ist nicht verfügbar
standardWrapper.unloading=Das Servlet [{0}] kann nicht allokiert werden, weil es entladen wurde
