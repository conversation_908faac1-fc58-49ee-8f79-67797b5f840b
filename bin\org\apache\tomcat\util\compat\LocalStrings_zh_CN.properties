# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jre9Compat.invalidModuleUri=提供的模块URI [{0}]无法转换为JarScanner要处理的URL
jre9Compat.javaPre9=类未找到，所以假设代码运行在pre-Java 8虚拟机上
jre9Compat.unexpected=创建对Java 9类的依赖和方法失败

jreCompat.noApplicationProtocol=Java 运行时不支持 SSLEngine.getApplicationProtocol()。要使用该功能你必须使用 Java 9。
jreCompat.noApplicationProtocols=Java Runtime不支持SSLParamerters.setApplicationProtocols(),必须使用Java 9才能使用这个特性。
