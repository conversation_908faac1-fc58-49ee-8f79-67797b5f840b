# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

ReplicationValve.crossContext.add=añadir contenedor de réplica de sesión de Contexto Cruzado a replicationValve threadlocal
ReplicationValve.crossContext.registerSession=retistrar id de sesión de Contexto Cruzado=[{0}] desde contexto [{1}]
ReplicationValve.crossContext.remove=quitar contenedor de réplica de sesión de Contexto Cruzado a replicationValve threadlocal
ReplicationValve.crossContext.sendDelta=enviar delta de sesión de Contexto Cruzado desde contexto [{0}].
ReplicationValve.filter.failure=No puedo compilar filtror=[{0}]
ReplicationValve.filter.loading=Cargando filtros de requerimiento=[{0}]
ReplicationValve.invoke.uri=Invocando requerimiento de réplica en [{0}]
ReplicationValve.nocluster=No cluster configured for this request.
ReplicationValve.resetDeltaRequest=Cluster is standalone: reset Session Request Delta at context [{0}]
ReplicationValve.send.failure=Unable to perform replication request.
ReplicationValve.send.invalid.failure=Unable to send session [id={0}] invalid message over cluster.
ReplicationValve.session.found=Context [{0}]: Found session [{1}] but it isn''t a ClusterSession.
ReplicationValve.session.indicator=Context [{0}]: Primarity of session [{0}] in request attribute [{1}] is [{2}].
ReplicationValve.session.invalid=Context [{0}]: Requested session [{1}] is invalid, removed or not replicated at this node.
ReplicationValve.stats=Average request time= [{0}] ms for Cluster overhead time=[{1}] ms for [{2}] requests [{3}] filter requests [{4}] send requests [{5}] cross context requests (Request=[{6}] ms Cluster=[{7}] ms).

simpleTcpCluster.clustermanager.notImplement=Manejador  [{0}] no implementa ClusterManager, la adición al cluster ha sido abortada.\n
simpleTcpCluster.member.addFailed=Incapaz de conectar con el sistema de replicación
simpleTcpCluster.member.removeFailed=Imposible remover el nodo del sistema de replicación
simpleTcpCluster.stopUnable=Inmposible deterner el cluster
