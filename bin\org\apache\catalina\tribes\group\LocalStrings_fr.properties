# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

channelCoordinator.alreadyStarted=Canal déjà démarré pour le niveau : [{0}]
channelCoordinator.invalid.startLevel=Niveau de départ invalide, les niveaux valides sont : SND_RX_SEQ,SND_TX_SEQ,MBR_TX_SEQ,MBR_RX_SEQ

groupChannel.listener.alreadyExist=L''écouteur existe déjà : [{0}][{1}]
groupChannel.noDestination=Aucune destination donnée
groupChannel.nullMessage=Impossible d'envoyer un message null
groupChannel.optionFlag.conflict=Conflit sur le drapeau optionnel d''un intercepteur : [{0}]
groupChannel.receiving.error=Erreur lors de la réception du message :
groupChannel.sendFail.noRpcChannelReply=Incapable de trouver le canal RPM, échec d'envoi de NoRpcChannelReply
groupChannel.unable.deserialize=Impossible de désérialiser le message [{0}]
groupChannel.unable.sendHeartbeat=Impossible d'envoyer l’événement périodique dans la pile d'intercepteurs de Tribes

rpcChannel.replyFailed=Impossible de renvoyer la réponse dans le RpcChannel
