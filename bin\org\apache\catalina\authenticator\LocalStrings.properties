# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

authenticator.certificates=No client certificate chain in this request
authenticator.changeSessionId=Session ID changed on authentication from [{0}] to [{1}]
authenticator.check.authorize=User name [{0}] obtained from the Connector and trusted to be valid. Obtaining roles for this user from the Tomcat Realm.
authenticator.check.authorizeFail=Realm did not recognise user [{0}]. Creating a Principal with that name and no roles.
authenticator.check.found=Already authenticated [{0}]
authenticator.check.sso=Not authenticated but SSO session ID [{0}] found. Attempting re-authentication.
authenticator.formlogin=Invalid direct reference to form login page
authenticator.jaspicCleanSubjectFail=Failed to clean JASPIC subject
authenticator.jaspicSecureResponseFail=Failed to secure response during JASPIC processing
authenticator.jaspicServerAuthContextFail=Failed to obtain a JASPIC ServerAuthContext instance
authenticator.loginFail=Login failed
authenticator.manager=Exception initializing trust managers
authenticator.noAuthHeader=No authorization header sent by client
authenticator.notContext=Configuration error:  Must be attached to a Context
authenticator.requestBodyTooBig=The request body was too large to be cached during the authentication process
authenticator.sessionExpired=The time allowed for the login process has been exceeded. If you wish to continue you must either click back twice and re-click the link you requested or close and re-open your browser
authenticator.tomcatPrincipalLogoutFail=Logout with TomcatPrincipal instance has failed
authenticator.unauthorized=Cannot authenticate with the provided credentials

basicAuthenticator.invalidCharset=The only permitted values are null, the empty string or UTF-8

digestAuthenticator.cacheRemove=A valid entry has been removed from client nonce cache to make room for new entries. A replay attack is now possible. To prevent the possibility of replay attacks, reduce nonceValidity or increase nonceCacheSize. Further warnings of this type will be suppressed for 5 minutes.

formAuthenticator.forwardErrorFail=Unexpected error forwarding to error page
formAuthenticator.forwardLogin=Forwarding request for [{0}] made with method [{1}] to login page [{2}] of context [{3}] using request method GET
formAuthenticator.forwardLoginFail=Unexpected error forwarding to login page
formAuthenticator.noErrorPage=No error page was defined for FORM authentication in context [{0}]
formAuthenticator.noLoginPage=No login page was defined for FORM authentication in context [{0}]

singleSignOn.debug.associate=SSO associating application session [{1}] with SSO session [{0}]
singleSignOn.debug.associateFail=SSO failed to associate application session [{0}] since SSO session [{1}] does not exist
singleSignOn.debug.cookieCheck=SSO checking for SSO cookie
singleSignOn.debug.cookieNotFound=SSO did not find an SSO cookie
singleSignOn.debug.deregister=SSO expiring application session [{0}] associated with SSO session [{1}]
singleSignOn.debug.deregisterFail=SSO failed to deregister the SSO session [{0}] because it was not in the cache
singleSignOn.debug.deregisterNone=SSO deregistered the SSO session [{0}] but found no associated application sessions
singleSignOn.debug.hasPrincipal=SSO found previously authenticated Principal [{0}]
singleSignOn.debug.invoke=SSO processing request for [{0}]
singleSignOn.debug.principalCheck=SSO looking for a cached Principal for SSO session [{0}]
singleSignOn.debug.principalFound=SSO found cached Principal [{0}] with authentication type [{1}]
singleSignOn.debug.principalNotFound=SSO did not find a cached Principal. Erasing SSO cookie for session [{0}]
singleSignOn.debug.register=SSO registering SSO session [{0}] for user [{1}] with authentication type [{2}]
singleSignOn.debug.removeSession=SSO removing application session [{0}] from SSO session [{1}]
singleSignOn.debug.sessionLogout=SSO processing a log out for SSO session [{0}] and application session [{1}]
singleSignOn.debug.sessionTimeout=SSO processing a time out for SSO session [{0}] and application session [{1}]
singleSignOn.debug.update=SSO updating SSO session [{0}] to authentication type [{1}]
singleSignOn.sessionExpire.contextNotFound=SSO unable to expire session [{0}] because the Context could not be found
singleSignOn.sessionExpire.engineNull=SSO unable to expire session [{0}] because the Engine was null
singleSignOn.sessionExpire.hostNotFound=SSO unable to expire session [{0}] because the Host could not be found
singleSignOn.sessionExpire.managerError=SSO unable to expire session [{0}] because the Manager threw an Exception when searching for the session
singleSignOn.sessionExpire.managerNotFound=SSO unable to expire session [{0}] because the Manager could not be found
singleSignOn.sessionExpire.sessionNotFound=SSO unable to expire session [{0}] because the Session could not be found

spnegoAuthenticator.authHeaderNoToken=The Negotiate authorization header sent by the client did not include a token
spnegoAuthenticator.authHeaderNotNego=The authorization header sent by the client did not start with Negotiate
spnegoAuthenticator.serviceLoginFail=Unable to login as the service principal
spnegoAuthenticator.ticketValidateFail=Failed to validate client supplied ticket
