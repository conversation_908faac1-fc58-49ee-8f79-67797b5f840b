# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

nioReceiver.alreadyStarted=ServerSocketChannel이 이미 시작되었습니다.
nioReceiver.cleanup.fail=selector close 시에 cleanup을 할 수 없습니다.
nioReceiver.clientDisconnect=복제 클라이언트가 연결이 끊겼습니다. 키를 poll할 때, 오류가 발생했습니다. 해당 클라이언트는 무시됩니다.
nioReceiver.requestError=NioReceiver에서 요청을 처리할 수 없습니다.
nioReceiver.run.fail=복제 리스너를 실행할 수 없습니다.
nioReceiver.start.fail=클러스터 receiver를 시작 할 수 없습니다.
nioReceiver.stop.fail=클러스터 receiver selector를 닫을 수 없습니다.
nioReceiver.stop.threadRunning=NioReceiver 쓰레드가 적절한 시간 내에 중지되지 않았습니다. Selector가 닫힐 때에 오류들이 발견될 수 있습니다.
nioReceiver.threadpool.fail=쓰레드풀을 초기화할 수 없습니다. 리스너가 시작되지 못했습니다.
nioReceiver.threadsExhausted=채널 키가 등록되지만, 마지막 [{0}] 밀리초 동안에 어떠한 interest 오퍼레이션도 없었습니다 (취소 여부: [{1}]). 키:[{2}], 최종 접근 시간:[{3}], 있을 법한 사유: 모든 쓰레드들이 사용 중이고, 쓰레드 덤프 수행.

nioReplicationTask.error.register.key=읽기를 위한 키 등록 중 오류 발생: [{0}]
nioReplicationTask.exception.drainChannel=TcpReplicationThread.drainChannel에서 예외 발생
nioReplicationTask.process.clusterMsg.failed=클러스터 메시지 처리가 실패했습니다.
nioReplicationTask.unable.ack=채널을 통해 ACK을 되돌려 전송할 수 없습니다. 채널이 단절되었나요?: [{0}]
nioReplicationTask.unable.drainChannel.ioe=복제 worker에서 IOException이 발생했으며, 채널을 깨끗이 비울 수 없습니다. 있음직한 사유: Keep alive 소켓이 닫히는 경우. [{0}]

nioSender.already.connected=NioSender가 이미 연결된 상태에 있습니다.
nioSender.datagram.already.established=데이터그램 채널이 이미 확립되어 있습니다. 연결이 진행 중인 상태일 수 있습니다.
nioSender.key.inValid=키가 유효하지 않습니다. 이는 필시 이미 취소되었던 키일 것입니다.
nioSender.not.connected=NioSender가 연결되어 있지 않습니다. 이는 일어나서는 안될 일입니다.
nioSender.receive.failedAck=실패한 ACK: org.apache.catalina.tribes.transport.Constants.FAIL_ACK_DATA를 받았습니다.
nioSender.sender.disconnected=Sender가 연결이 끊어진 상태입니다. SelectionKey를 처리할 수 없습니다.
nioSender.socketChannel.already.established=소켓 채널이 이미 확립되어 있습니다. 혹시 연결이 진행 중일 수 있습니다.
nioSender.unable.disconnect=NioSender의 연결을 끊을 수 없습니다. 메시지=[{0}]
nioSender.unable.receive.ack=Ack 메시지를 받을 수 없습니다. 소켓 채널에서 EOF에 도달했습니다.
nioSender.unknown.state=데이터가 알 수 없는 상태에 있습니다. readyOps=[{0}]

parallelNioSender.error.keepalive=Sender [{0}]을(를) 위해 keepalive 검사 중 오류 발생
parallelNioSender.operation.timedout=오퍼레이션이 제한 시간 초과되었습니다 (제한 시간: [{0}] 밀리초).
parallelNioSender.send.fail=멤버에 대한 전송이 실패하고 있습니다: [{0}]; 의심 멤버로 설정합니다.
parallelNioSender.send.fail.retrying=멤버로 전송이 실패했습니다: [{0}]. 의심 멤버로 설정하고 다시 시도합니다.
parallelNioSender.send.failed=병렬 NIO 전송 실패.
parallelNioSender.sendFailed.attempt=전송 실패, 총시도회수:[{0}] 최대시도회수:[{1}]
parallelNioSender.sender.disconnected.notRetry=[{0}]을(를) 위해 다시 전송 시도를 하지 않습니다. Sender가 연결이 끊겼습니다.
parallelNioSender.sender.disconnected.sendFailed=전송이 실패했으며 sender가 연결이 끊겼습니다. 재시도는 하지 않습니다.
parallelNioSender.unable.setup.NioSender=NioSender를 셋업할 수 없습니다.

pooledParallelSender.sender.disconnected=Sender가 연결되어 있지 않습니다.
pooledParallelSender.unable.open=NIO selector를 열 수 없습니다.
pooledParallelSender.unable.retrieveSender=Sender를 해당 sender 풀로부터 얻을 수 없습니다.
pooledParallelSender.unable.retrieveSender.timeout=데이터 sender를 조회할 수 없습니다. 제한 시간 초과 ([{0}] 밀리초) 오류.
