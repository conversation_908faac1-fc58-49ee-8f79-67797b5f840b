# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

channel.nio.ssl.sniDefault=Puffer ist nicht groß genug um den angefragten SNI Host-Namen zu ermitteln. Benutze den Standard Wert
channel.nio.ssl.sniHostName=Aus Verbindung [{0}] konnte der SNI Hostname [{1}] extrahiert werden
channel.nio.ssl.unwrapFail=Daten können nicht entpackt werden, ungültiger Status [{0}]

endpoint.apr.applyConf=Wende OpenSSLConfCmd auf SSL Kontext an
endpoint.apr.checkConf=Überprüfe OpenSSLConf
endpoint.apr.errApplyConf=Die OpenSSLConf konnte nicht auf den Context angewandt werden
endpoint.apr.errMakeConf=Konnte OpenSSLConf Kontext nicht erzeugen
endpoint.apr.pollAddInvalid=Ungültiger Versuch einen Socket [{0}] zum Poller hinzuzufügen
endpoint.apr.tooManyCertFiles=Es wurden mehr Zertifikatsdateien konfiguriert als ein APR Endpunkt handhaben kann
endpoint.debug.channelCloseFail=Kanal konnte nicht geschlossen werden
endpoint.debug.socketCloseFail=Socket konnte nicht geschlossen werden
endpoint.init.bind=Binden des Sockets fehlgeschlagen: [{0}] [{1}]
endpoint.init.notavail=APR nicht verfügbar
endpoint.noSslHostName=Es wurde kein Hostname für die SSL Host Konfiguration angegeben
endpoint.poll.limitedpollsize=Konnte Poller mit der angegebenen Große von [{0}] nicht erzeugen
endpoint.removeDefaultSslHostConfig=Die Standard SSLHostConfig (namens [{0}]) darf nicht entfernt werden
endpoint.sendfile.addfail=Sendfile-Fehler: [{0}][{1}]
endpoint.sendfile.error=Unerwarteter sendfile-Fehler
endpoint.serverSocket.closeFailed=Konnte Server Socket für [{0}] nicht schliessen
endpoint.setAttribute=Setze [{0}] auf [{1}]
endpoint.warn.incorrectConnectionCount=Falsche Verbindungsanzahl, mehrere socket.close-Aufrufe auf dem gleichen Socket
endpoint.warn.noLocalName=Lokaler Hostname für Socket [{0}] konnte nicht ermittelt werden

socket.apr.closed=Der zu  dieser Verbindung gehörende Socket [{0}] wurde geschlossen.

sslHostConfig.certificate.notype=Es wurden mehrere Zertifikate angegeben und mindestens einem fehlt ein erforderlicher Attributs Typ
sslHostConfig.fileNotFound=Die konfigurierte Datei [{0}] existiert nicht.\n
sslHostConfig.opensslconf.null=Versuch eine null OpenSSLConf zu setzen ignoriert

sslUtilBase.noVerificationDepth=Der truststoreProvider [{0}] unterstützt nicht die Option certificateVerificationDepth
sslUtilBase.noneSupported=Keine der spezifizierten [{0}] wird von der SSL Engine unterstützt: [{1}]
sslUtilBase.ssl3=SSLv3 wurde explizit eingeschalten. Dieses Protokoll ist als unsicher bekannt.
sslUtilBase.trustedCertNotValid=Das vertrauenswürdige Zertifikat mit alias [{0}] und DN [{1}] ist auf Grund von [{2}] nicht gültig. Zertifikate die von diesem signiert worden sind WERDEN akzeptiert.
