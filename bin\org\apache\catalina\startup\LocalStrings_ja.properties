# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

catalina.configFail=[{0}]からサーバー設定を読み込めません
catalina.noCluster=[{0}]のためにクラスタルールセットが見つかりません。 クラスタ構成が無効になっています。
catalina.noNaming=ネーミング環境が無効です。
catalina.serverStartFail=必要なサーバーコンポーネントを開始できなかったため、Tomcat を開始できませんでした。
catalina.shutdownHookFail=サーバーの停止中にシャットダウンフックでエラーが発生しました。
catalina.stopServer=シャットダウンポートが設定されていません。 OSシグナルでServerをシャットダウンします。 サーバはシャットダウンしません。

connector.noSetExecutor=Connector {0}]は外部エグゼキュータをサポートしていません。 メソッドsetExecutor(java.util.concurrent.Executor)が見つかりません。
connector.noSetSSLImplementationName=コネクター [{0}] は SSL 実装の変更に対応していません。setSslImplementationName(String) メソッドがありません。

contextConfig.altDDNotFound=代替配備記述子ファイル [{0}] が見つかりません。
contextConfig.annotationsStackOverflow=StackOverflowErrorのため、Webアプリケーション[{0}]のアノテーションのスキャンを完了できません。考えられる根本的な原因には、-Xssの設定が低すぎる事や不正な循環継承が考えられます。 処理中のクラス階層は[{1}]でした。
contextConfig.applicationMissing=アプリケーションのweb.xmlが見つかりません、既定値のみを使用します
contextConfig.applicationParse=アプリケーションのweb.xmlファイル [{0}] の解析エラーです
contextConfig.applicationPosition=[{0}]行の[{1}]列目で発生しました
contextConfig.applicationStart=アプリケーションのweb.xml [{0}] を解析します。
contextConfig.applicationUrl=アプリケーション web.xml の URL を取得できませんでした。
contextConfig.authenticatorConfigured=メソッド [{0}] のオーセンティケータを設定します
contextConfig.authenticatorInstantiate=クラス [{0}] のオーセンティケータをインスタンス化できません
contextConfig.authenticatorMissing=メソッド [{0}] のオーセンティケータを設定できません
contextConfig.authenticatorResources=Authenticators のマップリストをロードできません。
contextConfig.badUrl=コンテキスト記述子 [{0}] を処理できません。
contextConfig.cce=ライフサイクルイベントデータオブジェクト [{0}] はコンテキストではありません
contextConfig.contextClose=context.xmlを閉じる際のエラー
contextConfig.contextMissing=context.xml が見つかりません: [{0}]
contextConfig.contextParse=[{0}]のcontext.xmlの解析エラー
contextConfig.defaultError=[{1}]で[{0}]という名前の既定web.xmlの処理でエラーが発生しました
contextConfig.defaultMissing=グローバル web.xml が見つかりません。
contextConfig.defaultPosition=[{0}]行の[{1}]列目で発生しました
contextConfig.destroy=ContextConfig：破棄中
contextConfig.fileUrl=URL [{0}] のファイルオブジェクトを作成できません。
contextConfig.fixDocBase=コンテキスト [{0}] の docBase を修復中に例外が発生しました。
contextConfig.init=ContextConfig: 初期化中\n
contextConfig.inputStreamFile=アノテーションのファイル[{0}]を処理できません
contextConfig.inputStreamJar=アノテーションのJar [{1}]からJarエントリ[{0}]を処理できません。
contextConfig.inputStreamWebResource=アノテーションのWebリソース[{0}]を処理できません
contextConfig.invalidSciHandlesTypes=1つ以上のServletContentInitializersの@HandlesTypesアノテーションに対してチェックするためにクラス[{0}]をロードできません。
contextConfig.jarFile=アノテーションのためにJar [{0}]を処理できません。
contextConfig.jspFile.error=JSPファイル [{0}] は''/''で始まらなければいけません
contextConfig.jspFile.warning=警告: Servlet 2.4ではJSPファイル [{0}] は''/''で始まらなければいけません
contextConfig.missingRealm=認証するためにレルムが設定されていません
contextConfig.processAnnotationsDir.debug=[{0}] 配下のディレクトリからクラスファイルをスキャンします。
contextConfig.processAnnotationsJar.debug=アノテーション付きのクラスファイルのjarファイルのスキャン[{0}]
contextConfig.processAnnotationsWebDir.debug=アノテーション[{0}]のクラスファイルのWebアプリケーションディレクトリのスキャン
contextConfig.resourceJarFail=コンテキスト [{1}] へ静的リソースとして含める URL [{0}] に配置された JAR ファイルを処理できませんでした。
contextConfig.role.auth=<security-role>に定義されていないセキュリティロール名 [{0}] が<auth-constraint>の中で使用されました
contextConfig.role.link=<security-role>に定義されていないセキュリティロール名 [{0}] が<role-link>の中で使用されました
contextConfig.role.runas=<security-role>に定義されていないセキュリティロール名 [{0}] が<run-as>の中で使用されました
contextConfig.sci.debug=[{0}]に対してServletContainerInitializerを処理できません。 @HandlesTypesアノテーションで定義されているクラスが見つからないことが原因です。
contextConfig.sci.info=[{0}]に対してServletContainerInitializerを処理できません。 これは@HandlesTypesアノテーションに定義されているクラスが見つからないことが原因です。 完全なスタックトレースのDEBUGレベルロギングを有効にします。
contextConfig.servletContainerInitializerFail=名前[{0}]のコンテキストのServletContainerInitializersの検出に失敗しました。
contextConfig.start=ContextConfig: 処理を開始します
contextConfig.stop=ContextConfig: 処理を停止します
contextConfig.unavailable=以前のエラーのためにこのアプリケーションは利用できないようにマークします
contextConfig.unknownUrlProtocol=アノテーション処理中にURLプロトコル[{0}]が認識されませんでした。 URL [{1}]は無視されました。
contextConfig.urlPatternValue=urlPatterns属性とvalue属性の両方が、クラス[{1}]の[{0}]アノテーションに対して設定されています。
contextConfig.xmlSettings=Context[{0}]は、validation:[{1}]およびnamespaceAware：[{2}]を使用してweb.xmlおよびweb-fragment.xmlファイルを解析します

engineConfig.cce=ライフサイクルイベントデータオブジェクト [{0}] はエンジンではありません
engineConfig.start=EngineConfig: 処理を開始します
engineConfig.stop=EngineConfig: 処理を停止します

expandWar.copy=[{0}] から [{1}] へのコピー中のエラー
expandWar.createFailed=ディレクトリ [{0}] を作成できません。
expandWar.createFileFailed=ファイル[{0}]を作成できません。
expandWar.deleteFailed=[{0}] を削除できません。残ったファイルにより問題が生じるかもしれません。
expandWar.deleteOld=関連付けられた WAR ファイルと最終更新日時の異なる展開先ディレクトリ [{0}] が見つかりました。削除します。
expandWar.illegalPath=アーカイブ[{0}]は形式が正しくないため無視されます：定義されたdocBase [{3}]の外にあるので、[{2}]に拡張されていない不正なパス[{1}]がエントリに含まれています。
expandWar.lastModifiedFailed=[{0}] に最終更新時刻を設定できません。
expandWar.missingJarEntry=JarEntry [{0}] の入力ストリームを取得できません。WAR ファイルが破損している可能性があります。

failedContext.start=グローバル、ホスト単位またはコンテキスト固有のcontext.xmlファイルの処理に失敗しました。したがって、コンテキスト[{0}]を開始できません。

hostConfig.appBase=ホスト [{0}] のアプリケーションベースディレクトリ [{1}] は存在しないかディレクトリではありません。デプロイメントエラーを防ぐため deployOnStartUp および autoDeploy は false に設定しました。他のエラーは引き続き発生するかもしれません。
hostConfig.canonicalizing=[{1}] の配備解除中に [{0}] の正規化パスを取得できませんでした。
hostConfig.cce=ライフサイクルイベントデータオブジェクト [{0}] はホストではありません
hostConfig.context.remove=コンテキスト [{0}] の削除中に異常が発生しました。
hostConfig.context.restart=コンテキスト [{0}] を再起動中のエラー
hostConfig.createDirs=配備用のディレクトリを作成できません：[{0}]
hostConfig.deploy.error=Webアプリケーションディレクトリ[{0}]の配備中の例外
hostConfig.deployDescriptor=配備記述子 [{0}] を配備します
hostConfig.deployDescriptor.blocked=コンテキストパス[{0}]を持つWebアプリケーションは、アプリケーションのセキュアな配備に必要な設定が含まれている可能性がありますが、このホストのdeployXML設定によって処理が妨げられる配備記述子[{1}]が含まれていたため配備されていません。このアプリケーションを配備するには、[{2}]に適切な記述子を作成する必要があります。
hostConfig.deployDescriptor.error=配備記述子 [{0}] を配備中のエラーです
hostConfig.deployDescriptor.finished=配備記述子[{0}]の展開が[{1}] msで終了しました。
hostConfig.deployDescriptor.localDocBaseSpecified=docBase [{0}] はホストの appBase に含まれるため無視します。
hostConfig.deployDescriptor.threaded.error=デプロイメント記述子のマルチスレッド配備の完了待機中のエラー
hostConfig.deployDir=Webアプリケーションディレクトリ [{0}] を配備します
hostConfig.deployDir.error=Webアプリケーションディレクトリ [{0}] を配備中のエラー
hostConfig.deployDir.finished=ディレクトリ [{0}] の Web アプリケーションの配備は [{1}] ms で完了しました。
hostConfig.deployDir.threaded.error=ディレクトリのマルチスレッド配備の\n\
完了待機中のエラー
hostConfig.deployWar=Webアプリケーションアーカイブ [{0}] を配備します
hostConfig.deployWar.error=Webアプリケーションアーカイブ [{0}] を配備中のエラー
hostConfig.deployWar.finished=Web アプリケーションアーカイブ [{0}] の配備は [{1}] ms で完了しました。
hostConfig.deployWar.hiddenDir=WAR [{1}]が優先され、unpackWARsがfalseであるため、ディレクトリ[{0}]は無視されます。
hostConfig.deployWar.threaded.error=WARファイルのマルチスレッド配備の完了待機中のエラー
hostConfig.deploying=発見されたWebアプリケーションの配備
hostConfig.docBaseUrlInvalid=docBase に指定された文字列は URL として解釈できません。
hostConfig.expand=Web アプリケーションアーカイブ [{0}] を展開します。
hostConfig.expand.error=Web アプリケーションアーカイブ [{0}] の展開中に異常が発生しました。
hostConfig.ignorePath=自動デプロイでは appBase 内のパス [{0}] を無視します。
hostConfig.illegalWarName=War名[{0}]は無効です。 アーカイブは無視されます。
hostConfig.jmx.register=コンテキスト[{0}]を登録できませんでした
hostConfig.jmx.unregister=コンテキスト[{0}]の登録を解除できませんでした
hostConfig.reload=リロード中のコンテキスト [{0}]
hostConfig.resourceNotAbsolute=[{1}] は完全パスではないためコンテキスト [{0}] からリソースを削除できません。
hostConfig.start=HostConfig: 処理を停止します
hostConfig.stop=HostConfig: 処理を停止します
hostConfig.undeploy=コンテキストパス [{0}] のWebアプリケーションの配備を解除します
hostConfig.undeployVersion=コンテキスト [{0}] について有効なセッションの存在しない古いバージョンの配備を解除します。

passwdUserDatabase.readFail=/etc/passwd から全てのユーザーセットを取得できませんでした。

tomcat.addWebapp.conflictChild=コンテキスト [{2}] が存在するためWAR ファイル [{0}] をコンテキストパス [{1}] へ配備できません。
tomcat.addWebapp.conflictFile=[{2}] へファイルまたはディレクトリが存在するため WAR ファイル [{0}] をコンテキストパス [{1}] へ配備できません。
tomcat.baseDirMakeFail=基本ディレクトリとして使用する [{0}] を作成できません。
tomcat.baseDirNotDir=基本ディレクトリに指定された [{0}] はディレクトリではありません。
tomcat.defaultMimeTypeMappingsFail=既定の MIME タイプを読み込めません
tomcat.homeDirMakeFail=ホームディレクトリとして使用する [{0}] を作成できません。

userConfig.database=ユーザデータベースのロード中の例外です
userConfig.deploy=ユーザ [{0}] のWebアプリケーションを配備します
userConfig.deploy.threaded.error=ユーザーディレクトリのマルチスレッド配備の完了待機中のエラー
userConfig.deploying=ユーザのWebアプリケーションを配備します
userConfig.error=ユーザ [{0}] のWebアプリケーションを配備中のエラー
userConfig.start=UserConfig: 処理を開始します
userConfig.stop=UserConfig: 処理を停止します

versionLoggerListener.arg=コマンドライン引数:       {0}
versionLoggerListener.catalina.base=CATALINA_BASE:            {0}
versionLoggerListener.catalina.home=CATALINA_HOME:            {0}
versionLoggerListener.env=環境変数:                 {0} = {1}
versionLoggerListener.java.home=Java Home:                {0}
versionLoggerListener.os.arch=アーキテクチャ:           {0}
versionLoggerListener.os.name=OS 名:                    {0}
versionLoggerListener.os.version=OS バージョン:            {0}
versionLoggerListener.prop=システムプロパティ:       {0} = {1}
versionLoggerListener.serverInfo.server.built=Server ビルド:            {0}
versionLoggerListener.serverInfo.server.number=サーバーのバージョン番号: {0}
versionLoggerListener.serverInfo.server.version=Serverのバージョン名:     {0}
versionLoggerListener.vm.vendor=JVM ベンダ:               {0}
versionLoggerListener.vm.version=JVM バージョン:           {0}

webAnnotationSet.invalidInjection=メソッドに不正なリソース注入アノテーションが指定されました。
