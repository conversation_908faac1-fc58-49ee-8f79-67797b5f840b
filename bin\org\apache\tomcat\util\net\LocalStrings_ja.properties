# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

channel.nio.interrupted=現在のスレッドが中断されました
channel.nio.ssl.appInputNotEmpty=アプリケーションの入力バッファにはデータが残っています。残ったデータは失われます。
channel.nio.ssl.appOutputNotEmpty=アプリケーション出力バッファにはまだデータが含まれています。 データは失われました。
channel.nio.ssl.closeSilentError=コネクションを完全に切断しようとしましたが例外が発生しました。
channel.nio.ssl.closing=チャネルはクロージング状態です
channel.nio.ssl.eofDuringHandshake=ハンドシェイク中のEOF。
channel.nio.ssl.expandNetInBuffer=ネットワーク入力バッファを[{0}]バイトに拡張しています
channel.nio.ssl.expandNetOutBuffer=ネットワーク出力バッファを[{0}]バイトに拡張します
channel.nio.ssl.foundHttp=暗号化された TLS 接続から平文の HTTP リクエストを受信しました。
channel.nio.ssl.handshakeError=ハンドシェイクエラー
channel.nio.ssl.incompleteHandshake=ハンドシェイクが不完全です。データを読み取る前にハンドシェイクを完了する必要があります。
channel.nio.ssl.invalidCloseState=無効なクローズ状態で、ネットワークデータを送信しません。
channel.nio.ssl.invalidStatus=予期しないステータス[{0}]。
channel.nio.ssl.netInputNotEmpty=ネットワーク入力バッファにはまだデータが含まれています。 ハンドシェイクは失敗します。
channel.nio.ssl.netOutputNotEmpty=ネットワーク出力バッファにはまだデータが含まれています。 ハンドシェイクは失敗します。
channel.nio.ssl.notHandshaking=ハンドシェイク中にNOT_HANDSHAKING
channel.nio.ssl.pendingWriteDuringClose=書き込みを保留しているためネットワークバッファーにデータが残っています。SSL 切断メッセージを送信できません。代わりに close(true) で強制的に切断してください。
channel.nio.ssl.remainingDataDuringClose=ネットワークバッファ内にデータが残っていて、SSLクローズメッセージを送信できません。代わりにclose(true)でクローズします。
channel.nio.ssl.sniDefault=要求された SNI ホスト名を取得するために十分なデータを蓄積できないため既定値を使用します。
channel.nio.ssl.sniHostName=コネクション [{0}] から取得した SNI ホスト名は [{1}] です。
channel.nio.ssl.timeoutDuringHandshake=ハンドシェイクがタイムアウトしました。
channel.nio.ssl.unexpectedStatusDuringUnwrap=UNWRAPハンドシェイク中に予期しないステータス[{0}]が発生しました。
channel.nio.ssl.unexpectedStatusDuringWrap=ハンドシェイクWRAP中に予期しないステータス[{0}]が発生しました。
channel.nio.ssl.unwrapFail=データをアンラップできません、無効なステータス[{0}]
channel.nio.ssl.unwrapFailResize=バッファが小さすぎるためデータをアンラップできません。無効なステータス[{0}]
channel.nio.ssl.wrapException=ラップ中にハンドシェイクに失敗しました
channel.nio.ssl.wrapFail=データをラップできません。無効なステータス[{0}]

endpoint.accept.fail=ソケット受け付け失敗
endpoint.alpn.fail=[{0}]を使用してALPNのエンドポイントを設定できませんでした。
endpoint.alpn.negotiated=ALPNを使用して交渉された[{0}]プロトコル
endpoint.apr.applyConf=OpenSSLConfCmdを SSL contextに適用します。
endpoint.apr.checkConf=OpenSSLConfの確認中
endpoint.apr.errApplyConf=OpenSSLConfをSSLコンテキストに適用できませんでした。
endpoint.apr.errCheckConf=OpenSSLConfチェック中のエラー
endpoint.apr.errMakeConf=OpenSSLConfコンテキストを作成できませんでした。
endpoint.apr.failSslContextMake=SSLContextを作成できません。 SSLEngineがAprLifecycleListenerで有効になっていること、AprLifecycleListenerが正しく初期化されていること、有効なSSLProtocolが指定されていることを確認して下さい
endpoint.apr.invalidSslProtocol=SSLProtocol属性に無効な値[{0}]が指定されました
endpoint.apr.maxConnections.running=APR エンドポイントは実行中に maxConnection の値を変更できません。現在の値 [{0}] をそのまま使用します。
endpoint.apr.maxConnections.unlimited=APRエンドポイントは、無制限の接続をサポートしていません。 [{0}]の既存の値は引き続き使用されます。
endpoint.apr.noSendfileWithSSL=SSLが有効な場合、APR /nativeコネクタでSendfileはサポートされていません。
endpoint.apr.pollAddInvalid=ソケット [{0}] をポーラーに追加できませんでした。
endpoint.apr.pollError=Pollerはエラー[{0}]で失敗しました：[{1}]
endpoint.apr.pollMergeEvents=ソケット[{0}]のpoller イベント[{1}]をマージして、マージイベント[{2}]を作成。
endpoint.apr.pollUnknownEvent=認識できないイベント[{0}]を持つソケットがPollerから返されました。
endpoint.apr.remoteport=APR ソケット [{0}] はリモートポート [{1}] へ接続しました。
endpoint.apr.tooManyCertFiles=Apr エンドポイントに処理できるより多い証明書ファイルが構成されています。
endpoint.debug.channelCloseFail=チャンネルを切断できませんでした。
endpoint.debug.destroySocket=ソケット [{0}] を破棄します。
endpoint.debug.pollerAdd=addListに追加、ソケット[{0}]、タイムアウト[{1}]、フラグ[{2}]
endpoint.debug.pollerAddDo=Pollerソケット[{0}]に追加
endpoint.debug.pollerProcess=イベント[{1}]のソケット[{0}]の処理中
endpoint.debug.pollerRemove=Pollerから[{0}]を取り除こうとしています。
endpoint.debug.pollerRemoved=poller から [{0}] を削除しました。
endpoint.debug.registerRead=[{0}] に対する読み込みの監視を登録しました
endpoint.debug.registerWrite=[{0}] に対する書き込みの監視を登録しました
endpoint.debug.socket=ソケット[{0}]
endpoint.debug.socketCloseFail=ソケットを切断できませんでした。
endpoint.debug.socketTimeout=タイムアウト [{0}]
endpoint.debug.unlock.fail=port [{0}]のaccept をロック解除しようした際に例外が発生しました。
endpoint.debug.unlock.localFail=[{0}]のローカルアドレスを特定できません
endpoint.debug.unlock.localNone=ローカルアドレスが利用できなかったため、[{0}]のアクセプタのロックを解除できませんでした。
endpoint.duplicateSslHostName=ホスト名[{0}]に複数のSSLHostConfig要素が提供されました。 ホスト名は一意でなければなりません。
endpoint.err.close=ソケットをクローズしようとした際に例外が発生しました
endpoint.err.handshake=ハンドシェイク失敗
endpoint.err.unexpected=ソケット処理中の予期せぬエラー
endpoint.executor.fail=エグゼキュータは処理するソケット[{0}]を拒否しました。
endpoint.getAttribute=[{0}]は[{1}]です
endpoint.init.bind=ソケットバインドに失敗しました：[{0}] [{1}]
endpoint.init.bind.inherited=コネクタが1つを使用するように構成されている間、継承されたチャネルはありません。
endpoint.init.listen=ソケットの待ち受けを開始できません: [{0}] [{1}]
endpoint.init.notavail=APRは利用できません
endpoint.invalidJmxNameSslHost=ホスト [{0}] に関連付けられた SSLHostConfig に有効な JMX オブジェクト名を生成できません。
endpoint.invalidJmxNameSslHostCert=ホスト名 [{0}]、証明書タイプ [{1}] の SSLHostConfigCertificate のための正常な JMX オブジェクト名を生成できませんでした。
endpoint.jmxRegistrationFailed=名前 [{0}] の JMX オブジェクトを登録できませんでした。
endpoint.jsse.noSslContext=ホスト名[{0}]のSSLContextが見つかりませんでした
endpoint.launch.fail=new Runnableの起動に失敗しました
endpoint.nio.registerFail=Pollerからソケットのセレクタに登録できませんでした。
endpoint.nio.selectorCloseFail=Pollerを閉じるときにセレクターを閉じることができませんでした。
endpoint.nio.stopLatchAwaitFail=Pollerは予想された時間内に止まりませんでした
endpoint.nio.stopLatchAwaitInterrupted=このスレッドはPollerが停止するのを待つ間に中断されました
endpoint.nio.timeoutCme=タイムアウトの処理中の例外。 コードは繰り返しチェックされており、同時に変更されていません。 このエラーを繰り返すことができる場合は、Tomcatのバグを開いて、再現手順を提示してください。
endpoint.nio2.exclusiveExecutor=NIO2コネクタはシャットダウン時に排他的エグゼキュータを正しく動作させる必要があります。
endpoint.noSslHostConfig=コネクタ[{1}]のdefaultSSLHostConfigName に一致するSSLHostConfig要素がhostName [{0}]で見つかりませんでした
endpoint.noSslHostName=SSL のホスト設定にホスト名がありません。
endpoint.poll.error=予期せぬ poller エラー
endpoint.poll.fail=重大なPoller障害（Pollerの再始動）：[{0}] [{1}]
endpoint.poll.initfail=Pollerの作成に失敗しました。
endpoint.poll.limitedpollsize=サイズ [{0}] の Poller インスタンスを作成できません。
endpoint.process.fail=ソケットプロセッサーの割り当て中にエラーが発生しました。
endpoint.processing.fail=ソケットプロセッサの実行中エラー
endpoint.removeDefaultSslHostConfig=既定のSSLHostConfig（[{0}]）は削除できません
endpoint.sendfile.addfail=Sendfile 失敗: [{0}] [{1}]
endpoint.sendfile.error=予期せぬ sendfile エラー
endpoint.serverSocket.closeFailed=[{0}] によりサーバーソケットの切断に失敗しました。
endpoint.setAttribute=[{0}]を[{1}]に設定
endpoint.timeout.err=ソケットタイムアウト処理中のエラー
endpoint.unknownSslHostName=SSL ホスト名 [{0}] はこのエンドポイントから認識されていません。
endpoint.warn.executorShutdown=スレッドプール [{0}] と関連付けられたエグゼキューターは完全に停止できませんでした。いくつかのアプリケーションスレッドはまだ動作し続けている可能性があります。
endpoint.warn.incorrectConnectionCount=不正なコネクション数。複数のsocket.closeが同じソケットで呼び出されました。
endpoint.warn.noLocalAddr=ソケット [{0}] のローカルアドレスを取得できません。
endpoint.warn.noLocalName=ソケット [{0}] のローカルホスト名を取得できません。
endpoint.warn.noLocalPort=ソケット [{0}] のローカルポートが取得できません。
endpoint.warn.noRemoteAddr=ソケット [{0}] のリモートアドレスを取得できません。
endpoint.warn.noRemoteHost=ソケット [{0}] のリモートホスト名を取得できません。
endpoint.warn.noRemotePort=ソケット[{0}] のリモートポート番号を取得できません。
endpoint.warn.unlockAcceptorFailed=Acceptor スレッド[{0}]のロックを解除できませんでした。 強制的にハードソケットをシャットダウンします。

sniExtractor.clientHelloInvalid=ClientHelloメッセージが正しくフォーマットされていません。
sniExtractor.clientHelloTooBig=ClientHelloは単一のTLSレコードには表示されないため、SNI情報は抽出できませんでした

socket.apr.clientAbort=クライアントはコネクションを切断しました。
socket.apr.closed=コネクションに関連付けられたソケット [{0}] はすでに切断されています。
socket.apr.read.error=ラッパー[2]でAPR /ネイティブソケット[{1}]からのデータ読み込みで予期しないエラー[{0}]が発生しました。
socket.apr.write.error=ラッパー [{2}] 経由で APR/native ソケット [{1}] へデータを書き込み中に予期せぬエラー [{0}] が発生しました。
socket.closed=このコネクションに関連付けられたソケットは閉じられました。
socket.sslreneg=SSLコネクションの再ネゴシエーション時の例外

sslHostConfig.certificate.notype=指定された複数の証明書の中に、少なくとも1つは必須要素の存在しない証明書が含まれています。
sslHostConfig.certificateVerificationInvalid=証明書検証値[{0}]が認識されません
sslHostConfig.fileNotFound=構成ファイル[{0}]は存在しません
sslHostConfig.invalid_truststore_password=提供されたトラストストアパスワードは、トラストストアのロック解除および検証に使用できませんでした。 検証をスキップするnullパスワードでトラストストアにアクセスしようとしました。
sslHostConfig.mismatch=[{0}]プロパティは[{1}]という名前のSSLHostConfigで設定され、[{2}]構成構文用ですが、[{3}]構成構文でSSLHostConfigが使用されています。
sslHostConfig.opensslconf.alreadyset=別のOpenSSLConfを設定しようとする試みが無視されます。
sslHostConfig.opensslconf.null=Null OpenSSLConfを設定しようとしましたが無視されました
sslHostConfig.prefix_missing=[{1}]というSSLHostConfigのプロトコルのリストにプロトコル[{0}]が追加されました。 +/-接頭辞がないか確認してください。

sslHostConfigCertificate.mismatch=プロパティ[{0}]は[{1}]という名前のSSLHostConfigCertificateに設定されており、証明書の格納タイプ[{2}]用ですが、証明書は[{3}]タイプのストレージで使用されています。

sslImplementation.cnfe=クラス [{0}] のインスタンスを SSLImplementation として作成できません。

sslUtilBase.active=アクティブな[{0}]は次のとおりです：[{1}]
sslUtilBase.alias_no_key_entry=別名 [{0}] はキーエントリを発見できません
sslUtilBase.invalidTrustManagerClassName=[{0}]が提供するtrustManagerClassNameはjavax.net.ssl.TrustManagerを実装していません。
sslUtilBase.keystore_load_failed=[{0}] のキーストア [{1}] の読み込みは [{2}] により失敗しました。
sslUtilBase.noCertFile=SSLコネクタを使用する場合は、SSLHostConfigのcertificateFile属性を定義する必要があります。
sslUtilBase.noCrlSupport=トラストストアプロバイダー [{0}] は設定項目 certificateRevocationFile に対応していません。
sslUtilBase.noKeys=キーストアで見つかった秘密キーのエイリアスがありません。
sslUtilBase.noVerificationDepth=トラストストアプロバイダー [{0}] は設定項目 certificateVerificationDepth に未対応です。
sslUtilBase.noneSupported=指定された[{0}]のどれもSSLエンジンでサポートされていません：[{1}]
sslUtilBase.skipped=指定された[{0}]の一部はSSLエンジンでサポートされておらず、スキップされています：[{1}]
sslUtilBase.ssl3=SSLv3 が明示的に有効化化されています。このプロトコルは安全ではありません。
sslUtilBase.tls13.auth=JSSE TLS 1.3実装は、初期ハンドシェイク後の認証をサポートしていないため、オプションのクライアント認証と互換性がありません。
sslUtilBase.trustedCertNotChecked=エイリアス[{0}]を持つ信頼できる証明書の有効期限は、証明書が不明な型であるためチェックされませんでした。
sslUtilBase.trustedCertNotValid=エイリアス[{0}]とDN [{1}]を持つ信頼できる証明書が[{2}]のために無効です。 この信頼できる証明書で署名された証明書が受け入れられるでしょう
