# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

contextBindings.noContextBoundToCL=No naming context bound to this class loader
contextBindings.noContextBoundToThread=No naming context bound to this thread
contextBindings.unknownContext=Unknown context name : [{0}]

namingContext.alreadyBound=Name [{0}] is already bound in this Context
namingContext.contextExpected=Name is not bound to a Context
namingContext.failResolvingReference=Unexpected exception resolving reference
namingContext.invalidName=Name is not valid
namingContext.nameNotBound=Name [{0}] is not bound in this Context. Unable to find [{1}].
namingContext.noAbsoluteName=Cannot generate an absolute name for this namespace
namingContext.readOnly=Context is read only

selectorContext.methodUsingName=Call to method [{0}] with a Name of [{1}]
selectorContext.methodUsingString=Call to method [{0}] with a String of [{1}]
selectorContext.noJavaUrl=This context must be accessed through a java: URL
