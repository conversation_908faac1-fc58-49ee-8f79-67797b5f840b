# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

config.storeContextError=存储上下文[{0}]时出错
config.storeServerError=存储服务器时出错

factory.storeNoDescriptor=元素类[{0}]的描述符未配置！
factory.storeTag=存储标记[{0}]（对象：[{1}]）。

storeConfigListener.notServer=侦听器已添加到服务器以外的组件，因此将被忽略

storeFileMover.directoryCreationError=无法创建目录 [{0}]
storeFileMover.renameError=无法将 [{0}] 重命名为 [{1}]
