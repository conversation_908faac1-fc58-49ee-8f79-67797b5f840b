# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

filterDef.invalidFilterName=フィルター定義に無効な <フィルター名> [{0}] があります。

securityConstraint.uncoveredHttpMethod=URLパターン[{0}]のセキュリティ制約の場合、HTTPメソッド[{1}]のみが対象となります。 その他の方法はすべて対象外です。
securityConstraint.uncoveredHttpMethodFix=次の[{1}]ではない特定のHTTPメソッドのアクセスを拒否するURLパターン[{0}]を持つセキュリティ制約を追加します。
securityConstraint.uncoveredHttpOmittedMethod=URLパターン[{0}]を持つセキュリティ制約の場合、HTTPメソッド[{1}]は検出されません。
securityConstraint.uncoveredHttpOmittedMethodFix=保護されていないHTTPメソッド[{1}]でのアクセスを拒否するURLパターン[{0}]のセキュリティ制約を追加

servletDef.invalidServletName=サーブレット定義の<servlet-name> [{0}]が無効です。

webRuleSet.absoluteOrdering=<absolute-ordering>要素はweb-fragment.xmlで無効であり無視されます。
webRuleSet.absoluteOrderingCount=<absolute-ordering>要素は1回の発生に制限されます。
webRuleSet.nameCount=<name>要素は1回に制限されます。
webRuleSet.postconstruct.duplicate=クラス [{0}]にPostConstructメソッド定義が重複しています
webRuleSet.predestroy.duplicate=クラス[{0}]の@PreDestroyメソッドの定義が重複しています
webRuleSet.relativeOrdering=<ordering>要素はweb.xmlで無効であり無視されます。
webRuleSet.relativeOrderingCount=<ordering>要素は1回の出現に制限されます。

webXml.duplicateEnvEntry=env-entry 名 [{0}] が重複しています
webXml.duplicateFilter=同じ名前のファイル [{0}] が存在します。
webXml.duplicateFragment=同名のフラグメント [{0}] が複数見つかりました。相対的な順序付けは正式な機能ではありません。詳細は Servlet Speification の 8.2.2 節 2c 項を参照してください。絶対的な順序付けの利用を検討してください。
webXml.duplicateMessageDestination=message-destination 名 [{0}] が重複しています
webXml.duplicateMessageDestinationRef=message-destination-ref 名 [{0}] が重複しています
webXml.duplicateResourceEnvRef=resource-env-ref 名 [{0}] が重複しています
webXml.duplicateResourceRef=resource-ref 名 [{0}] が重複しています
webXml.duplicateServletMapping=サーブレット [{0}] と [{1}] を同じ url-pattern [{2}] にマッピングすることはできません。
webXml.duplicateTaglibUri=URI [{0}] のタグライブラリが重複しています。
webXml.mergeConflictDisplayName=[{1}] に配置されたフラグメント [{0}] の表示名は、他のフラグメントと異なります。
webXml.mergeConflictFilter=[{2}] に配置されたフラグメント [{1}] のフィルタ [{0}] の値は、他のフラグメントと異なります。
webXml.mergeConflictLoginConfig=[{1}] に配置されたフラグメント [{0}] の LoginConfig は、他のフラグメントと異なります。
webXml.mergeConflictOrder=フラグメントの相対順序には循環参照が含まれます。 これは、web.xmlで絶対順序を使用することで解決できます。
webXml.mergeConflictResource=[{2}] に配置されたフラグメント [{1}] のリソース [{0}] の値は、他のフラグメントと異なります。
webXml.mergeConflictServlet=[{2}] に配置されたフラグメント [{1}] の Servlet [{0}] の値は、他のフラグメントと異なります。
webXml.mergeConflictSessionCookieComment=[{1}] に配置されたフラグメント [{0}] のセッションCookieコメントの値は、他のフラグメントと異なります。
webXml.mergeConflictSessionCookieDomain=[{1}] に配置されたフラグメント [{0}] のセッションCookieドメインの値は、他のフラグメントと異なります。
webXml.mergeConflictSessionCookieHttpOnly=[{1}] に配置されたフラグメント [{0}] のセッションクッキー http-only フラグの値は、他のフラグメントと異なります。
webXml.mergeConflictSessionCookieMaxAge=[{1}] に配置されたフラグメント [{0}] のセッションクッキー max-age の値は、他のフラグメントと異なります。
webXml.mergeConflictSessionCookieName=[{1}] に配置されたフラグメント [{0}] のセッションCookie名の値は、他のフラグメントと異なります。
webXml.mergeConflictSessionCookiePath=[{1}] に配置されたフラグメント [{0}] のセッションクッキー path は、他のフラグメントと異なります。
webXml.mergeConflictSessionCookieSecure=[{1}] に配置されたフラグメント [{0}] のセッションクッキー secure フラグの値は、他のフラグメントと異なります。
webXml.mergeConflictSessionTimeout=[{1}] に配置されたフラグメント [{0}] のセッションタイムアウト時間は、他のフラグメントと異なります。
webXml.mergeConflictSessionTrackingMode=[{1}] に配置されたフラグメント名 [{0}] を含む複数のフラグメントについて、セッション追跡モードの設定が一貫しません。
webXml.mergeConflictString=[{0}] の [{1}] は [{3}] に配置された複数フラグメント [{2}] で別の値が定義されています。
webXml.multipleOther=<ordering> 要素に複数の <others> 要素が指定されました。
webXml.reservedName=予約名[{0}]を使用してweb.xmlファイルが検出されました。 name要素はこのフラグメントでは無視されます。
webXml.unrecognisedPublicId=public ID [{0}]は、既知のweb.xmlファイルのpublic IDと一致しないため、バージョンを特定できませんでした。
webXml.version.unknown=[{0}] は未知のバージョン文字列です。既定値を使用します。
webXml.wrongFragmentName=web.xmlのabsolute-orderingタグで間違ったフラグメント名[{0}]を使用しました！

webXmlParser.applicationParse=アプリケーションweb.xmlファイルの位置 [{0}] の解析エラー
webXmlParser.applicationPosition=行[{0}]列[{1}]で発生しました。
webXmlParser.applicationStart=[{0}]のアプリケーションweb.xmlファイルの解析
