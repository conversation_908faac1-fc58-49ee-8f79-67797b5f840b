# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

abstractStream.windowSizeDec=Connection [{0}], Flux [{1}], réduction de la fenêtre de contrôle de flux de [{2}] à [{3}]
abstractStream.windowSizeInc=Connection [{0}], Stream [{1}], augmentez la taille de la fenêtre de contrôle de flux de [{2}] à [{3}]
abstractStream.windowSizeTooBig=Connection [{0}], Flux [{1}], L''augmentation de la taille de la fenêtre de [{2}] à [{3}] a excédé le maximum autorisé

connectionPrefaceParser.eos=Fin de flux inattendue lors de la lecture de la préface du client, seuls [{0}] octets ont été lus
connectionPrefaceParser.ioError=Echec de la lecture des octets de la préface du client
connectionPrefaceParser.mismatch=Une séquence inattendue d''octets a été recue au début de la préface  client [{0}]

connectionSettings.debug=Connection [{0}], Paramètre type [{1}] mis à [{2}]
connectionSettings.enablePushInvalid=Connection [{0}], La valeur demandée pour activer le push [{1}] n''est pas une de celles permises (zéro ou un)
connectionSettings.headerTableSizeLimit=La Connection [{0}] a essayé de configurer une taille de [{1}] pour la table des en-têtes (headers), mais la limite est 16k
connectionSettings.maxFrameSizeInvalid=Connection [{0}], la taille maximum de trame demandée  [{1}] est en-dehors des limites permises [{2}] - [{3}]
connectionSettings.unknown=Connection [{0}], Un paramètre inconnu avec l''identifiant [{1}] et la valeur [{2}] a été ignoré
connectionSettings.windowSizeTooBig=Connection [{0}], La taille de fenêtre demandée [{1}] est plus grande que la valeur maximale autorisée [{2}]

frameType.checkPayloadSize=La taille de données [{0}] n''est pas valide pour une trame de type [{1}]
frameType.checkStream=Type de trame invalide [{0}]

hpack.integerEncodedOverTooManyOctets=Un entier de taille variable de HPACK a été encodé sur trop d''octets, le maximum est de [{0}]
hpack.invalidCharacter=Le caractère Unicode [{0}] ayant le code point [{1}] ne peut être encodé, parce qu''il est en-dehors de l''éventail permis 0-255.

hpackEncoder.encodeHeader=Encodage de l''en-tête [{0}] avec la valeur [{1}]

hpackdecoder.headerTableIndexInvalid=L''index [{0}] dans la table des en-têtes n''est pas valide car il y a [{1}] en-têtes statiques et [{2}] en-têtes dynamiques
hpackdecoder.maxMemorySizeExceeded=La taille de la table des en-têtes [{0}] dépasse la taille maximale [{1}]
hpackdecoder.notImplemented=Pas encore implémenté
hpackdecoder.nullHeader=L''en-tête à l''index [{0}] est nul
hpackdecoder.tableSizeUpdateNotAtStart=Toute mise à jour de la taille de la table doit être faite avant le début d'un bloc d'en-têtes
hpackdecoder.zeroNotValidHeaderTableIndex=Zéro n'est pas un index valide dans la table des en-têtes

hpackhuffman.huffmanEncodedHpackValueDidNotEndWithEOS=La valeur encodée en Huffman dans les en-têtes HPACK n'avait pas de données tampon d'EOS
hpackhuffman.stringLiteralEOS=La valeur encodée en Huffman dans les en-têtes HPACK contenait le symbole d'EOS
hpackhuffman.stringLiteralTooMuchPadding=Plus de 7 bits de données tampon de fin de flux ont été fournis à la fin d'une chaîne encodée avec Huffman

http2Parser.headerLimitCount=Connection [{0}], Slux [{1}], Trop d''en-têtes
http2Parser.headerLimitSize=Connection [{0}], Flux [{1}], La taille totale des en-têtes est trop grosse
http2Parser.headers.wrongFrameType=Connection [{0}], Le traitement des en-têtes est en cours pour le flux [{1}] mais une trame de type [{2}] a été reçue
http2Parser.headers.wrongStream=Connection [{0}], en têtes en cours pour le flux [{1}] mais une trame du flux [{2}] a été reçue
http2Parser.nonZeroPadding=Connection [{0}], Stream [{1}], rembourrage (padding) non-zéro recu
http2Parser.payloadTooBig=La taille des données est de [{0}] octets mais la taille maximale de la trame est de [{1}]
http2Parser.preface.invalid=Une préface de connection invalide a été reçue
http2Parser.preface.io=Impossible de lire la préface de la connection
http2Parser.processFrame=Connection [{0}], Flux [{1}], Type de trame [{2}], Drapeaux [{3}], Taille des données [{4}]
http2Parser.processFrame.tooMuchPadding=Connection [{0}], Flux [{1}], La taille [{2}] des données tampon est trop grosse pour la taille de données [{3}]
http2Parser.processFrame.unexpectedType=Attendu une trame de type [{0}] mais reçu une trame de type [{1}]
http2Parser.processFrameContinuation.notExpected=Connection [{0}], La trame de continuation a été reçue pour le flux [{1}] alors qu''aucun trainement d''en-têtes n''était en cours
http2Parser.processFrameData.lengths=Connection [{0}], Flux [{1}], Taille des données, [{2}], Taille des données tampon [{3}]
http2Parser.processFrameData.window=Connection [{0}], le client a envoyé plus de données que la "stream window" ne le permet
http2Parser.processFrameHeaders.decodingDataLeft=Des données restent après le décodage de HPACK, elles auraient dû être consommées
http2Parser.processFrameHeaders.decodingFailed=Une erreur de décodage HPACK des en-têtes HTTP s'est produite
http2Parser.processFrameHeaders.payload=Connection [{0}], Flux [{1}], Traitement des en-têtes avec une taille de données de [{2}]
http2Parser.processFramePriority.invalidParent=Connection [{0}], Flux [{1}], Un flux ne peut pas dépendre de lui-même
http2Parser.processFramePushPromise=Connexion [{0}], Flux (Stream) [{1}], les trames de promesse d''envoi ("Push promise frames") ne doivent pas être envoyées par le client.
http2Parser.processFrameSettings.ackWithNonZeroPayload=La trame de paramètres a été reçue avec un indicateur ACK activé et des données présentes
http2Parser.processFrameWindowUpdate.debug=Connection [{0}], Flux [{1}], Incrémentation de [{2}] de la taille de fenêtre
http2Parser.processFrameWindowUpdate.invalidIncrement=La trame de mise à jour de la fenêtre a été reçue avec un incrément invalide [{0}]
http2Parser.swallow.debug=Connection [{0}], Flux [{1}], Avalé [{2}] octets

http2Protocol.jmxRegistration.fail=L'enregistrement dans JMX du protocole HTTP/2 a échoué

pingManager.roundTripTime=Connection [{0}] Le temps d''aller retour est de [{1}]ns

stream.clientCancel=Le client a réinitialisé la stream avant que la réponse ne soit complète
stream.closed=Connection [{0}], Flux [{1}], Impossible d''écrire sur un flux après sa fermeture
stream.header.case=Connection [{0}], Flux [{1}], Le nom d''en-tête HTTP [{2}] doit être en miniscules
stream.header.connection=Connection [{0}], Flux [{1}], L''en-tête HTTP [connection] n''est pas autorisé dans une requête HTTP/2
stream.header.contentLength=Connection [{0}], Flux [{1}], La valeur de l''en-tête content-length [{2}] ne correspond pas à la taille des données reçue [{3}]
stream.header.debug=Connection [{0}], Flux [{1}], en-tête HTTP [{2}], valeur [{3}]
stream.header.duplicate=Connection [{0}], Flux [{1}], Reçu plusieurs en-têtes [{2}]
stream.header.empty=Connection [{0}], Flux [{1}], Le nom d''en-tête nul est invalide
stream.header.invalid=Connection [{0}], Flux [{1}], L''en-tête[{2}] contenait la valeur invalide [{3}]
stream.header.noPath=Connection [{0}], flux [{1}], Le [:path] pseudo en-tête est vide
stream.header.required=Connection [{0}], Flux [{1}], Un ou plusieurs en-têtes nécessaires sont manquants
stream.header.te=Connection [{0}], Flux [{1}], L''en-tête HTTP [te] n''est pas autorisé avec la valeur [{2}] dans une requête HTTP/2
stream.header.unexpectedPseudoHeader=Connection [{0}], Flux [{1}], Le pseudo en-tête [{2}] a été reçu après un en-tête normal
stream.header.unknownPseudoHeader=Connection [{0}], Flux [{1}], Un pseudo en-tête inconnu [{2}] a été reçu
stream.inputBuffer.copy=Copide de [{0}] octets depuis inBuffer vers outBuffer
stream.inputBuffer.dispatch=Des données on été ajoutées dans inBuffer alors que la lecture est surveillée, envoi d'un évènement de lecture
stream.inputBuffer.empty=Le tampon d'entrée du flux est vide, attente de données
stream.inputBuffer.readTimeout=Délai d'attente maximum dépassé pendant la lecture des données du client
stream.inputBuffer.reset=Flux réinitialisé
stream.inputBuffer.signal=Des données ont été ajoutées dans inBuffer alors que le thread de lecture attend, cela lui sera signalé
stream.notWritable=Connection [{0}], Flux [{1}], Impossible d''écrire sur ce flux
stream.outputBuffer.flush.debug=Connection [{0}], Flux [{1}], envoi des données mises en tampon depuis la position [{2}], writeInProgress [{3}] et closed [{4}]
stream.recycle=Connection [{0}], Stream [{1}] a été recyclée
stream.reprioritisation.debug=Connection [{0}], Flux [{1}], Exclusive [{2}], Parent [{3}], Poids [{4}]
stream.reset.fail=Connection [{0}], Flux [{1}], Echec de réinitialisation du flux
stream.reset.receive=Connection [{0}], Flux [{1}], Réinitialisation reçue à cause de [{2}]
stream.reset.send=Connection [{0}], Flux [{1}], Réinitialisation envoyée à cause de [{2}]
stream.trailerHeader.noEndOfStream=Connection [{0}], Flux [{1}], Les en-têtes de fin n''incluent pas l''indicateur de fin de flux
stream.writeTimeout=Temps d'attente maximum du client dépassé pour augmenter la fenêtre de contrôle de flux pour permettre l'écriture de données

streamProcessor.cancel=Connection [{0}], Flux [{1}], Le reste du corps de la requête n''est pas nécessaire
streamProcessor.error.connection=Connection [{0}], Stream [{1}], Une erreur s''est produite dans le traitement, fatale pour la connection
streamProcessor.error.stream=Connection [{0}], Flux [{1}], Une erreur d''est produite durant le traitement qui a été fatale au flux
streamProcessor.flushBufferedWrite.entry=Connection [{0}], Flux [{1}], Envoi des écritures mises en tampon
streamProcessor.service.error=Erreur durant le traitement de la requête

streamStateMachine.debug.change=Connection [{0}], Flux [{1}], L’état a changé de [{2}] vers [{3}]
streamStateMachine.invalidFrame=Connection [{0}], Flux [{1}], Etat [{2}], Type de trame [{3}]

upgradeHandler.allocate.debug=Connection [{0}], Flux [{1}], [{2}] octets alloués
upgradeHandler.allocate.left=Connection [{0}], Flux [{1}], [{2}] octets désalloués, essai d''allocation aux enfants
upgradeHandler.allocate.recipient=Connection [{0}], Flux [{1}], receveur potentiel [{2}] avec poids [{3}]
upgradeHandler.connectionError=Erreur de la connection
upgradeHandler.dependency.invalid=Connection [{0}], Flux [{1}], Un flux ne peut dépendre de lui-même
upgradeHandler.goaway.debug=Connection [{0}], Goaway, Dernier flux [{1}], Code d''erreur [{2}], Données de débogage [{3}]
upgradeHandler.init=Connection [{0}], Etat [{1}]
upgradeHandler.initialWindowSize.invalid=Connection [{0}], La valeur [{1}] initiale de la taille de fenêtre est invalide
upgradeHandler.invalidPreface=Connection [{0}], Préface de connection invalide
upgradeHandler.ioerror=Connection [{0}]
upgradeHandler.noAllocation=Connection [{0}], Flux [{1}], Temps d''attente maximum dépassé lors de l''allocation
upgradeHandler.noNewStreams=Connection [{0}], Flux [{1}], Flux ignoré car aucun nouveau flux n''est autorisé sur cette connection
upgradeHandler.pause.entry=Connection [{0}] mise en pause
upgradeHandler.pingFailed=La connection [{0}] n''a pas réussi à envoyer un ping au client
upgradeHandler.prefaceReceived=Connection [{0}], préface de la connection recue du client
upgradeHandler.pruneIncomplete=Connexion [{0}], Flux [{1}], Erreur lors de l''élimination complète de la connexion parce que des flux sont encore actifs / utilisés dans l''arbre de priorité, il y a [{2}] flux en trop
upgradeHandler.pruneStart=Connection [{0}] Début de l''élimination des anciens flux, la limite est de [{1}] et il y a actuellement [{2}] flux
upgradeHandler.pruned=Connection [{0}] Elimination du flux terminé [{1}]
upgradeHandler.prunedPriority=La connexion [{0}] a élagué le flux inutilisé [{1}] qui faisait peut-être partie de l''arbre de priorité
upgradeHandler.releaseBacklog=Connection [{0}], Flux [{1}] enlevée de la file d''attente
upgradeHandler.rst.debug=Connexion [{0}], Flux [{1}], Erreur [{2}], Message [{3}], RST (fermeture du flux)
upgradeHandler.sendPrefaceFail=Connexion [{0}], échec d''envoi de la préface au client
upgradeHandler.socketCloseFailed=Echec de la fermeture du socket
upgradeHandler.stream.closed=Le flux [{0}] a déjà été fermé auparavant
upgradeHandler.stream.even=Un nouvel ID de flux distant (remote stream) [{0}] a été requis, mais tous les flux distants doivent utiliser ID impairs
upgradeHandler.stream.notWritable=Connection [{0}], Flux [{1}], Impossible d''écrire sur ce flux
upgradeHandler.stream.old=Un nouveau flux distant avec l''ID [{0}] a été demandé mais le flux le plus récent est [{1}]
upgradeHandler.tooManyRemoteStreams=Le client a essayé d''utiliser plus de [{0}] flux actifs
upgradeHandler.tooMuchOverhead=Connection [{0}], Le traitement est trop coûteux donc la connection sera fermée
upgradeHandler.unexpectedAck=Connection [{0}], Flux [{1}], Une notification de réception de paramètres a été reçue alors qu''aucune n''était attendue
upgradeHandler.upgrade=Connexion [{0}], HTTP/1.1 transformée en flux [1]
upgradeHandler.upgrade.fail=Connection [{0}], Echec de l''upgrade de HTTP/1.1
upgradeHandler.upgradeDispatch.entry=Entrée, Connection [{0}], SocketStatus [{1}]
upgradeHandler.upgradeDispatch.exit=Sortie, Connection [{0}], SocketState [{1}]
upgradeHandler.windowSizeReservationInterrupted=Connection [{0}], Flux [{1}], réservé [{2}] octets
upgradeHandler.windowSizeTooBig=Connection [{0}], Flux [{1}], La taille de la fenêtre est trop grosse
upgradeHandler.writeBody=Connection [{0}], Flux [{1}], Taille des données [{2}]
upgradeHandler.writeHeaders=Connection [{0}], Stream [{1}]
upgradeHandler.writePushHeaders=Connection [{0}], Flux [{1}], Flux de push [{2}], EndOfStream [{3}]

windowAllocationManager.dispatched=Connection [{0}], Flux [{1}], Envoyé
windowAllocationManager.notified=Connection [{0}], Flux [{1}], Notifié
windowAllocationManager.notify=Connection [{0}], Flux [{1}], Attente de type [{2}], Notification de type [{3}]
windowAllocationManager.waitFor.connection=Connection [{0}], Flux [{1}], Attente d''une fenêtre de contrôle de flux de Connection (bloquante) avec une délai maximum d''attente de [{2}]
windowAllocationManager.waitFor.ise=Connection [{0}], Flux [{1}], Déjà en train d''attendre
windowAllocationManager.waitFor.stream=Connection [{0}], Flux [{1}], Attente d''une fenêtre de contrôle de flux de Flux (bloquante) avec une délai maximum d''attente de [{2}]
windowAllocationManager.waitForNonBlocking.connection=Connection [{0}], Flux [{1}], Attente d''une fenêtre de contrôle de flux de Connection (non bloquante)
windowAllocationManager.waitForNonBlocking.stream=Connection [{0}], Flux [{1}], Attente d''une fenêtre de contrôle de flux de Flux (non bloquante)

writeStateMachine.endWrite.ise=il est illégal de spécifier [{0}] pour le nouvel état dès lors qu''une écriture s''est terminée
writeStateMachine.ise=Il est illégal d''appeler [{0}()] dans l''état [{1}]
